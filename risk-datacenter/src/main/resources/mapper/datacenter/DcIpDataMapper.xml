<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.datacenter.mapper.DcIpDataMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.datacenter.model.DcIpData">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="ip_start" property="ipStart" jdbcType="VARCHAR"/>
        <result column="ip_end" property="ipEnd" jdbcType="VARCHAR"/>
        <result column="num_start" property="numStart" jdbcType="BIGINT"/>
        <result column="num_end" property="numEnd" jdbcType="BIGINT"/>
        <result column="num" property="num" jdbcType="INTEGER"/>
        <result column="land" property="land" jdbcType="VARCHAR"/>
        <result column="region" property="region" jdbcType="VARCHAR"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="area" property="area" jdbcType="VARCHAR"/>
        <result column="country" property="country" jdbcType="VARCHAR"/>
        <result column="country_english" property="countryEnglish" jdbcType="VARCHAR"/>
        <result column="isp" property="isp" jdbcType="VARCHAR"/>
        <result column="country_id" property="countryId" jdbcType="VARCHAR"/>
        <result column="international_code" property="internationalCode" jdbcType="VARCHAR"/>
        <result column="region_id" property="regionId" jdbcType="VARCHAR"/>
        <result column="city_id" property="cityId" jdbcType="VARCHAR"/>
        <result column="area_id" property="areaId" jdbcType="VARCHAR"/>
        <result column="lat" property="lat" jdbcType="VARCHAR"/>
        <result column="lng" property="lng" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List" >
        id, ip_start, ip_end, num_start, num_end, num,
        land, region, city, area, country, country_english,
        isp, country_id,international_code,region_id,city_id,area_id,
        lat, lng
    </sql>

    <select id="getAreaByIp" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from dc_data_ip
        where num_start &lt;= #{ipNum}
        and num_end &gt;= #{ipNum}
    </select>

</mapper>