<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.datacenter.mapper.DcSubmitReCertificationContactInfoMapper" >

    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.datacenter.DcSubmitContactInfo" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="operation_log_id" property="operationLogId" jdbcType="INTEGER" />
        <result column="user_key" property="userKey" jdbcType="VARCHAR" />
        <result column="relation" property="relation" jdbcType="VARCHAR" />
        <result column="contact_name" property="contactName" jdbcType="VARCHAR"
                typeHandler="com.youxin.risk.commons.mybatis.ContactMaskHandler"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"
                typeHandler="com.youxin.risk.commons.mybatis.ContactMaskHandler"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="hit_rule" jdbcType="VARCHAR" property="hitRule" />
        <result column="status_node" jdbcType="VARCHAR" property="statusAndNode" />
        <result column="recertification_flag" jdbcType="BIT" property="reCertificationFlag" />
    </resultMap>


    <sql id="table_name">
        dc_submit_recertification_contact_info
    </sql>

    <sql id="Base_Column_List" >
        id, operation_log_id, user_key, relation, contact_name, mobile, create_time, update_time,hit_rule,status_node,recertification_flag
    </sql>

    <!--批量插入  useGeneratedKeys="true"-->
    <insert id="insertBatch"   parameterType="java.util.List">
        insert into <include refid="table_name"/> (operation_log_id, user_key,relation, contact_name, mobile,hit_rule,status_node,recertification_flag
        )
        values
        <!--item就是List里每一项的对象名，要用","分割每一条数据，最后要";"结尾-->
        <foreach collection="list" item="item" index="index" separator="," close=";">
            ( #{item.operationLogId,jdbcType=VARCHAR}, #{item.userKey,jdbcType=VARCHAR}
            , #{item.relation,jdbcType=VARCHAR}
            , #{item.contactName,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.ContactMaskHandler}
            , #{item.mobile,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.ContactMaskHandler}
            , #{item.hitRule}, #{item.statusAndNode}, #{item.reCertificationFlag}
            )
        </foreach>
    </insert>

    <select id="getByUserKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from <include refid="table_name"/>
        WHERE user_key = #{userKey}  AND operation_log_id = ( SELECT operation_log_id FROM <include refid="table_name"/> WHERE user_key = #{userKey} ORDER BY id DESC LIMIT 1 )
    </select>


</mapper>