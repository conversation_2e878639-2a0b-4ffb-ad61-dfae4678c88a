<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.datacenter.mapper.DcSubmitAmountECMapper" >
  <resultMap id="BaseResultMap" type="com.youxin.risk.datacenter.model.DcSubmitAmountEC" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="operation_log_id" property="operationLogId" jdbcType="INTEGER"/>
    <result column="user_key" property="userKey" jdbcType="VARCHAR" />
    <result column="reg_num" property="regNum" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="capital" property="capital" jdbcType="VARCHAR" />
    <result column="person" property="person" jdbcType="VARCHAR" />
    <result column="address" property="address" jdbcType="VARCHAR" />
    <result column="business" property="business" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="VARCHAR" />
    <result column="period" property="period" jdbcType="VARCHAR" />
    <result column="composing_form" property="composingForm" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, operation_log_id, user_key, reg_num, name, capital, person, address, business, type, period, composing_form, create_time, update_time
  </sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from dc_submit_amount_enterprise_certification
    where id = #{id,jdbcType=INTEGER}
  </select>


  <insert id="insert" parameterType="com.youxin.risk.datacenter.model.DcSubmitAmountVehicle" >
    <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
      SELECT LAST_INSERT_ID() AS id
    </selectKey>
    insert into dc_submit_amount_enterprise_certification (
        operation_log_id,
        user_key,
        reg_num,
        name,
        capital,
        person,
        address,
        business,
        type,
        period,
        composing_form,
        create_time,
        update_time
      )
    values (
        #{operationLogId},
        #{userKey},
        #{regNum},
        #{name},
        #{capital},
        #{person},
        #{address},
        #{business},
        #{type},
        #{period},
        #{composingForm},
        #{createTime},
        #{updateTime}
      )
  </insert>

  <select id="getByUserKey" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from dc_submit_amount_enterprise_certification
    where user_key = #{userKey} order  by id DESC limit 1
  </select>

</mapper>