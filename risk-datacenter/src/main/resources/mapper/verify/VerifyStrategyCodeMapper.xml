<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.verify.mapper.VerifyStrategyCodeMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.verify.model.VerifyStrategyCode">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="strategy_code" jdbcType="VARCHAR" property="strategyCode" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="version" jdbcType="INTEGER" property="version" />
    </resultMap>


    <sql id="table_name">
      verify_strategy_code
    </sql>


    <insert id="insert" parameterType="com.youxin.risk.verify.model.VerifyStrategyCode">
        insert into
        <include refid="table_name"/>
        (strategy_code,create_time,version)
        values
        (#{strategyCode},now(),#{version})
    </insert>


</mapper>