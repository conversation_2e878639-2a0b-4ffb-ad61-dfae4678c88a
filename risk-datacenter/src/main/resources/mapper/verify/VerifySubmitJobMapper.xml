<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.verify.mapper.VerifySubmitJobMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.verify.model.VerifySubmitJob">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="operation_log_id" jdbcType="INTEGER" property="operationLogId" />
        <result column="user_key" jdbcType="VARCHAR" property="userKey" />
        <result column="industry" jdbcType="VARCHAR" property="industry" />
        <result column="company_position" jdbcType="VARCHAR" property="companyPosition" />
        <result column="salary" jdbcType="VARCHAR" property="salary" />
        <result column="pay_day" jdbcType="VARCHAR" property="payDay" />
        <result column="company_name" jdbcType="VARCHAR" property="companyName" />
        <result column="company_province" jdbcType="VARCHAR" property="companyProvince" />
        <result column="company_city" jdbcType="VARCHAR" property="companyCity" />
        <result column="company_district" jdbcType="VARCHAR" property="companyDistrict" />
        <result column="company_address" jdbcType="VARCHAR" property="companyAddress" />
        <result column="company_phone" jdbcType="VARCHAR" property="companyPhone"
                typeHandler="com.youxin.risk.commons.mybatis.JobMaskHandler"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="version" jdbcType="INTEGER" property="version" />
    </resultMap>


    <sql id="table_name">
      submit_job
    </sql>

    <sql id="Base_Column_List">
        id,operation_log_id,user_key,industry,company_position,salary,pay_day,
        company_name,company_province,company_city,company_district,company_address,
        company_phone,update_time,create_time,version
    </sql>

    <insert id="insert" parameterType="com.youxin.risk.verify.model.VerifySubmitJob">
        insert into
        <include refid="table_name"/>
        (operation_log_id,user_key,industry,company_position,salary,pay_day,company_name,company_province,company_city,company_district,company_address,company_phone,update_time,create_time,version)
        values
        (#{operationLogId},#{userKey},#{industry},#{companyPosition},#{salary},#{payDay},#{companyName},#{companyProvince},#{companyCity},#{companyDistrict},#{companyAddress},
            #{companyPhone,typeHandler=com.youxin.risk.commons.mybatis.JobMaskHandler},now(),now(),#{version})
    </insert>


    <select id="findLastSubmitInfoByUserKey" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        FROM submit_job WHERE user_key = #{userKey}
        ORDER BY id DESC limit 0,1
    </select>


    <select id="findLastSubmitInfoByUserKeyNoId" resultMap="BaseResultMap" >
        select industry,company_position,salary,pay_day,
        company_name,company_province,company_city,company_district,company_address,
        company_phone,update_time,create_time,version
        FROM submit_job WHERE user_key = #{userKey}
        ORDER BY id DESC limit 0,1
    </select>
</mapper>