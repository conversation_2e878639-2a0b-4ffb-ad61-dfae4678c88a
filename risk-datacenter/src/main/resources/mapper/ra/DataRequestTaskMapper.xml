<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.ra.mapper.DataRequestTaskMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.ra.model.DataRequestTask">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="apply_id" jdbcType="INTEGER" property="applyId"/>
        <result column="source_system" jdbcType="VARCHAR" property="sourceSystem"/>
        <result column="task_type" jdbcType="VARCHAR" property="taskType"/>
        <result column="is_force" jdbcType="BIT" property="force"/>
        <result column="user_key" jdbcType="VARCHAR" property="userKey"/>
        <result column="loan_key" jdbcType="VARCHAR" property="loanKey"/>
        <result column="idcard_number" jdbcType="VARCHAR" property="idcardNumber"/>
        <result column="expired_time" jdbcType="TIMESTAMP" property="expiredTime"/>
        <result column="task_mode" jdbcType="VARCHAR" property="taskMode"/>
        <result column="task_status" jdbcType="VARCHAR" property="taskStatus"/>
        <result column="job_id" jdbcType="VARCHAR" property="jobID"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
    </resultMap>

    <sql id="table_name">
      ra_data_request_task
    </sql>

    <insert id="insert" parameterType="com.youxin.risk.ra.model.DataRequestTask">
        insert into
        <include refid="table_name"/>
        (apply_id,source_system,task_type,is_force,user_key,loan_key,idcard_number,expired_time,task_mode,task_status,job_id,create_time,update_time,version)
        values
        (#{applyId},#{sourceSystem},#{taskType},#{force},#{userKey},#{loanKey},#{idcardNumber},#{expiredTime},#{taskMode},#{taskStatus},#{jobID},now(),now(),#{version})
    </insert>

    <select id="get" resultMap="BaseResultMap">
        select * from
        <include refid="table_name"/>
        where id = #{taskId}
    </select>


    <update id="update" parameterType="com.youxin.risk.ra.model.DataRequestTask">
        update
        <include refid="table_name"/>
        <set>
            <if test="applyId != null and applyId !=''">
                apply_id = #{applyId},
            </if>
            <if test="sourceSystem != null and sourceSystem!=''">
                source_system = #{sourceSystem},
            </if>
            <if test="taskType != null">
                task_type = #{taskType},
            </if>
            <if test="force != null">
                is_force = #{force},
            </if>
            <if test="userKey != null and userKey!=''">
                user_key = #{userKey},
            </if>
            <if test="loanKey != null and loanKey !=''">
                loan_key = #{loanKey},
            </if>
            <if test="idcardNumber != null and idcardNumber !=''">
                idcard_number = #{idcardNumber},
            </if>
            <if test="expiredTime != null">
                expired_time = #{expiredTime},
            </if>
            <if test="taskMode != null ">
                task_mode = #{taskMode},
            </if>
            <if test="taskStatus != null ">
                task_status = #{taskStatus},
            </if>
            <if test="jobID != null and jobID !=''">
                job_id = #{jobID},
            </if>
            <if test="updateTime != null ">
                update_time = #{updateTime},
            </if>
            <if test="version != null and version !=''">
                version = #{version},
            </if>
        </set>
        where id = #{id}
    </update>


    <select id="findDataRequestTaskByApplyIdAndType" resultMap="BaseResultMap">
        select * from
        <include refid="table_name"/>
        where apply_id = #{applyId} and task_type =#{taskType} order by id desc
        limit 1
    </select>

    <select id="findDataRequestTaskByUserAndTypeTaskStatus" resultMap="BaseResultMap">
        select * from
        <include refid="table_name"/>
        <where>
            <if test="sourceSystem != null">
                AND source_system = #{sourceSystem}
            </if>
            <if test="userKey != null">
                AND user_key = #{userKey}
            </if>
            <if test="taskType != null ">
                AND task_type = #{taskType}
            </if>
            <if test="taskStatus != null">
                AND task_status = #{taskStatus}
            </if>
        </where>
        order by id desc limit 1
    </select>

    <select id="findDataRequestTaskByUserKeyAndType" resultMap="BaseResultMap">
        select * from
        <include refid="table_name"/>
        where source_system=#{sourceSystem} AND user_key = #{userKey} AND task_type = #{taskType} order by id desc
    </select>

    <select id="findDataRequestTaskByJobIdAndType" resultMap="BaseResultMap">
        select * from
        <include refid="table_name"/>
        where job_id = #{jobId} and task_type = #{taskType} order by id desc
        limit 1
    </select>

    <select id="findLastTaskByUser" resultMap="BaseResultMap">
        select
        *
        FROM
        <include refid="table_name"/>
        t1 JOIN (
        SELECT MAX(id) AS id FROM
        <include refid="table_name"/>
        WHERE
        source_system=#{sourceSystem} and user_key = #{userKey}
        and <![CDATA[ create_time <= #{createTime} ]]>
        GROUP BY task_type
        ) t2 ON t1.id = t2.id
    </select>

    <select id="findDataRequestTaskByJobId" resultMap="BaseResultMap">
        select * from
        <include refid="table_name"/>
        where job_id = #{jobID} order by id desc limit 1
    </select>

    <select id="findOneBy" resultMap="BaseResultMap">
        select * from
        <include refid="table_name"/>
        where user_key = #{userKey} AND source_system = #{sourceSystem} and task_type = #{taskType} AND loan_key = #{loanKey} order by id desc
        limit 1
    </select>


</mapper>