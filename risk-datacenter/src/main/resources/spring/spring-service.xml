<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans"

       xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans-4.0.xsd">

    <!--<bean class="com.youxin.risk.commons.cacheloader.service.impl.DictionaryServiceImpl"/>-->
    <!--<bean class="com.youxin.risk.commons.cacheloader.DictionaryCacheLoader"/>-->
    <bean id="repeatService" class="com.youxin.risk.commons.tools.repeat.RepeatService">
        <property name="retryableJedis" ref="retryableJedis"/>
    </bean>
    <bean id="redisLock" class="com.youxin.risk.commons.tools.lock.RedisLock">
        <property name="retryableJedis" ref="retryableJedis"/>
    </bean>
    <bean id="verifyGpsAddressService" class="com.youxin.risk.commons.service.verify.VerifyGpsAddressService" />
    <bean id="dcVerifyGpsAddressService" class="com.youxin.risk.commons.service.datacenter.DcVerifyGpsAddressService" />
    <bean id="remoteDateServiceImpl" class="com.youxin.risk.datacenter.service.impl.RemoteDateServiceImpl" />
<!--    <bean id="verifyUserLineManagementService" class="com.youxin.risk.verify.service.impl.VerifyUserLineManagementService"/>-->
    <bean id="verifyResultService" class="com.youxin.risk.commons.service.verify.VerifyResultService"/>

    <bean id="eventService" class="com.youxin.risk.commons.service.engine.EventService"/>

    <!-- AmountResultProcesser子类定义 -->
    <bean id="amountResultProcessers" class="java.util.HashMap">
        <constructor-arg>
            <map>
                <entry key="AMOUNT_ASSIGN" value-ref="assignAmountProcesser" />
                <entry key="AMOUNT_REPAY" value-ref="repayAmountProcesser" />
                <entry key="AMOUNT_MANUAL" value-ref="manualAmountProcesser"/>
                <entry key="AMOUNT_MIDDLE" value-ref="middleAmountProcesser"/>
                <!-- 默认额度case,走父类保存额度流程 -->
                <entry key="AMOUNT_DEFAULT" value-ref="defaultAmountProcesser"/>
                <entry key="AMOUNT_Manual_A" value-ref="manualAmountAProcesser"/>
            </map>
        </constructor-arg>
    </bean>

    <bean id="threadPool" class="com.youxin.risk.ra.thread.ThreadPool">
        <property name="corePoolSize" value="300" />
    </bean>

    <bean id="diServiceServiceImpl" class="com.youxin.risk.commons.cacheloader.service.impl.DiServiceServiceImpl"/>
    <bean id="diServiceCacheLoader" class="com.youxin.risk.commons.cacheloader.DiServiceCacheLoader">
        <property name="diServiceService" ref="diServiceServiceImpl"/>
        <property name="invokeServiceClass" value="false" />
    </bean>

    <bean id="processNodeService" class="com.youxin.risk.commons.cacheloader.service.impl.ProcessNodeServiceImpl"/>
    <bean id="processNodeCacheLoader" class="com.youxin.risk.commons.cacheloader.ProcessNodeCacheLoader"/>

    <bean id="kafkaDynamicSender" class="com.youxin.risk.commons.kafkav2.sender.KafkaDynamicSender">
        <constructor-arg ref="kafkaMirrorTemplate"></constructor-arg>
        <constructor-arg ref="kafkaAppName"></constructor-arg>
    </bean>
</beans>