<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:task="http://www.springframework.org/schema/task"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
	http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-4.0.xsd">

<!--	<bean id="verifyScheduleTaskService" class="com.youxin.risk.verify.schedule.VerifyScheduleTaskService" init-method="startTask">
	</bean>-->
	<!--<bean id="dataCleanSchedule" class="com.youxin.risk.verify.schedule.DataCleanSchedule" init-method="startTask">
	</bean>-->


	<task:executor id="executor" pool-size="5" />
	<task:scheduler id="scheduler" pool-size="5" />

	<task:annotation-driven scheduler="scheduler" executor="executor" proxy-target-class="true"/>



</beans>