package com.youxin.risk.verify.constants;

import com.youxin.risk.verify.enums.VerifyNode;
import com.youxin.risk.verify.enums.VerifyStrategyType;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

public enum VerifyChannelConstants {
    PAY_DAY_LOAN {
        @Override
        public String getChannelCode() {
            return null;
        }

        @Override
        public VerifyStrategyType getStrategyType() {
            return VerifyStrategyType.VERIFY;
        }

        @Override
        public VerifyNode getNextNode(String nodeName, boolean isReloan){
        	return this.getNextNode(strategyProcessPaydayloan, nodeName, isReloan,PAY_DAY_LOAN_PROCESSES);
        }

        @Override
        public VerifyNode getNode(String nodeName, boolean isReloan){
        	return this.getNode(strategyProcessPaydayloan, nodeName, isReloan, PAY_DAY_LOAN_PROCESSES);
        }

		@Override
		public boolean isFirstNode(String nodeName, boolean isReloan) {
			return this.isFirstNode(strategyProcessPaydayloan, nodeName, isReloan, PAY_DAY_LOAN_PROCESSES);
		}
    },
    RONG_360 {

        @Override
        public String getChannelCode() {
            return "rong360";
        }

        @Override
        public VerifyStrategyType getStrategyType() {
            return VerifyStrategyType.RONG_360;
        }


        @Override
        public VerifyNode getNextNode(String nodeName, boolean isReloan){
        	return this.getNextNode(strategyProcessRong360, nodeName, isReloan,RONG_360_PROCESSES);
        }

        @Override
        public VerifyNode getNode(String nodeName, boolean isReloan){
        	return this.getNode(strategyProcessRong360, nodeName, isReloan, RONG_360_PROCESSES);
        }

		@Override
		public boolean isFirstNode(String nodeName, boolean isReloan) {
			return this.isFirstNode(strategyProcessRong360, nodeName, isReloan, RONG_360_PROCESSES);
		}

    },
    HAO_HUAN {
        @Override
        public String getChannelCode() {
            return "HAO_HUAN";
        }

        @Override
        public VerifyStrategyType getStrategyType() {
            return VerifyStrategyType.VERIFY_HAOHUAN;
        }

		@Override
		public VerifyNode getNextNode(String nodeName, boolean isReloan) {
			return this.getNextNode(strategyProcessHaoHuan, nodeName, isReloan, HAO_HUAN_PROCESSES);
		}

		@Override
		public VerifyNode getNode(String nodeName, boolean isReloan) {
			return this.getNode(strategyProcessHaoHuan, nodeName, isReloan, HAO_HUAN_PROCESSES);
		}

		@Override
		public boolean isFirstNode(String nodeName, boolean isReloan) {
			return this.isFirstNode(strategyProcessHaoHuan, nodeName, isReloan, HAO_HUAN_PROCESSES);
		}
    },
    REN_REN_DAI {
        @Override
        public String getChannelCode() {
            return null;
        }

        @Override
        public VerifyStrategyType getStrategyType() {
            return VerifyStrategyType.VERIFY_RRD;
        }

        @Override
        public VerifyNode getNextNode(String nodeName, boolean isReloan){
            return null;
        }

        @Override
        public VerifyNode getNode(String nodeName, boolean isReloan){
            return null;
        }

        @Override
        public boolean isFirstNode(String nodeName, boolean isReloan) {
            return true;
        }
    },
    ELEVATE_SOLUTION {

        @Override
        public String getChannelCode() {
            return "ELEVATE_SOLUTION";
        }

        @Override
        public VerifyStrategyType getStrategyType() {
            return VerifyStrategyType.VERIFY_ELEVATE_SOLUTION;
        }

        @Override
        public VerifyNode getNextNode(String nodeName, boolean isReloan) {
            return null;
        }

        @Override
        public VerifyNode getNode(String nodeName, boolean isReloan) {
            return null;
        }

        @Override
        public boolean isFirstNode(String nodeName, boolean isReloan) {
            return false;
        }
    };
    public abstract String getChannelCode();

    public abstract VerifyStrategyType getStrategyType();

    private static final String NEW = "NEW";

    private static final String RELOAN = "RELOAN";

    private static VerifyNode[] PAY_DAY_LOAN_PROCESSES= {VerifyNode.A, VerifyNode.B};

    private static VerifyNode[] RONG_360_PROCESSES= {VerifyNode.A};

    private static VerifyNode[] HAO_HUAN_PROCESSES = { VerifyNode.PA, VerifyNode.A, VerifyNode.B, VerifyNode.C };

    private static Map<String,VerifyNode[]> strategyProcessPaydayloan = new HashMap(){
    	{
    		this.put(NEW,PAY_DAY_LOAN_PROCESSES);
    		this.put(RELOAN, PAY_DAY_LOAN_PROCESSES);
    	}
    };

    private static Map<String,VerifyNode[]> strategyProcessRong360 = new HashMap(){
    	{
    		this.put(NEW,RONG_360_PROCESSES);
    		this.put(RELOAN, RONG_360_PROCESSES);
    	}
    };

    private static Map<String,VerifyNode[]> strategyProcessHaoHuan = new HashMap(){
    	{
    		this.put(NEW,HAO_HUAN_PROCESSES);
    		this.put(RELOAN, HAO_HUAN_PROCESSES);
    	}
    };
    public abstract VerifyNode getNextNode(String nodeName, boolean isReloan);

    public abstract VerifyNode getNode(String nodeName, boolean isReloan);

    public abstract boolean isFirstNode(String nodeName, boolean isReloan);

    public VerifyNode getNode(VerifyNode[] nodes,String nodeName){
    	if(StringUtils.isBlank(nodeName)){
    		return nodes[0];
    	}

    	for(VerifyNode node : nodes){
    		if(node.name().equals(nodeName)){
    			return node;
    		}
    	}

    	return null;
    }

    public VerifyNode getNode(Map<String,VerifyNode[]> strategyMap,String nodeName, boolean isReloan,VerifyNode[] defaultProcessList){
    	String key = NEW;
    	if(isReloan){
    		key = RELOAN;
    	}
    	VerifyNode[] processList = strategyMap.get(key);
    	if(processList == null){
    		processList = defaultProcessList;
    	}

    	return this.getNode(processList, nodeName);

    }

    public VerifyNode getNextNode(VerifyNode[] nodes,String nodeName){

    	for(int i=0;i<nodes.length;i++){
    		VerifyNode node = nodes[i];
    		if(node.name().equals(nodeName)){
    			if(i == nodes.length-1){
    				return null;
    			}else{
    				return nodes[i+1];
    			}
    		}
    	}
    	return null;
    }

    public VerifyNode getNextNode(Map<String,VerifyNode[]> strategyMap,String nodeName, boolean isReloan,VerifyNode[] defaultProcessList){
    	String key = NEW;
    	if(isReloan){
    		key = RELOAN;
    	}
    	VerifyNode[] processList = strategyMap.get(key);
    	if(processList == null){
    		processList = defaultProcessList;
    	}

    	return this.getNextNode(processList, nodeName);

    }

    public boolean isFirstNode(Map<String,VerifyNode[]> strategyMap,String nodeName, boolean isReloan,VerifyNode[] defaultProcessList) {
    	String key = NEW;
    	if(isReloan){
    		key = RELOAN;
    	}
    	VerifyNode[] processList = strategyMap.get(key);
    	if(processList == null){
    		processList = defaultProcessList;
    	}
    	return this.isFirstNode(processList, nodeName);
    }

    public boolean isFirstNode(VerifyNode[] nodes,String nodeName) {
    	return nodes != null && nodes.length > 0 && nodes[0].name().equals(nodeName);
    }


    public static VerifyChannelConstants getConstantsByChannelCode(
            String channelCode) {
        for (VerifyChannelConstants channel : VerifyChannelConstants.values()) {
            if (StringUtils.isEmpty(channel.getChannelCode())) {
                continue;
            } else {
                if (channel.getChannelCode().equals(channelCode)) {
                    return channel;
                }
            }
        }
        return PAY_DAY_LOAN;
    }

    public static VerifyChannelConstants getConstantsBySourceSystem(String sourceSystem){

  	  for (VerifyChannelConstants channel : VerifyChannelConstants.values()) {
  		  if(channel.name().equals(sourceSystem)){
  			  return channel;
  		  }
        }

  	  return PAY_DAY_LOAN;
  }
}
