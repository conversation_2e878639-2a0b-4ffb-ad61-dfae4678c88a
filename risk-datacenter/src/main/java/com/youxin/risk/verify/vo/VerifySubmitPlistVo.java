package com.youxin.risk.verify.vo;

import java.util.List;

public class VerifySubmitPlistVo extends VerifyCommonVo {
    private List<VerifySubmitPlistDetailsVo> plist;
    private String isAuthorized;
    private String os;

    public String getIsAuthorized() {
        return isAuthorized;
    }

    public void setIsAuthorized(String isAuthorized) {
        this.isAuthorized = isAuthorized;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public List<VerifySubmitPlistDetailsVo> getPlist() {
        return plist;
    }

    public void setPlist(List<VerifySubmitPlistDetailsVo> plist) {
        this.plist = plist;
    }

}
