package com.youxin.risk.verify.config;

import com.youxin.risk.commons.model.verify.VerifyConfig;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.verify.service.VerifyConfigLoaderService;
import com.youxin.risk.verify.vo.VerifyConfigCacheVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class VerifyConfigLoader {
    private static Logger logger = LoggerFactory
        .getLogger(VerifyConfigLoader.class);

    /** 缓存时间(分钟) */
    private static final Integer CACHE_MINUTE = 1;

    /** 本地缓存 */
    private static Map<String, VerifyConfigCacheVo> CONFIG_LOCAL_CACHE = new HashMap<String, VerifyConfigCacheVo>();

    private static VerifyConfigLoaderService verifyConfigLoaderService;

    private boolean isInitLoader = false;
    
    public void init() {
        load();
        reinit();
    }
    
    private void load() {
    	if (verifyConfigLoaderService == null) {
            throw new RuntimeException("如果需要<配置信息本地缓存>功能，需要注入ConfigService服务。");
        }

        List<VerifyConfig> list = verifyConfigLoaderService.getAllConfigInfo();
        if (list == null) {
            logger.error("配置信息列表为空。");
            return;
        }

        for (VerifyConfig configInfoVo : list) {
            VerifyConfigLoader.setCache(configInfoVo);
        }
    }
    
    private void reinit() {
    	if(isInitLoader) {
    		return;
    	}
    	synchronized(this){
    		if(isInitLoader) {
        		return;
        	}
    		isInitLoader = true;
    	}
    	
    	new Thread(new Runnable() {
			@Override
			public void run() {
				for(int i = 0;true;) {
					try {
						logger.info("loadVerifyConfig_start");
						load();
						i = 0;
						logger.info("loadVerifyConfig_end");
					} catch (Exception e) {
						i++;
						if(0 == i %3) {
							i = 0;
							logger.error("loadVerifyConfigException", e);
						}else {
							logger.warn("loadVerifyConfigException, errmsg=" + e.getMessage());
						}
					}finally {
						try {
							Thread.sleep(5* 60 * 1000);
						} catch (InterruptedException e) {
							// nothing
						}
					}
				}
			}
		}).start();
    }

    public static String getValue(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }

        VerifyConfig configInfoVo = VerifyConfigLoader.getCache(key);
        if (configInfoVo != null) {
            return configInfoVo.getValue();
        }

        try {
            configInfoVo = verifyConfigLoaderService.getConfigInfo(key);
        } catch (Exception e) {
            logger.error("读取配置[" + key + "]失败。", e);
            return null;
        }

        if (configInfoVo != null) {
            VerifyConfigLoader.setCache(configInfoVo);
            return configInfoVo.getValue();
        }

        logger.error("配置[{}]不存在。", key);
        return null;
    }

    public static Integer getValueAsInteger(String key) {
        return Integer.parseInt(VerifyConfigLoader.getValue(key));
    }

    public static Long getValueAsLong(String key) {
        return Long.parseLong(VerifyConfigLoader.getValue(key));
    }

    public static Double getValueAsDouble(String key) {
        return Double.parseDouble(VerifyConfigLoader.getValue(key));
    }

    public static Boolean getValueAsBoolean(String key) {
        return Boolean.parseBoolean(VerifyConfigLoader.getValue(key));
    }

    public static BigDecimal getValueAsBigDecimal(String key) {
        return new BigDecimal(VerifyConfigLoader.getValue(key));
    }

    public static Date getValueAsDate(String key, String format)
            throws ParseException {
        return org.apache.commons.lang3.time.DateUtils.parseDate(VerifyConfigLoader.getValue(key), format);
    }

    /**
     * 获取缓存
     *
     * @param key
     * @return
     */
    private static VerifyConfig getCache(String key) {
        VerifyConfigCacheVo cacheVo = CONFIG_LOCAL_CACHE.get(key);
        if (cacheVo == null || cacheVo.getCacheConfig() == null) {
            return null;
        }
        if (cacheVo.isExpire()) {
            return null;
        }
        return cacheVo.getCacheConfig();
    }

    /**
     * 设置缓存
     *
     * @param configInfoVo
     */
    public static void setCache(VerifyConfig configInfoVo) {
        if (CONFIG_LOCAL_CACHE.get(configInfoVo.getKey()) != null) {
            CONFIG_LOCAL_CACHE.remove(configInfoVo.getKey());
        }
        CONFIG_LOCAL_CACHE.put(configInfoVo.getKey(),
            new VerifyConfigCacheVo(CACHE_MINUTE, configInfoVo));

        logger.info("config cache key<{}> value<{}>", configInfoVo.getKey(),
            configInfoVo.getValue());
    }

    public static boolean setConfig(String key, String value) {
        try {
            VerifyConfigCacheVo configCache = CONFIG_LOCAL_CACHE.get(key);
            if (configCache == null) {
                logger.error("配置[" + key + "]不存在。");
                return false;
            }
            VerifyConfig config = configCache.getCacheConfig();
            config.setValue(value);
            VerifyConfigLoader.setCache(config);
            verifyConfigLoaderService.update(key, value, 0);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @SuppressWarnings("static-access")
    public void setVerifyConfigLoaderService(
            VerifyConfigLoaderService verifyConfigLoaderService) {
        VerifyConfigLoader.verifyConfigLoaderService = verifyConfigLoaderService;
    }
}
