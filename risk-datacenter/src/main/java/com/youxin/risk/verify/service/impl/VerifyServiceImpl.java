package com.youxin.risk.verify.service.impl;

import com.youxin.risk.commons.mongo.VerifyStrategyResultDao;
import com.youxin.risk.commons.utils.JsonUtils;
import com.youxin.risk.commons.vo.VerifyStrategyResultVo;
import com.youxin.risk.verify.service.VerifyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;

@Service
public class VerifyServiceImpl implements VerifyService {

    private static final Logger LOG = LoggerFactory
            .getLogger(VerifyServiceImpl.class);

    @Autowired
    private VerifyStrategyResultDao verifyStrategyResultDao;

    @Override
    public Boolean saveStrategyResultToMongo(VerifyStrategyResultVo vo) {
        int retryCount = 0;
        while(retryCount < 3) {
            try {
                if(vo.getId() == null) {
                    LOG.info("save verify strategy result to mongo,data={}", JsonUtils.toJson(vo));
                    this.verifyStrategyResultDao.insert(vo);
                }else {
                    LOG.info("update verify strategy result to mongo,data={}", JsonUtils.toJson(vo));
                    this.verifyStrategyResultDao.update(vo);
                }
                return true;
            }catch(Exception e) {
                if(retryCount == 2) {
                    LOG.error("save verify strategy result to mongo error,data={}",JsonUtils.toJson(vo),e);
                }else {
                    LOG.warn("save verify strategy result to mongo warning,data={}",JsonUtils.toJson(vo),e);
                }
            }
            retryCount++;
        }

        return false;

    }

    /**
     * 查询策略结果
     *
     * @param loanKey
     * @return
     */
    @Override
    public VerifyStrategyResultVo findVerifyStrategyResult(String loanKey) {

        int retryCount = 0;
        while(retryCount < 3) {
            try {
                return this.verifyStrategyResultDao.getByLoanKey(loanKey);
            }catch(Exception e) {
                if(retryCount == 2) {
                    LOG.error("query verify strategy result from mongo error,loanKey={}",loanKey,e);
                }else {
                    LOG.warn("query verify strategy result from mongo warning,loanKey={}",loanKey,e);
                }

            }
            retryCount++;
        }
        return null;
    }

}
