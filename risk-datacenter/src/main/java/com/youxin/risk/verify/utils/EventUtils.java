package com.youxin.risk.verify.utils;

import com.youxin.risk.commons.event.annotation.EventAnnotation;
import com.youxin.risk.commons.kafkav2.sender.KafkaDynamicSender;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;


/**
 * @ClassName EventUtils
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/12/21
 **/
@Component
@Lazy(false)
public class EventUtils {

    private final static Logger LOG = LoggerFactory.getLogger(EventUtils.class);

    public static final String KAFKA_TEMPLATE_ASYNC_SEND = "kafkaTemplate.async.send";
    @Autowired
    private KafkaDynamicSender kafkaDynamicSender;

    @EventAnnotation(value = KAFKA_TEMPLATE_ASYNC_SEND)
    public void sendMessage(KafkaTemplate sender,String topic, String data){
        kafkaDynamicSender.sendMessage(sender, topic, data);
    }
}
