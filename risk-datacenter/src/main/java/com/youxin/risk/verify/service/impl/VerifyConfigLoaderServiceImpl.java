package com.youxin.risk.verify.service.impl;

import com.youxin.risk.commons.model.verify.VerifyConfig;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.verify.service.VerifyConfigLoaderService;
import com.youxin.risk.verify.service.VerifyConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

@Service("verifyConfigLoaderService")
public class VerifyConfigLoaderServiceImpl
        implements VerifyConfigLoaderService {

    private static Logger logger = LoggerFactory.getLogger(VerifyConfigLoaderServiceImpl.class);
    
    @Autowired
    private VerifyConfigService verifyConfigService;
    
    @Override
    public List<VerifyConfig> getAllConfigInfo() {
        List<VerifyConfig> configs = verifyConfigService.getAll();
        if (configs == null || configs.isEmpty()) {
            return null;
        }
        return configs;
    }

    @Override
    public void update(String key, String value, Integer operator) {
        if (StringUtils.isEmpty(key)) {
            return;
        }
        // 修改
        VerifyConfig config = null;
        try {
            config = verifyConfigService.update(key, value, operator);
        } catch (Exception e) {
            logger.error("配置[" + key + "]修改失败。", e);
            return;
        }
        if (config == null) {
            logger.error("配置[{}]修改失败。", key);
            return;
        }
    }

    @Override
    public String getValue(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        // 值
        String value = null;

        // 数据库中取
        try {
            value = verifyConfigService.getValueByKey(key);
        } catch (Exception e) {
            logger.error("从库中读取配置[" + key + "]失败。", e);
            return null;
        }
        if (StringUtils.isNotEmpty(value)) {
            return value;
        }

        logger.error("配置[{}]不存在。", key);
        return null;
    }

    @Override
    public Integer getValueAsInteger(String key) {
        return Integer.parseInt(getValue(key));
    }

    @Override
    public Long getValueAsLong(String key) {
        return Long.parseLong(getValue(key));
    }

    @Override
    public Double getValueAsDouble(String key) {
        return Double.parseDouble(getValue(key));
    }

    @Override
    public Boolean getValueAsBoolean(String key) {
        return Boolean.parseBoolean(getValue(key));
    }

    @Override
    public Date getValueAsDate(String key, String format) throws ParseException {
        return org.apache.commons.lang3.time.DateUtils.parseDate(getValue(key), format);
    }

    @Override
    public VerifyConfig getConfigInfo(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        return verifyConfigService.getByKey(key);
    }
}
