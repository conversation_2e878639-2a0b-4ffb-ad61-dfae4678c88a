package com.youxin.risk.verify.service.impl;

import com.youxin.risk.commons.dao.verify.VerifyConfigMapper;
import com.youxin.risk.commons.model.verify.VerifyConfig;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.verify.service.VerifyConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class VerifyConfigServiceImpl implements VerifyConfigService {
    private static Logger logger = LoggerFactory
        .getLogger(VerifyConfigServiceImpl.class);

    @Autowired
    private VerifyConfigMapper verifyConfigMapper;

    /**
     * 持久化
     * 
     * @param entity
     */
    @Override
    public void persist(VerifyConfig entity) {
        verifyConfigMapper.persist(entity);
    }

    /**
     * 修改
     * 
     * @param key
     * @param value
     * @return
     */
    @Override
    public VerifyConfig update(String key, String value, Integer operator) {
        if (StringUtils.isEmpty(key) || StringUtils.isEmpty(value)) {
            logger.error("修改配置信息，key或value不允许为空。");
            return null;
        }
        VerifyConfig info = getByKey(key);
        if (info == null) {
            logger.error("修改配置信息，配置[{}]不存在。", key);
            return null;
        }
        info.setValue(value);
        info.setOperator(operator);
        verifyConfigMapper.update(info);
        return info;
    }

    /**
     * 按键查找配置信息
     * 
     * @param key
     * @return
     */
    @Override
    public VerifyConfig getByKey(String key) {
        return verifyConfigMapper.getByKey(key);
    }

    /**
     * 按键查找配置信息值
     * 
     * @param key
     * @return
     */
    @Override
    public String getValueByKey(String key) {
        VerifyConfig info = getByKey(key);
        if (info == null) {
            logger.error("配置[{}]不存在。", key);
            return null;
        }
        return info.getValue();
    }

    /**
     * 查找全部列表
     * 
     * @return
     */
    @Override
    public List<VerifyConfig> getAll() {
        return verifyConfigMapper.getAll();
    }

    /**
     * 查找全部配置映射
     * 
     * @return
     */
    @Override
    public Map<String, String> getAllMapping() {
        Map<String, String> mapping = new HashMap<String, String>();

        List<VerifyConfig> list = getAll();
        if (list != null && !list.isEmpty()) {
            for (VerifyConfig info : list) {
                mapping.put(info.getKey(), info.getValue());
            }
        }
        return mapping;
    }
}
