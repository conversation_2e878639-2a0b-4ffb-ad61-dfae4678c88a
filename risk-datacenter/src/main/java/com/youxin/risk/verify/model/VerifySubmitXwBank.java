package com.youxin.risk.verify.model;

import java.io.Serializable;
import java.util.Date;

import com.youxin.risk.ra.timeinterface.CreateTimeSetter;
import com.youxin.risk.ra.timeinterface.UpdateTimeSetter;

public class VerifySubmitXwBank implements CreateTimeSetter, UpdateTimeSetter, Serializable {

    private static final long serialVersionUID = -7962594209326506656L;


	private Integer id;


	private Date updateTime;


	private Date createTime;


	private Integer version;


	private String sourceSystem;


	private String userKey;

	private Integer loanId;


	private String mobile;

	private String name;

	private String certNo;

	private String certDate;

	private String certExpiryDate;

	private String certState;

	private String certCity;

	private String certDistCode;

	private String certPlace;

	private String certAuthority;

	private String birthDate;

	private String gender;

	private String ethnic;

	private String maritalStatus;

	private String livingCondition;

	private String addressType;

	private String provinceCode;

	private String cityCode;

	private String addrStreet;

	private String bankCardNo;

	private String bankCardType;

	private String bankMobile;

	private String loanAmount;

	private String loanPurpose;

	private String loanTenor;

	private String loanTenorUnit;

	private String rate;

	private String idcardF;

	private String idcardB;

	private String professionalType;


    @Override
    public void setUpdateTime() {
        this.updateTime = new Date();
    }

    @Override
    public void setCreateTime() {
        this.createTime = new Date();
    }

    public Integer getId() {
        return this.id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getMobile() {
        return this.mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }



    public Date getUpdateTime() {
        return this.updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }


    public String getSourceSystem() {
		return this.sourceSystem;
	}

	public void setSourceSystem(String sourceSystem) {
		this.sourceSystem = sourceSystem;
	}

	public Integer getLoanId() {
		return this.loanId;
	}

	public void setLoanId(Integer loanId) {
		this.loanId = loanId;
	}


	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCertNo() {
		return this.certNo;
	}

	public void setCertNo(String certNo) {
		this.certNo = certNo;
	}

	public String getCertDate() {
		return this.certDate;
	}

	public void setCertDate(String certDate) {
		this.certDate = certDate;
	}

	public String getCertExpiryDate() {
		return this.certExpiryDate;
	}

	public void setCertExpiryDate(String certExpiryDate) {
		this.certExpiryDate = certExpiryDate;
	}

	public String getCertState() {
		return this.certState;
	}

	public void setCertState(String certState) {
		this.certState = certState;
	}

	public String getCertCity() {
		return this.certCity;
	}

	public void setCertCity(String certCity) {
		this.certCity = certCity;
	}

	public String getCertDistCode() {
		return this.certDistCode;
	}

	public void setCertDistCode(String certDistCode) {
		this.certDistCode = certDistCode;
	}

	public String getCertPlace() {
		return this.certPlace;
	}

	public void setCertPlace(String certPlace) {
		this.certPlace = certPlace;
	}

	public String getCertAuthority() {
		return this.certAuthority;
	}

	public void setCertAuthority(String certAuthority) {
		this.certAuthority = certAuthority;
	}

	public String getBirthDate() {
		return this.birthDate;
	}

	public void setBirthDate(String birthDate) {
		this.birthDate = birthDate;
	}

	public String getGender() {
		return this.gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}

	public String getEthnic() {
		return this.ethnic;
	}

	public void setEthnic(String ethnic) {
		this.ethnic = ethnic;
	}

	public String getMaritalStatus() {
		return this.maritalStatus;
	}

	public void setMaritalStatus(String maritalStatus) {
		this.maritalStatus = maritalStatus;
	}

	public String getLivingCondition() {
		return this.livingCondition;
	}

	public void setLivingCondition(String livingCondition) {
		this.livingCondition = livingCondition;
	}

	public String getAddressType() {
		return this.addressType;
	}

	public void setAddressType(String addressType) {
		this.addressType = addressType;
	}

	public String getProvinceCode() {
		return this.provinceCode;
	}

	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}

	public String getCityCode() {
		return this.cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}

	public String getAddrStreet() {
		return this.addrStreet;
	}

	public void setAddrStreet(String addrStreet) {
		this.addrStreet = addrStreet;
	}

	public String getBankCardNo() {
		return this.bankCardNo;
	}

	public void setBankCardNo(String bankCardNo) {
		this.bankCardNo = bankCardNo;
	}

	public String getBankMobile() {
		return this.bankMobile;
	}

	public void setBankMobile(String bankMobile) {
		this.bankMobile = bankMobile;
	}

	public String getLoanAmount() {
		return this.loanAmount;
	}

	public void setLoanAmount(String loanAmount) {
		this.loanAmount = loanAmount;
	}

	public String getLoanPurpose() {
		return this.loanPurpose;
	}

	public void setLoanPurpose(String loanPurpose) {
		this.loanPurpose = loanPurpose;
	}

	public String getLoanTenor() {
		return this.loanTenor;
	}

	public void setLoanTenor(String loanTenor) {
		this.loanTenor = loanTenor;
	}

	public String getLoanTenorUnit() {
		return this.loanTenorUnit;
	}

	public void setLoanTenorUnit(String loanTenorUnit) {
		this.loanTenorUnit = loanTenorUnit;
	}

	public String getRate() {
		return this.rate;
	}

	public void setRate(String rate) {
		this.rate = rate;
	}

	public String getIdcardF() {
		return this.idcardF;
	}

	public void setIdcardF(String idcardF) {
		this.idcardF = idcardF;
	}

	public String getIdcardB() {
		return this.idcardB;
	}

	public void setIdcardB(String idcardB) {
		this.idcardB = idcardB;
	}

	public String getUserKey() {
        return this.userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey;
    }

    public Integer getVersion() {
        return this.version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

	public String getBankCardType() {
		return this.bankCardType;
	}

	public void setBankCardType(String bankCardType) {
		this.bankCardType = bankCardType;
	}

    public String getProfessionalType() {
        return professionalType;
    }

    public void setProfessionalType(String professionalType) {
        this.professionalType = professionalType;
    }
}
