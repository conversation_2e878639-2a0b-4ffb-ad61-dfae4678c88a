package com.youxin.risk.verify.service.impl;

import com.youxin.risk.commons.dao.verify.VerifyLineRepaymentMapper;
import com.youxin.risk.commons.model.verify.VerifyLineRepayment;
import com.youxin.risk.verify.service.VerifyLineRepaymentService;
import com.youxin.risk.verify.vo.VerifyLineRepayMentVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 */
@Service
public class VerifyLineRepaymentServiceImpl implements VerifyLineRepaymentService {

    @Autowired
    private VerifyLineRepaymentMapper verifyLineRepaymentMapper;

    @Override
    public VerifyLineRepayment findRecordByTransId(String transId, String userKey) {
        return verifyLineRepaymentMapper.findRecordByTransId(transId,userKey);
    }

    @Override
    public void saveOrUpdateRec(VerifyLineRepayment lineRepayment) {
        if (lineRepayment.getId() == null) {
            this.verifyLineRepaymentMapper.insert(lineRepayment);
        } else {
            this.verifyLineRepaymentMapper.update(lineRepayment);
        }
    }

    @Override
    public void updateFinshStatus(VerifyLineRepayMentVo repayMentVo, Integer isFinished) {

    }

}
