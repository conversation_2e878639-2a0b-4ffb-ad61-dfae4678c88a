package com.youxin.risk.verify.model;

import java.io.Serializable;
import java.util.Date;

public class VerifySubmitContactInfo implements Serializable {

    /**
	 * 
	 */
    private static final long serialVersionUID = -2929438983080975510L;

    private Integer id;

    private String userKey;

    private String relation;

    private Integer operationLogId;

    private String contactName;

    private String mobile;

    private Date updateTime;

    private Date createTime;

    private Integer version;


    public void setUpdateTime() {
        this.updateTime = new Date();
    }


    public void setCreateTime() {
        this.createTime = new Date();
    }

    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Integer getOperationLogId() {
        return operationLogId;
    }

    public void setOperationLogId(Integer operationLogId) {
        this.operationLogId = operationLogId;
    }

}
