package com.youxin.risk.verify.vo;


/**
 * <AUTHOR>
 */
public class VerifyLineAssignVo extends VerifyLineCommonVo {


    private VerifyUserLineManagementVo lastLineManagement;


	private UserLineLastVerifyResultVo lastVerifyResultVo;
	

    private Double userPoint;


    private String userLevel;


    private String userKey;


    private String registerPhone;

    public String getRegisterPhone() {
        return this.registerPhone;
    }

    public void setRegisterPhone(String registerPhone) {
        this.registerPhone = registerPhone;
    }

    public Double getUserPoint() {
		return userPoint;
	}

	public void setUserPoint(Double userPoint) {
		this.userPoint = userPoint;
	}

	public String getUserLevel() {
		return userLevel;
	}

	public void setUserLevel(String userLevel) {
		this.userLevel = userLevel;
	}

	public VerifyUserLineManagementVo getLastLineManagement() {
		return lastLineManagement;
	}

	public void setLastLineManagement(VerifyUserLineManagementVo lastLineManagement) {
		this.lastLineManagement = lastLineManagement;
	}

	public UserLineLastVerifyResultVo getLastVerifyResultVo() {
		return lastVerifyResultVo;
	}

	public void setLastVerifyResultVo(UserLineLastVerifyResultVo lastVerifyResultVo) {
		this.lastVerifyResultVo = lastVerifyResultVo;
	}
	
}
