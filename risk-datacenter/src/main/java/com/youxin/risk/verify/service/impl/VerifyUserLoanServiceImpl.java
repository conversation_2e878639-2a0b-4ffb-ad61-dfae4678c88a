/**
 * Copyright(c) 2011-2017 by YouCredit Inc.
 * All Rights Reserved
 */
package com.youxin.risk.verify.service.impl;

import com.youxin.risk.commons.dao.verify.VerifyUserLoanMapper;
import com.youxin.risk.commons.model.datacenter.common.OperationType;
import com.youxin.risk.commons.model.verify.VerifyUserLoan;
import com.youxin.risk.commons.utils.ObjectTransferUtils;
import com.youxin.risk.verify.service.OperationLogService;
import com.youxin.risk.verify.service.VerifyUserLoanService;
import com.youxin.risk.verify.vo.OperationLogVo;
import com.youxin.risk.verify.vo.VerifyUserLoanVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 用户借款服务类
 *
 * <AUTHOR>
 * @version 创建时间：2017年12月26日-下午4:23:48
 */
@Service
public class VerifyUserLoanServiceImpl implements VerifyUserLoanService {

	private static final Logger LOG = LoggerFactory.getLogger(VerifyUserLoanServiceImpl.class);


	@Autowired
    private OperationLogService operationLogService;

	@Autowired
	private VerifyUserLoanMapper verifyUserLoanMapper;


	@Override
	public void submitNotify(VerifyUserLoanVo vo) {

		OperationLogVo opLogVo = this.operationLogService.getOpLogVoFromInput(
				vo, OperationType.LOAN_NOTIFY);
		Integer opLogId = this.operationLogService.saveOperationLog(opLogVo);
		VerifyUserLoan userLoan;
		try {
			userLoan = ObjectTransferUtils.transferObject(vo,
					VerifyUserLoan.class);
			userLoan.setOperationLogId(opLogId);
			if(userLoan.getCreateTime()==null){
				userLoan.setCreateTime();
			}
			if(userLoan.getUpdateTime()==null){
				userLoan.setUpdateTime();
			}
			if(userLoan.getVersion()==null){
				userLoan.setVersion();
			}
			this.verifyUserLoanMapper.persist(userLoan);

		} catch (InstantiationException | IllegalAccessException
				| SecurityException e) {
			LOG.error("userloan 转换vo出错", e);
			throw new RuntimeException("参数转换出错");
		}

	}


}
