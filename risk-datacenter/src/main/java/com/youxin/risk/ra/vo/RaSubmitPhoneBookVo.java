package com.youxin.risk.ra.vo;

import com.youxin.risk.commons.vo.datavo.SubmitCommonVo;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

public class RaSubmitPhoneBookVo extends SubmitCommonVo {
    private List<SubmitPhoneBookDetailsVo> phoneList;
    
    private String phoneBookJobId;

    public List<SubmitPhoneBookDetailsVo> getPhoneList() {
        return phoneList;
    }

    public void setPhoneList(List<SubmitPhoneBookDetailsVo> phoneList) {
        this.phoneList = phoneList;
    }

    public String getPhoneBookJobId() {
		return phoneBookJobId;
	}

	public void setPhoneBookJobId(String phoneBookJobId) {
		this.phoneBookJobId = phoneBookJobId;
	}

	@Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this,
            ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
