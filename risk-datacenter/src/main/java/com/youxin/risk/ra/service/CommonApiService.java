package com.youxin.risk.ra.service;

import com.youxin.risk.commons.utils.HttpResult;

import java.util.Map;

/**
 * 此代码ra与verify公用，从老系统迁移至此
 * 本人只负责迁移该代码
 *
 */
public interface CommonApiService {

	HttpResult invokeApi(CommonApi api, Object param);

	HttpResult invokePostApiViaParameters(CommonApi api, Object obj)
			throws Exception;

	String createGetUrl(String baseUrl, Map<String, Object> map);


	HttpResult invokeApi(CommonApi api, Object param, Map<String, String> header);
}
