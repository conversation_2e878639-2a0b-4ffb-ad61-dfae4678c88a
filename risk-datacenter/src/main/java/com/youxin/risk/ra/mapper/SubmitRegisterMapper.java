package com.youxin.risk.ra.mapper;


import com.youxin.risk.ra.model.SubmitRegister;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SubmitRegisterMapper extends BaseMapper<SubmitRegister> {

    SubmitRegister findByApplyId(@Param("applyId")Integer applyId);

    SubmitRegister findLastSubmitByUserKey(@Param("userKey")String userKey, @Param("sourceSystem")String sourceSystem);

    String getUserKeyByMobileList(@Param("sourceSystemList")List<String> sourceSystemList, @Param("mobileList")List<String> mobileList);

}
