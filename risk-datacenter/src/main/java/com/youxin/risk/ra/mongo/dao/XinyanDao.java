/**
 * Copyright(c) 2011-2017 by YouCredit Inc.
 * All Rights Reserved
 */
package com.youxin.risk.ra.mongo.dao;

import com.youxin.risk.ra.mongo.vo.XinyanRecordVo;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.Order;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * 新颜DAO
 *
 * <AUTHOR>
 * @version 创建时间：2017年12月16日-下午12:53:10
 */
@Repository
public class XinyanDao extends RecordBaseDao<XinyanRecordVo>{


    public XinyanRecordVo getRecordByUser(String sourceSystem,String userKey,String type) {

        Query query = new Query();
        query.addCriteria(Criteria.where("sourceSystem").is(sourceSystem));
        query.addCriteria(Criteria.where("userKey").is(userKey));
        query.addCriteria(Criteria.where("type").is(type));
        query.with(new Sort(new Order(Direction.DESC,"createTime")));

        return this.findOne(query);
    }

    public List<Map<String, Object>> getRecordByUserAndType(String sourceSystem, String userKey, String type, Integer size) {
		List<Map<String, Object>> result = new ArrayList<>();
        Query query = new Query();
        query.addCriteria(Criteria.where("sourceSystem").is(sourceSystem));
        query.addCriteria(Criteria.where("userKey").is(userKey));
        query.addCriteria(Criteria.where("type").is(type));
        query.with(new Sort(new Order(Direction.DESC,"createTime")));
		query.limit(size);
		List<XinyanRecordVo> xinyanList = this.findListByOwnName(query, "XinyanVo");
		// 数据去重
		HashSet<Integer> taskSet = new HashSet<>();
		for(XinyanRecordVo record: xinyanList){
			if (taskSet.add(record.getTaskId())) {
				Map<String, Object> recMap = new HashMap<>();
				recMap.put("createTime", record.getCreateTime());
				if (record.getXinyanData() != null) {
					recMap.put("data", record.getXinyanData().getData());
					result.add(recMap);
				}
			}
		}
        return result;
    }

    public List<Map<String, Object>> getRecordByTaskIdList(List<Integer> taskIdList){
		List<Map<String, Object>> result = new ArrayList<>();
		Query query = new Query();
		query.addCriteria(Criteria.where("taskId").in(taskIdList));
		query.with(new Sort(new Order(Direction.DESC,"createTime")));
		List<XinyanRecordVo> xinyanList = this.findListByOwnName(query, "XinyanVo");
		// 数据去重
		HashSet<Integer> taskSet = new HashSet<>();
		for(XinyanRecordVo record: xinyanList){
			if (taskSet.add(record.getTaskId())) {
				Map<String, Object> recMap = new HashMap<>();
				recMap.put("createTime", record.getCreateTime());
				if (record.getXinyanData() != null) {
					recMap.put("data", record.getXinyanData().getData());
					result.add(recMap);
				}
			}
		}
		return result;
	}

}
