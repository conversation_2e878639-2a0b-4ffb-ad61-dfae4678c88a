/**
 * Copyright(c) 2011-2017 by YouCredit Inc.
 * All Rights Reserved
 */
package com.youxin.risk.ra.mongo.dao;

import com.youxin.risk.ra.mongo.vo.AbstractRecordVo;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

/**
 * <AUTHOR>
 * @version 创建时间：2017年12月16日-下午12:55:45
 */
public class RecordBaseDao<T>  extends MongoBaseDaoImpl<T> {

	public T getRecordByTaskId(Integer taskId){

		Query query = new Query();
		query.addCriteria(Criteria.where("taskId").is(taskId));

		return this.findOne(query);
	}

	public boolean saveRecord(AbstractRecordVo recordVo, Integer taskId){
		recordVo.setTaskId(taskId);

		return this.insert((T)recordVo);
	}

}
