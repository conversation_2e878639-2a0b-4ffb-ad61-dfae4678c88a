package com.youxin.risk.ra.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.youxin.risk.commons.dao.datacenter.DcSubmitIdcardMapper;
import com.youxin.risk.commons.model.datacenter.DcSubmitIdcard;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.MaskUtils;
import com.youxin.risk.commons.utils.ObjectTransferUtils;
import com.youxin.risk.datacenter.service.impl.HfqUserInfoService;
import com.youxin.risk.ra.mapper.SubmitIdcardMapper;
import com.youxin.risk.ra.model.SubmitIdcard;
import com.youxin.risk.ra.service.SubmitIdcardService;
import com.youxin.risk.ra.vo.RaSubmitIdcardVo;
import com.youxin.risk.verify.service.VerifySubmitService;
import com.youxin.risk.verify.vo.VerifySubmitIdcardVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service("raSubmitIdcardService")
public class SubmitIdcardServiceImpl implements SubmitIdcardService {

    private static Logger logger = LoggerFactory.getLogger(SubmitIdcardServiceImpl.class);

    @Autowired
    private SubmitIdcardMapper submitIdcardMapper;

    @Autowired
    private DcSubmitIdcardMapper dcSubmitIdcardMapper;

    @Resource
    private HfqUserInfoService hfqUserInfoService;

    @Autowired
    @Qualifier("verifySubmitServImpl")
    private VerifySubmitService verifySubmitService;

    @Override
    public void saveSubmitIdcard(SubmitIdcard idcard) {
        this.submitIdcardMapper.insert(idcard);
    }


    @Override
    public RaSubmitIdcardVo getSubmitIdcardVoByUserApply(String sourceSystem, String userKey, Integer applyId) {
        RaSubmitIdcardVo raSubmitIdcardVo = null;
        SubmitIdcard submitIdcard = this.submitIdcardMapper.findByApplyId(applyId);
        if (null == submitIdcard) {
            try {
                //调用好分期接口获取用户信息
                JSON userInfo = hfqUserInfoService.getUserInfo(userKey);
                //保存到数据库中
                VerifySubmitIdcardVo idcardVo = JSON.toJavaObject(userInfo, VerifySubmitIdcardVo.class);
                //ip 不能为空，判断一下
                if (idcardVo.getIp() == null) {
                    idcardVo.setIp("");
                }
                verifySubmitService.submitIdcard(idcardVo);
                submitIdcard = this.submitIdcardMapper.findIdcardByUserKey(sourceSystem, userKey);
                LoggerProxy.info("return lost rePoll raSubmitIdcard", logger, "userKey={},raSubmitIdcard={}", userKey, JSON.toJSONString(submitIdcard));
            } catch (Exception e) {
                LoggerProxy.error("lost raIdcard insert error", logger, "userKey={}", userKey, e);
            }
        }
        if (submitIdcard != null) {
            try {
                raSubmitIdcardVo = ObjectTransferUtils.transferObject(submitIdcard, RaSubmitIdcardVo.class);
            } catch (Exception e) {
                logger.error("transfer model to vo error");
            }
        }
        return raSubmitIdcardVo;
    }


    @Override
    public RaSubmitIdcardVo getIdcardVoByUserKey(String sourceSystem, String userKey) {
        SubmitIdcard submitIdcard = this.submitIdcardMapper.findIdcardByUserKey(sourceSystem, userKey);
        if (submitIdcard == null) {
            return null;
        }
        try {
            return ObjectTransferUtils.transferObject(submitIdcard, RaSubmitIdcardVo.class);
        } catch (Exception e) {
            logger.error("transfer model to vo error");
            return null;
        }
    }

    @Override
    public List<SubmitIdcard> findSubmitIdcardByIdNumber(String idNumber) {
        idNumber = MaskUtils.maskValue(idNumber);
        List<DcSubmitIdcard> idcards = dcSubmitIdcardMapper.findSubmitIdcardByIdNumber(idNumber);
        List<SubmitIdcard> results = Lists.newArrayList();
        if (CollectionUtils.isEmpty(idcards)) {
            logger.info("findSubmitIdcardByIdNumber从dc获取身份证信息为空, idCardNo: {}", idNumber);
            return this.submitIdcardMapper.findSubmitIdcardByIdNumber(idNumber);
        }
        for (DcSubmitIdcard idcard : idcards) {
            SubmitIdcard submitIdcard = new SubmitIdcard();
            submitIdcard.setUserKey(idcard.getUserKey());
            submitIdcard.setIdcardNumber(idcard.getIdcardNumber());
            results.add(submitIdcard);
        }
        return results;
    }

    @Override
    public String getIdNumberByUserKey(String sourceSystem, String userKey) {
        // 优先从dc获取
        DcSubmitIdcard idCard = dcSubmitIdcardMapper.getByUserKey(userKey);
        if (Objects.nonNull(idCard)) {
            return idCard.getIdcardNumber();
        }
        logger.info("getIdNumberByUserKey从dc获取身份证信息为空, userKey: {}, sourceSystem: {}", userKey, sourceSystem);
        SubmitIdcard submitIdcard = this.submitIdcardMapper.findIdcardByUserKey(sourceSystem, userKey);
        if (submitIdcard == null) {
            return null;
        }
        return submitIdcard.getIdcardNumber();
    }

    @Override
    public int updateFaceUrlByUserKey(String sourceSystem, String userKey, String faceUrl) {
        SubmitIdcard idcard = this.submitIdcardMapper.findIdcardByUserKey(sourceSystem, userKey);
        if (idcard == null) {
            return 0;
        }
        if (StringUtils.isNotBlank(idcard.getFaceUrl()) && StringUtils.equals(idcard.getFaceUrl(), faceUrl)) {
            return 1;
        }
        SubmitIdcard updateIdCard = new SubmitIdcard();
        updateIdCard.setId(idcard.getId());
        updateIdCard.setFaceUrl(faceUrl);
        updateIdCard.setUpdateTime(new Date());
        return submitIdcardMapper.updateBySelective(updateIdCard);
    }
}
