package com.youxin.risk.datacenter.model;

import com.youxin.risk.commons.model.BaseModel;

/**
 * dc 提额需要用到住址信息
 * 
 * <AUTHOR>
 * 
 * @date 2020-09-02
 */
public class DcSubmitAmountHouse extends BaseModel {

    /**
     * operation_log表id
     */
    private Long operationLogId;

    /**
     * 用户key
     */
    private String userKey;

    /**
     * 居住城市
     */
    private String livingCity;
    /**
     * 居住地址
     */
    private String livingAddress;
    /**
     * 住房类型
     */
    private String livingType;

    /**
     * 购房总额
     */
    private String totalAmount;

    /**
     * 房贷期数
     */
    private String buyPeriod;

    /**
     * 是否有其他房产
     */
    private String isOtherHouse;


    public Long getOperationLogId() {
        return operationLogId;
    }

    public void setOperationLogId(Long operationLogId) {
        this.operationLogId = operationLogId;
    }

    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey == null ? null : userKey.trim();
    }

    public String getLivingCity() {
        return livingCity;
    }

    public void setLivingCity(String livingCity) {
        this.livingCity = livingCity;
    }

    public String getLivingAddress() {
        return livingAddress;
    }

    public void setLivingAddress(String livingAddress) {
        this.livingAddress = livingAddress;
    }

    public String getLivingType() {
        return livingType;
    }

    public void setLivingType(String livingType) {
        this.livingType = livingType;
    }

    public String getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(String totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getBuyPeriod() {
        return buyPeriod;
    }

    public void setBuyPeriod(String buyPeriod) {
        this.buyPeriod = buyPeriod;
    }

    public String getIsOtherHouse() {
        return isOtherHouse;
    }

    public void setIsOtherHouse(String isOtherHouse) {
        this.isOtherHouse = isOtherHouse;
    }
}