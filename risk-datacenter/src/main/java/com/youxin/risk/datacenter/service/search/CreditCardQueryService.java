/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.datacenter.service.search;

import java.util.Map;

import javax.annotation.Resource;

import com.youxin.risk.datacenter.service.search.annotation.DcServiceCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.google.common.base.Preconditions;
import com.youxin.risk.commons.model.datacenter.DcSubmitBankCard;
import com.youxin.risk.commons.model.datacenter.DcSubmitCreditcard;
import com.youxin.risk.datacenter.service.SubmitBankCardService;
import com.youxin.risk.datacenter.service.SubmitCreditCardService;

@Component
public class CreditCardQueryService implements DcQueryService<DcSubmitCreditcard> {

    @Resource
    private SubmitCreditCardService creditCardService;

    @DcServiceCode(name = "CREDITCARD")
    public DcSubmitCreditcard getByUserKey(Map<String, Object> params) {
        String userKey = (String) params.get("userKey");
        Preconditions.checkArgument(!StringUtils.isBlank(userKey));
        return creditCardService.getByUserKey(userKey);
    }
}