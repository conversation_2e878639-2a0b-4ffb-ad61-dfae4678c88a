package com.youxin.risk.datacenter.service.impl.dcsubmit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.datacenter.DcSubmitHouse;
import com.youxin.risk.commons.model.datacenter.common.SubmitDataType;
import com.youxin.risk.commons.model.datacenter.vo.SubmitHouseVo;
import com.youxin.risk.datacenter.service.SubmitHouseService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 车产
 *
 * <AUTHOR>
 * @since 2022/09/21 15:28
 */
@Service
public class HouseBizServiceImpl extends AbstractSubmitBizService {

    @Resource
    private SubmitHouseService submitHouseService;

    @Override
    protected SubmitDataType getSubmitDataType() {
        return SubmitDataType.SUBMIT_HOUSE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitData(JSONObject msgObj) {
        SubmitHouseVo submitVo = JSON.toJavaObject(msgObj, SubmitHouseVo.class);
        submitCar(submitVo);
    }

    public void submitCar(SubmitHouseVo submitVo) {
        DcSubmitHouse submitHouse = new DcSubmitHouse();
        BeanUtils.copyProperties(submitVo, submitHouse);
        submitHouse.setOperationLogId(1L);
        submitHouseService.insertItem(submitHouse);
    }
}
