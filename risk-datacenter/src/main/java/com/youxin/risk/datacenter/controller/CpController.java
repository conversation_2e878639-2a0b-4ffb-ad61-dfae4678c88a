package com.youxin.risk.datacenter.controller;

import com.youxin.risk.commons.model.WhiteListDto;
import com.youxin.risk.datacenter.pojo.JsonResultVo;
import com.youxin.risk.datacenter.service.DcWhiteListService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 给cp服务开的接口
 */
@RestController
@RequestMapping("/datacenter")
public class CpController {
    private Logger logger = LoggerFactory.getLogger(CpController.class);

    @Autowired
    private DcWhiteListService dcWhiteListService;

    @RequestMapping(value = "/whitelist", method = RequestMethod.POST)
    public JsonResultVo whiteList(@RequestBody List<WhiteListDto> whiteListDto) {
        logger.info("CpController receive cp service request");
        dcWhiteListService.insert(whiteListDto);
        return JsonResultVo.success("新增成功");
    }
}
