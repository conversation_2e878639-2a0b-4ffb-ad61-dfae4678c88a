package com.youxin.risk.datacenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.dao.datacenter.DcSubmitPlistShardingMapper;
import com.youxin.risk.commons.model.datacenter.DcSubmitPlist;
import com.youxin.risk.commons.model.verify.VerifyUserLineManagement;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.datacenter.service.search.DataQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 *
 *    获取用户额度信息
 */
@Service
public class AllPlistServiceImpl implements DataQueryService<List<DcSubmitPlist>> {


    @Autowired
    DcSubmitPlistShardingMapper dcSubmitPlistShardingMapper;


    @Override
    public String getServiceCode() {
        return "allPlistService";
    }

    /**
     * 从  mysql 表查询自有数据
     *
     * @param param
     * @return
     */
    @Override
    public List<DcSubmitPlist> queryDcData(JSONObject param) {

        String userKey = param.getString("userKey");
        String shardingTableName = DcSubmitPlist.getShardingTableName(userKey);
        List<DcSubmitPlist> allDcSubmitPlist = this.dcSubmitPlistShardingMapper.getAllByUserkey(shardingTableName, userKey);
        return allDcSubmitPlist;

    }
}
