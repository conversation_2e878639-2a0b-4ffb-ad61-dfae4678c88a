package com.youxin.risk.datacenter.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.weicai.caesar.CaesarUtil;
import com.youxin.apollo.client.NacosClient;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.constants.ApolloNamespace;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.model.datacenter.DcOperationLog;
import com.youxin.risk.commons.model.datacenter.common.OperationType;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.DateUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.datacenter.constants.SubmitAmountDelayHanderType;
import com.youxin.risk.datacenter.delayqueue.SubmitAmountDelayService;
import com.youxin.risk.datacenter.model.DcIdentifyAmount;
import com.youxin.risk.datacenter.pojo.JsonResultVo;
import com.youxin.risk.verify.redis.RedisService;
import com.youxin.risk.verify.service.VerifyResultService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.youxin.risk.commons.apollo.ApolloNamespaceEnum.DC_SPACE;


public abstract class AbstractSubmitAmountService {

    private static final Logger logger = LoggerFactory.getLogger(AbstractSubmitAmountService.class);


    //自定义线程池 提交到dc
    private ExecutorService executorService = new ThreadPoolExecutor(20, 100,
            5, TimeUnit.MINUTES, new ArrayBlockingQueue<>(10000), new ThreadPoolExecutor.CallerRunsPolicy());

    private static final String lockPrefix = "IDENTITY_AMOUNT_";

    @Value("${risk.gateway.url}")
    private String gatewayUrl;

    @Autowired(required = false)
    @Qualifier("cacheRedisService")
    private RedisService redisService;

    @Autowired
    private OperationLogDbService operationLogDbService;

    @Autowired
    private SubmitAmountDelayService submitAmountDelayService;

    @Autowired
    private DcIdentityAmountService dcIdentityAmountService;

    @Autowired
    private VerifyResultService verifyResultService;


    /**
     * 提交提额信息
     */
    public JsonResultVo submitAmountData(JSONObject param) {

        //设置失败重试次数
        param.put("retryTime", 3);

        List<String> keyList = new ArrayList<>();
        //公共的必要参数 key
        keyList.add("sourceSystem");
        keyList.add("userKey");
        //具体接口的必要参数 key
        keyList.addAll(getCheckNullKeyList());
        //校验必要参数
        List<String> nullParamList = checkNullParametersByKeyList(param, keyList);
        //校验业务自定义参数
        nullParamList.addAll(checkNullCustomizeParameters(param));
        if (nullParamList.size() > 0) {
            return JsonResultVo.error(-1, "缺少必要参数").addData("missingParameters", nullParamList);
        }
        // 先同步执行 出错让业务方重试
        AbstractSubmitAmountService.this.submitAndTrigger(param);

        /*//异步执行流程，直接返回结果
        this.executorService.execute(() -> AbstractSubmitAmountService.this.submitAndTrigger(param));*/
        LoggerProxy.info("AbstractSubmitAmountService.submitAmountData", logger, "userKey={}, currentTime={}", param.getString("userKey"), DateUtil.formatCurrent("yyyy-MM-dd HH:mm:ss.SSS"));

        return JsonResultVo.success();
    }


    /**
     * 获取需要校验的数据参数
     */
    protected abstract List<String> getCheckNullKeyList();

    /**
     * 根据 key 校验必要参数
     */
    private List<String> checkNullParametersByKeyList(JSONObject param, List<String> keyList) {

        List<String> result = new ArrayList<>();
        for (String key : keyList) {
            if (!param.containsKey(key) || StringUtils.isEmpty(param.getString(key))) {
                result.add(key);
            }
        }
        return result;
    }


    /**
     * 校验业务自定义必要参数
     */
    protected abstract List<String> checkNullCustomizeParameters(JSONObject param);


    /**
     * 提交信息并触发提额
     */
    public void submitAndTrigger(JSONObject param) {

        //保存到数据库
        submitToDataBase(param);

        //认证项类型
        String certificationItem = param.getString("operationType");
        if(checkNeedCallBack(certificationItem)){
            //异步触发提额流程
            this.executorService.execute(() -> AbstractSubmitAmountService.this.raiseTheAmount(param));
        } else {
            String userKey = param.getString("userKey");
            String requestId = param.getString("agencyRequestId");
            DcIdentifyAmount dcIdentifyAmount = new DcIdentifyAmount();
            dcIdentifyAmount.setCreateTime(new Date());
            dcIdentifyAmount.setUpdateTime(new Date());
            dcIdentifyAmount.setOperationType(certificationItem);
            dcIdentifyAmount.setRequestId(requestId);
            dcIdentifyAmount.setUserKey(userKey);
            dcIdentityAmountService.insert(dcIdentifyAmount);
        }

    }


    /**
     * 保存到数据库的逻辑交给子类实现
     *
     * @param param
     */
    private void submitToDataBase(JSONObject param) {

        try {
            //设置上传的数据类型
            setSubmitType(param);

            //设置时间
            param.put("createTime", new Date());
            param.put("updateTime", new Date());

            DcOperationLog opLogVo = getOpLogVoFromInput(param);
            //保存到请求日志表
            long operationLogId = insertLog(opLogVo);
            param.put("operationLogId", operationLogId);

            //保存上传的提额相关数据
            insertAmountParam(param);
        } catch (Exception e) {
            LoggerProxy.error("submitAmountError", logger, "param={}", param, e);
            String delayStr = NacosClient.getByNameSpace(ApolloNamespace.commonSpace, "submit.delay.time", "30");
            Long delayTime = Long.parseLong(delayStr);
            submitAmountDelayService.schedule(param, delayTime, SubmitAmountDelayHanderType.getClassByName(param.getString("operationType")));
        }
    }


    /**
     * 根据上传数据拼装 日志表对象
     *
     * @param param
     * @return
     */
    private DcOperationLog getOpLogVoFromInput(JSONObject param) {

        DcOperationLog opLogVo = JSON.toJavaObject(param, DcOperationLog.class);
        opLogVo.setIp(param.getString("ip") == null ? "" : param.getString("ip"));//dc数据库ip字段设置不为null，verify测试环境not null，线上为null
        return opLogVo;
    }

    /**
     * 设置上传的数据类型
     *
     * @param param
     */
    protected abstract void setSubmitType(JSONObject param);

    private Long insertLog(DcOperationLog opLogVo) {
        return this.operationLogDbService.insertItem(opLogVo);
    }

    /**
     * 加密敏感数据
     *
     * @param param
     */
    protected final void encryptData(JSONObject param, String key) {

        String value = param.getString(key);
        try {
            //将未加密的数据加密
            if (!CaesarUtil.isEncrypted(value)) {
//                LoggerProxy.info("beforeEncrypt", logger, "key={},value={}", key, value);
                value = CaesarUtil.encode(value);
//                LoggerProxy.info("afterEncrypt", logger, "key={},value={}", key, value);
                param.put(key, value);
            }
        } catch (Exception e) {
            LoggerProxy.error("encryptError", logger, "key={},value={}", key, value, e);
        }
    }

    /**
     * 保存上传的提额相关数据
     */
    protected abstract void insertAmountParam(JSONObject param);


    /**
     * 提额操作的锁类型，默认是天下信用以外的类型
     *
     * @return
     */
    protected String getLockType() {
        return "OTHER";
    }


    /**
     * 几天之内不可重复提交 假设15天之不允许，返回16,90天之内不允许，返回91
     *
     * @return
     */
    protected int getNotRepeatDay() {
        return 16;
    }


    /**
     * 触发的提额流程
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = "dcTransactionManager")
    public void raiseTheAmount(JSONObject param) {
        //认证项类型
        String certificationItem = param.getString("operationType");
        //前端传值来控制是否提额
        boolean trigger = param.getBoolean("isTriggerItem");
        if (!trigger) {
            return;
        }
        String userKey = param.getString("userKey");
        String requestId = param.getString("agencyRequestId");
        //提额操作的锁类型 天下信用数据和其它数据使用不同的锁
        String lockType = getLockType();
        String lock = lockPrefix + "_" + lockType + userKey;
        Boolean isAcquired = false;
        try {
            isAcquired = redisService.acquireLock(lock, 60);
            //加锁，防止重复提额
            if (!isAcquired) {
                return;
            }
            logger.info("identify amount get lock,userKey:{},certificationItem:{},requestId:{}", userKey, certificationItem, requestId);
            //查询用户15天内是否被 certificationItem 触发过提额
            if (dcIdentityAmountService.queryRecordInFifteenDays(userKey, certificationItem, getNotRepeatDay())) {
                logger.info("find identify amount record in " + (getNotRepeatDay() - 1) + " days,do nothing,userKey:{},certificationItem:{},requestId:{}", userKey, certificationItem, requestId);
                return;
            }
            //查询该用户最近一次是否通过，即该用户是否有额度，没有额度的用户不进行认证提额
            if (!verifyResultService.isPassedRecently(userKey)) {
                logger.info("this user have no verifyResult record or the last record is reject,do nothing,userKey:{}", userKey);
                return;
            }

            DcIdentifyAmount dcIdentifyAmount = new DcIdentifyAmount();
            dcIdentifyAmount.setCreateTime(new Date());
            dcIdentifyAmount.setUpdateTime(new Date());
            dcIdentifyAmount.setOperationType(certificationItem);
            dcIdentifyAmount.setRequestId(requestId);
            dcIdentifyAmount.setUserKey(userKey);
            dcIdentityAmountService.insert(dcIdentifyAmount);
            callGateway(param);
            logger.info("identify amount complete,userKey:{},certificationItem:{},requestId:{}", userKey, certificationItem, requestId);
        } catch (Exception e) {
            logger.info("identify amount error,userKey:{},param:{}", userKey, JSON.toJSONString(param), e);
            throw new RuntimeException(e);
        } finally {
            if (isAcquired) {
                logger.info("identify amount release lock,userKey:{},certificationItem:{},requestId:{}", userKey, certificationItem, requestId);
                redisService.releaseLock(lock);
            }
        }
    }

    /**
     * 检查是否需要发起事件 后续与业务方商量迁移去掉风控内部发起事件
     * @param operationType
     * @return
     */
    public static boolean checkNeedCallBack(String operationType) {
        boolean ret = true;
        try{
            Set<String> noCallBackOperationType = ApolloClientAdapter.getMapConfig(DC_SPACE, "upload.no.callback.operationType", String.class).keySet();
            if(noCallBackOperationType.contains(operationType)){
                ret = false;
            }
        }catch (Exception ex){
            LoggerProxy.error("checkNeedCallBack", logger, "", ex);
        }
        return ret;
    }

    protected void callGateway(JSONObject param) {
        String certificationItem = param.getString("operationType");
        String requestId = param.getString("agencyRequestId");
        JSONObject gwData = new JSONObject();
        gwData.put("type", "async");
        gwData.put("agencyRequestId", requestId);
        gwData.put("requestId", requestId);
        // 组织调用gateway数据
        JSONObject gwMsg = new JSONObject();
        if (OperationType.AMOUNT_TIANXIAXINYONG.name().equals(certificationItem)) {
            gwMsg.put("eventCode", "haohuanAmountCreditReport");
            gwMsg.put("isDoubtful", param.getInteger("isDoubtful"));
            gwMsg.put("uploadReportType", param.getString("uploadReportType"));
        } else if (OperationType.AMOUNT_TIANXIAXINYONG_EDUCATION.name().equals(certificationItem)) {
            gwMsg.put("eventCode", "haohuanAmountTxxyEducationReport");
            gwMsg.put("isDoubtful", param.getInteger("isDoubtful"));
            gwMsg.put("uploadReportType", param.getString("uploadReportType"));
        } else if(OperationType.HFQ_TAX_VERIFICATION.name().equals(certificationItem)){
            gwMsg.put("eventCode", "haoHuanTaxVerificantionEvent");
        } else {
            // todo 这是个坑
            gwMsg.put("eventCode", "haoHuanAmountIdentify");
        }

        gwMsg.put("userKey", param.getString("userKey"));
        gwMsg.put("sourceSystem", "HAO_HUAN");
        gwMsg.put("transId", requestId);
        gwMsg.put("certificationItem", certificationItem);
        gwMsg.put("agencyRequestId", requestId);


        gwData.put("message", gwMsg);
        HashMap<String, String> request = new HashMap<>();
        request.put("icode", "0100010104");
        request.put("data", gwData.toJSONString());
        String result = SyncHTTPRemoteAPI.post(gatewayUrl + "/risk/api/analyse", request, 10000);
        JSONObject resultJson = JSON.parseObject(result);
        JSONObject data = resultJson.getJSONObject("data");
        String retCode = data.getString("retCode");
        if (!RetCodeEnum.SUCCESS.equals(retCode)) {
            logger.error("Identify Amount error,gateway response:{}", result);
            throw new RuntimeException("Identify Amount error");
        }
        logger.info("identify amount to gateway success,request={},resp={}",
                JSONObject.toJSONString(request), result);
    }

}
