package com.youxin.risk.datacenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.dao.datacenter.DcRequestTaskMapper;
import com.youxin.risk.commons.model.datacenter.DcRequestTask;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.datacenter.service.AbstractSubmitAmountService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class SubmitDpServiceImpl extends AbstractSubmitAmountService {

    private static final Logger logger = LoggerFactory.getLogger(SubmitDpServiceImpl.class);

    @Autowired
    DcRequestTaskMapper dcRequestTaskMapper;

    private final String RECORD_TYPE_KEY = "type";

    @Value("${data.url.write.base}")
    private String babelWriteUrl;

    /**
     * 获取需要校验的数据参数
     */
    @Override
    protected List<String> getCheckNullKeyList() {
        return new ArrayList<>();
    }

    @Override
    protected List<String> checkNullCustomizeParameters(JSONObject param) {

        List<String> result = new ArrayList<>();
        JSONObject job = param.getJSONObject("job");
        //job 相关信息
        String jobId = job.getString("jobID");
        String systemID = job.getString("systemID");
        if (StringUtils.isEmpty(jobId)) {
            result.add("job-jobId");
        }
        if (StringUtils.isEmpty(systemID)) {
            result.add("job-systemID");
        }
        // babel需要的数据类型
        String type = param.getString(RECORD_TYPE_KEY);
        if (StringUtils.isEmpty(type)) {
            result.add(RECORD_TYPE_KEY);
        }

        return result;
    }

    @Override
    protected void setSubmitType(JSONObject param) {
        param.put("operationType", param.getString(RECORD_TYPE_KEY));
    }

    @Override
    protected void insertAmountParam(JSONObject param) {

        //插入task 表
        JSONObject job = param.getJSONObject("job");
        DcRequestTask dcRequestTask = new DcRequestTask();
        dcRequestTask.setOperationLogId(param.getLong("operationLogId"));
        dcRequestTask.setJobId(job.getString("jobID"));
        dcRequestTask.setSourceSystem(param.getString("sourceSystem"));
        dcRequestTask.setUserKey(param.getString("userKey"));
        dcRequestTask.setCreateTime(new Date());
        dcRequestTask.setUpdateTime(new Date());
        dcRequestTaskMapper.insert(dcRequestTask);

        //保存前加密敏感数据
        encryptData(param);

        LoggerProxy.info("before submitDP", logger, "request={}", param.toJSONString());
        //透传信息到 数据平台
        String responseStr = SyncHTTPRemoteAPI.postJson(babelWriteUrl + "/v1/record/" + param.getString(RECORD_TYPE_KEY), param.toJSONString(), 60000);
        LoggerProxy.info("after submitDP", logger, " result={}", responseStr);
    }


    /**
     * 特殊字段加密处理
     * @param param
     */
    private void encryptData(JSONObject param) {
        String type = param.getString(RECORD_TYPE_KEY);
        //保存前加密敏感数据
        // 支付宝
        if("HFQ_ALIPAY_VERIFICATION".equalsIgnoreCase(type)){
            JSONObject data = param.getJSONObject("data");
            if(!Objects.isNull(data)){
                JSONObject personal = data.getJSONObject("personal");
                if(!Objects.isNull(personal)){
                    encryptData(personal, "realName");
                    encryptData(personal, "alipayAccount");
                }
            }
        }else if("HFQ_PROVIDENTFUND_VERIFICATION".equalsIgnoreCase(type)){
            // 公积金
            JSONArray data = param.getJSONArray("data");
            if(!Objects.isNull(data)){
                for(int i=0; i<data.size(); i++){
                    JSONObject obj = data.getJSONObject(i);
                    JSONObject personalInfo = obj.getJSONObject("personalInfo");
                    if(!Objects.isNull(personalInfo)) {
                        encryptData(personalInfo, "name");
                        encryptData(personalInfo, "phoneNumber");
                        encryptData(personalInfo, "personalAccount");
                    }
                }
            }
        }
    }
}
