package com.youxin.risk.datacenter.delayqueue;

import com.youxin.risk.common.delayqueue.SubmitDelayMessage;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.Random;
import java.util.concurrent.TimeUnit;

public class SubmitAmountRedisDelayQueue implements InitializingBean {

    @Value("${submit.amount.delay.queue.prefix}")
    private String delayQueueNamePrefix;

    private int delayQueueNamePosfix = new Random().nextInt(1000);

    @Autowired
    private RedissonClient redissonClient;

    private RBlockingQueue<SubmitDelayMessage> blockingQueue;
    private RDelayedQueue<SubmitDelayMessage> delayedQueue;

    @Override
    public void afterPropertiesSet() throws Exception {
        blockingQueue = redissonClient.getBlockingQueue(delayQueueNamePrefix + delayQueueNamePosfix);
        delayedQueue = redissonClient.getDelayedQueue(blockingQueue);
    }

    public void offer(SubmitDelayMessage e, long delay, TimeUnit timeUnit) {
        delayedQueue.offer(e, delay, timeUnit);
    }

    public SubmitDelayMessage take() throws InterruptedException {
        return blockingQueue.take();
    }
}
