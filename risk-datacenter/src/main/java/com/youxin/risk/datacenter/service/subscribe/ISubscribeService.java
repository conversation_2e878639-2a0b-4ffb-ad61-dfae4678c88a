package com.youxin.risk.datacenter.service.subscribe;

import com.youxin.risk.commons.model.datacenter.common.SubmitDataType;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/13 19:23
 * @desc
 */
public interface ISubscribeService<T,S> {

    /**
     * 订阅接口
     * 首先根据订阅信息查询上传数据中是否已有满足条件的数据。如有，则回调di（参数为requestId）
     * 如果没有，则监听该用户 该数据类型的上传接口
     * @param subscribeVo
     */
    void subscribe(T subscribeVo);

    /**
     * 数据类型 参考 {@link SubmitDataType}
     * @return
     */
    String getDataType();

    /**
     * 回调di
     * @param dcSubscribePO
     */
    void callback(S dcSubscribePO);
}
