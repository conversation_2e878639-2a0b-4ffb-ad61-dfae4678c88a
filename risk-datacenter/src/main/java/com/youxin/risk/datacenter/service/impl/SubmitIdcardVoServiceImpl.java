package com.youxin.risk.datacenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.datacenter.service.search.DataQueryService;
import com.youxin.risk.ra.service.SubmitIdcardService;
import com.youxin.risk.ra.utils.StringUtils;
import com.youxin.risk.ra.vo.RaSubmitIdcardVo;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 *
 *    获取 身份证信息
 */
@Service
public class SubmitIdcardVoServiceImpl implements DataQueryService<RaSubmitIdcardVo> {



    @Qualifier("submitIdcardServiceImpl")
    @Resource
    private com.youxin.risk.datacenter.service.SubmitIdcardService dcSubmitIdcardService;

    @Qualifier("raSubmitIdcardService")
    @Resource
    private SubmitIdcardService submitIdcardService;


    @Override
    public String getServiceCode() {
        return "submitIdcardVo";
    }

    /**
     * 从  mysql 表查询自有数据
     *
     * @param param
     * @return
     */
    @Override
    public RaSubmitIdcardVo queryDcData(JSONObject param) {

        String userKey = param.getString("userKey");
        String sourceSystem = param.getString("sourceSystem");
        String apiLoanSource = (String) param.get("apiLoanSource");

        RaSubmitIdcardVo raSubmitIdcardVo = dcSubmitIdcardService.getSubmitIdcardVoByUserKey(userKey, apiLoanSource);
        if (StringUtils.isBlank(apiLoanSource) && Objects.nonNull(raSubmitIdcardVo) && StringUtils.isBlank(raSubmitIdcardVo.getIdcardNumber())) {
            raSubmitIdcardVo = submitIdcardService.getIdcardVoByUserKey(sourceSystem, userKey);
        }

        return raSubmitIdcardVo;

    }
}
