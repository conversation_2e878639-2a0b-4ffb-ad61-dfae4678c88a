package com.youxin.risk.datacenter.mapper;

import com.youxin.risk.datacenter.model.DcSubmitAmountHouse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DcSubmitAmountHouseMapper {


    int insert(DcSubmitAmountHouse record);

    DcSubmitAmountHouse selectByPrimaryKey(Integer id);

    DcSubmitAmountHouse getByUserKey(@Param("userKey")String userKey);

    List<DcSubmitAmountHouse> getLatestByUserKey(@Param("userKey")String userKey);
}