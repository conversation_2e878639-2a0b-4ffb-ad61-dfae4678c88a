/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.datacenter.service.search;

import com.google.common.base.Preconditions;
import com.youxin.risk.commons.model.datacenter.DcVerifySubmit;
import com.youxin.risk.datacenter.service.VerifySubmitService;
import com.youxin.risk.datacenter.service.search.annotation.DcServiceCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Component
public class VerifySubmitQueryService implements DcQueryService<DcVerifySubmit> {

    @Resource
    private VerifySubmitService verifySubmitService;

    @DcServiceCode(name = "VERIFY_SUBMIT_INFO")
    public List<DcVerifySubmit> getListByUserKey(Map<String, Object> params) {
        String userKey = (String) params.get("userKey");
        String apiLoanSource = (String) params.get("apiLoanSource");
        Preconditions.checkArgument(!StringUtils.isBlank(userKey));
        return verifySubmitService.getListByUserKey(userKey, apiLoanSource);
    }
}
