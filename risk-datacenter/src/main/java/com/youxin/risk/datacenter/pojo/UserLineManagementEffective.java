package com.youxin.risk.datacenter.pojo;

import com.youxin.risk.commons.model.verify.VerifyUserLineManagement;

/**
 * 当前生效额度，与历史额度对象结构一致，为了方便区分使用两个对象。
 * <AUTHOR>
 */
public class UserLineManagementEffective extends VerifyUserLineManagement {

    private String sessionId;
    /**
     * 本次额度变更是否是好分期主动发起
     */
    private boolean isActiveNotify;
    /**
     * 若本次额度变更是业务主动发起时，字段值为额度变更对应的业务操作类型
     */
    private String activeNotifyType;

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public boolean isActiveNotify() {
        return isActiveNotify;
    }

    public void setActiveNotify(boolean activeNotify) {
        isActiveNotify = activeNotify;
    }

    public String getActiveNotifyType() {
        return activeNotifyType;
    }

    public void setActiveNotifyType(String activeNotifyType) {
        this.activeNotifyType = activeNotifyType;
    }
}
