package com.youxin.risk.datacenter.service.subscribe.verify.data.impl;

import com.youxin.risk.datacenter.client.CrawlerClient;
import com.youxin.risk.datacenter.client.GatewayClient;
import com.youxin.risk.datacenter.mapper.DcVerifyDataSubscribeMapper;
import com.youxin.risk.datacenter.model.VerifyDataSubScribeModel;
import com.youxin.risk.datacenter.pojo.VerifyDataSubscribeVo;
import com.youxin.risk.datacenter.service.subscribe.ISubscribeService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

import static com.youxin.risk.datacenter.model.VerifyDataSubScribeModel.*;

/**
 * @description: 审核相关数据订阅类
 * @author: juxiang
 * @create: 2023-11-21 14:31
 **/

public abstract class AbstractVerifyDataSubscribeService<T extends VerifyDataSubscribeVo,S extends VerifyDataSubScribeModel>
        implements ISubscribeService<T,S> {

    protected abstract DcVerifyDataSubscribeMapper getMapper();
    protected abstract CrawlerClient getCallbackClient();

    /**
     * 改抽象接口用于已经确认事件跑到了end节点
     * @param loanKey
     * @return
     */
    public abstract Map<String,Object> triggeredDataAcquisition(String loanKey);

    /**
     * 改抽象接口用于不确认事件是否已经跑到了end节点
     * @param loanKey
     * @return
     */
    public abstract  Map<String,Object> getData(String loanKey);

    public  Boolean needCallBack(Map<String, Object> data){
        return MapUtils.isNotEmpty(data);
    };

    @Override
    public void subscribe(T subscribeVo) {
        DcVerifyDataSubscribeMapper mapper = this.getMapper();
        VerifyDataSubScribeModel verifyDataSubScribeModel = mapper.getByRequestId(subscribeVo.getRequestId());
        if(Objects.nonNull(verifyDataSubScribeModel) && verifyDataSubScribeModel.callBacked()){
            return;
        }
        this.insertOrUpdate(verifyDataSubScribeModel,subscribeVo);
    }

    @Override
    public void callback(S verifyDataSubScribeModel) {
        this.callbackCrawler(this.getData(verifyDataSubScribeModel.getLoanKey()),verifyDataSubScribeModel.getRequestId(),verifyDataSubScribeModel.getChannel());
    }

    private VerifyDataSubScribeModel insertOrUpdate(VerifyDataSubScribeModel verifyDataSubScribeModel, T subscribeVo) {
        if(Objects.isNull(verifyDataSubScribeModel)){
            // 插入操作
            verifyDataSubScribeModel=new VerifyDataSubScribeModel();
            BeanUtils.copyProperties(subscribeVo,verifyDataSubScribeModel);
            verifyDataSubScribeModel.setCreateTime(new Date());
            verifyDataSubScribeModel.setRequestTimes(1);
            verifyDataSubScribeModel.setStatus(STATUS_CREATE);
            this.getMapper().insert(verifyDataSubScribeModel);
        }else {
            // 更新操作
            verifyDataSubScribeModel.setRequestTimes(verifyDataSubScribeModel.getRequestTimes()+1);
            verifyDataSubScribeModel.setUpdateTime(new Date());
            this.getMapper().update(verifyDataSubScribeModel);
        }
        return  verifyDataSubScribeModel;
    }

    public boolean callbackCrawler(Map<String, Object> data,String requestId,String channel) {
        return this.getCallbackClient().verifyDataCallBack(data,requestId,channel);
    }

    public void callBackAndUpdate(Map<String,Object> data,VerifyDataSubScribeModel verifyDataSubScribeModel){
        Assert.notNull(verifyDataSubScribeModel.getStatus(),"status can not null!");
        Boolean success=this.callbackCrawler(data,verifyDataSubScribeModel.getRequestId(),verifyDataSubScribeModel.getChannel());
        verifyDataSubScribeModel.setUpdateTime(new Date());
        Integer oldStatus=verifyDataSubScribeModel.getStatus();
        if(success){
            verifyDataSubScribeModel.setStatus(STATUS_CALLBACKED);
        }else {
            verifyDataSubScribeModel.setStatus(STATUS_CALL_FAILED);
        }
        this.getMapper().updateWithStatus(oldStatus,verifyDataSubScribeModel);
    }

    public void updateByRequestId(VerifyDataSubScribeModel verifyDataSubScribeModel){
        this.getMapper().updateByRequestId(verifyDataSubScribeModel);
    }
}
