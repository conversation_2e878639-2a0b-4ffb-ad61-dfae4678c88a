package com.youxin.risk.datacenter.service.search;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.datacenter.service.impl.LastPointEventVerifyResultForPuDaoServiceImpl;
import com.youxin.risk.datacenter.service.search.annotation.DcServiceCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 审核结果
 */
@Component
public class DcVerifyResultService implements DcQueryService<Map<String, Object>>{
    private final static Logger logger = LoggerFactory.getLogger(DcVerifyResultService.class);

    @Autowired
    private LastPointEventVerifyResultForPuDaoServiceImpl lastPointEventVerifyResultForPuDaoService;

    @DcServiceCode(name = "VERIFY_RESULT")
    public Map<String, Object> getByUserKey(Map<String, Object> params) {
        LoggerProxy.info("LastPointEventVerifyResultService",logger,"params = {}",params);
        JSONObject jsonParam = new JSONObject();
        jsonParam.put("userKey",params.get("userKey"));
        jsonParam.put("sourceSystem",params.get("sourceSystem"));
        jsonParam.put("eventCode",params.get("eventCode"));
        return lastPointEventVerifyResultForPuDaoService.queryDcData(jsonParam);
    }
}
