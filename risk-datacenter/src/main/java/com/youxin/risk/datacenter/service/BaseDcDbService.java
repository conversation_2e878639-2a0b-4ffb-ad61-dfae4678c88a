package com.youxin.risk.datacenter.service;


import com.youxin.risk.commons.model.BaseModel;
import com.youxin.risk.commons.remote.model.datacenter.DcRequestService;

/**
 * 
 * <AUTHOR>
 *
 */
public interface BaseDcDbService<T extends BaseModel> {

    T getItem(T item);

    T getItemByPrimaryKey(long id);

    Long insertItem(T item);

    T getByOperationId(Long operationId);

    T queryTableDataByService(DcRequestService dcRequestService) throws Exception;



}
