package com.youxin.risk.datacenter.service.subscribe.upload.data.impl;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.commons.dao.datacenter.DcSubmitPlistShardingMapper;
import com.youxin.risk.commons.model.datacenter.DcSubmitPlist;
import com.youxin.risk.commons.model.datacenter.common.SubmitDataType;
import com.youxin.risk.commons.utils.DateUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.datacenter.pojo.SubscribeVo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/13 19:48
 * @desc plist 订阅信息
 */
@Service
public class PListSubscribeService extends AbstractSubscribeService {

    @Resource
    private DcSubmitPlistShardingMapper dcSubmitPlistShardingMapper;

    @Override
    public boolean isFetchedWithinTheAcquisitionTime(SubscribeVo subscribeVo) {
        String userKey = subscribeVo.getUserKey();
        Date acquisitionTime = subscribeVo.getUploadTime();
        String shardingTableName = DcSubmitPlist.getShardingTableName(userKey);
        List<DcSubmitPlist> dcSubmitPlistRecords =
                dcSubmitPlistShardingMapper.listByUserKeyAndAcquisitionTime(shardingTableName, userKey,
                        acquisitionTime);
        if (CollectionUtils.isEmpty(dcSubmitPlistRecords)) {
            return false;
        }
        LoggerProxy.info("isFetchedWithinTheAcquisitionTime", LOGGER, "plist: userKey:{}, acquisitionTime:{}, data:{}",
                userKey, DateUtil.format(acquisitionTime, DateUtil.LONG_WEB_FORMAT), JSON.toJSONString(dcSubmitPlistRecords));
        return true;
    }

    @Override
    public String getDataType() {
        return SubmitDataType.SUBMIT_PLIST.name();
    }
}
