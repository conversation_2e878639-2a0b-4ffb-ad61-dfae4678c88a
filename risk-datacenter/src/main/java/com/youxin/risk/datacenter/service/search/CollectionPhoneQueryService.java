/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.datacenter.service.search;

import com.youxin.risk.commons.model.datacenter.DcCollectionPhone;
import com.youxin.risk.commons.vo.datavo.CollectionPhoneVo;
import com.youxin.risk.datacenter.service.CollectionPhoneService;
import com.youxin.risk.datacenter.service.search.annotation.DcServiceCode;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Component
public class CollectionPhoneQueryService implements DcQueryService<DcCollectionPhone> {

    @Resource
    private CollectionPhoneService collectionPhoneService;

    @DcServiceCode(name = "COLLECTION_PHONE")
    public List<CollectionPhoneVo> getAllCollectionPhone(Map<String, Object> params) {
        return collectionPhoneService.getAllCollectionPhone();
    }
}
