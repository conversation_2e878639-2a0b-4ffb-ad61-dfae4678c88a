package com.youxin.risk.datacenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.JsonUtils;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class HfqUserInfoService {

    private static final Logger logger = LoggerFactory.getLogger(HfqUserInfoService.class);

    @Value("${hfq.user.url}")
    private String userUrl;


    /** 调用好分期接口获取用户信息
    *
    * */
    public JSONObject getUserInfo(String userKey) {
        JSONObject resulet = null;
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("userKey", userKey);
            String response = SyncHTTPRemoteAPI.postJson(userUrl, JsonUtils.toJson(params), 60000);
            LoggerProxy.info("getHfqUserInfo", logger, "userKey={},reponse={}", userKey, response);
            JSONObject resultObject = JSONObject.parseObject(response);
            Integer code = resultObject.getInteger("code");
            if (null != code && 0 == code) {
                resulet = resultObject.getJSONObject("data");
            }
        } catch (Exception e) {
            LoggerProxy.error("getHfqUserInfoError", logger, "userKey={}", userKey, e);
        }
        return resulet;
    }


}
