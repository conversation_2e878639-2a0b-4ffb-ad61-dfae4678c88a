package com.youxin.risk.datacenter.mapper;

import com.youxin.risk.datacenter.model.DcSubscribePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/14 17:50
 * @desc
 */
public interface DcSubscribeMapper {
    /**
     * 根据requestId查询
     * 且is_fetched=1
     * @param requestId
     * @return
     */
    DcSubscribePO getByRequestId(@Param("requestId") String requestId);

    /**
     * 根据userKey查询
     * 且 is_fetched=0
     *
     * @param userKey
     * @return
     */
    List<DcSubscribePO> getByUserKeyAndDataType(@Param("userKey") String userKey, @Param("dataType") String dataType);

    /**
     * requestId为唯一索引，如果表中无该requestId,则新增
     *
     * @param dcSubscribePO
     * @return
     */
    int insert(DcSubscribePO dcSubscribePO);

    /**
     * 更新
     * @param dcSubscribePO
     * @return
     */
    int update(DcSubscribePO dcSubscribePO);
}
