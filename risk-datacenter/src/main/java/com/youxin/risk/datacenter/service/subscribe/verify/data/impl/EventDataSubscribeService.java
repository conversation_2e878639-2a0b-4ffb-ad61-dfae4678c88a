package com.youxin.risk.datacenter.service.subscribe.verify.data.impl;

import com.youxin.risk.datacenter.client.CrawlerClient;
import com.youxin.risk.datacenter.client.GatewayClient;
import com.youxin.risk.datacenter.mapper.DcVerifyDataSubscribeMapper;
import com.youxin.risk.datacenter.pojo.VerifyDataSubscribeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.youxin.risk.datacenter.service.search.VerifyDataService.EVENT_EXITING;

/**
 * @description:
 * @author: juxiang
 * @create: 2023-11-22 10:49
 **/
@Component
public class EventDataSubscribeService  extends AbstractVerifyDataSubscribeService{
    @Autowired
    DcVerifyDataSubscribeMapper dcVerifyDataSubscribeMapper;

    @Autowired
    CrawlerClient client;

    @Autowired
    GatewayClient gatewayClient;

    @Override
    public String getDataType() {
        return EVENT_EXITING;
    }


    @Override
    protected DcVerifyDataSubscribeMapper getMapper() {
        return dcVerifyDataSubscribeMapper;
    }

    @Override
    protected CrawlerClient getCallbackClient() {
        return client;
    }

    @Override
    public Map<String, Object> triggeredDataAcquisition(String loanKey) {
        return gatewayClient.eventExitingByLoanKey(loanKey);
    }

    @Override
    public Map<String, Object> getData(String loanKey) {
        return gatewayClient.eventExitingByLoanKey(loanKey);
    }
}
