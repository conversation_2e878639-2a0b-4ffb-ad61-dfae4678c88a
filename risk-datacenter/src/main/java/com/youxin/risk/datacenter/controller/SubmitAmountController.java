package com.youxin.risk.datacenter.controller;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.datacenter.pojo.JsonResultVo;
import com.youxin.risk.datacenter.service.impl.*;
import com.youxin.risk.verify.controller.BaseController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping(value = "/submit/amount")
public class SubmitAmountController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(SubmitAmountController.class);

    @Autowired
    private SubmitAmountCreditCardServiceImpl submitAmountCreditCardService;

    @Autowired
    private SubmitAmountEducationServiceImpl submitAmountEducationService;

    @Autowired
    private SubmitAmountTianxiaxinyongServiceImpl submitAmountTianxiaxinyongService;

    @Autowired
    private SubmitAmountTxxyEducationServiceImpl submitAmountTxxyEducationService;

    @Autowired
    private SubmitAmountHouseServiceImpl submitAmountHouseService;

    @Autowired
    private SubmitAmountJobServiceImpl submitAmountJobService;

    @Autowired
    private SubmitAmountMarriageServiceImpl submitAmountMarriageService;

    @Autowired
    private SubmitAmountVehicleServiceImpl submitAmountVehicleService;

    @Autowired
    private SubmitAmountECServiceImpl submitAmountECService;

    @Autowired
    private SubmitAmountTxxyVehicleServiceImpl submitAmountTxxyVehicleService;

    @Autowired
    private SubmitAmountZhimaScoreServiceImpl submitAmountZhimaScoreService;

    @Autowired
    private SubmitLearnInternetInformationServiceImpl submitLearnInternetInformationService;

    @Autowired
    private SubmitDpServiceImpl submitDpService;

    /**
     * 上传信用卡信息
     * @param param
     * @return
     */
    @RequestMapping(value = "/creditcard")
    @ResponseBody
    public JsonResultVo submitCreditCard(@RequestBody JSONObject param) {
        logger.info("submit amount creditcard,param:{}",param.toJSONString());
        try {
            return submitAmountCreditCardService.submitAmountData(param);
        } catch (Exception e) {
            LoggerProxy.error("submitAmountCreditCardError", logger, "param={}", param, e);
            return JsonResultVo.error();
        }

    }

    /**
     * 上传学历信息
     * @param param
     * @return
     */
    @RequestMapping(value = "/education")
    @ResponseBody
    public JsonResultVo submitEducation(@RequestBody JSONObject param) {
        logger.info("submit amount education,param:{}",param.toJSONString());
        try {
            return submitAmountEducationService.submitAmountData(param);
        } catch (Exception e) {
            LoggerProxy.error("submitAmountEducationError", logger, "param={}", param, e);
            return JsonResultVo.error();
        }
    }


    /**
     * 上传天下信用报告
     * @param param
     * @return
     */
    @RequestMapping(value = "/tianxiaxinyong")
    @ResponseBody
    public JsonResultVo submitTianxiaxinyong(@RequestBody JSONObject param) {
        logger.info("submit amount tianxiaxinyong,param:{}",param.toJSONString());
        try {
            return submitAmountTianxiaxinyongService.submitAmountData(param);
        } catch (Exception e) {
            LoggerProxy.error("submitTianxiaxinyongError", logger, "param={}", param, e);
            return JsonResultVo.error();
        }
    }

    /**
     * 上传天下信用学历报告
     * @param param
     * @return
     */
    @RequestMapping(value = "/txxyEducation")
    @ResponseBody
    public JsonResultVo submitTxxyEducation(@RequestBody JSONObject param) {
        logger.info("submit amount txxyEducation,param:{}",param.toJSONString());
        try {
            return submitAmountTxxyEducationService.submitAmountData(param);
        } catch (Exception e) {
            LoggerProxy.error("submitTxxyEducationError", logger, "param={}", param, e);
            return JsonResultVo.error();
        }
    }

    /**
     * 上传住房信息
     * @param param
     * @return
     */
    @RequestMapping(value = "/house")
    @ResponseBody
    public JsonResultVo submitHouse(@RequestBody JSONObject param) {
        logger.info("submit amount house,param:{}",param.toJSONString());
        try {
            return submitAmountHouseService.submitAmountData(param);
        } catch (Exception e) {
            LoggerProxy.error("submitHouseError", logger, "param={}", param, e);
            return JsonResultVo.error();
        }
    }

    /**
     * 上传工作信息
     * @param param
     * @return
     */
    @RequestMapping(value = "/job")
    @ResponseBody
    public JsonResultVo submitJob(@RequestBody JSONObject param) {
        logger.info("submit amount job,param:{}",param.toJSONString());
        try {
            return submitAmountJobService.submitAmountData(param);
        } catch (Exception e) {
            LoggerProxy.error("submitJobError", logger, "param={}", param, e);
            return JsonResultVo.error();
        }
    }

    /**
     * 上传婚姻状况
     * @param param
     * @return
     */
    @RequestMapping(value = "/marriage")
    @ResponseBody
    public JsonResultVo submitMarriage(@RequestBody JSONObject param) {
        logger.info("submit amount marriage,param:{}",param.toJSONString());
        try {
            return submitAmountMarriageService.submitAmountData(param);
        } catch (Exception e) {
            LoggerProxy.error("submitMarriageError", logger, "param={}", param, e);
            return JsonResultVo.error();
        }
    }

    /**
     * 上传车辆信息
     * @param param
     * @return
     */
    @RequestMapping(value = "/vehicle")
    @ResponseBody
    public JsonResultVo submitVehicle(@RequestBody JSONObject param) {
        logger.info("submit amount vehicle,param:{}",param.toJSONString());
        try {
            return submitAmountVehicleService.submitAmountData(param);
        } catch (Exception e) {
            LoggerProxy.error("submitVehicleError", logger, "param={}", param, e);
            return JsonResultVo.error();
        }
    }


    /**
     * 上传企业认证信息
     * @param param
     * @return
     */
    @RequestMapping(value = "/enterpriseCertification")
    @ResponseBody
    public JsonResultVo submitEnterpriseCertification(@RequestBody JSONObject param) {
        logger.info("submit amount enterpriseCertification,param:{}",param.toJSONString());
        try {
            return submitAmountECService.submitAmountData(param);
        } catch (Exception e) {
            LoggerProxy.error("submitEnterpriseCertificationError", logger, "param={}", param, e);
            return JsonResultVo.error();
        }
    }

    /**
     * 上传天下信用车辆认证数据
     * @param param
     * @return
     */
    @RequestMapping(value = "/txxyVehicle")
    @ResponseBody
    public JsonResultVo submitTxxyVehicle(@RequestBody JSONObject param) {
        logger.info("submit amount txxyVehicle,param:{}",param.toJSONString());
        try {
            return submitAmountTxxyVehicleService.submitAmountData(param);
        } catch (Exception e) {
            LoggerProxy.error("submitTxxyVehicleError", logger, "param={}", param, e);
            return JsonResultVo.error();
        }
    }

    /**
     * 上传芝麻分信息
     *
     * @param param
     * @return
     */
    @RequestMapping(value = "/zhimaScore")
    @ResponseBody
    public JsonResultVo submitZhimaScore(@RequestBody JSONObject param) {
        logger.info("submit amount zhimaScore,param:{}", param.toJSONString());
        try {
            return submitAmountZhimaScoreService.submitAmountData(param);
        } catch (Exception e) {
            LoggerProxy.error("submitZhimaScoreError", logger, "param={}", param, e);
            return JsonResultVo.error();
        }
    }

    /**
     * 上传学信网提额数据
     *
     * @param param
     * @return
     */
    @RequestMapping(value = "/learnInternetInformation")
    @ResponseBody
    public JsonResultVo submitLearnInternetInformation(@RequestBody JSONObject param) {
        logger.info("submit amount learnInternetInformation, param:{}", param.toJSONString());
        try {
            return submitLearnInternetInformationService.submitAmountData(param);
        } catch (Exception e) {
            LoggerProxy.error("submitLearnInternetInformationError", logger, "param={}", param, e);
            return JsonResultVo.error();
        }
    }



    /**
     *
     * @param param
     * @return
     */
    @RequestMapping(value = "/unifyDp")
    @ResponseBody
    public JsonResultVo submitAmountUnifyDp(@RequestBody JSONObject param) {
        logger.info("submit amount unifyDp, param:{}", param.toJSONString());
        try {
            return submitDpService.submitAmountData(param);
        } catch (Exception e) {
            LoggerProxy.error("submitAmountUnifyDpError", logger, "param={}", param, e);
            return JsonResultVo.error();
        }
    }


    /**
     * 公积金提额
     * @param param
     * @return
     */
    @RequestMapping(value = "/providentFundDp")
    @ResponseBody
    public JsonResultVo submitAmountProvidentFundDp(@RequestBody JSONObject param) {
        logger.info("submit amount providentFundDp, param:{}", param.toJSONString());
        try {
            return submitDpService.submitAmountData(param);
        } catch (Exception e) {
            LoggerProxy.error("submitAmountProvidentFundError", logger, "param={}", param, e);
            return JsonResultVo.error();
        }
    }

    /**
     * 支付宝提额
     * @param param
     * @return
     */
    @RequestMapping(value = "/alipayDp")
    @ResponseBody
    public JsonResultVo submitAmountAlipayDp(@RequestBody JSONObject param) {
        logger.info("submit amount alipayDp, param:{}", param.toJSONString());
        try {
            return submitDpService.submitAmountData(param);
        } catch (Exception e) {
            LoggerProxy.error("submitAmountAlipayDpError", logger, "param={}", param, e);
            return JsonResultVo.error();
        }
    }
}

