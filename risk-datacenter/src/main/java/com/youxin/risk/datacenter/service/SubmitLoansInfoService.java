package com.youxin.risk.datacenter.service;

import com.youxin.risk.commons.model.datacenter.DcSubmitLoansInfo;
import com.youxin.risk.ra.vo.RaSubmitLoansInfoVo;

public interface SubmitLoansInfoService extends BaseDcDbService<DcSubmitLoansInfo>{

    DcSubmitLoansInfo getByUserKey(String userKey, String apiLoanSource);

    /**
     * 根据userKey获取贷款信息
     *
     * @param userKey
     * @return
     */
    RaSubmitLoansInfoVo getLoansInfoVoByUserKey(String userKey, String apiLoanSource);
}
