package com.youxin.risk.datacenter.mapper;

import com.youxin.risk.datacenter.model.DcSubmitAmountCreditCard;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DcSubmitAmountCreditCardMapper {

    Long insert(DcSubmitAmountCreditCard record);

    DcSubmitAmountCreditCard getByUserKey(@Param("userKey")String userKey);

    List<DcSubmitAmountCreditCard> getLatestByUserKey(@Param("userKey")String userKey);

}