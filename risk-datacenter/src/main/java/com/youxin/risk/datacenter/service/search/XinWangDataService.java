package com.youxin.risk.datacenter.service.search;

import java.util.Map;

import javax.annotation.Resource;

import com.youxin.risk.datacenter.service.search.annotation.DcServiceCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.google.common.base.Preconditions;
import com.youxin.risk.commons.model.datacenter.DcSubmitXw;
import com.youxin.risk.datacenter.service.SubmitXwBankService;

@Component
public class XinWangDataService implements DcQueryService<DcSubmitXw> {


    @Resource
    private SubmitXwBankService submitXwBankService;

    @DcServiceCode(name = "XW")
    public DcSubmitXw getByUserKey(Map<String, Object> params) {
        String userKey = (String) params.get("userKey");
        Preconditions.checkArgument(!StringUtils.isBlank(userKey));
        return submitXwBankService.getByUserKey(userKey);
    }
}