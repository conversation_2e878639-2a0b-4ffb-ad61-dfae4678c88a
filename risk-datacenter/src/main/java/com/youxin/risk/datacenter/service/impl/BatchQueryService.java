package com.youxin.risk.datacenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.model.BaseModel;
import com.youxin.risk.commons.model.datacenter.DcOperationLog;
import com.youxin.risk.commons.model.datacenter.common.OperationType;
import com.youxin.risk.commons.remote.model.RpcResponse;
import com.youxin.risk.commons.remote.model.datacenter.DcRequestService;
import com.youxin.risk.commons.remote.model.datacenter.DcResponse;
import com.youxin.risk.commons.remote.model.datacenter.DcResponseService;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.datacenter.constants.DcRequestType;
import com.youxin.risk.datacenter.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 按表 注册信息、银行卡信息等查询用户好还用户基础信息
 */
@Service
public class BatchQueryService implements InitializingBean {

    public static void main(String[] args){
        System.out.println("enum: " + DcRequestType.valueOf("submit2"));
    }
    private  HashMap<String, BaseDcDbService> dcDbServiceHashMap = new HashMap<>();

    @Autowired
    SubmitAddressService submitAddressService;

    @Autowired
    SubmitBankCardService submitBankCardService;

    @Autowired
    SubmitContactInfoService submitContactInfoService;

    @Autowired
    SubmitCreditCardService submitCreditCardService;

    @Autowired
    SubmitIdcardService submitIdcardService;

    @Autowired
    SubmitJobService submitJobService;

    @Autowired
    SubmitLoansInfoService submitLoansInfoService;

    @Autowired
    SubmitPlistService submitPlistService;

    @Autowired
    SubmitRegisterService submitRegisterService;

    @Autowired
    SubmitSocialContactService submitSocialContactService;

    @Autowired
    VerifySubmitService verifySubmitService;

    @Autowired
    SubmitXwBankService submitXwBankService;

    @Autowired
    OperationLogDbService operationLogDbService;

    private Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public void afterPropertiesSet() throws Exception {
        dcDbServiceHashMap.put(OperationType.ADDRESS.name(), submitAddressService);
        dcDbServiceHashMap.put(OperationType.BANK_CARD.name(), submitBankCardService);
        dcDbServiceHashMap.put(OperationType.CONTACT.name(), submitContactInfoService);
        dcDbServiceHashMap.put(OperationType.CREDITCARD.name(), submitCreditCardService);
        dcDbServiceHashMap.put(OperationType.ID_CARD.name(), submitIdcardService);
        dcDbServiceHashMap.put(OperationType.JOB.name(), submitJobService);
        dcDbServiceHashMap.put(OperationType.LOANSINFO.name(), submitLoansInfoService);
//        dcDbServiceHashMap.put(OperationType.PLIST.name(), submitPlistService);
        dcDbServiceHashMap.put(OperationType.REGISTER.name(), submitRegisterService);
        dcDbServiceHashMap.put(OperationType.SOCIAL_CONTACT.name(), submitSocialContactService);
        dcDbServiceHashMap.put(OperationType.XIN_WANG.name(), submitXwBankService);
        dcDbServiceHashMap.put(OperationType.SUBMIT.name(), verifySubmitService);

        dcDbServiceHashMap.put(OperationType.OPERATION_LOG.name(), operationLogDbService);
    }

    public RpcResponse<DcResponse> doQuery(List<DcRequestService> services, String userKeyReq){
        RpcResponse<DcResponse> response =  new RpcResponse<>();
        List<DcResponseService> dcResponseServices = new ArrayList<>();
        DcResponse dcResponse = new DcResponse();
        for(DcRequestService dcRequestService : services){
            DcResponseService dcResponseService = new DcResponseService();
            dcResponseService.setServiceCode(dcRequestService.getServiceCode());
            BaseModel model = null;
            try{
                if(dcDbServiceHashMap.get(dcRequestService.getServiceCode()) != null){
                    model = dcDbServiceHashMap.get(dcRequestService.getServiceCode()).queryTableDataByService(dcRequestService);
                    HashMap<String,Object> resultHmap = new HashMap<>();
                    resultHmap.put("data", JSON.toJSONString(model));
                    dcResponseService.setResult(resultHmap);
                    if(model == null){
                        dcResponseService.setRetCode(RetCodeEnum.FAILED.getValue());
                    }else {
                        dcResponseService.setRetCode(RetCodeEnum.SUCCESS.getValue());
                    }
                    dcResponseServices.add(dcResponseService);
                }else{//其他查询type不支持
                    dcResponseService.setRetCode(RetCodeEnum.FAILED.getValue());
                    dcResponseServices.add(dcResponseService);
                }
            }catch (Exception ex){
                dcResponseService.setRetCode(RetCodeEnum.FAILED.getValue());
                dcResponseServices.add(dcResponseService);
                LoggerProxy.error("handQueryError" , logger, "serviceCode= " + dcRequestService.getServiceCode(), ex);
            }
        }
        dcResponse.setUserKey(userKeyReq);
        response.setRetCode(RetCodeEnum.SUCCESS.getValue());
        dcResponse.setServices(dcResponseServices);
        response.setResult(dcResponse);
        return response;
    }



}
