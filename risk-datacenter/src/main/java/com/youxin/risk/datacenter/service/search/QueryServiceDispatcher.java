package com.youxin.risk.datacenter.service.search;

import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.datacenter.service.search.annotation.DcServiceCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodIntrospector;
import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2019-01-03
 */
@Component
public class QueryServiceDispatcher {

    private final static Logger logger = LoggerFactory.getLogger(QueryServiceDispatcher.class);

    private Map<String, Function<Map<String, Object>, Object>> serviceMap = new ConcurrentHashMap<>();

    private static boolean matches(Method method) {
        return AnnotatedElementUtils.isAnnotated(method, DcServiceCode.class.getName());
    }

    @Autowired
    void initServiceMap(List<DcQueryService> dcQueryServiceList) {
        for (DcQueryService dcQueryService : dcQueryServiceList) {
            injectDcService(dcQueryService);
        }
        LoggerProxy.info("QueryServiceDispatcher", logger, " register dc service_code_list:{}, list size:{}", serviceMap.keySet(), serviceMap.size());
    }

    private void injectDcService(DcQueryService dcQueryService) {
        Set<Method> annotatedMethods = MethodIntrospector.selectMethods(dcQueryService.getClass(), QueryServiceDispatcher::matches);
        for (Method annotatedMethod : annotatedMethods) {
            DcServiceCode methodAnnotation = annotatedMethod.getAnnotation(DcServiceCode.class);
            String[] serviceCodeNames = methodAnnotation.name();
            for (String serviceCode : serviceCodeNames) {
                if(serviceMap.containsKey(serviceCode)) {
                    LoggerProxy.error("QueryServiceDispatcher", logger, " found duplicate dc_service_code:{}", serviceCode);
                    throw new IllegalStateException("found duplicate service_code:"+ serviceCode);
                }
                serviceMap.put(serviceCode, args -> ReflectionUtils.invokeMethod(annotatedMethod, dcQueryService, args));
            }
        }
    }

    public Object dispatch(String serviceCode, Map<String, Object> params) {
        return serviceMap.get(serviceCode).apply(params);
    }
}
