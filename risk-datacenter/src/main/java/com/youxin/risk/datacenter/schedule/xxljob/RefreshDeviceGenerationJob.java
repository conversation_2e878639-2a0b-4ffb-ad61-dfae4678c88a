package com.youxin.risk.datacenter.schedule.xxljob;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.youxin.risk.commons.xxl.job.XxlJobBase;
import com.youxin.risk.datacenter.service.DcDeviceGenerationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
public class RefreshDeviceGenerationJob implements XxlJobBase {

    private static final Logger LOGGER = LoggerFactory.getLogger(RefreshDeviceGenerationJob.class);

    @Resource
    private DcDeviceGenerationService dcDeviceGenerationService;

    @Override
    @XxlJob(value = "refreshDeviceGenerationJob")
    public ReturnT<String> execJobHandler(String param) {
        LOGGER.info("refreshDeviceGenerationJob start");
        dcDeviceGenerationService.refreshDeviceGeneration();
        LOGGER.info("refreshDeviceGenerationJob end");
        return ReturnT.SUCCESS;
    }

}
