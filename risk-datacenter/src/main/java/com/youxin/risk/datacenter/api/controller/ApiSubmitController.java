package com.youxin.risk.datacenter.api.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.datacenter.vo.api.*;
import com.youxin.risk.commons.utils.MaskUtils;
import com.youxin.risk.datacenter.api.service.ApiSubmitService;
import com.youxin.risk.datacenter.pojo.JsonResultVo;
import com.youxin.risk.ra.utils.StringUtils;
import com.youxin.risk.verify.controller.BaseController;
import com.youxin.risk.verify.utils.ValidCodeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping(value = "/submit/api")
public class ApiSubmitController extends BaseController {

    private static final Logger LOG = LoggerFactory.getLogger(ApiSubmitController.class);

    @Autowired
    @Qualifier("apiSubmitServImpl")
    private ApiSubmitService apiVerifySubmitService;

    @RequestMapping(value = "/idcard")
    @ResponseBody
    public JsonResultVo submitIdCard(@RequestBody JSONObject param) {

        try {
            LOG.info("submit api idcard, param: {}", param.getString("userKey"));
            RiskApiSubmitIdcardVo idCardVo = JSON.toJavaObject(param, RiskApiSubmitIdcardVo.class);

            // 校验参数：身份证号和身份证名不能为空
            if (StringUtils.isBlank(idCardVo.getIdcardNumber())) {
                return JsonResultVo.error().addData("missingParameters", "idcardNumber");
            }
            if (StringUtils.isBlank(idCardVo.getIdcardName())) {
                return JsonResultVo.error().addData("missingParameters", "idcardName");
            }
            if (!ValidCodeUtil.validateIdCard(MaskUtils.unMaskValue(idCardVo.getIdcardNumber()))) {
                LOG.warn("idcardNumber is invalid, userkey: {}, idno: {}", idCardVo.getUserKey(), idCardVo.getIdcardNumber());
                return JsonResultVo.error().addData("idcardNumberInvalid", "idcardNumber");
            }
            Integer insertCount = apiVerifySubmitService.submitIdCard(idCardVo);
            return JsonResultVo.success().addData("insertCount", insertCount);
        } catch (Exception e) {
            LOG.error("submit api idcard error, param: {}", param, e);
            return JsonResultVo.error();
        }
    }

    @RequestMapping(value = "/register")
    @ResponseBody
    public JsonResultVo submitRegister(@RequestBody JSONObject param) {
        try {
            LOG.info("submit api register, param: {}", param);
            RiskApiSubmitRegisterVo registerVo = JSON.toJavaObject(param, RiskApiSubmitRegisterVo.class);
            Integer insertCount = apiVerifySubmitService.submitRegister(registerVo);
            return JsonResultVo.success().addData("insertCount", insertCount);
        } catch (Exception e) {
            LOG.error("submit api register error, param: {}", param, e);
            return JsonResultVo.error();
        }
    }

    @RequestMapping(value = "/bankcard")
    @ResponseBody
    public JsonResultVo submitBankcard(@RequestBody JSONObject param) {
        try {
            LOG.info("submit api bankCard, param: {}", param);
            RiskApiSubmitBankCardVo bankCardVo = JSON.toJavaObject(param, RiskApiSubmitBankCardVo.class);
            Integer insertCount = apiVerifySubmitService.submitBankCard(bankCardVo);
            return JsonResultVo.success().addData("insertCount", insertCount);
        } catch (Exception e) {
            LOG.error("submit api bankCard error, param: {}", param, e);
            return JsonResultVo.error();
        }
    }

    @RequestMapping(value = "/contact")
    @ResponseBody
    public JsonResultVo submitContact(@RequestBody JSONObject param) {
        try {
            LOG.info("submit api contact, param: {}", param);
            RiskApiSubmitContactVo contactVo = JSON.toJavaObject(param, RiskApiSubmitContactVo.class);
            Integer insertCount = apiVerifySubmitService.submitContact(contactVo);
            return JsonResultVo.success().addData("insertCount", insertCount);
        } catch (Exception e) {
            LOG.error("submit api contact error, param: {}", param, e);
            return JsonResultVo.error();
        }
    }

    @RequestMapping(value = "/other")
    @ResponseBody
    public JsonResultVo submitOther(@RequestBody JSONObject param) {
        try {
            LOG.info("submit api other, param: {}", param);
            RiskApiSubmitOtherVo otherVo = JSON.toJavaObject(param, RiskApiSubmitOtherVo.class);
            Integer insertCount = apiVerifySubmitService.submitOther(otherVo);
            return JsonResultVo.success().addData("insertCount", insertCount);
        } catch (Exception e) {
            LOG.error("submit api other error, param: {}", param, e);
            return JsonResultVo.error();
        }
    }

}

