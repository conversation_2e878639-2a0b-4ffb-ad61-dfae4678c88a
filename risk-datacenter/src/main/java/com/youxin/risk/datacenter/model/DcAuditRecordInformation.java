package com.youxin.risk.datacenter.model;

import com.youxin.risk.commons.model.BaseModel;

import java.math.BigInteger;
import java.util.Date;

public class DcAuditRecordInformation extends BaseModel {

    /**
     * 用户ID
     */
    private BigInteger uid;

    /**
     * 资金方
     */
    private String funder;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 拒绝次数
     */
    private int count;

    public BigInteger getUid() {
        return uid;
    }

    public void setUid(BigInteger uid) {
        this.uid = uid;
    }

    public String getFunder() {
        return funder;
    }

    public void setFunder(String funder) {
        this.funder = funder;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

}
