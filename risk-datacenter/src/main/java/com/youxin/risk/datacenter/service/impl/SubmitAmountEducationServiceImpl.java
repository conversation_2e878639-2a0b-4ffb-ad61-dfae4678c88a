package com.youxin.risk.datacenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.datacenter.common.OperationType;
import com.youxin.risk.datacenter.mapper.DcSubmitAmountEducationMapper;
import com.youxin.risk.datacenter.model.DcSubmitAmountEducation;
import com.youxin.risk.datacenter.service.AbstractSubmitAmountService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class SubmitAmountEducationServiceImpl extends AbstractSubmitAmountService {


    private static final Logger logger = LoggerFactory.getLogger(SubmitAmountEducationServiceImpl.class);


    @Autowired
    DcSubmitAmountEducationMapper dcSubmitAmountEducationMapper;

    /**
     * 获取需要校验的数据参数
     */
    @Override
    protected List<String> getCheckNullKeyList() {

        List<String> keyList = new ArrayList<>();
        //毕业院校
        keyList.add("university");
        //最高学历
        keyList.add("highestEducationLevel");
        //最高学位
        keyList.add("highestDegree");
        //毕业年份
        keyList.add("graduateYear");
        return keyList;
    }

    @Override
    protected List<String> checkNullCustomizeParameters(JSONObject param) {
        return new ArrayList<>();
    }

    @Override
    protected void setSubmitType(JSONObject param) {

        param.put("operationType", OperationType.AMOUNT_EDUCATION.name());
    }

    @Override
    protected void insertAmountParam(JSONObject param) {

        DcSubmitAmountEducation dcSubmitAmountEducation = JSON.toJavaObject(param, DcSubmitAmountEducation.class);
        dcSubmitAmountEducationMapper.insert(dcSubmitAmountEducation);

    }

}
