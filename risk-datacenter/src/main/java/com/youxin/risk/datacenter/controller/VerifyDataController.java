package com.youxin.risk.datacenter.controller;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.datacenter.model.VerifyDataSubScribeModel;
import com.youxin.risk.datacenter.pojo.VerifyDataSubscribeVo;
import com.youxin.risk.datacenter.service.impl.VerifyDataCallbackService;
import com.youxin.risk.datacenter.service.subscribe.ISubscribeService;
import com.youxin.risk.datacenter.service.subscribe.SubscribeServiceHandler;
import com.youxin.risk.datacenter.service.subscribe.verify.data.impl.AbstractVerifyDataSubscribeService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import static com.youxin.risk.datacenter.model.VerifyDataSubScribeModel.STATUS_CALLBACKED;

/**
 * @description:审核相关数据接口
 * @author: juxiang
 * @create: 2023-11-22 14:41
 **/
@RestController
@RequestMapping("/verifyData")
public class VerifyDataController {

    @Autowired
    VerifyDataCallbackService verifyDataCallbackService;
    @Autowired
    SubscribeServiceHandler subscribeServiceHandler;
    @RequestMapping("/callback")
    public void callback(@RequestBody JSONObject jsonObject){
        verifyDataCallbackService.callback(jsonObject);
    }


    @RequestMapping("/dataToBeCalledBack")
    public String dataToBeCalledBack(@RequestBody JSONObject requestBody){
        VerifyDataSubScribeModel verifyDataSubScribeModel = JSONObject.toJavaObject(requestBody.getJSONObject("params"),
                VerifyDataSubScribeModel.class);
        Assert.notNull(verifyDataSubScribeModel,"params can not be null!");
        AbstractVerifyDataSubscribeService subscribeService = (AbstractVerifyDataSubscribeService)subscribeServiceHandler
                .getSubscribeService(verifyDataSubScribeModel.getDataType());
        Map data = subscribeService.getData(verifyDataSubScribeModel.getLoanKey());
        if (subscribeService.needCallBack(data)){
            verifyDataSubScribeModel.setStatus(STATUS_CALLBACKED);
            subscribeService.updateByRequestId(verifyDataSubScribeModel);
        }
        return JSONObject.toJSONString(data);
    }
}
