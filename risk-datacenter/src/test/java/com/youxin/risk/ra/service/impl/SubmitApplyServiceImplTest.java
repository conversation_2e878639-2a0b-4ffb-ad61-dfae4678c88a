package com.youxin.risk.ra.service.impl;

import com.youxin.risk.ra.model.SubmitApply;
import com.youxin.risk.ra.service.SubmitApplyService;
import junit.framework.TestCase;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class SubmitApplyServiceImplTest extends TestCase {

    @Autowired
    SubmitApplyService service;

    @Test
    public void testGetLastApply() {
        List<SubmitApply> lastApply = service.getLastApply("PAY_DAY_LOAN", "uk123abc");
        assertEquals(10, lastApply.size());
        Map<Integer, String> id2type = lastApply.stream().collect(Collectors.toMap(SubmitApply::getId, SubmitApply::getApplyType));
        assertEquals(id2type.get(********), "ADDRESS");
        assertEquals(id2type.get(********), "BANK_CARD");
        assertEquals(id2type.get(********), "CONTACT");
        assertEquals(id2type.get(********), "CREDITCARD");
        assertEquals(id2type.get(********), "ID_CARD");
        assertEquals(id2type.get(********), "JOB");
        assertEquals(id2type.get(********), "LOCATE");
        assertEquals(id2type.get(********), "PLIST");
        assertEquals(id2type.get(********), "REGISTER");
    }
}