mode.name=dev
app.name=risk-metrics-dpc

tomcat.home=/opt/app/tomcat

app.home=/opt/app/tomcat/webapps/ROOT
app.log.home=${catalina.base}/logs

console.log.level=DEBUG

# redis,kafka，必输项
metrics.remote.queue=redis
#必输项
metrics.remote.redis.is.cluster=1
#127.0.0.1:8379，必输项
metrics.remote.queue.server=172.16.2.40:7000,172.16.2.40:7001,172.16.2.40:7002,\
  172.16.2.40:7100,172.16.2.40:7101,172.16.2.40:7102
metrics.remote.queue.redis.password=passwd123

#命令空间，多个以,分隔，不在配置列表中的不处理
metrics.namespaces=renrendai_antifraud_risk,renrendai_antifraud_shield,renrendai_antifraud_ra_verify,antifraud_risk_dc,\
  antifraud_risk_gw,antifraud_risk_engine,antifraud_risk_channel,antifraud_risk_rm
#远端队列key，监听多个以,分隔
metrics.remote.queue.redis.keys=metrics-point-queue,metrics-point-queue-shield,metrics-point-queue-ra-verify,risk_rm_cal_feature_costTime,risk_rm_cal_strategy_costTime
#每个远端queue消息处理任务数（含读取和写入influxdb）
metrics.remote.queue.task.size=1


metrics.influxdb2.server=http://***********:8066
metrics.influxdb2.server.username=influx
metrics.influxdb2.server.password=influx


metrics.point.kafka.hosts=hadoop-1:9092,hadoop-2:9092,hadoop-3:9092
metrics.point.kafka.topic=metrics.point.kafka.topic_local
metrics.point.kafka.group.id=metrics.point.kafka.group_local
metrics.point.kafka.topic.list=metrics.point.kafka.topic_local,metrics.point.kafka.topic.gateway_local
metrics.point.mirror.kafka.hosts=***********:9092,***********:9092,***********:9092

youxin.env=DEV