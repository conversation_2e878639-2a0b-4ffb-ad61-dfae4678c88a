mode.name=test
app.name=risk-metrics-dpc

tomcat.home=/opt/app/tomcat

app.home=/opt/app/tomcat/webapps/ROOT
app.log.home=${catalina.base}/logs

console.log.level=DEBUG

# redis,kafka��������
metrics.remote.queue=redis
#������
metrics.remote.redis.is.cluster=1
#127.0.0.1:8379��������
metrics.remote.queue.server=r-2ze3b4116f021d94.redis.rds.aliyuncs.com:6379
metrics.remote.queue.redis.password=d3799e0d-4e40-42

#����ռ䣬�����,�ָ������������б��еĲ�����
metrics.namespaces=renrendai_antifraud_risk,renrendai_antifraud_shield,renrendai_antifraud_ra_verify,antifraud_risk_dc,\
  antifraud_risk_gw,antifraud_risk_engine,antifraud_risk_channel,antifraud_risk_rm
#Զ�˶���key�����������,�ָ�
metrics.remote.queue.redis.keys=metrics-point-queue,metrics-point-queue-shield,metrics-point-queue-ra-verify
#ÿ��Զ��queue��Ϣ����������������ȡ��д��influxdb��
metrics.remote.queue.task.size=1


metrics.influxdb2.server=http://***********:8066
metrics.influxdb2.server.username=influx
metrics.influxdb2.server.password=influx


metrics.point.kafka.hosts=haofenqi-kafka-cname.test.weicai.com.cn:29092
metrics.point.kafka.topic=metrics.point.kafka.topic_test
metrics.point.kafka.group.id=metrics.point.kafka.group_test
metrics.point.kafka.topic.list=metrics.point.kafka.topic_test,metrics.point.kafka.topic.gateway_test
metrics.point.mirror.kafka.hosts=haofenqi-kafka-cname.test.weicai.com.cn:29092

youxin.env=DEV