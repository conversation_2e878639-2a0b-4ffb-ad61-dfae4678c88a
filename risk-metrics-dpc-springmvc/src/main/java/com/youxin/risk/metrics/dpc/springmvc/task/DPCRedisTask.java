package com.youxin.risk.metrics.dpc.springmvc.task;

import com.youxin.risk.metrics.config.ConfigUtils;
import com.youxin.risk.metrics.constants.PropertiesKey;
import com.youxin.risk.metrics.dpc.springmvc.model.MsgQueueEnum;
import com.youxin.risk.metrics.dpc.springmvc.queue.QueueListener;
import com.youxin.risk.metrics.dpc.springmvc.queue.impl.RedisQueueListener;
import com.youxin.risk.metrics.dpc.springmvc.util.DPCUtils;
import com.youxin.risk.metrics.helpers.ThreadUtils;
import com.youxin.risk.metrics.helpers.Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.*;

@Component
public class DPCRedisTask implements InitializingBean {
    private static Logger logger = LoggerFactory.getLogger(DPCRedisTask.class);

    private QueueListener listener;

    private Executor tasks;

    private boolean isOK;

    @Autowired
    private BaseTask baseTask;

    @Override
    public void afterPropertiesSet() throws Exception {
        init();
    }

    public void init() {
        try {
            isOK = true;
            initListener();
            initTask();
        } catch (Exception e) {
            logger.error(DPCRedisTask.class.getName() + " init exception", e);
            isOK = false;
        }
        logger.info("metrics-dpc init end, isOK=" + isOK);
    }

    private void initListener() throws Exception {
        listener = new RedisQueueListener();
    }

    private void initTask() {

        int taskSize = ConfigUtils.getInteger(PropertiesKey.DPC_REMOTE_QUEUE_TASK_SIZE);
        if (taskSize <= 0) {
            taskSize = Runtime.getRuntime().availableProcessors() + 1;
        }

        String queueKeys = ConfigUtils.getString(PropertiesKey.DPC_REMOTE_QUEUE_REDIS_KEYS);
        if (null == queueKeys || "".equals(queueKeys)) {
            throw new IllegalArgumentException("'" + PropertiesKey.DPC_REMOTE_QUEUE_REDIS_KEYS + "'");
        }

        String[] queueArr = queueKeys.split(",");
        int thSize = queueArr.length * taskSize;

        tasks = new ThreadPoolExecutor(thSize, thSize,
                1L, TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(thSize),
                ThreadUtils.defaultThreadFactory(), ThreadUtils.defaultHandler());

        for (String queueKey : queueArr) {
            if (null == queueKey || "".equals(queueKey)) {
                continue;
            }
            for (int i = 0; i < taskSize; i++) {
                tasks.execute(new ReadAndWrite(queueKey));
            }
        }
    }

    private class ReadAndWrite implements Runnable {

        private String queueKey;

        private long step = 1000*3L;

        private long times;

        private ReadAndWrite(String queueKey) {
            this.queueKey = queueKey;
        }

        @Override
        public void run() {
            boolean isRun;
            for (; ; ) {
                if (!isOK) {
                    break;
                }
                isRun = DPCUtils.isIsDPCRunning();
                if (!isRun) {
                    continue;
                }
                String msg = null;
                try {
                    msg = listener.pop(queueKey);
                    write(msg);
                } catch (Exception e) {
                    logger.error("ReadAndWriteException " + e.getMessage());
                } finally {
                    if (!isRun) {
                        Utils.threadSleep(3000);
                    } else if (null == msg) {
                        Utils.threadSleep(50);
                        long temp = System.currentTimeMillis()/step;
                        if(temp != times){
                            times = temp;
                            logger.info("redis-queue is empty ; queueKey =" + queueKey);
                        }
                    }
                }
            }
        }
    }

    private void write(String msg) {
        if (null == msg || "".equals(msg)) {
            return;
        }
        baseTask.singleMsgHandle(msg, new ArrayList<>(), MsgQueueEnum.REDIS);
    }

}
