package com.youxin.risk.metrics.dpc.springmvc.controller;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.metrics.dpc.springmvc.service.InfluxDBAPI;
import com.youxin.risk.metrics.dpc.springmvc.service.InfluxDBHelper;
import com.youxin.risk.metrics.dpc.springmvc.task.BaseTask;
import com.youxin.risk.metrics.dpc.springmvc.task.DPCKafkaTask;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @create 2023/3/6 12:05
 * @desc
 */
@RestController
@RequestMapping("/metrics")
public class MetricsController {


    /**
     * 列出所有的数据库
     * @return
     */
    @RequestMapping(value = "/showDatabases", method = RequestMethod.GET)
    public String showDatabases() {
        return JSON.toJSONString(InfluxDBHelper.getInfluxDBByDbName("_internal").showDatabases());
    }


    /**
     * 展示指定数据库的保留策略
     *
     */
    @RequestMapping(value = "/showRetentionPolicyByDatabase", method = RequestMethod.GET)
    public String showRetentionPolicyByDatabase(String database) {
        return InfluxDBHelper.getInfluxDBByDbName(database).showRetentionPolicyByDatabase(database);
    }

    /**
     * 更新指定数据库的保留策略
     *
     */
    @RequestMapping(value = "/updateRetentionPolicy", method = RequestMethod.GET)
    public String updateRetentionPolicy(String database, String policyName, String duration, boolean isDefault) {
        InfluxDBHelper.getInfluxDBByDbName(database).updateRetentionPolicy(database, policyName, duration, isDefault);
        return "OK";
    }

    /**
     * 更新指定数据库的保留策略
     */
    @RequestMapping(value = "/createRetentionPolicy", method = RequestMethod.GET)
    public String createRetentionPolicy(String database, String policyName, String duration, boolean isDefault) {
        InfluxDBHelper.getInfluxDBByDbName(database).createRetentionPolicy(database, policyName, duration, isDefault);
        return "OK";
    }


    /**
     * 创建数据库
     * @param name
     * @return
     */
    @RequestMapping(value = "/createDatabase", method = RequestMethod.GET)
    public String createDatabase(String name) {
        return InfluxDBHelper.getInfluxDBByDbName(name).createDatabase(name);
    }

    /**
     * 删除数据库
     * @param name
     * @return
     */
    @RequestMapping(value = "/deleteDatabase", method = RequestMethod.GET)
    public String deleteDatabase(String name) {
        InfluxDBHelper.getInfluxDBByDbName(name).deleteDatabase(name);
        return "OK";
    }


    @RequestMapping(value = "/testQuery", method = RequestMethod.GET)
    public String testQuery() {
        InfluxDBAPI influxDB = InfluxDBHelper.getInfluxDBByDbName("antifraud_risk_data_monitor");

        String command = "SELECT count(*)  FROM third_data_item_type ";
        QueryResult queryResult = influxDB.query(new Query(command));

        return queryResult.toString();
    }


    @RequestMapping(value = "/caffeineCache/stats",method = RequestMethod.GET)
    public String caffeineCacheStats() {
        return BaseTask.caffeineCacheStats();
    }

}
