package com.youxin.risk.commons.delayqueue;

import com.youxin.risk.commons.utils.JacksonUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.concurrent.TimeUnit;

public class DelayTaskScheduler {

	private final static Logger logger = LoggerFactory.getLogger(DelayTaskScheduler.class);
	@Autowired
	private RedisDelayQueue delayedQueue;

	public void schedule(Map<String,Object> param, Long delay,Class<? extends DelayMessageHandler> cls){
		if (cls == null) {
			throw new IllegalArgumentException("handle is null");
		}
		send(new RouteLetterMessage(param,cls,delay));
	}

	private void send(RouteLetterMessage lm){
		//LoggerProxy.info("send",logger,"delay task:{}", JacksonUtil.toJson(lm.getId()));
		try {
			delayedQueue.offer(lm,lm.getDelay(), TimeUnit.SECONDS);
		}catch (Exception ex){
			LoggerProxy.info("send",logger,"delay task:{} ,error", JacksonUtil.toJson(lm),ex);
		}
	}

}
