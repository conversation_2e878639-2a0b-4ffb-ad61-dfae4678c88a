package com.youxin.risk.commons.web.controller;

import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.exception.RiskRuntimeException;
import com.youxin.risk.commons.remote.model.RpcRequest;
import com.youxin.risk.commons.remote.model.RpcResponse;
import com.youxin.risk.commons.remote.model.channel.ChannelResponse;
import com.youxin.risk.commons.utils.JacksonUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class BaseController {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    protected RpcRequest generatorRequest(String message, Class<?> type) {
        try {
            if(StringUtils.isBlank(message)) {
                throw new RiskRuntimeException(RetCodeEnum.ILLEGAL_ARGUMENT, "message is blank");
            }
            RpcRequest request = JacksonUtil.toObject(message, RpcRequest.class, type);
            if(null == request) {
                throw new RiskRuntimeException(RetCodeEnum.ILLEGAL_ARGUMENT, "request is blank");
            }
            if(StringUtils.isBlank(request.getAppName())) {
                throw new RiskRuntimeException(RetCodeEnum.ILLEGAL_ARGUMENT, "appName is blank");
            }
            if(StringUtils.isBlank(request.getRequestId())) {
                throw new RiskRuntimeException(RetCodeEnum.ILLEGAL_ARGUMENT, "requestId is blank");
            }
            if(request.getData() == null) {
                throw new RiskRuntimeException(RetCodeEnum.ILLEGAL_ARGUMENT, "data is null");
            }
            return request;
        } catch(RiskRuntimeException e) {
            throw e;
        } catch(Exception e) {
            throw new RiskRuntimeException(RetCodeEnum.ILLEGAL_ARGUMENT, "parse message to request failed");
        }
    }

    protected RpcResponse generatorErrorResponse2(Throwable e) {
        RpcResponse response = new RpcResponse();
        if(e instanceof RiskRuntimeException) {
            LoggerProxy.error("RiskRuntimeException", logger, "", e);
            RiskRuntimeException riskRuntimeException = (RiskRuntimeException) e;
            response.setRetCode(riskRuntimeException.getExceptionCode().getValue());
            response.setRetMsg(riskRuntimeException.getMessage());
        } else {
            LoggerProxy.error("unKnownError", logger, "", e);
            response.setRetCode(RetCodeEnum.FAILED.getValue());
            response.setRetMsg(RetCodeEnum.FAILED.getRetMsg());
        }
        return response;
    }

    @Deprecated
    protected RpcResponse<ChannelResponse> generatorErrorResponse(Throwable e) {
        RpcResponse<ChannelResponse> response = new RpcResponse<>();
        if(e instanceof RiskRuntimeException) {
            LoggerProxy.error("RiskRuntimeException", logger, "", e);
            RiskRuntimeException riskRuntimeException = (RiskRuntimeException) e;
            response.setRetCode(riskRuntimeException.getExceptionCode().getValue());
            response.setRetMsg(riskRuntimeException.getMessage());
        } else {
            LoggerProxy.error("unKnownError", logger, "", e);
            response.setRetCode(RetCodeEnum.FAILED.getValue());
            response.setRetMsg(RetCodeEnum.FAILED.getRetMsg());
        }
        return response;
    }
}