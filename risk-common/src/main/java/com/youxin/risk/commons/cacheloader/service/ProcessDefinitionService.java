package com.youxin.risk.commons.cacheloader.service;

import com.youxin.risk.commons.model.ProcessDefinition;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/11/13 10:55
 */
public interface ProcessDefinitionService {
    List<ProcessDefinition> selectAll();

    List<ProcessDefinition> selectByUpdateTime(Date updateTime);

    ProcessDefinition selectByProcessDefId(String processDefId);
}
