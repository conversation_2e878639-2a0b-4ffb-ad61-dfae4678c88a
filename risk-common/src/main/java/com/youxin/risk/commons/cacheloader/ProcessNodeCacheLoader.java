package com.youxin.risk.commons.cacheloader;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.cache.CacheManager;
import com.youxin.risk.commons.cache.CacheType;
import com.youxin.risk.commons.cacheloader.service.ProcessNodeService;
import com.youxin.risk.commons.constants.ConfigTableEnum;
import com.youxin.risk.commons.constants.NodeTypeEnum;
import com.youxin.risk.commons.model.MatrixDataSource;
import com.youxin.risk.commons.model.NodeData;
import com.youxin.risk.commons.model.NodeDataConfig;
import com.youxin.risk.commons.model.NodeFeature;
import com.youxin.risk.commons.model.NodeStrategy;
import com.youxin.risk.commons.model.ProcessNode;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/11/14 14:56
 */
public class ProcessNodeCacheLoader extends BaseCacheLoader {
    @Resource
    private ProcessNodeService processNodeService;

    @Override
    @Scheduled(fixedDelay = 10000)
    public void load() {
        super.load(ConfigTableEnum.admin_process_node.toString());
    }

    @Override
    protected int loadPart() {
        List<ProcessNode> processNodes = processNodeService.selectByUpdateTime(getCacheTime());
        if (!CollectionUtils.isEmpty(processNodes)) {
            for (ProcessNode node : processNodes) {
                NodeTypeEnum type = NodeTypeEnum.value(node.getNodeType());
                switch (type) {
                    case DATA:
                        node.setDatas(processNodeService.selectNodeDatas(node.getNodeCode()));
                        node.setMatrixDataSources(processNodeService.selectNodeDataGroups(node.getNodeCode()));
                        CacheManager.putToCache(CacheType.process_node, node.getNodeCode(), node);
                        break;
                    case FEATURE:
                        node.setFeatures(processNodeService.selectNodeFeatures(node.getNodeCode()));
                        CacheManager.putToCache(CacheType.process_node, node.getNodeCode(), node);
                        break;
                    case STRATEGY:
                        node.setStrategy(processNodeService.selectNodeStrategy(node.getNodeCode()));
                        CacheManager.putToCache(CacheType.process_node, node.getNodeCode(), node);
                        break;
                    case CUSTOM:
                        CacheManager.putToCache(CacheType.process_node, node.getNodeCode(), node);
                        break;
                    case SERIAL:
                        node.setDatas(JSONArray.parseArray(JSON.toJSONString(processNodeService.selectNodeDataConfigs(node.getNodeCode())),
                                NodeData.class));
                        CacheManager.putToCache(CacheType.process_node, node.getNodeCode(), node);
                        break;
                    default:
                        LoggerProxy.error("nodeTypeError", logger, "node={}", JSONObject.toJSONString(node));
                        break;
                }
            }
            return processNodes.size();
        }
        return 0;
    }

    @Override
    protected void loadAll() {
        List<ProcessNode> processNodes = processNodeService.selectAll();
        if (CollectionUtils.isEmpty(processNodes)) {
            return;
        }
        Map<String, List<NodeData>> nodeDataMap = processNodeService.selectAllNodeData();
        Map<String, List<MatrixDataSource>> nodeDataGroupMap = processNodeService.selectAllNodeDataGroup();
        Map<String, List<NodeFeature>> nodeFeatureMap = processNodeService.selectAllNodeFeature();
        Map<String, NodeStrategy> nodeStrategyMap = processNodeService.selectAllNodeStrategy();
        Map<String, List<NodeDataConfig>> nodeDataConfigMap = processNodeService.selectAllNodeDataConfig();
        Map<String, Object> processNodeMap = Maps.newHashMap();
        for (ProcessNode node : processNodes) {
            NodeTypeEnum type = NodeTypeEnum.value(node.getNodeType());
            switch (type) {
                case DATA:
                    node.setDatas(nodeDataMap.get(node.getNodeCode()));
                    node.setMatrixDataSources(nodeDataGroupMap.get(node.getNodeCode()));
                    processNodeMap.put(node.getNodeCode(), node);
                    break;
                case FEATURE:
                    node.setFeatures(nodeFeatureMap.get(node.getNodeCode()));
                    processNodeMap.put(node.getNodeCode(), node);
                    break;
                case STRATEGY:
                    node.setStrategy(nodeStrategyMap.get(node.getNodeCode()));
                    processNodeMap.put(node.getNodeCode(), node);
                    break;
                case CUSTOM:
                    processNodeMap.put(node.getNodeCode(), node);
                    break;
                case SERIAL:
                    node.setDatas(JSONArray.parseArray(JSON.toJSONString(nodeDataConfigMap.get(node.getNodeCode())),
                            NodeData.class));
                    processNodeMap.put(node.getNodeCode(), node);
                    break;
                default:
                    LoggerProxy.error("nodeTypeError", logger, "node={}", JSONObject.toJSONString(node));
                    break;
            }
        }
        CacheManager.setCache(CacheType.process_node, processNodeMap);
    }
}
