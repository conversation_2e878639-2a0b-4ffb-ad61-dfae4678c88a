package com.youxin.risk.commons.mybatis;

import com.youxin.risk.commons.utils.MaskUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import static com.youxin.risk.commons.utils.MaskUtils.MASK_CONTACT_INFO_INSERT;
import static com.youxin.risk.commons.utils.MaskUtils.UNMASK_CONTACT_INFO_SELECT;

@MappedJdbcTypes(JdbcType.VARCHAR)
public class ContactMaskHandler extends BaseTypeHandler<String> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String value, JdbcType jdbcType) throws SQLException {
        ps.setString(i, MaskUtils.maskValue(value, MASK_CONTACT_INFO_INSERT));
    }

    @Override
    public String getNullableResult(ResultSet rs, String s) throws SQLException {
        String result = rs.getString(s);
        return MaskUtils.unMaskValue(result, UNMASK_CONTACT_INFO_SELECT);
    }

    @Override
    public String getNullableResult(ResultSet rs, int i) throws SQLException {
        String result = rs.getString(i);
        return MaskUtils.unMaskValue(result, UNMASK_CONTACT_INFO_SELECT);
    }

    @Override
    public String getNullableResult(CallableStatement cs, int i) throws SQLException {
        String result = cs.getString(i);
        return MaskUtils.unMaskValue(result, UNMASK_CONTACT_INFO_SELECT);
    }
}
