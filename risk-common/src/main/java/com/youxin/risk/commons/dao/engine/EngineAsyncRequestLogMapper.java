package com.youxin.risk.commons.dao.engine;

import com.youxin.risk.commons.model.EngineAsyncRequestLog;
import com.youxin.risk.commons.model.EngineAsyncRequestLogExtend;
import com.youxin.risk.commons.model.engine.CallDiSuccessRate;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/10/11 10:35
 */
public interface EngineAsyncRequestLogMapper {

    int insert(EngineAsyncRequestLog engineAsyncRequestLog);

    void multiInsert(List<EngineAsyncRequestLog> list);

    int updateStatus(EngineAsyncRequestLog engineAsyncRequestLog);

    int updateStatusWithOld(@Param("record") EngineAsyncRequestLog engineAsyncRequestLog, @Param("oldStatus") String oldStatus);

    EngineAsyncRequestLog getByAsyncRequestId(String asyncRequestId);

    List<EngineAsyncRequestLog> getList(@Param("sessionId") String sessionId, @Param("nodeId") String nodeId);

    List<EngineAsyncRequestLog> getListBySessionId(@Param("sessionId")String sessionId);

    /**
     * 查询待重试的数据源请求记录
     * @param includeEventCodes 包含的事件编码集合
     * @param excludeEventCodes 排除的事件编码集合
     * @param currentTime 当前时间
     * @param limit
     * @return
     */
    List<EngineAsyncRequestLogExtend> selectRetryLogByEventCodes(@Param("includeEventCodes") Collection<String> includeEventCodes,
                                                                 @Param("excludeEventCodes") Collection<String> excludeEventCodes,
                                                                 @Param("currentTime") String currentTime,
                                                                 @Param("limit") int limit);


    List<EngineAsyncRequestLogExtend> selectNotRequiredAndExpiredRequestLog();

    List<EngineAsyncRequestLogExtend> selectFetchedOldAndExpiredRequestLog();

    int failNotRequired(@Param("sessionId")String sessionId, @Param("nodeId")String nodeId);

    List<EngineAsyncRequestLog> selectIdAndCreateTimeAfterId(@Param("startId") Long startId,@Param("limit") Integer limit);


    int deleteByIds(@Param("idList") List<Long> idList);

    int deleteOrderByCreateTime(@Param("days") int retainDays, @Param("limit") int limit);


    int deleteBySessionId(@Param("sessionId") String sessionId);

    int deleteById(Long id);

    List<EngineAsyncRequestLog> getListBySessionIdList(@Param("sessionIdList")List<String> sessionIdList);
}
