package com.youxin.risk.commons.kafkav2.handler.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.cache.CacheApi;
import com.youxin.risk.commons.kafkav2.KafkaContext;
import com.youxin.risk.commons.kafkav2.handler.KafkaMessageHandler;
import com.youxin.risk.commons.tools.redis.RetryableJedis;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public abstract class BaseKafKaMessageHandler implements KafkaMessageHandler {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    private static final int KEY_TIME_OUT_SECONDS = 6 * 60 * 60;

    private static final int DEFAULE_MAX_RETRY_TIMES = 10;

    private RetryableJedis retryableJedis;

    private int retryTimes = DEFAULE_MAX_RETRY_TIMES;

    private String retryKeyPrefix = "";

    @Override
    public void handler(KafkaContext context) {
        try {
            before(context);
            if (context.isTerminated()) {
                return;
            }

            if (checkTryTimes(context)) {
                try {
                    handlerTerminatedMessage(context);
                } catch (Exception e) {
                    logger.error("context={}", context, e);
                }
                context.setTerminated(true);
                return;
            }

            handler0(context);
        } catch (Exception e) {
            LoggerProxy.error("receivedMessageHandlerException", logger, "", e);
        } finally {
            updateTryTimes(context);
//            LoggerProxy.info("receivedMessageHandlerEnd", logger, "cost={}", context.getIntervalStartTime());
        }
    }

    private void printJobId(KafkaContext context) {
        try {
            JSONObject messageJson = JSONObject.parseObject(context.getMessage());
            String jobId = messageJson.getString("jobID");
            String recordType = messageJson.getString("recordType");
            if (StringUtils.isNotBlank(jobId)) {
                LoggerProxy.info("receivedKafkaMessage", logger, "jobId={},recordType={}", jobId, recordType);
            }
        } catch (Exception e) {
            // nothing
        }
    }

    protected void handlerTerminatedMessage(KafkaContext context) {

    }

    protected void before(KafkaContext context) {
    }

    private String cacheKey(KafkaContext context) {
        StringBuilder key = new StringBuilder("kafka_consumer_msg_retry_times_key_");
        if (StringUtils.isBlank(retryKeyPrefix)) {
            retryKeyPrefix = getClass().getName().replaceAll("\\.", "_");
        }
        key.append(retryKeyPrefix).append("_");
        return key.append(context.getMD5()).toString();
    }

    /**
     * 校验失败（不继续处理业务流程，返回true）
     *
     * @param context
     * @return
     */
    private boolean checkTryTimes(KafkaContext context) {
        if (!isCheckTryTimes(context)) {
            return false;
        }
        try {
            String v = retryableJedis.get(cacheKey(context));
            if (null == v) {
                // 不存在肯定不是重复的消息
                context.setRetryTimes(1);
                return false;
            }
            context.setRetryTimes(Integer.parseInt(v));
            LoggerProxy.warn("repeatKafkaMessage", logger, "logId={}, times={}, maxTimes={}",context.getLogId(), v, retryTimes);
            // 存在且重试次数大于retryTimes时，认为重复
            if (maxTimes() <= Long.parseLong(v)) {
                LoggerProxy.error("largerThanRetryTimes", logger, "");
                LoggerProxy.warn("repeatKafkaMessageDetail", logger, "logId ={}, context={}", context.getLogId(), JSON.toJSONString(context));
                return true;
            }
            return false;
        } catch (Exception e) {
            LoggerProxy.warn("checkRepeatException", logger, "", e);
        }
        return false;
    }

    private void updateTryTimes(KafkaContext context) {
        if (!isCheckTryTimes(context)) {
            return;
        }
        try {
            retryableJedis.incrBy(cacheKey(context), 1);
        } catch (Exception e) {
            LoggerProxy.warn("updateTryTimesException", logger, "", e);
        }

        try {
            retryableJedis.expire(cacheKey(context), KEY_TIME_OUT_SECONDS);
        } catch (Exception e) {
            LoggerProxy.warn("expireTryTimesException", logger, "", e);
        }

    }

    private boolean isCheckTryTimes(KafkaContext context) {
        return null != context && StringUtils.isNotBlank(context.getMD5()) && null != retryableJedis;
    }

    private long maxTimes() {
        try {
            String value = CacheApi.getDictSysConfig("kafkaMsgRetryMaxTimes", String.valueOf(retryTimes));
            return Long.parseLong(value);
        } catch (Exception e) {
            return retryTimes;
        }
    }


    protected abstract void handler0(KafkaContext context);

    public RetryableJedis getRetryableJedis() {
        return retryableJedis;
    }

    public void setRetryableJedis(RetryableJedis retryableJedis) {
        this.retryableJedis = retryableJedis;
    }

    public int getRetryTimes() {
        return retryTimes;
    }

    public void setRetryTimes(int retryTimes) {
        this.retryTimes = retryTimes;
    }

}