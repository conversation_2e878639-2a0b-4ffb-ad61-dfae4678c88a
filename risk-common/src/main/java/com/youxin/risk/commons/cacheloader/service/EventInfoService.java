package com.youxin.risk.commons.cacheloader.service;

import com.youxin.risk.commons.model.EventInfo;
import com.youxin.risk.commons.model.EventInput;
import com.youxin.risk.commons.model.EventOutput;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/11/12 16:46
 */
public interface EventInfoService {
    List<EventInfo> selectAll();

    List<EventInfo> selectByUpdateTime(Date updateTime);

    Map<String, List<EventInput>> selectAllInput();

    Map<String, List<EventOutput>> selectAllOutput();

    List<EventInput> selectInput(String eventCode);

    List<EventOutput> selectOutput(String eventCode);
}
