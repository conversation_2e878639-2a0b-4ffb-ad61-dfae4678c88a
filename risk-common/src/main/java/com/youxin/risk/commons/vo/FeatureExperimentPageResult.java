package com.youxin.risk.commons.vo;

import lombok.Getter;

import java.util.Collections;
import java.util.List;

/**
 * 特征镜像分页结果
 *
 * <AUTHOR>
 */
@Getter
public class FeatureExperimentPageResult<C extends FeatureExperimentResultVo> {
    @SuppressWarnings("unchecked")
    public static final FeatureExperimentPageResult EMPTY = new FeatureExperimentPageResult(0, Collections.emptyList());

    private final long total;
    private final List<C> result;

    public FeatureExperimentPageResult(long total, List<C> result) {
        this.total = total;
        this.result = result;
    }

    public static FeatureExperimentPageResult empty() {
        return EMPTY;
    }
}
