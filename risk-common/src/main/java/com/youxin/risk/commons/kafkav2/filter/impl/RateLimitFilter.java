package com.youxin.risk.commons.kafkav2.filter.impl;

import com.youxin.risk.commons.cache.CacheApi;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.exception.RiskRuntimeException;
import com.youxin.risk.commons.kafkav2.KafkaContext;
import com.youxin.risk.commons.kafkav2.filter.KafkaMessageFilter;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.SystemUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * kafka消息限流
 *
 * <AUTHOR>
 */
public class RateLimitFilter implements KafkaMessageFilter, InitializingBean {

    private Logger logger = LoggerFactory.getLogger(RateLimitFilter.class);

    private AtomicInteger counter;

    // 限流的字段key
    private String limitConfDictCode;

    private long lastTime = System.currentTimeMillis();

    // 一秒
    private static final int ONE_SENCEND = 1000;

    @Override
    public void afterPropertiesSet() throws Exception {
        if (StringUtils.isNotBlank(limitConfDictCode)) {
            counter = new AtomicInteger();
        } else {
            LoggerProxy.info("noneCounter", logger, "");
        }
    }

    @Override
    public void doFilter(KafkaContext context) {
        if (null == counter) {
            return;
        }
        int threshold = getThresholdSec();
        if (0 >= threshold) {
            return;
        }
        if (count() > threshold) {
            LoggerProxy.warn("rateLimiterCheckFalse", logger, "");
            SystemUtil.threadSleep(ONE_SENCEND);
//             throw new RiskRuntimeException(RetCodeEnum.RATE_LIMIT);
        }
    }

    private int count() {
        long now = System.currentTimeMillis();
        int count;
        if (ONE_SENCEND <= now - lastTime) {
            count = counter.getAndSet(0);
        } else {
            count = counter.incrementAndGet();
            if (Integer.MAX_VALUE - 10000 <= count) {
                synchronized (this) {
                    // 极端可能统计不准，忽略
                    counter.set(0);
                }
            }
        }
        lastTime = now;
        return count;
    }

    private int getThresholdSec() {
        try {
            String dict = CacheApi.getDictSysConfig(limitConfDictCode);
            if (StringUtils.isNotBlank(dict)) {
                return Integer.parseInt(dict);
            }
        } catch (Exception e) {
            LoggerProxy.error("getDictAndParseToIntError", logger, "dictCode=" + limitConfDictCode);
        }
        return 0;
    }

    public String getLimitConfDictCode() {
        return limitConfDictCode;
    }

    public void setLimitConfDictCode(String limitConfDictCode) {
        this.limitConfDictCode = limitConfDictCode;
    }
}