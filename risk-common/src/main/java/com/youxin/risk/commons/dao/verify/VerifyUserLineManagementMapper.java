package com.youxin.risk.commons.dao.verify;

import com.youxin.risk.commons.model.verify.VerifyUserLineManagement;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface VerifyUserLineManagementMapper {


    VerifyUserLineManagement getByUserKey(@Param("sourceSystem") String sourceSystem, @Param("userKey") String userKey);

    VerifyUserLineManagement getLastPreloanByUserKey(@Param("sourceSystem") String sourceSystem, @Param("userKey") String userKey);

    VerifyUserLineManagement getAmountAssignByUserKey(@Param("sourceSystem") String sourceSystem, @Param("userKey") String userKey);

    List<VerifyUserLineManagement> getLatestAmountByType(@Param("sourceSystem") String sourceSystem, @Param("userKey") String userKey, @Param("strategyTypes") List strategyTypes);


    List<VerifyUserLineManagement> getListByUserKey(@Param("sourceSystem") String sourceSystem, @Param("userKey") String userKey);


    /**
     * 将verify_user_line_management 中的is_active为1的设置为0
     *
     * @param userKey userkey
     * @param lineId  lineId
     */
    void updateNoInvalid(@Param("userKey") String userKey, @Param("lineId") Integer lineId);


    /**
     * 保存VerifyUserLineManagement对象
     *
     * @param verifyUserLineManagement VerifyUserLineManagement 对象
     */
    Long saveUserLineManagemert(VerifyUserLineManagement verifyUserLineManagement);




    Date getFirstCreateTimeByUserKey(@Param("userKey") String userKey);


    List<Map<String, Object>> getVerifyUserLineManagementMapper(String businessUserKey);

    List<VerifyUserLineManagement> selectAfterId(@Param("startId") Long startId,@Param("limit") Integer limit);

    Date getLastAssignDate(@Param("sourceSystem") String sourceSystem, @Param("userKey") String userKey);


    List<VerifyUserLineManagement> getCheckVerifyUserLineManagement(@Param("userKey") String userKey);
}
