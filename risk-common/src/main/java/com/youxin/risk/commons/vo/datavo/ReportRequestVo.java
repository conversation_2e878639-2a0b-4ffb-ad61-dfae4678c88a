package com.youxin.risk.commons.vo.datavo;

import com.youxin.risk.commons.constants.AnalysisSystemStatus;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class ReportRequestVo extends SubmitCommonVo {
    private Integer id;
    private Integer loanId;
    private String loanKey;
    private Integer loanDuration;
    private Double principalAmount;
    private Double limitAmount;
    private Double longitude;
    private Double latitude;
    private AnalysisSystemStatus status;
    private String reportKey;
    private String wifiSSID;
    private String wifiLevel;
    private String wifiMac;
    private Integer batteryLevel;
    private String batteryPlugType;
    private String deviceName;
    private Integer lowBatteryMode;
    private String shard;
    private Integer periodNo;
    private String step;
    private String userStatusInfo;//可选认证
    private String userAdditionalInfo;

    public Integer getLoanId() {
        return this.loanId;
    }

    public void setLoanId(Integer loanId) {
        this.loanId = loanId;
    }

    public Integer getLoanDuration() {
        return this.loanDuration;
    }

    public void setLoanDuration(Integer loanDuration) {
        this.loanDuration = loanDuration;
    }

    public Double getPrincipalAmount() {
        return this.principalAmount;
    }

    public void setPrincipalAmount(Double principalAmount) {
        this.principalAmount = principalAmount;
    }

    public Double getLimitAmount() {
        return this.limitAmount;
    }

    public void setLimitAmount(Double limitAmount) {
        this.limitAmount = limitAmount;
    }

    public Double getLongitude() {
        return this.longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getLatitude() {
        return this.latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public AnalysisSystemStatus getStatus() {
        return this.status;
    }

    public void setStatus(AnalysisSystemStatus status) {
        this.status = status;
    }

    public String getReportKey() {
        return this.reportKey;
    }

    public void setReportKey(String reportKey) {
        this.reportKey = reportKey;
    }

    public String getWifiSSID() {
        return this.wifiSSID;
    }

    public void setWifiSSID(String wifiSSID) {
        this.wifiSSID = wifiSSID;
    }

    public String getWifiLevel() {
        return this.wifiLevel;
    }

    public void setWifiLevel(String wifiLevel) {
        this.wifiLevel = wifiLevel;
    }

    public String getWifiMac() {
        return this.wifiMac;
    }

    public void setWifiMac(String wifiMac) {
        this.wifiMac = wifiMac;
    }

    public Integer getBatteryLevel() {
        return this.batteryLevel;
    }

    public void setBatteryLevel(Integer batteryLevel) {
        this.batteryLevel = batteryLevel;
    }

    public String getBatteryPlugType() {
        return this.batteryPlugType;
    }

    public void setBatteryPlugType(String batteryPlugType) {
        this.batteryPlugType = batteryPlugType;
    }

    public String getDeviceName() {
        return this.deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public Integer getLowBatteryMode() {
        return this.lowBatteryMode;
    }

    public void setLowBatteryMode(Integer lowBatteryMode) {
        this.lowBatteryMode = lowBatteryMode;
    }

    public String getShard() {
        return this.shard;
    }

    public void setShard(String shard) {
        this.shard = shard;
    }

    public Integer getPeriodNo() {
        return this.periodNo;
    }

    public void setPeriodNo(Integer periodNo) {
        this.periodNo = periodNo;
    }

    public String getStep() {
        return this.step;
    }

    public void setStep(String step) {
        this.step = step;
    }


    public String getUserStatusInfo() {
        return this.userStatusInfo;
    }

    public void setUserStatusInfo(String userStatusInfo) {
        this.userStatusInfo = userStatusInfo;
    }

    public Integer getId() {
        return this.id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLoanKey() {
        return this.loanKey;
    }

    public void setLoanKey(String loanKey) {
        this.loanKey = loanKey;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this,
                ToStringStyle.SHORT_PREFIX_STYLE);
    }

    public String getUserAdditionalInfo() {
        return userAdditionalInfo;
    }

    public void setUserAdditionalInfo(String userAdditionalInfo) {
        this.userAdditionalInfo = userAdditionalInfo;
    }
}
