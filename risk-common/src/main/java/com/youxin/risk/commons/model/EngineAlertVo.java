package com.youxin.risk.commons.model;

import com.youxin.risk.commons.constants.EngineEventStatusEnum;
import lombok.Data;

/**
 *<AUTHOR>
 *@create 2024/12/25 14:19
 *@desc 引擎报警vo
 */
@Data
public class EngineAlertVo {
    //卡单事件
    private String eventCode;
    /**
     * @see  EngineEventStatusEnum
     */
    private String status;
    //卡单数据源或变量
    private String dataCode;
    // 当前事件当前数据源卡单数量
    private Long cnt;
    // 当前事件卡单总数量
    private Long sessionIdCnt;
}
