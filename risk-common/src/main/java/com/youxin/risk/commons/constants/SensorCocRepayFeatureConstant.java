package com.youxin.risk.commons.constants;

/**
 * 神策BillEntry和CashOutClick事件特征常量
 * <AUTHOR>
 * @since 2022/3/22 19:24
 */
public class SensorCocRepayFeatureConstant {
    public static final String NULL = "NULL";

    public static final Integer LENGTH = 5;

    public static final String BILL_ENTRY = "BillEntry";

    public static final String CASH_OUT_CLICK = "CashOutClick";

    public static final String BILLENTRY_BILLENTRY = "BillEntry_BillEntry";

    public static final String BILLENTRY_CASHOUTCLICK = "BillEntry_CashOutClick";

    public static final String CASHOUTCLICK_BILLENTRY = "CashOutClick_BillEntry";

    public static final String CASHOUTCLICK_CASHOUTCLICK = "CashOutClick_CashOutClick";

    // 最近30天内BillEntry事件的平均间隔秒数
    public static final String AVG_BILLHV2HV_SEC_GAP = "avg_billhv2hv_sec_gap";

    // 最近30天内BillEntry和CashOutClick事件序列中连续的CashOutClick的平均间隔秒数
    public static final String AVG_COC2COC_SEC_GAP = "avg_coc2coc_sec_gap";

    // 最近30天内BillEntry和CashOutClick事件序列中连续的CashOutClick+BillEntry的平均间隔秒数
    public static final String AVG_COC2REPAY_SEC_GAP = "avg_coc2repay_sec_gap";

    // 最近30天内BillEntry和CashOutClick事件序列中连续的BillEntry+CashOutClick的平均间隔秒数
    public static final String AVG_REPAY2COC_SEC_GAP = "avg_repay2coc_sec_gap";

    // 最近30天内BillEntry和CashOutClick事件序列中连续的BillEntry的平均间隔秒数
    public static final String AVG_REPAY2REPAY_SEC_GAP = "avg_repay2repay_sec_gap";

    // 最近30天内BillEntry和CashOutClick事件序列中连续的CashOutClick的次数
    public static final String COC2COC_CNT = "coc2coc_cnt";

    // 最近30天内BillEntry和CashOutClick事件序列中连续的CashOutClick+BillEntry的次数
    public static final String COC2REPAY_CNT = "coc2repay_cnt";

    // 最近30天内BillEntry事件的最大间隔秒数
    public static final String MAX_BILLHV2HV_SEC_GAP = "max_billhv2hv_sec_gap";

    // 最近30天内BillEntry和CashOutClick事件序列中连续的CashOutClick的最大间隔秒数
    public static final String MAX_COC2COC_SEC_GAP = "max_coc2coc_sec_gap";

    // 最近30天内BillEntry和CashOutClick事件序列中连续的CashOutClick+BillEntry的最大间隔秒数
    public static final String MAX_COC2REPAY_SEC_GAP = "max_coc2repay_sec_gap";

    // 最近30天内BillEntry和CashOutClick事件序列中连续的BillEntry+CashOutClick的最大间隔秒数
    public static final String MAX_REPAY2COC_SEC_GAP = "max_repay2coc_sec_gap";

    // 最近30天内BillEntry和CashOutClick事件序列中连续的BillEntry的最大间隔秒数
    public static final String MAX_REPAY2REPAY_SEC_GAP = "max_repay2repay_sec_gap";

    // 最近30天内BillEntry事件的最小间隔秒数
    public static final String MIN_BILLHV2HV_SEC_GAP = "min_billhv2hv_sec_gap";

    // 最近30天内BillEntry和CashOutClick事件序列中连续的CashOutClick的最小间隔秒数
    public static final String MIN_COC2COC_SEC_GAP = "min_coc2coc_sec_gap";

    // 最近30天内BillEntry和CashOutClick事件序列中连续的CashOutClick+BillEntry的最小间隔秒数
    public static final String MIN_COC2REPAY_SEC_GAP = "min_coc2repay_sec_gap";

    // 最近30天内BillEntry和CashOutClick事件序列中连续的BillEntry+CashOutClick的最小间隔秒数
    public static final String MIN_REPAY2COC_SEC_GAP = "min_repay2coc_sec_gap";

    // 最近30天内BillEntry和CashOutClick事件序列中连续的BillEntry的最小间隔秒数
    public static final String MIN_REPAY2REPAY_SEC_GAP = "min_repay2repay_sec_gap"
            ;
    // 最近30天内BillEntry和CashOutClick事件序列中连续的BillEntry+CashOutClick的次数
    public static final String REPAY2COC_CNT = "repay2coc_cnt";

    // 最近30天内BillEntry和CashOutClick事件序列中连续的BillEntry的次数
    public static final String REPAY2REPAY_CNT = "repay2repay_cnt";

}
