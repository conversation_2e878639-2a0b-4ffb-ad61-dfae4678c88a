package com.youxin.risk.commons.model.datacenter;

import com.youxin.risk.commons.model.BaseModel;
import com.youxin.risk.commons.model.datacenter.common.VerifyCommonData;

import java.util.Date;

/**
 * verify基础数据推送地理位置表
 * 
 * <AUTHOR>
 * 
 * @date 2018-10-11
 */
public class DcSubmitLocate extends BaseModel {

    /**
     * operation_log表id
     */
    private Long operationLogId;

    /**
     * 用户key
     */
    private String userKey;

    /**
     * 经度
     */
    private Double latitude;

    /**
     * 纬度
     */
    private Double longitude;

    /**
     * 提交GPS的对应的业务环节
     */
    private String type;

    public Long getOperationLogId() {
        return operationLogId;
    }

    public void setOperationLogId(Long operationLogId) {
        this.operationLogId = operationLogId;
    }

    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey == null ? null : userKey.trim();
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}