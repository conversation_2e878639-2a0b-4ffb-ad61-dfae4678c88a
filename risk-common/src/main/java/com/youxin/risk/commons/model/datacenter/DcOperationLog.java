package com.youxin.risk.commons.model.datacenter;

import com.youxin.risk.commons.model.BaseModel;

import java.util.Date;

/**
 * verify基础数据推送公共字段表
 * 
 * <AUTHOR>
 * 
 * @date 2018-10-11
 */
public class DcOperationLog extends BaseModel {
//    private Integer id;

    /**
     * app版本
     */
    private String appVersion;

    /**
     * 渠道代码
     */
    private String channelCode;

    /**
     * 手机型号
     */
    private String mobileModel;

    /**
     * 平台
     */
    private String platform;

    /**
     * 系统版本
     */
    private String osVersion;

    /**
     * 源系统
     */
    private String sourceSystem;

    /**
     * 用户key
     */
    private String userKey;

    /**
     * ip地址
     */
    private String ip;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * channel
     */
    private String channel;

    /**
     * 是否越狱
     */
    private Integer jailBroken;

    /**
     * is_copy_package
     */
    private Integer isCopyPackage;

    /**
     * 设备
     */
    private String device;

    /**
     * 操作类型
     */
    private String operationType;






    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion == null ? null : appVersion.trim();
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode == null ? null : channelCode.trim();
    }

    public String getMobileModel() {
        return mobileModel;
    }

    public void setMobileModel(String mobileModel) {
        this.mobileModel = mobileModel == null ? null : mobileModel.trim();
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform == null ? null : platform.trim();
    }

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion == null ? null : osVersion.trim();
    }

    public String getSourceSystem() {
        return sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem == null ? null : sourceSystem.trim();
    }

    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey == null ? null : userKey.trim();
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip == null ? null : ip.trim();
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId == null ? null : deviceId.trim();
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel == null ? null : channel.trim();
    }

    public Integer getJailBroken() {
        return jailBroken;
    }

    public void setJailBroken(Integer jailBroken) {
        this.jailBroken = jailBroken;
    }

    public Integer getIsCopyPackage() {
        return isCopyPackage;
    }

    public void setIsCopyPackage(Integer isCopyPackage) {
        this.isCopyPackage = isCopyPackage;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device == null ? null : device.trim();
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType == null ? null : operationType.trim();
    }

}