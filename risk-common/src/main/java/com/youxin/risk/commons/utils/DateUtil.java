package com.youxin.risk.commons.utils;

import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.Objects;

/**
 * DateUtil
 *
 * <AUTHOR>
 */
public final class DateUtil extends DateUtils {

    private static Logger logger = LoggerFactory.getLogger(DateUtil.class);

    /**
     * yyyyMMdd
     */
    public static final String SHORT_FORMAT = "yyyyMMdd";

    /**
     * yyyyMMddHHmmss
     */
    public static final String LONG_FORMAT = "yyyyMMddHHmmss";

    /**
     * yyyyMMddHHmmss
     */
    public static final String LONG_FORMAT_MILL = "yyyyMMddHHmmssSSS";

    /**
     * yyyy-MM-dd
     */
    public static final String WEB_FORMAT = "yyyy-MM-dd";

    /**
     * HHmmss
     */
    public static final String TIME_FORMAT = "HHmmss";

    /**
     * yyyyMM
     */
    public static final String MONTH_FORMAT = "yyyyMM";

    /**
     * yyyy年MM月dd日
     */
    public static final String CHINA_FORMAT = "yyyy年MM月dd日";

    /**
     * yyyy-MM-dd HH:mm:ss
     */
    public static final String LONG_WEB_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * yyyy-MM-dd HH:mm
     */
    public static final String LONG_WEB_FORMAT_NO_SEC = "yyyy-MM-dd HH:mm";

    public static final String FINAL_TIME = "2099-12-31 23:59:59";

    public static final String TIME_MILLIS="SSS";

    private static String[] patterns = {LONG_FORMAT, LONG_WEB_FORMAT, WEB_FORMAT, SHORT_FORMAT};

    public static ThreadLocal<SimpleDateFormat> sdf = ThreadLocal.withInitial(()->new SimpleDateFormat("yyyy-MM-dd 00:00:00"));

    /**
     * 比较两个格式化后的时间:间隔的天数
     * @param bigDate
     * @param smallDate
     * @return
     * @throws
     */
    public static int intervalDays(Date bigDate, Date smallDate) throws ParseException {
        Calendar cal = Calendar.getInstance();
        cal.setTime(sdf.get().parse(sdf.get().format(bigDate)));
        long time1 = cal.getTimeInMillis();

        cal.setTime(sdf.get().parse(sdf.get().format(smallDate)));
        long time2 = cal.getTimeInMillis();
        long between_days=(time1-time2)/(1000*3600*24);

        return Integer.parseInt(String.valueOf(between_days));
    }

    /**
     * 日期字符串解析成日期对象基础方法，可以在此封装出多种便捷的方法直接使用
     *
     * @param dateStr 日期字符串
     * @param format  输入的格式
     * @return 日期对象
     * @throws ParseException
     */
    public static Date parse(String dateStr, String format) {
        try {
            if (StringUtils.isBlank(format)) {
                throw new ParseException("format can not be null.", 0);
            }

            if (dateStr == null || dateStr.length() < format.length()) {
                throw new ParseException("date string's length is too small.", 0);
            }
            return new SimpleDateFormat(format, Locale.SIMPLIFIED_CHINESE).parse(dateStr);
        } catch (Exception e) {
            throw new RuntimeException("parse excepion, dateStr=" + dateStr + ", format=" + format);
        }
    }

    /**
     * 日期字符串解析成日期对象基础方法，可以在此封装出多种便捷的方法直接使用
     *
     * @param dateStr 日期字符串
     * @param format  输入的格式
     * @return 日期对象
     * @throws ParseException
     */
    public static Date parsePlus(String dateStr, String format) {
        try {
            if (StringUtils.isBlank(format)) {
                throw new ParseException("format can not be null.", 0);
            }

            if (dateStr == null || dateStr.length() < format.length()) {
                throw new ParseException("date string's length is too small.", 0);
            }
            if(format.equals(TIME_MILLIS)){
                return new Date(Long.valueOf(dateStr));
            }
            return new SimpleDateFormat(format, Locale.SIMPLIFIED_CHINESE).parse(dateStr);
        } catch (Exception e) {
            throw new RuntimeException("parse excepion, dateStr=" + dateStr + ", format=" + format);
        }
    }

    public static Date parse(String s) {
        if (StringUtils.isBlank(s)) {
            return null;
        }
        for (String pattern : patterns) {
            try {
                return new SimpleDateFormat(pattern, Locale.SIMPLIFIED_CHINESE).parse(s);
            } catch (ParseException e) {
                continue;
            }
        }
        throw new RuntimeException("Unparseable date: \"" + s + "\"");
    }

    /**
     * 日期字符串解析成日期对象基础方法，可以在此封装出多种便捷的方法直接使用
     *
     * @param dateStr 日期字符串
     * @param format  输入的格式
     * @return 日期对象
     * @throws ParseException
     */
    public static Date parseDate(String dateStr, String format) {
        try {
            if (StringUtils.isBlank(format)) {
                return null;
            }

            if (dateStr == null || dateStr.length() < format.length()) {
                return null;
            }
            return new SimpleDateFormat(format, Locale.SIMPLIFIED_CHINESE).parse(dateStr);
        } catch (Exception e) {
            logger.error("日期转换异常，dateStr {}，format {}", dateStr, format, e);
        }

        return null;
    }

    /**
     * 计算两个日期相隔天数
     *
     * @param d1
     * @param d2
     * @return
     */
    public static int interval(Date d1, Date d2) {
        return Math.abs(daysBetween(d1, d2));
    }

    public static int daysBetween(Date date1, Date date2) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date1);
        long time1 = cal.getTimeInMillis();
        cal.setTime(date2);
        long time2 = cal.getTimeInMillis();
        long betweenDays = (time2 - time1) / (1000 * 3600 * 24);
        return (int) betweenDays;
    }

    public static int getDistHours(Date startTime, Date endTime) {
        long num = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60);
        return (int) num;
    }

    public static int getDistMinutes(Date startTime, Date endTime) {
        long num = (endTime.getTime() - startTime.getTime()) / 60000;
        return (int) num;
    }

    public static int getDistSeconds(Date startTime, Date endTime) {
        long num = (endTime.getTime() - startTime.getTime()) / 1000;
        return (int) num;
    }

    public static long secondsPassed(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);

        return (System.currentTimeMillis() - cal.getTimeInMillis()) / 1000;
    }

    /**
     * 指定日期前一定日期
     *
     * @param date
     * @param daysBefore
     * @param monthBefore
     * @return
     */
    public static Date getBeforeDateCompareDate(Date date, int daysBefore, int monthBefore) {
        Calendar today = Calendar.getInstance();
        today.setTime(date);
        if (daysBefore != 0) {
            today.add(Calendar.DATE, 0 - daysBefore);
        }
        if (monthBefore != 0) {
            today.add(Calendar.MONTH, 0 - monthBefore);
        }
        return today.getTime();
    }

    public static Date format(Object o) {
        if (o == null) {
            return null;
        }
        if (o instanceof Date) {
            return (Date) o;
        } else if (o instanceof String) {
            return parse((String) o);
        } else {
            throw new IllegalArgumentException("For input object: \"" + o + "\"");
        }
    }

    /**
     * 日期对象解析成日期字符串基础方法，可以据此封装出多种便捷的方法直接使用
     *
     * @param date   待格式化的日期对象
     * @param format 输出的格式
     * @return 格式化的字符串
     */
    public static String format(Date date, String format) {
        if (date == null || StringUtils.isBlank(format)) {
            return StringUtils.EMPTY;
        }

        return new SimpleDateFormat(format, Locale.SIMPLIFIED_CHINESE).format(date);
    }

    /**
     * 日期字符串格式化基础方法，可以在此封装出多种便捷的方法直接使用
     *
     * @param dateStr   日期字符串
     * @param formatIn  输入的日期字符串的格式
     * @param formatOut 输出日期字符串的格式
     * @return 已经格式化的字符串
     * @throws ParseException
     */
    public static String format(String dateStr, String formatIn, String formatOut) throws ParseException {

        Date date = parse(dateStr, formatIn);
        return format(date, formatOut);
    }

    /**
     * 格式化当前时间
     *
     * @param format 输出的格式
     * @return
     */
    public static String formatCurrent(String format) {
        if (StringUtils.isBlank(format)) {
            return StringUtils.EMPTY;
        }

        return format(new Date(), format);
    }

    /**
     * 把日期对象按照<code>yyyyMMdd</code>格式解析成字符串
     *
     * @param date 待格式化的日期对象
     * @return 格式化的字符串
     */
    public static String formatShort(Date date) {
        return format(date, SHORT_FORMAT);
    }

    /**
     * 把日期字符串按照<code>yyyyMMdd</code>格式，进行格式化
     *
     * @param dateStr  待格式化的日期字符串
     * @param formatIn 输入的日期字符串的格式
     * @return 格式化的字符串
     */
    public static String formatShort(String dateStr, String formatIn) throws ParseException {
        return format(dateStr, formatIn, SHORT_FORMAT);
    }

    /**
     * 把日期对象按照<code>yyyy-MM-dd</code>格式解析成字符串
     *
     * @param date 待格式化的日期对象
     * @return 格式化的字符串
     */
    public static String formatWeb(Date date) {
        return format(date, WEB_FORMAT);
    }

    /**
     * 把日期字符串按照<code>yyyy-MM-dd</code>格式，进行格式化
     *
     * @param dateStr  待格式化的日期字符串
     * @param formatIn 输入的日期字符串的格式
     * @return 格式化的字符串
     * @throws ParseException
     */
    public static String formatWeb(String dateStr, String formatIn) throws ParseException {
        return format(dateStr, formatIn, WEB_FORMAT);
    }

    /**
     * 把日期对象按照<code>yyyyMM</code>格式解析成字符串
     *
     * @param date 待格式化的日期对象
     * @return 格式化的字符串
     */
    public static String formatMonth(Date date) {

        return format(date, MONTH_FORMAT);
    }

    /**
     * 把日期对象按照<code>HHmmss</code>格式解析成字符串
     *
     * @param date 待格式化的日期对象
     * @return 格式化的字符串
     */
    public static String formatTime(Date date) {
        return format(date, TIME_FORMAT);
    }

    /**
     * 获取yyyyMMddHHmmss+n位随机数格式的时间戳
     *
     * @param n 随机数位数
     * @return
     */
    public static String getTimestamp(int n) {
        return formatCurrent(LONG_FORMAT) + RandomStringUtils.randomNumeric(n);
    }

    /**
     * 获取yyyyMMddHHmmss
     *
     * @return
     */
    public static String formatFull() {
        return formatCurrent(LONG_FORMAT);
    }

    /**
     * 获取yyyyMMddHHmmssSSS
     *
     * @return
     */
    public static String formatFullMill() {
        return formatCurrent(LONG_FORMAT_MILL);
    }

    /**
     * 根据日期格式返回昨日日期
     *
     * @param format 日期格式
     * @return
     */
    public static String getYesterdayDate(String format) {
        return getDateCompareToday(format, -1, 0);
    }

    /**
     * 把当日日期作为基准，按照格式返回相差一定间隔的日期
     *
     * @param format     日期格式
     * @param daysAfter  和当日比相差几天，例如3代表3天后，-1代表1天前
     * @param monthAfter 和当日比相差几月，例如2代表2月后，-3代表3月前
     * @return
     */
    public static String getDateCompareToday(String format, int daysAfter, int monthAfter) {
        Calendar today = Calendar.getInstance();
        if (daysAfter != 0) {
            today.add(Calendar.DATE, daysAfter);
        }
        if (monthAfter != 0) {
            today.add(Calendar.MONTH, monthAfter);
        }
        return format(today.getTime(), format);
    }

    /**
     * 根据日期格式返回上月的日期
     *
     * @param format
     * @return
     */
    public static String getLastMonth(String format) {
        Calendar today = Calendar.getInstance();
        today.add(Calendar.MONTH, -1);
        return format(today.getTime(), format);
    }

    /**
     * 平移当前时间，以分为单元，minutes
     *
     * @param minutes
     * @return
     */
    public static Date addCurMin(long minutes) {
        return DateUtils.addMinutes(new Date(), (int) minutes);
    }

    /**
     * 平移当前时间，以秒为单元，minutes
     *
     * @param secs
     * @return
     */
    public static Date addCurSeconds(long secs) {
        return addSeconds(new Date(), (int) secs);
    }

    /**
     * 平移当前时间，以秒为单元，minutes
     *
     * @param secs
     * @return
     */
    public static Date addCurSeconds(Date date, long secs) {
        return addSeconds(date, (int) secs);
    }

    /**
     * 判断两个日期是否是同一天
     *
     * @param date1 date1
     * @param date2 date2
     * @return
     */
    public static boolean isSameDay(Date date1, Date date2) {
        return DateUtils.isSameDay(date1, date2);
    }

    public static Date subtractDay(Date beginTime, int days) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(beginTime);
        cal.add(Calendar.DATE, -days);

        return cal.getTime();
    }

    public static Boolean isBelong(String begintime, String endtime) {
        // 判断是否跨天比较
        Integer beginint = Integer.valueOf(begintime.substring(0, begintime.indexOf(":")));
        Integer endint = Integer.valueOf(endtime.substring(0, endtime.indexOf(":")));
        Boolean crossFlag = false;
        if (beginint > endint) {
            crossFlag = true;
        }
        SimpleDateFormat df = new SimpleDateFormat("HH:mm");// 设置日期格式
        Date now = null;
        Date beginTime = null;
        Date endTime = null;
        try {
            now = df.parse(df.format(new Date()));
            beginTime = df.parse(begintime);
            endTime = df.parse(endtime);
        } catch (Exception e) {
            logger.error("判断当前时间是否在制定时间段内异常，begintime {}，endtime {}", begintime, endtime, e);
        }
        Boolean flag = null;
        if (crossFlag) {
            flag = !belongCalendar(now, endTime, beginTime);
        } else {
            flag = belongCalendar(now, beginTime, endTime);
        }
        return flag;
    }

    /**
     * 判断时间是否在时间段内
     *
     * @param nowTime
     * @param beginTime
     * @param endTime
     * @return
     */
    public static boolean belongCalendar(Date nowTime, Date beginTime, Date endTime) {
        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);
        Calendar begin = Calendar.getInstance();
        begin.setTime(beginTime);
        Calendar end = Calendar.getInstance();
        end.setTime(endTime);
        if (date.after(begin) && date.before(end)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 判断时间点是否在范围内，只比较HHmm
     *
     * @param begintime
     * @param endtime
     * @param valDate
     * @return
     */
    public static boolean belongTimePeriod(String begintime, String endtime, Date valDate) {
        DateFormat df = new SimpleDateFormat("HH:mm");//创建日期转换对象HH:mm:ss为时分秒，年月日为yyyy-MM-dd
        try {
            Date start = df.parse(begintime);//将字符串转换为date类型
            Date end = df.parse(endtime);
            String compareDateStr = df.format(valDate);
            Date compareDate = df.parse(compareDateStr);
            if (compareDate.getTime() > start.getTime() && compareDate.getTime() < end.getTime()) {
                return true;
            }
        } catch (ParseException e) {
            return false;
        }
        return false;
    }


    /**
     * 获取两个日期相差的月份 2015-10-10与 2015-11-09 相差为0月 2015-10-10与 2015-11-10 相差为1月
     *
     * @param date1
     * @param date2
     * @return
     */
    public static int getDistMonths(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            throw new RuntimeException("日期不允许为空");
        }

        int result;
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();
        cal1.setTime(date1.before(date2) ? date1 : date2);
        cal2.setTime(date1.before(date2) ? date2 : date1);
        if (cal2.get(Calendar.DAY_OF_MONTH) - cal1.get(Calendar.DAY_OF_MONTH) >= 0) {
            result = 12 * (cal2.get(Calendar.YEAR) - cal1.get(Calendar.YEAR)) + cal2.get(Calendar.MONTH)
                    - cal1.get(Calendar.MONTH);
        } else {
            result = 12 * (cal2.get(Calendar.YEAR) - cal1.get(Calendar.YEAR)) + cal2.get(Calendar.MONTH)
                    - cal1.get(Calendar.MONTH) - 1;
        }
        return result;
    }

    /**
     * 获取后几天的日期
     *
     * @param num
     * @return
     */
    public static Date getNextDayDate(Date cDate, int num) {
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
        Calendar date = Calendar.getInstance();
        try {
            date.setTime(cDate);
        } catch (Exception e) {
            e.printStackTrace();
        }
        date.set(Calendar.DATE, date.get(Calendar.DATE) + num);
        String nextDate = dft.format(date.getTime()) + " 00:00:00";
        Date ndate = null;
        try {
            ndate = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").parse(nextDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return ndate;
    }

    public static Integer differenceMinutes(Date date1, Date date2) {
        return (int) ((date1.getTime() - date2.getTime()) / 60000L);
    }

    /**
     * 获取当天零点
     *
     * @return
     */
    public static Date getCurrentDateZero() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date zero = calendar.getTime();
        return zero;
    }

    /**
     * @param date
     * @return
     */
    public static Date getDate(Date date) {
        Calendar calender = Calendar.getInstance();
        calender.setTime(date);
        calender.set(Calendar.HOUR_OF_DAY, 0);
        calender.set(Calendar.MINUTE, 0);
        calender.set(Calendar.SECOND, 0);
        return calender.getTime();
    }

    /**
     * 截取 时分 2021-09-03T00:00:00+08:00
     *
     * @param date
     * @return
     */
    public static String getHourMinutes(String date) {
        if (StringUtils.isBlank(date)) {
            return date;
        }
        String t[] = date.split("T");
        String day = t[0].substring(t[0].indexOf("-") + 1);
        String hhMmSs = t[1].substring(0, t[1].indexOf("+"));

        return day + " " + hhMmSs.substring(0, hhMmSs.lastIndexOf(":"));
    }

    /**
     * 时间 10分取整，例：12:04:05 -> 12:10:00
     *
     * @param date
     * @param format
     * @return
     */
    public static String roundUpTenMinutes(String date, String format) {
        if (StringUtils.isBlank(date)) {
            return date;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.setTime(simpleDateFormat.parse(date));
            int minute = calendar.get(Calendar.MINUTE);
            int remainder = minute % 10;
            if (remainder != 0) {
                minute = minute + (10 - remainder);
            }
            calendar.set(Calendar.MINUTE, minute);
            calendar.set(Calendar.SECOND, 0);
            return format(calendar.getTime(), format);
        } catch (ParseException e) {
            logger.info("roundUpTenMinutes error:", e);
            return date;
        }
    }

    /**
     * 时间十分钟，五舍六入取整
     *
     * @return
     */
    public static Date roundingTenMinutes(Date date) {
        if (Objects.isNull(date)) {
            return date;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int minute = calendar.get(Calendar.MINUTE);
        int remainder = minute % 10;
        if (remainder > 5) {
            minute = minute + (10 - remainder);
        } else {
            minute = minute - remainder;
        }
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    public static Date parseDateWithTimeZone(String dateStr) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        Date date = null;
        try {
            date = df.parse(dateStr);
        } catch (Exception ex) {
            logger.info("formatDateWithTimeZone error:", ex);
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR_OF_DAY, 8);
        return calendar.getTime();
    }

    public static Date parseDateWithTimeZoneZero(String dateStr) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        Date date = null;
        try {
            date = df.parse(dateStr);
        } catch (Exception ex) {
            logger.info("formatDateWithTimeZone error:", ex);
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR_OF_DAY, 8);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    public static int diffDays(Date date1, Date date2) {
        date1 = DateUtil.parse(DateUtil.format(date1, WEB_FORMAT), WEB_FORMAT);
        date2 = DateUtil.parse(DateUtil.format(date2, WEB_FORMAT), WEB_FORMAT);
        return (int) ((date2.getTime() - date1.getTime()) / (1000 * 3600 * 24));
    }

    /**
     * 获取两个日期相差的月份 2015-10-10与 2015-11-09 相差为0月 2015-10-10与 2015-11-10 相差为1月
     *
     * @param date1
     * @param date2
     * @return
     */
    public static boolean isDistMoreThanOneMonth(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            throw new RuntimeException("日期不允许为空");
        }

        int result;
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();
        cal1.setTime(date1.before(date2) ? date1 : date2);
        cal2.setTime(date1.before(date2) ? date2 : date1);
        int dayDis=cal2.get(Calendar.DAY_OF_MONTH) - cal1.get(Calendar.DAY_OF_MONTH);
        if (dayDis>= 0) {
            result = 12 * (cal2.get(Calendar.YEAR) - cal1.get(Calendar.YEAR)) + cal2.get(Calendar.MONTH)
                    - cal1.get(Calendar.MONTH);
            return result>0;
        } else {
            result = 12 * (cal2.get(Calendar.YEAR) - cal1.get(Calendar.YEAR)) + cal2.get(Calendar.MONTH)
                    - cal1.get(Calendar.MONTH) - 1;
            return result>1;
        }
    }

    public static boolean isDistMoreThanDays(Date beforeDate,Date afterDate,int days){
        int interval = interval(afterDate, beforeDate);
        return interval>=days;
    }

    public static boolean isBeforeDate(Date before,Date after){
        if(Objects.isNull(before) || Objects.isNull(after)){
            return false;
        }
        return before.before(after);
    }


    /**
     * 获取相差多少种之前的时间
     *
     * @return
     */
    public static Date beforeMinutes(Date date,int minutes) {
        if(Objects.isNull(date)){
            return date;
        }
        return new Date(date.getTime()-minutes*60000);
    }

    /**
     * 获取当天00:00:00
     *
     * @param date
     * @return
     */
    public static Date getDateBegin(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    /**
     * 获取天数差
     *
     * @param lastVerifyCreateTime
     * @return
     */
    public static int getDayDiff(long lastVerifyCreateTime) {
        int result;
        // 将时间戳转换为秒
        double lastVerifyCreateTimeF = lastVerifyCreateTime / 1000.0;

        // 将时间戳转换为LocalDateTime（需要指定时区）
        LocalDateTime dtObject = LocalDateTime.ofInstant(Instant.ofEpochMilli((long) (lastVerifyCreateTimeF * 1000)), ZoneId.systemDefault());

        // 获取当天的0点时间
        LocalDateTime midnightLastVerifyCreateTime = dtObject.withHour(0).withMinute(0).withSecond(0).withNano(0);

        // 获取当前时间
        LocalDateTime currentTime = LocalDateTime.now();

        // 获取当前日期的0点时间
        LocalDateTime midnight = currentTime.withHour(0).withMinute(0).withSecond(0).withNano(0);

        // 转换时间差为整天数
        long daysGap = ChronoUnit.DAYS.between(
                midnightLastVerifyCreateTime.atZone(ZoneId.systemDefault()).toLocalDate(),
                midnight.atZone(ZoneId.systemDefault()).toLocalDate()
        );

        // 判断天数差是否大于等于30天
        if (daysGap >= 30) {
            result = 30;
        } else {
            result = (int) daysGap;
        }
        return result;
    }


    /**
     * 获取分钟差
     *
     * @param lastVerifyCreateTime
     * @return
     */
    public static double getMinDiff(long lastVerifyCreateTime) {
        // 将时间戳转换为秒
        double lastVerifyCreateTimeF = lastVerifyCreateTime / 1000.0;

        // 获取当前时间的时间戳（秒）
        long currentTime = Instant.now().getEpochSecond();

        // 转换时间差为分钟
        return (currentTime - lastVerifyCreateTimeF) / 60.0;
    }

}
