/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.commons.utils;

import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.CookieStore;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.conn.ClientConnectionManager;
import org.apache.http.conn.scheme.PlainSocketFactory;
import org.apache.http.conn.scheme.Scheme;
import org.apache.http.conn.scheme.SchemeRegistry;
import org.apache.http.conn.ssl.SSLSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.AbstractHttpClient;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.conn.PoolingClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.CoreConnectionPNames;
import org.apache.http.params.CoreProtocolPNames;
import org.apache.http.params.HttpParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.KeyStore;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * User: liuchuandong Date: 13-9-5 Time: 下午3:13 Func: httpclient处理类
 */
public class HttpUtils {

    private static Logger logger = LoggerFactory.getLogger(HttpUtils.class);

    static PoolingClientConnectionManager cm;
    static HttpParams params;
    public static final int TIMEOUT = 60000;// 连接超时时间
    public static final int SO_TIMEOUT = 60000;// 数据传输超时
    static HttpClient client;

    /**
     * 静态初始化
     */
    static {
        SchemeRegistry schemeRegistry = new SchemeRegistry();
        schemeRegistry.register(new Scheme("http", 80, PlainSocketFactory.getSocketFactory()));
        schemeRegistry.register(new Scheme("https", 443, SSLSocketFactory.getSocketFactory()));
        cm = new PoolingClientConnectionManager(schemeRegistry);
        cm.setMaxTotal(800);
        cm.setDefaultMaxPerRoute(400);
        params = new BasicHttpParams();
        params.setParameter(CoreConnectionPNames.CONNECTION_TIMEOUT, TIMEOUT);
        params.setParameter(CoreConnectionPNames.SO_TIMEOUT, SO_TIMEOUT);
    }

    /**
     * 是否为https
     *
     * @return
     */
    public static boolean isHttps(String url) {
        if (StringUtils.isEmpty(url)) {
            return false;
        }

        return url.toUpperCase().startsWith("HTTPS");
    }

    /**
     * 获取client
     *
     * @return
     */
    public static DefaultHttpClient getHttpClient3() {
        DefaultHttpClient client = new DefaultHttpClient(cm, params);
        client.getParams().setParameter(CoreProtocolPNames.HTTP_CONTENT_CHARSET, "UTF-8");
        SSLContext sslContext;
        try {
            sslContext = SSLContext.getInstance("SSL");
            // set up a TrustManager that trusts everything
            // try {
            // sslContext.init(null,
            // new TrustManager[] { new X509TrustManager() {
            // public X509Certificate[] getAcceptedIssuers() {
            // return null;
            // }
            //
            // public void checkClientTrusted(
            // X509Certificate[] certs, String authType) {
            // }
            //
            // public void checkServerTrusted(
            // X509Certificate[] certs, String authType) {
            // }
            // } }, new SecureRandom());
            // } catch (KeyManagementException e) {
            // }

            SSLSocketFactory ssf = new SSLSocketFactory(sslContext, SSLSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
            ClientConnectionManager ccm = client.getConnectionManager();
            SchemeRegistry sr = ccm.getSchemeRegistry();
            sr.register(new Scheme("https", 443, ssf));
        } catch (Exception e) {
        }
        return client;
    }

    /**
     * 获取client
     *
     * @return
     */
    public static DefaultHttpClient getHttpClient(List<SSLCertConfig> certVoList, CookieStore cookieStore) {
        DefaultHttpClient client = new DefaultHttpClient(cm, params);
        client.getParams().setParameter(CoreProtocolPNames.HTTP_CONTENT_CHARSET, "UTF-8");
        if (cookieStore != null) {
        	client.setCookieStore(cookieStore);
        }
        KeyStore trustStore = null;
        InputStream fis = null;
        if (!StringUtils.isEmpty(certVoList)) {
            for (SSLCertConfig certConfig : certVoList) {
                try {
                    // 添加环信证书
                    fis = HttpsUtils.class.getClassLoader().getResourceAsStream(certConfig.getPath());
                    trustStore = KeyStore.getInstance(KeyStore.getDefaultType());
                    trustStore.load(fis, certConfig.getPassword().toCharArray()); // 加载KeyStore
                    SSLSocketFactory ssf = new SSLSocketFactory(trustStore);
                    ClientConnectionManager ccm = client.getConnectionManager();
                    SchemeRegistry sr = ccm.getSchemeRegistry();
                    sr.register(new Scheme("https", 443, ssf));
                } catch (Exception e) {
                    logger.error("", e);
                }
            }
        }

        return client;
    }

    public static DefaultHttpClient getHttpClient() {
        return HttpUtils.getHttpClient(null, null);
    }

    /**
     * post请求
     *
     * @param url
     * @param header
     * @param requestBody
     * @return
     */
    public static HttpResult post(String url, Map<String, String> header, String requestBody) {
        return HttpUtils.post(url, header, requestBody, null, null);
    }

    public static HttpResult post(String url, Map<String, String> header, String requestBody,
            List<SSLCertConfig> certConfigList, CookieStore cookieStore) {
        client = HttpUtils.getHttpClient(certConfigList, cookieStore);
        HttpPost post = new HttpPost(url);
        try {
            HttpUtils.setHeader(post, header);
            StringEntity stringEntity = new StringEntity(requestBody, "UTF-8");
            post.setEntity(stringEntity);
            HttpResponse response = client.execute(post);
            CookieStore newCookieStore = null;
            if (client instanceof AbstractHttpClient) {
            	newCookieStore = ((AbstractHttpClient) client).getCookieStore();
            }
            HttpResult result = HttpUtils.getResult(response, newCookieStore);
            return result;
        } catch (IOException e) {
            logger.error(String.format("请求url[%s]出错", url), e);
            return null;
        } finally {
            if (null != post) {
                post.abort();
            }
            if (null != client) {
                HttpUtils.close();
            }
        }
    }

    public static HttpResult post(String url, Map<String, String> header, Map<String, String> param) {
        client = HttpUtils.getHttpClient();
        HttpPost post = new HttpPost(url);
        try {
            HttpUtils.setHeader(post, header);
            HttpUtils.setParam(post, param);
            HttpResponse response = client.execute(post);
            HttpResult result = HttpUtils.getResult(response);
            HttpUtils.close();
            return result;
        } catch (IOException e) {
            post.abort();
            logger.error("HTTP Error POST 请求失败,uri={},param={}",url,JsonUtils.toJson(param), e);
            // e.printStackTrace();
            return null;
        }
    }

    /**
     * get请求
     *
     * @param url
     * @param header
     * @return
     */
    public static HttpResult get(String url, Map<String, String> header) {
        client = HttpUtils.getHttpClient();
        HttpGet get = new HttpGet(url);
        try {
            HttpUtils.setHeader(get, header);
            HttpResponse response = client.execute(get);
            HttpResult result = HttpUtils.getResult(response);
            HttpUtils.close();
            return result;
        } catch (IOException e) {
            get.abort();
            logger.error("HTTP Error Get url={}",url,e);
            return null;
        }
    }

    /**
     * 带cookie的httpget请求
     * @param url
     * @param header
     * @param cookieStore
     * @return
     */
    public static HttpResult get(String url, Map<String, String> header, CookieStore cookieStore) {
    	client = HttpUtils.getHttpClient(null, cookieStore);
        HttpGet get = new HttpGet(url);
        try {
            HttpUtils.setHeader(get, header);
            HttpResponse response = client.execute(get);
            HttpResult result = HttpUtils.getResult(response);
            HttpUtils.close();
            return result;
        } catch (IOException e) {
            get.abort();
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 可设置超时时间
     *
     * @param url
     * @param timeout
     * @param so_timeout
     * @return
     */
    public static HttpResult get(String url, int timeout, int so_timeout) {
        client = HttpUtils.getHttpClient();
        HttpGet get = new HttpGet(url);
        try {
            // 请求超时
            client.getParams().setParameter(CoreConnectionPNames.CONNECTION_TIMEOUT, timeout);
            // 读取超时
            client.getParams().setParameter(CoreConnectionPNames.SO_TIMEOUT, so_timeout);

            HttpResponse response = client.execute(get);
            HttpResult result = HttpUtils.getResult(response);
            HttpUtils.close();
            return result;
        } catch (IOException e) {
            get.abort();
            logger.error("HTTP Error Get url={}",url,e);
            LoggerProxy.error("HttpUtils.get",logger,e.getMessage(), e);
            return null;
        }
    }

    /**
     * delete 请求
     *
     * @param url
     * @param header
     * @return
     */
    public static HttpResult delete(String url, Map<String, String> header) {
        client = HttpUtils.getHttpClient();
        HttpDelete delete = new HttpDelete(url);
        try {
            HttpUtils.setHeader(delete, header);
            HttpResponse response = client.execute(delete);
            HttpResult result = HttpUtils.getResult(response);
            HttpUtils.close();
            return result;
        } catch (IOException e) {
            delete.abort();
            e.printStackTrace();
            return null;
        }
    }

    /**
     * put请求
     *
     * @param url
     * @param header
     * @param requestBody
     * @return
     */
    public static HttpResult put(String url, Map<String, String> header, String requestBody) {
        client = HttpUtils.getHttpClient();
        HttpPut put = new HttpPut(url);
        try {
            HttpUtils.setHeader(put, header);
            StringEntity stringEntity = new StringEntity(requestBody, "UTF-8");
            put.setEntity(stringEntity);
            HttpResponse response = client.execute(put);
            HttpResult result = HttpUtils.getResult(response);
            HttpUtils.close();
            return result;
        } catch (IOException e) {
            put.abort();
            e.printStackTrace();
            return null;
        }
    }

    public static HttpResult put(String url, Map<String, String> header, Map<String, String> param) {
        client = HttpUtils.getHttpClient();
        HttpPut put = new HttpPut(url);
        try {
            HttpUtils.setHeader(put, header);
            HttpUtils.setParam(put, param);
            HttpResponse response = client.execute(put);
            HttpResult result = HttpUtils.getResult(response);
            HttpUtils.close();
            return result;
        } catch (IOException e) {
            put.abort();
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 释放连接
     */
    public static void close() {
        client.getConnectionManager().closeIdleConnections(0L, TimeUnit.SECONDS);
    }

    /**
     * 通用请求
     *
     * @param response
     * @return
     * @throws IOException
     */
    private static HttpResult getResult(HttpResponse response) throws IOException {
        HttpEntity entity = response.getEntity();
        String content = "";
        int status = response.getStatusLine().getStatusCode();
        if (entity != null) {
            content = HttpUtils.inputStreamToString(entity.getContent());
        }
        HttpUtils.close();
        HttpResult result = new HttpResult();
        result.setStatus(status);
        result.setMessage(content);
        return result;
    }

    private static HttpResult getResult(HttpResponse response, CookieStore cookieStore) throws IOException {
        HttpEntity entity = response.getEntity();
        String content = "";
        int status = response.getStatusLine().getStatusCode();
        if (entity != null) {
            content = HttpUtils.inputStreamToString(entity.getContent());
        }
        HttpUtils.close();
        HttpResult result = new HttpResult();
        result.setStatus(status);
        result.setMessage(content);
        result.setCookieStore(cookieStore);
        return result;
    }

    /**
     * 设置请求头
     *
     * @param base
     * @param header
     */
    private static void setHeader(HttpRequestBase base, Map<String, String> header) {
        if (header == null) {
			return;
		}
        Iterator<Map.Entry<String, String>> it = header.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<String, String> entry = it.next();
            base.setHeader(entry.getKey(), entry.getValue());
        }
    }

    /**
     * 设置请求参数
     *
     * @param base
     * @param param
     */
    private static void setParam(HttpEntityEnclosingRequestBase base, Map<String, String> param)
            throws UnsupportedEncodingException {
        if (param == null) {
			return;
		}
        List<NameValuePair> pairs = new ArrayList<NameValuePair>(param.size());
        for (Map.Entry<String, String> entry : param.entrySet()) {
            String value = entry.getValue();
            if (value != null) {
                pairs.add(new BasicNameValuePair(entry.getKey(), value));
            }
        }
        if (pairs != null && pairs.size() > 0) {
            base.setEntity(new UrlEncodedFormEntity(pairs, "UTF-8"));
        }
    }

    /**
     * 获取内容
     *
     * @param input
     * @return
     * @throws IOException
     */
    public static String inputStreamToString(InputStream input) throws IOException {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        byte[] data = new byte[1024];
        int count = -1;
        while ((count = input.read(data, 0, 1024)) != -1) {
			outStream.write(data, 0, count);
		}
        data = null;
        return new String(outStream.toByteArray(), "UTF-8");
    }

    public static String download(String urlStr){
    	InputStream inputStream = null;
    	try{
			URL url = new URL(urlStr);
			HttpURLConnection conn = (HttpURLConnection)url.openConnection();
            //设置超时间  30秒
			conn.setConnectTimeout(30*1000);
			 inputStream = conn.getInputStream();
			byte[] bytes = IOUtils.toByteArray(inputStream);
			return Base64Utils.encode(bytes);
		} catch (Exception  e) {

		}finally{
			if(inputStream != null){
				try {
					inputStream.close();
				} catch (IOException e) {
				}
			}
		}



    	return null;
    }

    public static void main(String[] args) {
        Map<String, String> param = new HashMap<String, String>();
        param.put("key", "57b94ad2676f03c234eff8ca0f855836");
        param.put("keywords", "");
        param.put("city", "北京");
        param.put("citylimit", "true");
        HttpResult result = HttpUtils.post("http://restapi.amap.com/v3/assistant/inputtips", new HashMap<String, String>(),
                param);
        System.out.println(result.getMessage());
    }
}
