package com.youxin.risk.commons.cacheloader.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import javax.annotation.Resource;

import org.springframework.util.CollectionUtils;

import com.youxin.risk.commons.cacheloader.service.DictionaryService;
import com.youxin.risk.commons.dao.admin.DictionaryMapper;
import com.youxin.risk.commons.model.Dictionary;

/**
 * 字典缓存Service实现类
 * 
 * <AUTHOR>
 */
public class DictionaryServiceImpl implements DictionaryService {

    @Resource
    private DictionaryMapper dictionaryMapper;

    /**
     * 全量加载，Map<dict_code,Map<dict_value,Dictionary>>
     */
    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> selectAll() {
        Map<String, Object> map = new ConcurrentHashMap<String, Object>();
        List<Dictionary> dictList = dictionaryMapper.selectAllInMaster();
        if (!CollectionUtils.isEmpty(dictList)) {
            for (Dictionary dict : dictList) {
                Map<String, Dictionary> valueMap = (Map<String, Dictionary>) map.get(dict.getDictCode());
                if (valueMap == null) {
                    valueMap = new ConcurrentHashMap<String, Dictionary>();
                    map.put(dict.getDictCode(), valueMap);
                }
                valueMap.put(dict.getDictValue(), dict);
            }
        }
        return map;
    }

    @Override
    public List<Dictionary> selectByUpdateTime(Date updateTime) {
        return dictionaryMapper.selectByUpdateTimeInMaster(updateTime);
    }

}