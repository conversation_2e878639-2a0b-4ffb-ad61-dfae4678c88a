package com.youxin.risk.commons.model;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/3/11 11:47
 */
public class ProcessSplitFlow extends BaseModel {
    private static final long serialVersionUID = 6080409700634989573L;

    private String splitFlowCode;
    private String splitFlowName;
    private String onlineProcessDefId;
    private String processDefId;
    private Integer splitFlowPercent;
    private Date beginTime;
    private Date endTime;
    private String status;

    public String getSplitFlowCode() {
        return splitFlowCode;
    }

    public void setSplitFlowCode(String splitFlowCode) {
        this.splitFlowCode = splitFlowCode;
    }

    public String getSplitFlowName() {
        return splitFlowName;
    }

    public void setSplitFlowName(String splitFlowName) {
        this.splitFlowName = splitFlowName;
    }

    public String getOnlineProcessDefId() {
        return onlineProcessDefId;
    }

    public void setOnlineProcessDefId(String onlineProcessDefId) {
        this.onlineProcessDefId = onlineProcessDefId;
    }

    public String getProcessDefId() {
        return processDefId;
    }

    public void setProcessDefId(String processDefId) {
        this.processDefId = processDefId;
    }

    public Integer getSplitFlowPercent() {
        return splitFlowPercent;
    }

    public void setSplitFlowPercent(Integer splitFlowPercent) {
        this.splitFlowPercent = splitFlowPercent;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
