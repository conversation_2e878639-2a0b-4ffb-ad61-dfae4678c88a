package com.youxin.risk.commons.utils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.util.TypeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 简化版JSON合并工具类
 * 直接合并两个JSON对象，不做额外转换
 */
public class JsonKeyFormatUtil {
    private final static Logger logger = LoggerFactory.getLogger(JsonKeyFormatUtil.class);

    /**
     * 合并verifyResult和originResult
     * originResult已经包含下划线格式的数据，不需要额外转换
     *
     * @param verifyResult 原始验证结果JSON对象
     * @param originResult 添加的原始结果JSON对象（已包含下划线格式）
     * @return 合并后的JSON对象
     */
    public static JSONObject mergeResults(JSONObject verifyResult, JSONObject originResult) {
        return mergeResults(verifyResult, originResult, null);
    }
    
    /**
     * 合并verifyResult和originResult，支持灰度功能
     * originResult已经包含下划线格式的数据，不需要额外转换
     *
     * @param verifyResult 原始验证结果JSON对象
     * @param originResult 添加的原始结果JSON对象（已包含下划线格式）
     * @param userKey 用户标识，用于灰度控制
     * @return 合并后的JSON对象
     */
    public static JSONObject mergeResults(JSONObject verifyResult, JSONObject originResult, String userKey) {
        if (verifyResult == null) {
            return originResult;
        }
        if (originResult == null) {
            return verifyResult;
        }

        return mergeResults(verifyResult, (Map<String, Object>) originResult, userKey);
    }

    /**
     * 合并verifyResult和originResult
     * originResult已经包含下划线格式的数据，不需要额外转换
     *
     * @param verifyResult 原始验证结果Map对象
     * @param originResult 添加的原始结果Map对象（已包含下划线格式）
     * @return 合并后的JSON对象
     */
    public static JSONObject mergeResults(Map<String, Object> verifyResult, Map<String, Object> originResult) {
        return mergeResults(verifyResult, originResult, null);
    }
    
    /**
     * 合并verifyResult和originResult，支持灰度功能
     * originResult已经包含下划线格式的数据，不需要额外转换
     *
     * @param verifyResult 原始验证结果Map对象
     * @param originResult 添加的原始结果Map对象（已包含下划线格式）
     * @param userKey 用户标识，用于灰度控制
     * @return 合并后的JSON对象
     */
    public static JSONObject mergeResults(Map<String, Object> verifyResult, Map<String, Object> originResult, String userKey) {
        if (verifyResult == null) {
            verifyResult = new HashMap<>();
        }
        if (originResult == null) {
            return new JSONObject(verifyResult);
        }

        // 记录原始数据
        if (JsonLogUtil.isLogEnabled()) {
            logger.info("[mergeResults] 原始数据: verifyResult={}, originResult={}, userKey={}", 
                    JSON.toJSONString(verifyResult), JSON.toJSONString(originResult), userKey);
        }

        // 如果需要应用灰度功能，则转换为下划线格式，否则使用原始
        if (!JsonGrayFeatureUtil.checkGrayFeature(userKey)) {
            JSONObject result = new JSONObject(verifyResult);
            
            if (JsonLogUtil.isLogEnabled()) {
                logger.info("[mergeResults] 未应用下划线功能，结果: {}, 调用栈: {}, userKey={}",
                        JSON.toJSONString(result), JsonLogUtil.getCallerStackTrace(), userKey);
            }
            
            return result;
        }

        // 创建结果对象，以verifyResult为基础
        JSONObject result = new JSONObject(verifyResult);

        // 遍历originResult中的所有键，添加到结果中
        for (String key : originResult.keySet()) {
            // 如果该键不存在于结果中，则添加
            if (!result.containsKey(key)) {
                result.put(key, originResult.get(key));
            }
        }

        if (JsonLogUtil.isLogEnabled()) {
            logger.info("[mergeResults] 应用下划线功能后，结果: {}, 调用栈: {}, userKey={}",
                    JSON.toJSONString(result), JsonLogUtil.getCallerStackTrace(), userKey);
        }

        return result;
    }

    /**
     * 将驼峰命名转换为下划线命名
     * 例如: hisAplLckScore -> his_apl_lck_score
     *
     * @param camelCase 驼峰命名的字符串
     * @return 下划线命名的字符串
     */
    public static String camelToUnderscore(String camelCase) {
        if (camelCase == null || camelCase.isEmpty()) {
            return camelCase;
        }

        StringBuilder result = new StringBuilder();
        result.append(Character.toLowerCase(camelCase.charAt(0)));

        for (int i = 1; i < camelCase.length(); i++) {
            char ch = camelCase.charAt(i);
            if (Character.isUpperCase(ch)) {
                result.append('_');
                result.append(Character.toLowerCase(ch));
            } else {
                result.append(ch);
            }
        }

        return result.toString();
    }

    /**
     * 通用获取方法，先尝试使用下划线形式获取值，如果获取不到再尝试使用驼峰形式获取值，支持灰度功能
     * 例如：先尝试获取"loan_id"，如果不存在再尝试获取"loanId"
     *
     * @param map 要处理的Map对象
     * @param camelCaseKey 驼峰形式的键名（如"loanId"）
     * @return 获取到的值，如果两种形式都不存在则返回null
     */
    public static Object getValueWithBothFormats(Map<String, Object> map, String camelCaseKey) {
        return getValueWithBothFormats(map, camelCaseKey, null);
    }
    
    /**
     * 通用获取方法，先尝试使用下划线形式获取值，如果获取不到再尝试使用驼峰形式获取值，支持灰度功能
     * 例如：先尝试获取"loan_id"，如果不存在再尝试获取"loanId"
     *
     * @param map 要处理的Map对象
     * @param camelCaseKey 驼峰形式的键名（如"loanId"）
     * @param userKey 用户标识，用于灰度控制
     * @return 获取到的值，如果两种形式都不存在则返回null
     */
    public static Object getValueWithBothFormats(Map<String, Object> map, String camelCaseKey, String userKey) {
        if (map == null || camelCaseKey == null || camelCaseKey.isEmpty()) {
            return null;
        }

        // 转换为下划线形式
        String underscoreKey = camelToUnderscore(camelCaseKey);

        // 如果需要应用灰度功能，则先尝试使用下划线形式获取
        if (JsonGrayFeatureUtil.checkGrayFeature(userKey) && map.containsKey(underscoreKey)) {
            Object value = map.get(underscoreKey);
            
            if (JsonLogUtil.isLogEnabled()) {
                logger.info("[getValueWithBothFormats] 获取到下划线格式的数据: key={}, underscoreKey={}, value={}, userKey={}, 调用栈: {}", 
                        camelCaseKey, underscoreKey, value, userKey, JsonLogUtil.getCallerStackTrace());
            }
            
            return value;
        }

        // 如果下划线形式不存在或不应用灰度功能，再尝试使用驼峰形式获取
        if (map.containsKey(camelCaseKey)) {
            Object value = map.get(camelCaseKey);
            
            if (JsonLogUtil.isLogEnabled()) {
                logger.info("[getValueWithBothFormats] 获取到驼峰格式的数据: key={}, value={}, userKey={}, 调用栈: {}", 
                        camelCaseKey, value, userKey, JsonLogUtil.getCallerStackTrace());
            }
            
            return value;
        }

        // 两种形式都不存在，返回null
        if (JsonLogUtil.isLogEnabled()) {
            logger.info("[getValueWithBothFormats] 未获取到数据: key={}, underscoreKey={}, userKey={}, 调用栈: {}", 
                    camelCaseKey, underscoreKey, userKey, JsonLogUtil.getCallerStackTrace());
        }
        
        return null;
    }

    /**
     * 通用获取方法，先尝试使用下划线形式获取值，如果获取不到再尝试使用驼峰形式获取值，支持灰度功能
     * 例如：先尝试获取"loan_id"，如果不存在再尝试获取"loanId"
     *
     * @param jsonObject 要处理的JSONObject对象
     * @param camelCaseKey 驼峰形式的键名（如"loanId"）
     * @param userKey 用户标识，用于灰度控制
     * @return 获取到的值，如果两种形式都不存在则返回null
     */
    public static Object getValueWithBothFormats(JSONObject jsonObject, String camelCaseKey, String userKey) {
        if (jsonObject == null) {
            return null;
        }
        return getValueWithBothFormats((Map<String, Object>) jsonObject, camelCaseKey, userKey);
    }

    /**
     * 判断Map中是否存在指定键的驼峰形式或下划线形式
     * 例如：判断是否存在"loanId"或"loan_id"
     *
     * @param map 要处理的Map对象
     * @param camelCaseKey 驼峰形式的键名（如"loanId"）
     * @return 如果存在任一形式则返回true，否则返回false
     */
    public static boolean containsKeyInBothFormats(Map<String, Object> map, String camelCaseKey) {
        if (map == null || camelCaseKey == null || camelCaseKey.isEmpty()) {
            return false;
        }

        // 转换为下划线形式
        String underscoreKey = camelToUnderscore(camelCaseKey);

        // 检查是否存在驼峰形式或下划线形式
        return map.containsKey(camelCaseKey) || map.containsKey(underscoreKey);
    }

    /**
     * 判断JSONObject中是否存在指定键的驼峰形式或下划线形式
     * 例如：判断是否存在"loanId"或"loan_id"
     *
     * @param jsonObject 要处理的JSONObject对象
     * @param camelCaseKey 驼峰形式的键名（如"loanId"）
     * @return 如果存在任一形式则返回true，否则返回false
     */
    public static boolean containsKeyInBothFormats(JSONObject jsonObject, String camelCaseKey) {
        if (jsonObject == null) {
            return false;
        }
        return containsKeyInBothFormats((Map<String, Object>) jsonObject, camelCaseKey);
    }

    /**
     * 组合获取值的方法，先从指定map获取值，然后判断备选map中是否存在该键（驼峰或下划线形式），如果存在则使用备选map中的值
     * 例如：先从params获取"loanId"，然后判断verifyResult中是否存在"loanId"或"loan_id"，如果存在则使用verifyResult中的值
     *
     * @param primaryMap 优先获取值的Map对象
     * @param fallbackMap 备选获取值的Map对象
     * @param camelCaseKey 驼峰形式的键名（如"loanId"）
     * @return 获取到的值，如果两种形式都不存在则返回null
     */
    public static Object getValueWithFallback(Map<String, Object> primaryMap, Map<String, Object> fallbackMap, String camelCaseKey) {
        return getValueWithFallback(primaryMap, fallbackMap, camelCaseKey, null);
    }

    /**
     * 组合获取值的方法，先从指定map获取值，然后判断备选map中是否存在该键（驼峰或下划线形式），如果存在则使用备选map中的值，支持灰度功能
     * 例如：先从params获取"loanId"，然后判断verifyResult中是否存在"loanId"或"loan_id"，如果存在则使用verifyResult中的值
     *
     * @param primaryMap 优先获取值的Map对象
     * @param fallbackMap 备选获取值的Map对象
     * @param camelCaseKey 驼峰形式的键名（如"loanId"）
     * @param userKey 用户标识，用于灰度控制
     * @return 获取到的值，如果所有来源都不存在则返回null
     */
    public static Object getValueWithFallback(Map<String, Object> primaryMap, Map<String, Object> fallbackMap, String camelCaseKey, String userKey) {
        if (primaryMap == null || camelCaseKey == null || camelCaseKey.isEmpty()) {
            return null;
        }

        // 先从primaryMap中获取值
        Object value = primaryMap.get(camelCaseKey);
        
        if (JsonLogUtil.isLogEnabled()) {
            logger.info("[getValueWithFallback] 从primaryMap获取值: key={}, value={}, userKey={}", 
                    camelCaseKey, value, userKey);
        }

        // 如果fallbackMap不为空且包含该键（驼峰或下划线形式），则使用fallbackMap中的值
        if (containsKeyInBothFormats(fallbackMap, camelCaseKey)) {
            Object oldValue = value;
            value = getValueWithBothFormats(fallbackMap, camelCaseKey, userKey);
            
            if (JsonLogUtil.isLogEnabled()) {
                logger.info("[getValueWithFallback] 从fallbackMap获取值: key={}, oldValue={}, newValue={}, userKey={}, 调用栈: {}", 
                        camelCaseKey, oldValue, value, userKey, JsonLogUtil.getCallerStackTrace());
            }
        } else if (JsonLogUtil.isLogEnabled()) {
            logger.info("[getValueWithFallback] 未从fallbackMap获取值: key={}, value={}, userKey={}, 调用栈: {}", 
                    camelCaseKey, value, userKey, JsonLogUtil.getCallerStackTrace());
        }

        return value;
    }
    
    /**
     * 组合获取值的方法，先从指定JSONObject获取值，如果值不存在则从备选JSONObject中获取驼峰或下划线形式的值，支持灰度功能
     * 例如：先从params获取"loanId"，如果不存在则从verifyResult中获取"loanId"或"loan_id"
     *
     * @param primaryJson 优先获取值的JSONObject对象
     * @param fallbackJson 备选获取值的JSONObject对象
     * @param camelCaseKey 驼峰形式的键名（如"loanId"）
     * @param userKey 用户标识，用于灰度控制
     * @return 获取到的值，如果所有来源都不存在则返回null
     */
    public static Object getValueWithFallback(JSONObject primaryJson, JSONObject fallbackJson, String camelCaseKey, String userKey) {
        if (primaryJson == null) {
            return null;
        }
        return getValueWithFallback((Map<String, Object>) primaryJson,
                fallbackJson != null ? (Map<String, Object>) fallbackJson : null,
                camelCaseKey, userKey);
    }

    /**
     * 函数式接口，用于设置键值对
     */
    @FunctionalInterface
    public interface KeyValueSetter {
        /**
         * 设置键值对
         * @param key 键名
         * @param value 值
         */
        void set(String key, Object value);
    }

    /**
     * 同时设置驼峰和下划线格式的键值对，通过回调函数设置
     * 例如：同时设置"strategyType"和"strategy_type"
     *
     * @param setter 设置键值对的回调函数
     * @param camelCaseKey 驼峰形式的键名（如"strategyType"）
     * @param value 要设置的值
     */
    public static void setWithBothFormats(KeyValueSetter setter, String camelCaseKey, Object value) {
        setWithBothFormats(setter, camelCaseKey, value, null);
    }
    
    /**
     * 同时设置驼峰和下划线格式的键值对，通过回调函数设置，支持灰度功能
     * 例如：同时设置"strategyType"和"strategy_type"
     *
     * @param setter 设置键值对的回调函数
     * @param camelCaseKey 驼峰形式的键名（如"strategyType"）
     * @param value 要设置的值
     * @param userKey 用户标识，用于灰度控制
     */
    public static void setWithBothFormats(KeyValueSetter setter, String camelCaseKey, Object value, String userKey) {
        if (setter == null || camelCaseKey == null || camelCaseKey.isEmpty()) {
            return;
        }
        
        if (JsonLogUtil.isLogEnabled()) {
            logger.info("[setWithBothFormats] 开始设置键值对: camelCaseKey={}, value={}, userKey={}", 
                    camelCaseKey, value, userKey);
        }
        
        // 设置驼峰形式
        setter.set(camelCaseKey, value);
        
        // 如果需要应用灰度功能，则同时设置下划线形式
        if (JsonGrayFeatureUtil.checkGrayFeature(userKey)) {
            // 转换为下划线形式
            String underscoreKey = camelToUnderscore(camelCaseKey);
            setter.set(underscoreKey, value);
            
            if (JsonLogUtil.isLogEnabled()) {
                logger.info("[setWithBothFormats] 同时设置下划线格式: camelCaseKey={}, underscoreKey={}, value={}, 调用栈: {}", 
                        camelCaseKey, underscoreKey, value, JsonLogUtil.getCallerStackTrace());
            }
        } else if (JsonLogUtil.isLogEnabled()) {
            logger.info("[setWithBothFormats] 仅设置驼峰格式: camelCaseKey={}, value={}, 调用栈: {}", 
                    camelCaseKey, value, JsonLogUtil.getCallerStackTrace());
        }
    }

    /**
     * 批量设置多个键值对，同时使用驼峰和下划线格式，通过回调函数设置
     *
     * @param setter 设置键值对的回调函数
     * @param values 键值对Map，键为驼峰形式
     */
    public static void setAllWithBothFormats(KeyValueSetter setter, Map<String, Object> values) {
        setAllWithBothFormats(setter, values, null);
    }
    
    /**
     * 批量设置多个键值对，同时使用驼峰和下划线格式，通过回调函数设置，支持灰度功能
     *
     * @param setter 设置键值对的回调函数
     * @param values 键值对Map，键为驼峰形式
     * @param userKey 用户标识，用于灰度控制
     */
    public static void setAllWithBothFormats(KeyValueSetter setter, Map<String, Object> values, String userKey) {
        if (setter == null || values == null || values.isEmpty()) {
            return;
        }
        
        if (JsonLogUtil.isLogEnabled()) {
            logger.info("[setAllWithBothFormats] 开始批量设置键值对: values={}, userKey={}", 
                    JSON.toJSONString(values), userKey);
        }

        for (Map.Entry<String, Object> entry : values.entrySet()) {
            setWithBothFormats(setter, entry.getKey(), entry.getValue(), userKey);
        }
        
        if (JsonLogUtil.isLogEnabled()) {
            logger.info("[setAllWithBothFormats] 批量设置键值对完成, 调用栈: {}", 
                    JsonLogUtil.getCallerStackTrace());
        }
    }
    
    /**
     * 同时设置两个JSONPath路径的值，支持灰度功能
     *
     * @param rootObject 要设置的根对象
     * @param path1 第一个路径
     * @param path2 第二个路径
     * @param value 要设置的值
     * @param userKey 用户标识，用于灰度控制
     */
    public static void setJSONPathWithTwoPaths(Object rootObject, String path1, String path2, Object value, String userKey) {
        if (rootObject == null) {
            return;
        }
        
        if (JsonLogUtil.isLogEnabled()) {
            logger.info("[setJSONPathWithTwoPaths] 开始设置JSONPath: path1={}, path2={}, value={}, userKey={}", 
                    path1, path2, value, userKey);
        }

        com.alibaba.fastjson.JSONPath.set(rootObject, path1, value);
        
        // 如果需要应用灰度功能，则设置第二个路径
        if (JsonGrayFeatureUtil.checkGrayFeature(userKey)) {
            com.alibaba.fastjson.JSONPath.set(rootObject, path2, value);
            
            if (JsonLogUtil.isLogEnabled()) {
                logger.info("[setJSONPathWithTwoPaths] 同时设置两个路径: path1={}, path2={}, value={}, 调用栈: {}", 
                        path1, path2, value, JsonLogUtil.getCallerStackTrace());
            }
        } else if (JsonLogUtil.isLogEnabled()) {
            logger.info("[setJSONPathWithTwoPaths] 仅设置第一个路径: path1={}, value={}, 调用栈: {}", 
                    path1, value, JsonLogUtil.getCallerStackTrace());
        }
    }
    
    /**
     * 从嵌套JSON结构中获取值，并同时设置驼峰和下划线格式的键值对，支持灰度功能
     * 例如：从currLineManagement.reasonCode.source_type获取值，然后同时设置sourceType和source_type
     *
     * @param setter 设置键值对的回调函数
     * @param source 源数据对象
     * @param nestedKey 嵌套的键名（如"currLineManagement"）
     * @param subKey 子键名（如"reasonCode"）
     * @param targetKey 目标键名（如"source_type"）
     * @param camelCaseResultKey 结果中的驼峰形式键名（如"sourceType"）
     * @param userKey 用户标识，用于灰度控制
     * @return 是否成功获取并设置值
     */
    public static boolean getNestedValueAndSetBothFormats(KeyValueSetter setter, JSONObject source,
                                                          String nestedKey, String subKey, String targetKey, String camelCaseResultKey, String userKey) {
        if (setter == null || source == null || nestedKey == null || subKey == null ||
                targetKey == null || camelCaseResultKey == null) {
            return false;
        }

        // 尝试获取嵌套对象
        Object nestedObj = getValueWithBothFormats(source, nestedKey, userKey);
        if (nestedObj == null) {
            return false;
        }
        JSONObject nestedJson = TypeUtils.castToJavaBean(nestedObj, JSONObject.class);

        // 尝试获取子对象
        Object subObj = getValueWithBothFormats(nestedJson, subKey, userKey);
        if (subObj == null) {
            return false;
        }
        JSONObject subJson = TypeUtils.castToJavaBean(subObj, JSONObject.class);

        // 尝试获取目标值
        if (!subJson.containsKey(targetKey)) {
            return false;
        }

        Object value = subJson.get(targetKey);

        // 同时设置驼峰和下划线格式
        setWithBothFormats(setter, camelCaseResultKey, value, userKey);
        
        // 添加日志打印
        if (JsonLogUtil.isLogEnabled()) {
            logger.info("[getNestedValueAndSetBothFormats] 嵌套获取并设置: nestedKey={}, subKey={}, targetKey={}, resultKey={}, value={}, userKey={}, 调用栈: {}", 
                    nestedKey, subKey, targetKey, camelCaseResultKey, value, userKey, JsonLogUtil.getCallerStackTrace());
        }

        return true;
    }
    
    /**
     * 从嵌套JSON结构中获取值，并同时设置驼峰和下划线格式的键值对，支持灰度功能
     * 简化版本，直接使用JSONObject的put方法
     *
     * @param result 结果JSONObject
     * @param source 源数据对象
     * @param nestedKey 嵌套的键名（如"currLineManagement"）
     * @param subKey 子键名（如"reasonCode"）
     * @param targetKey 目标键名（如"source_type"）
     * @param camelCaseResultKey 结果中的驼峰形式键名（如"sourceType"）
     * @param userKey 用户标识，用于灰度控制
     * @return 是否成功获取并设置值
     */
    public static boolean getNestedValueAndSetBothFormats(JSONObject result, JSONObject source,
                                                          String nestedKey, String subKey, String targetKey, String camelCaseResultKey, String userKey) {
        if (result == null) {
            return false;
        }
        return getNestedValueAndSetBothFormats(result::put, source, nestedKey, subKey, targetKey, camelCaseResultKey, userKey);
    }

    /**
     * 检查字符串是否是下划线格式
     * 例如: "his_apl_lck_score" 返回 true，"hisAplLckScore" 返回 false
     *
     * @param key 要检查的字符串
     * @return 如果是下划线格式返回true，否则返回false
     */
    public static boolean isUnderscoreFormat(String key) {
        if (key == null || key.isEmpty()) {
            return false;
        }

        // 检查是否包含大写字母
        if (key.chars().anyMatch(Character::isUpperCase)) {
            return false;
        }

        // 检查下划线使用是否正确
        if (key.contains("_")) {
            // 不允许连续下划线
            if (key.contains("__")) {
                return false;
            }
            // 不允许以下划线开头或结尾
            if (key.startsWith("_") || key.endsWith("_")) {
                return false;
            }
            // 下划线分割的每个部分都必须是小写字母
            return Arrays.stream(key.split("_"))
                    .allMatch(part -> !part.isEmpty() && part.chars().allMatch(ch ->
                            Character.isLowerCase(ch) || Character.isDigit(ch)));
        }

        // 如果不包含下划线，则必须全部是小写字母或数字
        return key.chars().allMatch(ch -> Character.isLowerCase(ch) || Character.isDigit(ch));
    }

    /**
     * 从verifyResult中获取reasonCode，支持从currLineManagement嵌套路径或根路径获取，同时处理驼峰和下划线格式
     * 例如：先尝试从verifyResult.currLineManagement.reasonCode获取，如果不存在则尝试从verifyResult.reasonCode获取
     * 同时处理下划线格式：verifyResult.curr_line_management.reason_code和verifyResult.reason_code
     *
     * @return 合并后的reasonCode对象
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> getNestedMapValue(Map<String, Object> rootMap, String valueKey, String parentKey){
        return getNestedMapValue(rootMap, valueKey, parentKey, null);
    }

    /**
     * 从verifyResult中获取reasonCode，支持从currLineManagement嵌套路径或根路径获取，同时处理驼峰和下划线格式
     * 例如：先尝试从verifyResult.currLineManagement.reasonCode获取，如果不存在则尝试从verifyResult.reasonCode获取
     * 同时处理下划线格式：verifyResult.curr_line_management.reason_code和verifyResult.reason_code
     *
     * @param userKey 用户标识，用于灰度控制
     * @return 合并后的reasonCode对象
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> getNestedMapValue(Map<String, Object> rootMap, String valueKey, String parentKey, String userKey) {
        if (rootMap == null) {
            return null;
        }

        if (JsonLogUtil.isLogEnabled()) {
            logger.info("[getNestedValue] 开始获取嵌套值: valueKey={}, parentKey={}, userKey={}", valueKey, parentKey, userKey);
        }

        // 1. 先从rootMap中获取驼峰格式的值
        Map<String, Object> value = (Map<String, Object>) rootMap.get(valueKey);

        // 2. 如果存在父节点，则从中获取值覆盖原值
        if (rootMap.containsKey(parentKey)) {
            Map<String, Object> parentNode = (Map<String, Object>) rootMap.get(parentKey);
            if (parentNode != null && parentNode.containsKey(valueKey)) {
                value = (Map<String, Object>) parentNode.get(valueKey);
            }
        }

        // 3. 获取下划线格式的值
        String underscoreValueKey = camelToUnderscore(valueKey);
        String underscoreParentKey = camelToUnderscore(parentKey);
        Map<String, Object> underscoreValue = (Map<String, Object>) rootMap.get(underscoreValueKey);

        // 4. 如果存在下划线格式的父节点，则从中获取值覆盖原值
        if (rootMap.containsKey(underscoreParentKey)) {
            Map<String, Object> underscoreParentNode = (Map<String, Object>) rootMap.get(underscoreParentKey);
            if (underscoreParentNode != null && underscoreParentNode.containsKey(underscoreValueKey)) {
                underscoreValue = (Map<String, Object>) underscoreParentNode.get(underscoreValueKey);
            }
        }

        // 5. 合并两个结果
        JSONObject result = mergeResults(value, underscoreValue, userKey);

        if (JsonLogUtil.isLogEnabled()) {
            logger.info("[getNestedValue] 获取嵌套值结果: result={}, valueKey={}, parentKey={}, userKey={}, 调用栈: {}", 
                    JSON.toJSONString(result), valueKey, parentKey, userKey, JsonLogUtil.getCallerStackTrace());
        }

        return result;
    }

    /**
     * 从嵌套对象中获取值，先尝试使用驼峰格式获取，如果没有值再尝试使用下划线格式获取
     * 例如：先尝试从verifyResult.currLineManagement.loanRate获取，再尝试从verifyResult.curr_line_management.loan_rate获取
     *
     * @param verifyResult 要处理的JSON对象
     * @param nestedKey 嵌套对象的键名（如"currLineManagement"或"curr_line_management"）
     * @param subKey 嵌套对象中的子键名（如"loanRate"或"loan_rate"）
     * @return 获取到的值，如果两种形式都不存在则返回null
     */
    public static Object getNestedObject(Map<String, Object>  verifyResult, String nestedKey, String subKey){
        return getNestedObject(verifyResult, nestedKey, subKey, null);
    }

    /**
     * 从嵌套对象中获取值，先尝试使用驼峰格式获取，如果没有值再尝试使用下划线格式获取
     * 例如：先尝试从verifyResult.currLineManagement.loanRate获取，再尝试从verifyResult.curr_line_management.loan_rate获取
     *
     * @param verifyResult 要处理的JSON对象
     * @param nestedKey 嵌套对象的键名（如"currLineManagement"或"curr_line_management"）
     * @param subKey 嵌套对象中的子键名（如"loanRate"或"loan_rate"）
     * @return 获取到的值，如果两种形式都不存在则返回null
     */
    public static Object getNestedObject(Map<String, Object>  verifyResult, String nestedKey, String subKey, String userKey) {
        if (verifyResult == null || nestedKey == null || subKey == null) {
            return null;
        }

        // 记录原始数据
        if (JsonLogUtil.isLogEnabled()) {
            logger.info("[getNestedObject] 原始数据: verifyResult={}, nestedKey={}, subKey={}, userKey={}",
                    JSON.toJSONString(verifyResult), nestedKey, subKey, userKey);
        }

        // 转换为下划线形式
        String underscoreNestedKey = camelToUnderscore(nestedKey);
        String underscoreSubKey = camelToUnderscore(subKey);
        Object value = null;

        // 1. 先尝试使用驼峰格式获取值
        if (verifyResult.containsKey(nestedKey)) {
            Object nestedObj = verifyResult.get(nestedKey);
            if (nestedObj instanceof Map) {
                Map<String, Object> nestedMap = (Map<String, Object>) nestedObj;
                if (nestedMap.containsKey(subKey)) {
                    value = nestedMap.get(subKey);
                }
            }
        }

        // 2. 如果驼峰格式不存在或未获取到值，尝试使用下划线格式获取值
        if (value == null) {
            if (verifyResult.containsKey(underscoreNestedKey)) {
                Object nestedObj = verifyResult.get(underscoreNestedKey);
                if (nestedObj instanceof Map) {
                    Map<String, Object> nestedMap = (Map<String, Object>) nestedObj;
                    if (nestedMap.containsKey(underscoreSubKey)) {
                        value = nestedMap.get(underscoreSubKey);
                    }
                }
            }
        }

        if (JsonLogUtil.isLogEnabled()) {
            logger.info("[getNestedObject] 获取结果: value={}, 调用栈: {}",
                    value, JsonLogUtil.getCallerStackTrace());
        }

        return value;
    }

    /**
     * 将JSONObject第一层的键名转换为下划线格式
     * 例如：将"isPassed"键转换为"is_passed"键
     *
     * @param jsonObject 要处理的JSON对象
     * @return 处理后的JSON对象（为避免副作用，返回新对象）
     */
    public static JSONObject convertToCamelCase(JSONObject jsonObject) {
        if (jsonObject == null || jsonObject.isEmpty()) {
            return jsonObject;
        }

        return convertToCamelCase((Map<String, Object>) jsonObject);
    }

    /**
     * 将Map第一层的键名转换为下划线格式
     * 例如：将"isPassed"键转换为"is_passed"键
     *
     * @param map 要处理的Map对象
     * @return 处理后的JSONObject对象
     */
    public static JSONObject convertToCamelCase(Map<String, Object> map) {
        if (map == null || map.isEmpty()) {
            return new JSONObject();
        }

        // 创建新的JSONObject以保存转换结果
        JSONObject result = new JSONObject();

        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            // 将键名转换为下划线格式
            String underscoreKey = camelToUnderscore(key);
            result.put(underscoreKey, value);
        }

        return result;
    }
}