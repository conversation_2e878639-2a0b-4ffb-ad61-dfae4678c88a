package com.youxin.risk.commons.event.impl;

/**
 * @InterfaceName EventService
 * @Description 事件管理服务接口
 * <AUTHOR>
 * @Date 2021/12/13 11:36 上午
 **/
public interface EventService {
    /**
     * 发布一个事件,会顺序调用所有的监听器
     * @param value
     * @param parameters
     */
    void fire(String value, Object... parameters);

    /**
     * 发布一个事件,会异步调用所有的监听器
     * @param value
     * @param parameters
     */
    void fireAsynchronous(String value, Object... parameters);

}
