package com.youxin.risk.commons.vo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.youxin.risk.commons.model.Event;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018年06月06日下午13:22:41
 */
@Document(collection = "StrategyInvoke")
public class StrategyVo {
    private BigInteger id;

    //业务线
    private String sourceSystem;

    private String userKey;

    private String loanKey;

    private String step;

    // 策略类型
    private String strategyType;

    /**
     * 策略子类型，人人贷业务线值同strategyType，好分期不同
     * 例如：strategyType=VERIFY_HAOHUAN，type可能是VERIFY_HAOHUAN_A、VERIFY_HAOHUAN_B等
     */
    private String type;

    private String eventCode;

    private String sessionId;

    //操作者
    private String developer;

    //策略版本
    private String strategyVersion;

    //代码ID
    private Long strategyCodeId;

    private String stragegyCode;

    private String xml;

    private String result;

    private String remark;

    private Map dataInput;

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }


    private Date createTime;

    // 对应风险模型
    private String modelId;
    // 序号
    private Boolean riskApproval;
    // 风险审批
    private Boolean businessApproval;
    // 业务审批
    private Boolean managerApproval;
    // 领导审批
    private Integer sequenceNo;

    private String rowKey;

    private String variableMapStr;

    private String ruleSetMapStr;

    private String mirrorResult;

    private String mirrorRuleSetMapStr;

    private String variableRequestParams;


    /**
     * 构建策略vo
     *
     * @param strategyCalcRequestVo
     * @param verifyResultInfo
     * @param event
     * @return
     */
    public static StrategyVo buildStrategyVo(StrategyCalcRequestVo strategyCalcRequestVo, String verifyResultInfo,
                                        Event event) {
        StrategyVo strategyVo = new StrategyVo();
        BeanUtils.copyProperties(strategyCalcRequestVo, strategyVo);
        strategyVo.setCreateTime(new Date());
        strategyVo.setUserKey(event.getUserKey());
        strategyVo.setVariableMapStr(JSON.toJSONString(strategyCalcRequestVo.getVariableMap()));
        strategyVo.setRuleSetMapStr(JSON.toJSONString(strategyCalcRequestVo.getRuleSetMap()));
        if (event.isMirror()) {
            strategyVo.setMirrorResult(verifyResultInfo);
            strategyVo.setResult(JSON.toJSONString(event.getOnlineVerifyResult()));
        } else {
            strategyVo.setResult(verifyResultInfo);
        }
        strategyVo.setDataInput(event.dataInput());
        strategyVo.setStrategyCodeId(strategyCalcRequestVo.getPolicyCodeId());
        strategyVo.setVariableRequestParams(event.clearVariableRequestParamsStr());
        return strategyVo;
    }

    public String getVariableRequestParams() {
        return variableRequestParams;
    }

    public void setVariableRequestParams(String variableRequestParams) {
        this.variableRequestParams = variableRequestParams;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public Boolean getRiskApproval() {
        return riskApproval;
    }

    public void setRiskApproval(Boolean riskApproval) {
        this.riskApproval = riskApproval;
    }

    public Boolean getBusinessApproval() {
        return businessApproval;
    }

    public void setBusinessApproval(Boolean businessApproval) {
        this.businessApproval = businessApproval;
    }

    public Boolean getManagerApproval() {
        return managerApproval;
    }

    public void setManagerApproval(Boolean managerApproval) {
        this.managerApproval = managerApproval;
    }

    public Integer getSequenceNo() {
        return sequenceNo;
    }

    public void setSequenceNo(Integer sequenceNo) {
        this.sequenceNo = sequenceNo;
    }

    public String getStragegyCode() {
        return this.stragegyCode;
    }

    public void setStragegyCode(String stragegyCode) {
        this.stragegyCode = stragegyCode;
    }

    public String getSourceSystem() {
        return this.sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem;
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getEventCode() {
        return eventCode;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getDeveloper() {
        return this.developer;
    }

    public void setDeveloper(String developer) {
        this.developer = developer;
    }

    public String getStrategyVersion() {
        return this.strategyVersion;
    }

    public void setStrategyVersion(String strategyVersion) {
        this.strategyVersion = strategyVersion;
    }

    public Long getStrategyCodeId() {
        return this.strategyCodeId;
    }

    public void setStrategyCodeId(Long strategyCodeId) {
        this.strategyCodeId = strategyCodeId;
    }

    public String getXml() {
        return this.xml;
    }

    public void setXml(String xml) {
        this.xml = xml;
    }

    public String getUserKey() {
        return this.userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey;
    }

    public String getLoanKey() {
        return this.loanKey;
    }

    public void setLoanKey(String loanKey) {
        this.loanKey = loanKey;
    }

    public String getStep() {
        return this.step;
    }

    public void setStep(String step) {
        this.step = step;
    }

    public String getResult() {
        return this.result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public BigInteger getId() {
        return this.id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getStrategyType() {
        return strategyType;
    }

    public String getRealStrategyType(){
        return StringUtils.isNotBlank(strategyType) ? strategyType : type;
    }

    public void setStrategyType(String strategyType) {
        this.strategyType = strategyType;
    }

    public String getRowKey() {
        return rowKey;
    }

    public void setRowKey(String rowKey) {
        this.rowKey = rowKey;
    }

    public String getVariableMapStr() {
        return variableMapStr;
    }

    public void setVariableMapStr(String variableMapStr) {
        this.variableMapStr = variableMapStr;
    }

    public String getRuleSetMapStr() {
        return ruleSetMapStr;
    }

    public void setRuleSetMapStr(String ruleSetMapStr) {
        this.ruleSetMapStr = ruleSetMapStr;
    }

    public String getMirrorResult() {
        return mirrorResult;
    }

    public void setMirrorResult(String mirrorResult) {
        this.mirrorResult = mirrorResult;
    }

    public String log() {
        return new ToStringBuilder(this)
                .append("id", id)
                .append("sourceSystem", sourceSystem)
                .append("userKey", userKey)
                .append("loanKey", loanKey)
                .append("step", step)
                .append("strategyType", strategyType)
                .append("type", type)
                .append("developer", developer)
                .append("strategyVersion", strategyVersion)
                .append("strategyCodeId", strategyCodeId)
//                .append("xml", xml)
                .append("result", result)
                .append("remark", remark)
                .append("createTime", createTime)
                .toString();
    }

    /**
     * 数据入参
     * @param dataInput
     */
    public void setDataInput(Map dataInput) {
        this.dataInput = dataInput;
    }

    public Map getDataInput() {
        return dataInput;
    }

    @JSONField(serialize = false)
    public Map<String, Object> getVariableMap() {
        if (StringUtils.isBlank(variableMapStr)) {
            return new HashMap<>();
        }
        return JSON.parseObject(variableMapStr, Map.class);
    }
}
