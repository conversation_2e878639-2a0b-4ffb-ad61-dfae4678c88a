package com.youxin.risk.commons.dao.datacenter;

import com.youxin.risk.commons.model.datacenter.DcSubmitContactInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface DcSubmitAddContactInfoMapper {

    int deleteByPrimaryKey(Integer id);

    Long insert(DcSubmitContactInfo record);

    int insertSelective(DcSubmitContactInfo record);

    DcSubmitContactInfo selectByPrimaryKey(long id);

    int updateByPrimaryKeySelective(DcSubmitContactInfo record);

    int updateByPrimaryKey(DcSubmitContactInfo record);

    void insertBatch(List<DcSubmitContactInfo> list);

    DcSubmitContactInfo getByOperationId(Long operationId);

    DcSubmitContactInfo getLastByUserKey(@Param("userKey") String userKey);

    List<DcSubmitContactInfo> getLastsByUserKey(@Param("userKey") String userKey);

    // 修数用的方法，勿使用
    List<Map<String, Object>> queryById(Map<String, Object> map);

    // 修数用的方法，勿使用
    int updateById(Map<String, Object> map);
}