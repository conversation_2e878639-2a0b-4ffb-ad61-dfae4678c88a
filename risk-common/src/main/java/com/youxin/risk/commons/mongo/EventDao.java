//package com.youxin.risk.commons.mongo;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.base.Stopwatch;
//import com.google.common.collect.Lists;
//import com.google.common.collect.MapDifference;
//import com.youxin.apollo.client.YouxinApolloClient;
//import com.youxin.risk.commons.constants.ApolloNamespace;
//import com.youxin.risk.commons.model.Event;
//import com.youxin.risk.commons.utils.LoggerProxy;
//import com.youxin.risk.commons.utils.MapUtils;
//import com.youxin.risk.commons.utils.XmlUtils;
//import com.youxin.risk.commons.vo.EventVo;
//import org.apache.commons.lang3.StringUtils;
//import org.bson.BsonSerializationException;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.dao.DuplicateKeyException;
//import org.springframework.data.domain.Sort;
//import org.springframework.data.mongodb.core.FindAndModifyOptions;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.query.Criteria;
//import org.springframework.data.mongodb.core.query.Query;
//import org.springframework.data.mongodb.core.query.Update;
//import org.springframework.util.CollectionUtils;
//
//import java.lang.reflect.Field;
//import java.nio.charset.StandardCharsets;
//import java.util.ArrayList;
//import java.util.Collections;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//import java.util.TreeMap;
//import java.util.concurrent.TimeUnit;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR>
// * @date 2018/10/18 11:28
// */
//public class EventDao1 extends BaseMongoDao {
//
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(EventDao1.class);
//    public static final String VERIFY_RESULT = "verify_result";
//    public static final String TEMPLATE_SOURCE = "template";
//    public static final String SHARDING_TEMPLATE_SOURCE = "shardingTemplate";
//
//    @Autowired
//    @Qualifier("shardingMongoTemplate")
//    private MongoTemplate shardingTemplate;
//
//    private static int tryTimes = 16;
//
//    private static int tryTimes2 = 5;
//
//    public EventDao1() {
//        collectionName = "risk_event";
//    }
//
//    @Override
//    @Deprecated
//    public void insert(Object obj) {
//        if (obj instanceof  EventVo){
//            try{
//                EventVo eventVo = (EventVo) obj;
//                Event event = eventVo.getEvent();
//                String eventCode = event == null? "null": event.getEventCode();
//                LoggerProxy.info("insertToTransferMongo", LOGGER, "eventCoce: {}", eventCode);
//                this.template.insert(obj, this.collectionName);
//
//                saveVerifyResultMongo(eventVo);
//            } catch(DuplicateKeyException e){
//                LoggerProxy.warn("duplicateKeyWarn",LOGGER,e.getMessage(),e);
//            }
//        }
//    }
////    public void saveOrUpdateShardingMongo(EventVo eventVo) throws Exception {
////        Query query = new Query(Criteria.where("sessionId").is(eventVo.getSessionId()));
////        Boolean exisEvent = shardingTemplate.exists(query,EventVo.class,this.collectionName);
////        if(!exisEvent){
////            this.shardingTemplate.insert(eventVo, this.collectionName);
////        }else{
////            Update update = new Update();
////            Field[] fields = EventVo.class.getDeclaredFields();
////            for (Field field : fields) {
////                field.setAccessible(true);
////                Object value = field.get(eventVo);
////                update.set(field.getName(), value);
////            }
////            FindAndModifyOptions options = FindAndModifyOptions.options().upsert(true);
////            shardingTemplate.findAndModify(query,update,options,EventVo.class,this.collectionName);
////        }
////    }
//
//
//    /**
//     * 写入VerifyResult
//     * @param eventVo
//     */
//    @Deprecated
//    public void saveVerifyResultMongo(EventVo eventVo){
//        try {
//            EventVo verifyResult = new EventVo();
//            verifyResult.setUserKey(eventVo.getUserKey());
//            verifyResult.setCreateTime(eventVo.getCreateTime());
//            verifyResult.setId(eventVo.getId());
//            verifyResult.setSessionId(eventVo.getSessionId());
//            verifyResult.setLoanKey(eventVo.getLoanKey());
//            verifyResult.setSourceSystem(eventVo.getSourceSystem());
//            Event event = new Event();
//            event.setParams(eventVo.getEvent().getParams());
//            /** 如果eventCode是:haoHuanVerify,存对应的xml文件 VerifyResultXmlServiceAdapter使用 **/
//            if ("haoHuanVerify".equals(eventVo.getEvent().getEventCode())) {
//                event.setXml(eventVo.getEvent().getXml());
//            }
//            event.setVerifyResult(eventVo.getEvent().getVerifyResult());
//            verifyResult.setEvent(event);
//            this.template.insert(verifyResult, VERIFY_RESULT);
//        }catch (Exception e){
//            LoggerProxy.error("saveOrUpdateVerifyResultMongo",LOGGER,e.getMessage(),e);
//        }
//    }
//
//    /**
//     * 更新新表 VerifyResult
//     * @param eventVo
//     */
////    @Deprecated
////    public void updateVerifyResultMongo(EventVo eventVo){
////        try {
////            Query query = new Query(Criteria.where("sessionId").is(eventVo.getSessionId()));
////            /** 如果eventCode是:haoHuanVerify,存对应的xml文件 **/
////            Update update = new Update();
////            if ("haoHuanVerify".equals(eventVo.getEvent().getEventCode())) {
////                update.set("event.xml",eventVo.getEvent().getXml());
////            }
////            update.set("event.params",eventVo.getEvent().getParams());
////            update.set("event.verifyResult",eventVo.getEvent().getVerifyResult());
////            /** 增加更新时间 **/
////            update.set("updateTime",new Date());
////            this.template.upsert(query,update, VERIFY_RESULT);
////        }catch (Exception e){
////            LoggerProxy.error("updateVerifyResultMongo",LOGGER,e.getMessage(),e);
////        }
////    }
//
//    /**
//     * 过渡阶段 数据在新旧节点都更新
//     *
//     * @param vo
//     */
////    @Deprecated
////    public void updateBySessionId(EventVo vo) {
////        Query query = new Query(Criteria.where("sessionId").is(vo.getSessionId()));
////        Update update = new Update();
////        update.set("event", vo.getEvent());
////        updateWithRetry(query, update, this.collectionName, vo.getEvent());
////    }
//
//
////    private void updateWithRetry(Query query ,Update update,String collectionName,Event event){
////        Map<String, Object> verifyResult = event.getVerifyResult();
////        LoggerProxy.info("tempMongoLog", LOGGER, "LoanKey={}， verifyResult={}", event.getLoanKey(), JSON.toJSONString(verifyResult));
////        int theTryTimes = getTryTimes(0);
////        for(int i=0 ; i <= theTryTimes; i++){
////            try {
////                LoggerProxy.info("updateToTransferMongo", LOGGER, "eventCode: {}", event.getEventCode());
////                Stopwatch stopwatch = Stopwatch.createStarted();
////                template.updateFirst(query, update, collectionName);
////                long elapsed = stopwatch.elapsed(TimeUnit.MILLISECONDS);
////                LoggerProxy.info("updateToTransferMongoTime", LOGGER, elapsed + "");
////                return;
////            } catch (BsonSerializationException e) {
////                //判断错误日志包含mongo16m信息
////                if (!e.getMessage().contains("larger than maximum of")) {
////                    LOGGER.info("updateWithRetry event tryTimes:{},userKey={},errorMsg={}", i,event.getUserKey(),e.getMessage());
////                    throw e;
////                }
////                if (i == theTryTimes) {
////                    String errorMsg = "update data to mongo error(data larger than 16m),tryTimes all used, collectionName=" + this.collectionName;
////                    throwException(e, errorMsg);
////                }
////
////                // 超16m处理
////                boolean dealFlag = simplifyEvent(event);
////                if(!dealFlag){
////                    throw e;
////                }
////
////                update.set("event", event);
////            }
////        }
////    }
//
////    public boolean simplifyEvent(Event event){
////        boolean dealFlag = true;
////        String maxKey = findMaxField(event);
////        BigField bigField = BigField.getByName(maxKey);
////        if (null == bigField) {
////            String errorMsg = "mongo16m 超大数据需要手动处理, maxKey=" + maxKey;
////            LoggerProxy.error("simplifyEventError", LOGGER, errorMsg);
////            dealFlag = false;
////        }else {
////            boolean truncateRet = truncateEvent(bigField.name(), bigField.getPath(), event);
////            if (!truncateRet) {
////                String errorMsg = "mongo16m 超大数据自动处理失败, maxKey=" + maxKey;
////                LoggerProxy.error("simplifyEventError", LOGGER, errorMsg);
////                dealFlag = false;
////            }else {
////                LoggerProxy.info("simplifyEvent", LOGGER, "mongo16m 超大数据自动处理成功");
////            }
////        }
////        return dealFlag;
////    }
//
////    public int getTryTimes(int flag) {
////        int theTryTimes = tryTimes;
////        String tryTimesStr = "3";
////        if(flag == 0) {
////            tryTimesStr = YouxinApolloClient.getByNameSpace(ApolloNamespace.engineSpace, "event.mongo.update.tryTimes"
////                    , String.valueOf(tryTimes));
////        }else if(flag == 1){
////            tryTimesStr = YouxinApolloClient.getByNameSpace(ApolloNamespace.engineSpace, "event.mongo.update.setData.tryTimes"
////                    , String.valueOf(tryTimes2));
////        }
////
////        if (StringUtils.isNotBlank(tryTimesStr)) {
////            theTryTimes = Integer.parseInt(tryTimesStr);
////        }
////        LOGGER.info("event mongo update tryTimes:{}", theTryTimes);
////        return theTryTimes;
////    }
//
////    private void throwException(BsonSerializationException e, String errorMsg) {
////        LOGGER.error(errorMsg);
////        throw e;
////    }
//
//
////    private String findMaxField(Event event) {
////        int maxLength = 0;
////        String maxKey = null;
////        for (String key : event.getDataVo().keySet()) {
////            int length = JSON.toJSONString(event.getDataVo().get(key)).getBytes(StandardCharsets.UTF_8).length;
////            if (length >= maxLength) {
////                maxLength = length;
////                maxKey = key;
////            }
////        }
////        Object content = event.getDataVo().get(maxKey);
////        String body = JSON.toJSONString(content);
////        int subLength = 1000;
////        if (body.length() > subLength) {
////            body = body.substring(0, subLength) + "......";
////        }
////        LOGGER.error("mongo数据超过16M,sessionId={},最大字段名称={},最大字段字节数={},class类型={},数据内容={}",
////                event.getSessionId(), maxKey, maxLength, content.getClass(), body);
////        try{
////            Map<String, Object> map = XmlUtils.flatXMLString2Map(event.getXml());
////            Map<String,Object> standards = (Map)((Map)map.get("feature")).get("standard");
////            TreeMap<String, Integer> sdMap = new TreeMap<>();
////            TreeMap<String, Integer> drMap = new TreeMap<>();
////            for(String featureName: standards.keySet()){
////                if (featureName.endsWith("sd")) {
////                    int length = JSON.toJSONString(standards.get(featureName)).getBytes(StandardCharsets.UTF_8).length;
////                    sdMap.put(featureName, length);
////                }
////                if (featureName.endsWith("dr")) {
////                    int length = JSON.toJSONString(standards.get(featureName)).getBytes(StandardCharsets.UTF_8).length;
////                    drMap.put(featureName, length);
////                }
////            }
////            List<Map.Entry<String, Integer>> sdList = new ArrayList<>(sdMap.entrySet());
////            List<Map.Entry<String, Integer>> drList = new ArrayList<>(drMap.entrySet());
////            Collections.sort(sdList, (o1, o2) -> o1.getValue().compareTo(o2.getValue()));
////            Collections.sort(drList, (o1, o2) -> o1.getValue().compareTo(o2.getValue()));
////            String biggestFeatureSd = "";
////            String biggestFeatureLengthSd = "";
////            String biggestFeatureDr = "";
////            String biggestFeatureLengthDr = "";
////            if (sdList.size() > 3) {
////                biggestFeatureSd = sdList.get(sdList.size()-1).getKey() + "," + sdList.get(sdList.size()-2).getKey() + "," + sdList.get(sdList.size()-3).getKey();
////                biggestFeatureLengthSd = sdList.get(sdList.size()-1).getValue() + "," + sdList.get(sdList.size()-2).getValue() + "," + sdList.get(sdList.size()-3).getValue();
////            }
////            if (drList.size() > 3) {
////                biggestFeatureDr = drList.get(drList.size()-1).getKey() + "," + drList.get(drList.size()-2).getKey() + "," + drList.get(drList.size()-3).getKey();
////                biggestFeatureLengthDr = drList.get(drList.size()-1).getValue() + "," + drList.get(drList.size()-2).getValue() + "," + drList.get(drList.size()-3).getValue();
////            }
////            LOGGER.error("mongo数据超过16M,sessionId={},前三标准特征名称={},前三标准特征字节数={},前三衍生特征名称={},前三衍生特征字节数={}",
////                    event.getSessionId(), biggestFeatureSd, biggestFeatureLengthSd, biggestFeatureDr, biggestFeatureLengthDr);
////        }catch (Exception e){
////            LOGGER.error("mongo数据超过16M,sessionId={},获取最大特征失败",
////                    event.getSessionId(), e);
////        }
////        return maxKey;
////    }
//
//
//    /**
//     * 对json数据内的数组进行截断操作
////     *
////     * @param jsonObject
////     * @param path
//     * @return
//     */
////    private void truncateOps(JSONObject jsonObject, String path, int order) {
////        String[] pathArr = path.split("#");
////        if (pathArr.length == 0) {
////            pathArr = new String[]{path};
////        }
////        JSONObject tmp = jsonObject;
////        JSONArray opTarget = null;
////        for (int i = 0; i < pathArr.length; i++) {
////            if (i == pathArr.length - 1) {
////                opTarget = tmp.getJSONArray(pathArr[i]);
////            } else {
////                try {
////                    tmp = tmp.getJSONObject(pathArr[i]);
////                } catch (Exception e) {
////                    tmp = tmp.getJSONArray(pathArr[i]).getJSONObject(0);
////                }
////            }
////        }
////
////        if (null == opTarget) {
////            return;
////        }
////        reduceDataSize(opTarget, order);
////    }
//
//    private void reduceDataSize(JSONArray opTarget, int order) {
//        int size = opTarget.size();
//        if (size / 2 > 0) {
//            if(1 == order){
//                opTarget.subList(0, size / 2).clear();
//            }
//            if(-1 == order){
//                opTarget.subList(size / 2, size - 1).clear();
//            }
//        }
//    }
//
////    private String truncateOps(String jsonStr, String path, int order) {
////        Object jsonObject = JSON.parse(jsonStr);
////        if (jsonObject instanceof JSONObject) {
////            JSONObject tmp = (JSONObject) jsonObject;
////            truncateOps(tmp, path, order);
////            return tmp.toJSONString();
////        } else if (jsonObject instanceof  JSONArray) {
////            JSONArray tmp = (JSONArray) jsonObject;
////            reduceDataSize(tmp, order);
////            return tmp.toJSONString();
////        } else {
////            return jsonStr;
////        }
////    }
//
//
////    private void truncateOps(Map map, String path) {
////        String[] pathArr = path.split("#");
////        if (pathArr.length == 0) {
////            pathArr = new String[]{path};
////        }
////        List target = null;
////        for (int i = 0; i < pathArr.length; i++) {
////            if (i == pathArr.length - 1) {
////                target = (List) map.get(pathArr[i]);
////            } else {
////                try {
////                    map = (Map) map.get(pathArr[i]);
////                } catch (Exception e) {
////                    List list = (List) map.get(pathArr[i]);
////                    map = (Map) list.get(0);
////                }
////            }
////        }
////        if (null == target) {
////            return;
////        }
////        int size = target.size();
////        if (size / 2 > 0) {
////            target.subList(0, size / 2).clear();
////        }
////    }
//
////    public Object queryMaxKey(Map dataMap, Event event){
////        Object maxBigFieldKey = null;
////        Object maxKey = "";
////        int maxBigFieldLength = 0;
////        int maxLength = 0;
////        for (Object key : dataMap.keySet()) {
////            int length = JSON.toJSONString(dataMap.get(key)).getBytes(StandardCharsets.UTF_8).length;
////            // 获取符合枚举值的最大key
////            if(BigFieldThirdParty.contains((String)key)){
////                if (length >= maxBigFieldLength) {
////                    maxBigFieldLength = length;
////                    maxBigFieldKey = key;
////                }
////            }
////            // 获取最大的key
////            if(length >= maxLength){
////                maxLength = length;
////                maxKey = key;
////            }
////        }
////
////        if(maxKey.equals(maxBigFieldKey)){
////            LoggerProxy.info("queryMaxKeyEquals", LOGGER,
////                    "Key={}, Length={}, userKey={}"
////                    , maxBigFieldKey, maxBigFieldLength, event.getUserKey());
////
////        }else {
////            LoggerProxy.warn("queryMaxKeyNoEquals", LOGGER,
////                    "maxBigFieldKey={}, maxBigFieldLength={}, maxKey={}, maxLength={}, userKey={}",
////                    maxBigFieldKey, maxBigFieldLength, maxKey, maxLength, event.getUserKey());
////        }
////        return maxBigFieldKey;
////    }
//
//
//    /**
//     * 对thirdParty字段单独处理
//     *
//     * @param event
//     */
////    private boolean thirdPartyMapOpt(Event event) {
////        Object originalDataObject = event.getDataVo().get(BigField.thirdPartyData.name());
////        Map dataMap = (Map) originalDataObject;
////
////        // 获取枚举值中的最大的key
////        Object maxKey = queryMaxKey(dataMap, event);
////        if (null == maxKey) {
////            return false;
////        }
////
////        Object content = dataMap.get(maxKey);
////        try {
////            String thirdPartyDataKeys = YouxinApolloClient.getByNameSpace(ApolloNamespace.engineSpace, "event.mongo.thirdPartyData.keys.new", "");
////            List<Map> thirdPartyDataKeyList = JSON.parseArray(thirdPartyDataKeys, Map.class);
////            if (!thirdPartyDataKeyList.isEmpty()){
////                for (Map map : thirdPartyDataKeyList) {
////                    if (maxKey.toString().equals(map.get("name"))){
////                        boolean flag;
////                        String order = (String)(map.get("order"));
////                        if("-1".equals(order)){
////                            flag = truncateContent(content, dataMap, maxKey, map.get("path").toString(), -1);
////                        }else{
////                            flag = truncateContent(content, dataMap, maxKey, map.get("path").toString());
////                        }
////                        if(flag){
////                            LoggerProxy.info("thirdPartyMapOpt", LOGGER, "处理成功, maxKey={}", maxKey);
////                        }
////                        return flag;
////                    }
////                }
////            }
////        }catch (Exception e){
////            LOGGER.info("run enum key and path errorMsg",e);
////        }
////        LOGGER.info("类型匹配失败,userKey={}",event.getUserKey());
////        return false;
////    }
//
////    private boolean truncateContent(Object content, Map dataMap, Object maxKey, String path){
////        // 默认数组截断前半段
////        return truncateContent(content, dataMap, maxKey, path, 1);
////    }
//
////    private boolean truncateContent(Object content, Map dataMap, Object maxKey, String path, int order) {
////        if (content instanceof JSONObject) {
////            truncateOps((JSONObject) content, path, order);
////        } else if (content instanceof Map) {
////            truncateOps((Map) content, path);
////        } else if (content instanceof String) {
////            content = truncateOps((String) content, path, order);
////            dataMap.put(maxKey, content);
////        } else {
////            LOGGER.info("content class={}", content.getClass());
////            return false;
////        }
////        return true;
////    }
//
//
//    /**
//     * 对将存储的数据进行截断操作
//     *
//     * @param key
//     * @param event
//     */
////    private boolean truncateEvent(String key, String path, Event event) {
////        if (BigField.thirdPartyData.name().equals(key)) {
////            return thirdPartyMapOpt(event);
////        }
////
////        //如果是日历，要把原始数据删除
////        if (BigField.calendarInfoVo.name().equals(key)) {
////            JSONObject calendarInfo = JSONObject.parseObject((String) event.getDataVo().get("CALENDAR_INFO"));
////            if (null != calendarInfo) {
////                String jobId = calendarInfo.getString("jobId");
////                Query query = new Query(Criteria.where("jobId").is(jobId));
////                Update update = new Update();
////                update.set("data", "{}");
////                //risk_service_data_collection 是来源表
////                shardingTemplate.updateFirst(query, update, "risk_service_data_collection");
////                event.getDataVo().remove("calendarInfoVo");
////                event.setXml("");
////                query = new Query(Criteria.where("sessionId").is(event.getSessionId()));
////                update = new Update();
////                update.set("event", event);
////                template.updateFirst(query, update, this.collectionName);
////                shardingTemplate.updateFirst(query, update, this.collectionName);
////                LoggerProxy.error("mongo16RemoveCalendarInfoVo",LOGGER, "key={},jobId={}","calendarInfoVo",jobId);
////                return false;
////            }
////        }
////        Object originalDataObject = event.getDataVo().get(key);
////        if (originalDataObject instanceof Map) {
////            Map dataMap = (Map) originalDataObject;
////            truncateOps(dataMap, path);
////            event.getDataVo().put(key, dataMap);
////            return true;
////        } else if (originalDataObject instanceof String) {
////            try {
////                String dataStr = (String) originalDataObject;
////                event.getDataVo().put(key, truncateOps(dataStr, path, 1));
////                return true;
////            } catch (Exception e) {
////                LOGGER.error("truncateEvent error:", e);
////                return false;
////            }
////        } else if (originalDataObject instanceof JSONObject) {
////            JSONObject jsonObject = (JSONObject) originalDataObject;
////            truncateOps(jsonObject, path, 1);
////            event.getDataVo().put(key, jsonObject);
////            return true;
////        } else if (originalDataObject instanceof JSONArray) {
////            JSONArray array = (JSONArray) originalDataObject;
////            reduceDataSize(array, 1);
////            event.getDataVo().put(key, array);
////            return true;
////        } else {
////            LOGGER.info("event.DataVo中类型适配失败,key={},class={}", key, originalDataObject.getClass());
////            return false;
////        }
////    }
//
//    //@Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
////    private void update(MongoTemplate template, Query query, Update update, String collectionName) {
////        template.updateFirst(query, update, collectionName);
////    }
//
////    public EventVo getBySessionId(String sessionId) {
////        Query query = new Query();
////        query.addCriteria(Criteria.where("sessionId").is(sessionId)).limit(1);
////        query.with(new Sort(new Sort.Order(Sort.Direction.DESC, "createTime")));
////        EventVo vo = getTransferEventRecord(query,"getBySessionId-"+sessionId);
////        if(vo == null){
////            vo = getRecord(query,"getBySessionId");
////        }
////        return vo;
////    }
//
////    public EventVo lastEvent(Event event) {
////        Query query = new Query();
////        query.addCriteria(Criteria.where("userKey").is(event.getUserKey()).and("sourceSystem").is(event.getSourceSystem())
////                .and("sessionId").ne(event.getSessionId())).limit(1);
////        query.with(new Sort(new Sort.Order(Sort.Direction.DESC, "createTime")));
////        EventVo vo = getTransferEventRecord(query,"lastEventNotCurrent-"+event.getUserKey()+"-"+event.getSessionId());
////        if(vo == null){
////            vo = getRecord(query,"lastEventNotCurrent");
////        }
////        return vo;
////    }
//
////    public EventVo getLastEventByUserKey(String userKey) {
////        Query query = new Query();
////        query.addCriteria(Criteria.where("userKey").is(userKey)).limit(1);
////        query.with(new Sort(new Sort.Order(Sort.Direction.DESC, "createTime")));
////        EventVo vo = getTransferEventRecord(query,"getLastEventByUserKey-"+userKey);
////        if(vo == null){
////            vo = getRecord(query,"getLastEventByUserKey");
////        }
////        return vo;
////    }
//
////    public List<EventVo> getLastEventByUserKey(String userKey, String eventCode, String sourceSystem, List<String> loanKeyList, Integer limit) {
////        LoggerProxy.info(LOGGER,"countAllQueryCollection=risk_event");
////        Query query = new Query();
////        Criteria criteria = new Criteria();
////        if (StringUtils.isNotBlank(userKey)) {
////            criteria.and("userKey").is(userKey);
////        }
////        if (!CollectionUtils.isEmpty(loanKeyList)) {
////            criteria.and("loanKey").in(loanKeyList);
////        }
////        if (StringUtils.isNotBlank(sourceSystem)) {
////            criteria.and("sourceSystem").is(sourceSystem);
////        }
////        if (StringUtils.isNotBlank(eventCode)) {
////            criteria.and("event.params.eventCode").is(eventCode);
////        }
////        if (null == limit || limit == 0) {
////            limit = 1;
////        }
////        query.addCriteria(criteria).limit(limit);
////        query.with(new Sort(new Sort.Order(Sort.Direction.DESC, "createTime")));
////        List<EventVo> list = getTransferEventRecords(query,"getLastEventByUserKey");
////        if(CollectionUtils.isEmpty(list)){
////            list = this.shardingTemplate.find(query,EventVo.class,this.collectionName);
////        }
////        return list;
////    }
//
////    @Deprecated
////    public EventVo getByLoanKey(String loanKey) {
////        Query query = new Query();
////        query.addCriteria(Criteria.where("loanKey").is(loanKey)).limit(1);
////        query.with(new Sort(new Sort.Order(Sort.Direction.DESC, "createTime")));
////        EventVo vo = getTransferEventRecord(query,"getByLoanKey-"+loanKey);
////        if(vo == null){
////            vo = getRecord(query,"getByLoanKey");
////        }
////        return vo;
////    }
//
//    /**
//     * 新增一个数据源参数
//     * @param userKey
//     * @param sourceSystem
//     * @param eventCode
//     * @param dataSource:通过数据源参数来决定查那个表
//     * @return
//     */
////    @Deprecated
////    public List<EventVo> getListByUserKey(String userKey, String sourceSystem, String eventCode,String dataSource) {
////        LoggerProxy.info("getListByUserKey",LOGGER,"countAllQueryCollection=risk_event");
////        Query query = new Query();
////        query.addCriteria(Criteria.where("userKey").is(userKey).and("sourceSystem").is(sourceSystem));
////        if (StringUtils.isNotEmpty(eventCode)) {
////            query.addCriteria(Criteria.where("event.params.eventCode").is(eventCode));
////        }
////        query.with(new Sort(new Sort.Order(Sort.Direction.DESC, "createTime")));
////
////        List<EventVo> eventVos = new ArrayList<>();
////        /**查老表risk_event **/
////        List<EventVo> transferEventRecordList = getTransferEventRecords(query,"getListByUserKey-"+userKey+"-"+eventCode);
////        Set<String> sessionIdSet = transferEventRecordList.stream().map(eventVo -> eventVo.getSessionId()).collect(Collectors.toSet());
////        Map<String, EventVo> sessionIdMap = transferEventRecordList.stream().collect(Collectors.toMap(transferEvent -> transferEvent.getSessionId(), transferEvent -> transferEvent));
////        try {
////         /** 根据输入的数据源,来决定查那个表 **/
////            if (SHARDING_TEMPLATE_SOURCE.equals(dataSource)) {
////                LoggerProxy.info("getListByUserKey_shardingTemplate",LOGGER,"countAllQueryCollection=risk_event");
////                eventVos = this.shardingTemplate.find(query,EventVo.class,this.collectionName);
////                if (!CollectionUtils.isEmpty(eventVos)){
////                    LoggerProxy.info("getListByUserKey_shardingTemplate_isNotNUll",LOGGER,"countAllQueryCollection=risk_event");
////                }
////            } else {
////                LoggerProxy.info("getListByUserKey_template",LOGGER,"countAllQueryCollection=risk_event");
////                eventVos = this.template.find(query,EventVo.class,VERIFY_RESULT);
////                /** 兼容 如果外层有xml、param、result 设置到里面 **/
////                if (!CollectionUtils.isEmpty(eventVos)) {
////                    for (EventVo eventVo : eventVos) {
////                        Event event = eventVo.getEvent();
////                        Map<String, Object> verifyResult = eventVo.getVerifyResult();
////                        if (verifyResult !=null && !verifyResult.isEmpty()){
////                            event.setVerifyResult(verifyResult);
////                        }
////
////                        Map<String, Object> params = eventVo.getParams();
////                        if (params !=null && !params.isEmpty()){
////                            event.setParams(params);
////                        }
////
////                        if (!StringUtils.isBlank(eventVo.getXml())){
////                            event.setXml(eventVo.getXml());
////                        }
////                    }
////                }
////            }
////            /** 只比较 template **/
////            if (TEMPLATE_SOURCE.equals(dataSource)) {
////                Set<String> collect = eventVos.stream().map(eventVo -> eventVo.getSessionId()).collect(Collectors.toSet());
////                if (collect.size() != sessionIdSet.size()) {
////                    /** 现在是双写 **/
////                    LoggerProxy.info("getListByUserKey_size", LOGGER, "eventVos {},transfer{}", collect, sessionIdSet);
////                }
////                boolean infoLog = true;
////                for (EventVo eventVo : eventVos) {
////                    String sessionId = eventVo.getSessionId();
////                    if (!sessionIdSet.contains(sessionId)) {
////                        if (infoLog) {
////                            LoggerProxy.info("getListByUserKey_template_risk_event", LOGGER, "sessionId {}", sessionId);
////                            infoLog = false;
////                        }
////                        transferEventRecordList.add(eventVo);
////                    }
////                    try {
////                        EventVo transferEventVo = sessionIdMap.get(sessionId);
////                        /** 原来的transfer没有值不比较 **/
////                        if (transferEventVo==null){
////                            continue;
////                        }
////                        Map<String, Object> map1 = new HashMap<>();
////                        map1.put("params", transferEventVo.getEvent().getParams());
////                        map1.put("verifyResult", transferEventVo.getEvent().getVerifyResult());
////                        if ("haoHuanVerify".equals(transferEventVo.getEvent().getEventCode())) {
////                            map1.put("xml", transferEventVo.getEvent().getXml());
////                        }
////                        Map<String, Object> map2 = new HashMap<>();
////                        map2.put("params", eventVo.getEvent().getParams());
////                        map2.put("verifyResult", eventVo.getEvent().getVerifyResult());
////                        if ("haoHuanVerify".equals(eventVo.getEvent().getEventCode())) {
////                            map2.put("xml", eventVo.getEvent().getXml());
////                        }
////
////                        MapDifference<String, Object> difference = MapUtils.compareWithFlatten(map1, map2, true);
////                        Map<String, Object> left = difference.entriesOnlyOnLeft();
////                        String leftJoin = String.join(",", left.keySet());
////
////                        Map<String, Object> right = difference.entriesOnlyOnRight();
////                        String rightJoin = String.join(",", right.keySet());
////
////                        Map<String, MapDifference.ValueDifference<Object>> stringValueDifferenceMap = difference.entriesDiffering();
////
////                        LoggerProxy.info("getListByUserKey_compare", LOGGER, "dataSource {} left{} right {} " +
////                                "entriesDiffering {}", dataSource, leftJoin, rightJoin, JSON.toJSONString(stringValueDifferenceMap));
////                    } catch (Exception e) {
////                        LoggerProxy.warn("getListByUserKey_compare_error", LOGGER, e.getMessage(), e);
////                    }
////
////                }
////            }
////        }catch (Exception ex){
////            LoggerProxy.error("getListByUserKey", LOGGER, "get data from sharding mongo, collectionName={},userKey={},sourceSystem={}",this.collectionName,userKey,sourceSystem,ex);
////        }
////        return transferEventRecordList;
////    }
//
//
//    /**
//     * 过渡阶段 数据查询 优先查询sharding
//     *
//     * @param query
//     * @param method
//     * @return
//     */
//    //@Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
////    private EventVo getRecord(Query query,String method){
////        EventVo vo = this.shardingTemplate.findOne(query, EventVo.class, this.collectionName);
////        return vo;
////    }
//
////    private EventVo getTransferEventRecord(Query query, String method){
////        EventVo vo = null;
////        try{
////            vo = this.template.findOne(query, EventVo.class, this.collectionName);
////        }catch (Exception e){
////            LoggerProxy.error("queryEventTransferMongoError",LOGGER,e.getMessage(),e);
////        }
////        return vo;
////    }
//
////    private List<EventVo> getTransferEventRecords(Query query, String method){
////        List<EventVo> eventVoList = null;
////        try{
////            eventVoList = this.template.find(query, EventVo.class, this.collectionName);
////        }catch (Exception e){
////            LoggerProxy.error("queryTransferEventMongoError",LOGGER,e.getMessage(),e);
////            eventVoList = Lists.newArrayList();
////        }
////        return eventVoList;
////    }
//
//    /**
//     * 数据超大时，只对下面这些Key做截取处理
//     */
//    private enum BigField {
//        shortMessageVo("calls"),
//        phoneList(""),
//        newShortMessageVo("calls"),
//        PHONE_CALLRECORD("calls"),
//        calendarInfoVo("calendars"),
//        thirdPartyData(""),
//        allPlistService(""),
//        smsReportService("records#calls");
//
//        BigField(String path) {
//            this.path = path;
//        }
//
//        /**
//         * 注path格式使用‘#’分割，举例：calendars#name#calls, 如是单层路径无需分割符
//         */
//        private String path;
//
//        public String getPath() {
//            return path;
//        }
//
//        public static boolean contains(String key) {
//            boolean ret = false;
//            for (BigField bigField : BigField.values()) {
//                if (bigField.name().equals(key)) {
//                    ret = true;
//                    break;
//                }
//            }
//            return ret;
//        }
//
//        public static BigField getByName(String name) {
//            for (BigField bigField : BigField.values()) {
//                if (bigField.name().equals(name)) {
//                    return bigField;
//                }
//            }
//            return null;
//        }
//
//    }
//
//    private enum BigFieldThirdParty {
//        smsReportService("records#calls"),
//        asyncCalendarInfoService("records#calendarInfo#calendars"),
//        asyncSmsReportService("calls"),
//        asyncPhoneBookAreaService("phoneBooks"),
//        allPlistService(""),
//        userPhoneInfo(""),
//        sensorsData("events"),
//        getUploadPhoneInfoRecordByUserKey(""),
//        getUploadGpsInfoRecordByUserKey(""),
//        asyncPhoneBookService("phoneBooks");
//
//        BigFieldThirdParty(String path) {
//            this.path = path;
//        }
//
//        /**
//         * 注path格式使用‘#’分割，举例：calendars#name#calls, 如是单层路径无需分割符
//         */
//        private String path;
//
//        public static boolean contains(String key) {
//            boolean ret = false;
//            for (BigFieldThirdParty bigField : BigFieldThirdParty.values()) {
//                if (bigField.name().equals(key)) {
//                    ret = true;
//                    break;
//                }
//            }
//            return ret;
//        }
//
//    }
//}
