package com.youxin.risk.commons.limiter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.NacosClientAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * apollo比率限流解析器
 *
 * <AUTHOR>
 */
public class ApolloRateLimitParser implements RateLimitParser {
    private static final Logger LOGGER = LoggerFactory.getLogger(ApolloRateLimitParser.class);

    private final ApolloNamespaceEnum namespace;
    private final String configKey;

    public ApolloRateLimitParser(ApolloNamespaceEnum namespace, String configKey) {
        this.namespace = namespace;
        this.configKey = configKey;
    }

    @Override
    public Map<String, List<RateLimit>> parse() {
        try {
            String config = NacosClientAdapter.getStringConfig(configKey, "{}");
            Map<String, List<RateLimitMetaData>> metaDataMap = JSON.parseObject(config, new TypeReference<HashMap<String, List<RateLimitMetaData>>>() {
            });
            Map<String, List<RateLimit>> rateLimitMap = new HashMap<>(metaDataMap.size());
            metaDataMap.forEach((eventCode, metaDataList) -> {
                List<RateLimit> rateLimitList = metaDataList.stream().map(RateLimitMetaData::transfer).collect(Collectors.toList());
                rateLimitMap.put(eventCode, rateLimitList);
            });
            return rateLimitMap;
        } catch (Exception e) {
            LoggerProxy.error("apolloRateLimitParserError", LOGGER, "namespace={},configKey={}", namespace, configKey, e);
        }
        return Collections.emptyMap();
    }

    @Override
    public List<RateLimit> parseList() {
        try {
            String config = NacosClientAdapter.getStringConfig( configKey, "[]");
            List<RateLimitMetaData> metaDataList = JSON.parseObject(config, new TypeReference<List<RateLimitMetaData>>() {
            });
            return metaDataList.stream().map(RateLimitMetaData::transfer).collect(Collectors.toList());
        } catch (Exception e) {
            LoggerProxy.error("apolloRateLimitParseListError", LOGGER, "namespace={},configKey={}", namespace, configKey, e);
        }
        return Collections.emptyList();
    }


}
