package com.youxin.risk.commons.dao.datacenter;

import com.youxin.risk.commons.model.datacenter.DcOperationLog;
import com.youxin.risk.commons.model.datacenter.common.OperationType;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface DcOperationLogMapper {
    int deleteByPrimaryKey(Integer id);

    Long insert(DcOperationLog record);

    int insertSelective(DcOperationLog record);

    DcOperationLog selectByPrimaryKey(long id);

    DcOperationLog selectByItem(DcOperationLog record);

    int updateByPrimaryKeySelective(DcOperationLog record);

    int updateByPrimaryKey(DcOperationLog record);

    DcOperationLog getByUserKeyAndSourceSystem(@Param("userKey") String userKey, @Param("sourceSystem")String sourceSystem);

    DcOperationLog selectBy(@Param("userKey")String userKey, @Param("sourceSystem")String sourceSystem, @Param("operationType")String operationType);

    List<DcOperationLog> getListByUserKeyAndSourceSystemAndType(@Param("userKey") String userKey, @Param("sourceSystem") String sourceSystem, @Param("operationType") String operationType);

    List<DcOperationLog> distinctLogByDeviceId(@Param("sourceSystem") String sourceSystem, @Param("userKey") String userKey);

    List<String> getAllDeviceIdByUserKey(@Param("userKey") String userKey);

    List<String> getAllIpByUserKey(@Param("userKey") String userKey);

    DcOperationLog getLastByUserKey(@Param("userKey") String userKey);

    String getLastNotNullPlatform(@Param("userKey") String userKey);

    DcOperationLog getByUserKeyAndSourceSystems(@Param("userKey") String userKey, @Param("sourceSystemList") List<String> sourceSystem, @Param("type") OperationType type);

    List<Map<String, Long>> getMonitorData(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("operationTypes") List<String> operationTypes);

    DcOperationLog findLastOperationLogByUserAndOperationType(@Param("userKey") String userKey,
                                                              @Param("sourceSystem") String sourceSystem,
                                                              @Param("operationType") String operationType);

    List<String> getAllUserKeyByDeviceIds(@Param("deviceIds") List<String> deviceIds);

    // 数据清理使用
    int deleteByIdAndType(@Param("startId") long startId, @Param("endId") long endId, @Param("operationTypes") List<String> operationTypes, @Param("limit") int limit);

    String getLastNotNullAndEmptyPlatform(@Param("userKey") String userKey);
}