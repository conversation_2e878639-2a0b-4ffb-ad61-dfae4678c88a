package com.youxin.risk.commons.cacheloader.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;

import com.google.common.collect.Maps;
import com.youxin.risk.commons.cacheloader.service.RmFeatureService;
import com.youxin.risk.commons.dao.fs.RmFeatureMapper;
import com.youxin.risk.commons.model.RmFeature;
import com.youxin.risk.commons.model.RmFeatureDependProperty;

/**
 *
 */
public class RmFeatureServiceImpl implements RmFeatureService {
    @Resource
    private RmFeatureMapper rmFeatureMapper;

    @Override
    public Map<String, List<RmFeatureDependProperty>> selectAll() {
        List<RmFeature> featureList = rmFeatureMapper.selectAll();
        if (CollectionUtils.isEmpty(featureList)){
            return null;
        }
        Map<String, List<RmFeatureDependProperty>> result = Maps.newHashMap();
        featureList.forEach(feature->result.put(feature.getFeatureName(), rmFeatureMapper.selectDependById(feature.getSubmitDetailId())));
        return result;
    }

    @Override
    public List<RmFeature> selectByUpdateTime(Date updateTime) {
        return rmFeatureMapper.selectByUpdateTime(updateTime);
    }

    @Override
    public List<RmFeatureDependProperty> getDependById(Long submitDetailId) {
        return rmFeatureMapper.selectDependById(submitDetailId);
    }

}
