/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.commons.vo.datavo;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 创建时间：2017年12月18日-下午12:00:33
 */
public class TaobaoOrderVo {

	private String amount;

	private Date time;

	private String orderStatus;

	private String orderQuantity;

	private String freight;

	private String address;

	private List<TaobaoProductVo> products;

	public String getAmount() {
		return this.amount;
	}

	public void setAmount(String amount) {
		this.amount = amount;
	}

	public Date getTime() {
		return this.time;
	}

	public void setTime(Date time) {
		this.time = time;
	}

	public String getOrderStatus() {
		return this.orderStatus;
	}

	public void setOrderStatus(String orderStatus) {
		this.orderStatus = orderStatus;
	}

	public String getAddress() {
		return this.address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public List<TaobaoProductVo> getProducts() {
		return this.products;
	}

	public void setProducts(List<TaobaoProductVo> products) {
		this.products = products;
	}

	public String getOrderQuantity() {
		return this.orderQuantity;
	}

	public void setOrderQuantity(String orderQuantity) {
		this.orderQuantity = orderQuantity;
	}

	public String getFreight() {
		return this.freight;
	}

	public void setFreight(String freight) {
		this.freight = freight;
	}



}
