package com.youxin.risk.commons.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.youxin.risk.commons.constants.VariableConfigEnum;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2023/9/6 11:37
 * @desc
 */
@Data
public class VariableConfigDO extends BaseModel{
    public static final String STATUS_ENABLE="ENABLE";
    public static final String STATUS_DISABLE="DISABLE";
    private static final String CONF_DETAIL_SUFFIX="%";
    public static VariableConfigDO buildDefault(){
        VariableConfigDO variableConfigDO=new VariableConfigDO();
        variableConfigDO.setAirRunningStatus(STATUS_DISABLE);

        return variableConfigDO;
    }
    /**
     * 策略类型
     */
    private String strategyType;
    /**
     * 策略节点编码
     */
    private String strategyNodeCode;
    /**
     * 变量名称
     */
    private String variableCode;
    /**VariableConfigDO
     * 策略是否使用
     */
    @Deprecated
    private Boolean isStrategyUsed;
    /**
     * 空跑状态
     */
    private String airRunningStatus;
    /**
     * @see VariableConfigEnum
     * 空跑类型
     */
    private String airRunningType;
    /**
     * 配置详情
     */
    private String configDetail;
    /**
     * 空跑开始时间
     */
    private Date airRunningStartTime;
    /**
     * 空跑结束时间
     */
    private Date airRunningEndTime;

    /**
     * 空跑更新时间
     */
    private Date airRunningUpdateTime;

    /**
     * 空跑更新人
     */
    private String airRunningUpdateUser;
    /**
     * 服务key/资源包配置
     * @see DatasourceConfigDO#datasourceCode
     */
    private String datasourceCode;
    /**
     * 更新人
     */
    private String updateUser;

    private String datasourceType;

    /**
     * 使用变量的规则集列表
     */
    private String useRuleScoreList;

    /**
     * 用于在空跑类型是随机的时候，转换成configDetail%的样式
     * @return
     */
    public String getTransConfigDetail(){
        return VariableConfigEnum.isRandomType(this.configDetail)?this.configDetail+CONF_DETAIL_SUFFIX:this.configDetail;
    }


    public boolean isDisable(){
        return STATUS_DISABLE.equals(this.airRunningStatus);
    }
}
