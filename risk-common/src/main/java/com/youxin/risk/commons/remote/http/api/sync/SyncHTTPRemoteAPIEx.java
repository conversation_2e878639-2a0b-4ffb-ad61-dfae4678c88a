package com.youxin.risk.commons.remote.http.api.sync;

import org.apache.commons.collections.MapUtils;
import org.apache.http.client.methods.HttpGet;

import java.util.HashMap;
import java.util.Map;

import static com.youxin.risk.commons.utils.StringUtils.replaceBlank;

/**
 * 远程操作工具类，尽量兼容原有代码，只是增加了重试逻辑、抛出原始异常和httpcode到上游
 *
 * <AUTHOR>
 */
public class SyncHTTPRemoteAPIEx {

    public static void main(String[] args) {
        String url = "http//127.0.0.1:8800/test";
        Map<String, Object> param = new HashMap<>();
        param.put("q","fdsf1`");
        param.put("con","a b");
        param.put("test", "\"aaa\"");
        HttpGet httpmethod = new HttpGet(createTongDunGetUrl(url, param));
        System.out.println(httpmethod);
    }

    public static String getTongDun(String url, Map<String, String> header, Map<String, Object> params, int timeoutMillis) {
        url = createTongDunGetUrl(url, params);
        return SyncHTTPRemoteAPI.get(url, header, timeoutMillis);
    }

    public static String createTongDunGetUrl(String url, Map<String, Object> params) {
        if (MapUtils.isEmpty(params)) {
            return url;
        }
        if (!url.endsWith("?")) {
            url += "?";
        }
        for (Map.Entry entry : params.entrySet()) {
            String value = String.valueOf(entry.getValue());
            // 非标准处理方式，特殊使用替换方式，不使用urlencode，同盾方会报错
            value = value.replaceAll("[ + ^/?%#&=`\"\\\\{}|<>]", "");
            value = replaceBlank(value);
            url += String.format("%s=%s&", entry.getKey(), value);
        }
        if (url.endsWith("&")) {
            url = url.substring(0, url.length() - 1);
        }
        return url;
    }

}