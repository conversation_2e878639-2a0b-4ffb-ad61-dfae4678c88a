package com.youxin.risk.commons.model;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.Set;
import java.util.stream.Collectors;

/**
 */
public class DataSourceExperiment extends BaseModel {

    private static final long serialVersionUID = 6406493146324211189L;

    private String expCode;
    private String expName;
    private String expType;
    private String sourceSystem;
    private String serviceCode;
    private String eventCode;
    private String step;
    private Integer expFlowPercent;
    private Date beginTime;
    private Date endTime;
    private String status;
    private String cmpField;
    private String cmpType;
    private String oldServiceCode;
    private String newServiceCode;

    public String getServiceCode() {
        return serviceCode;
    }

    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode;
    }

    public String getEventCode() {
        return eventCode;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public String getExpCode() {
        return expCode;
    }

    public void setExpCode(String expCode) {
        this.expCode = expCode;
    }

    public String getExpName() {
        return expName;
    }

    public void setExpName(String expName) {
        this.expName = expName;
    }

    public String getExpType() {
        return expType;
    }

    public void setExpType(String expType) {
        this.expType = expType;
    }

    public String getSourceSystem() {
        return sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem;
    }

    public String getStep() {
        return step;
    }

    public void setStep(String step) {
        this.step = step;
    }

    public Integer getExpFlowPercent() {
        return expFlowPercent;
    }

    public void setExpFlowPercent(Integer expFlowPercent) {
        this.expFlowPercent = expFlowPercent;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCmpField() {
        return cmpField;
    }

    public void setCmpField(String cmpField) {
        this.cmpField = cmpField;
    }


    public String getOldServiceCode() {
        return oldServiceCode;
    }

    public void setOldServiceCode(String oldServiceCode) {
        this.oldServiceCode = oldServiceCode;
    }

    public String getNewServiceCode() {
        return newServiceCode;
    }

    public void setNewServiceCode(String newServiceCode) {
        this.newServiceCode = newServiceCode;
    }

    public void setCmpType(String cmpType) {
        this.cmpType = cmpType;
    }

    public String getCmpType() {
        return cmpType;
    }

    public Set<String> getCmpFieldSet() {
        if (cmpField == null || cmpField.trim().length() == 0) {
            return Collections.emptySet();
        }
        return Arrays.stream(cmpField.split("\n")).map(e -> e.replace(" ", "")).collect(Collectors.toSet());
    }
}
