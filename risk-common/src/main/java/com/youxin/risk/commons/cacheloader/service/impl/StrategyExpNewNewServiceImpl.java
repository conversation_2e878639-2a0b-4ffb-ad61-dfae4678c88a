package com.youxin.risk.commons.cacheloader.service.impl;

import com.youxin.risk.commons.cacheloader.service.StrategyExpNewService;
import com.youxin.risk.commons.dao.admin.StrategyExpMapper;
import com.youxin.risk.commons.model.StrategyExp;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


public class StrategyExpNewNewServiceImpl implements StrategyExpNewService {
    @Resource
    private StrategyExpMapper strategyExpMapper;

    @Override
    public List<StrategyExp> selectAll() {
        return strategyExpMapper.selectAll();
    }

    @Override
    public List<StrategyExp> selectByUpdateTime(Date updateTime) {
        return strategyExpMapper.selectByUpdateTime(updateTime);
    }

    @Override
    public List<StrategyExp> selectEnable() {
        return strategyExpMapper.selectEnable();
    }
}
