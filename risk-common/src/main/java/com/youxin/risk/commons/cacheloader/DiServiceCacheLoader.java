package com.youxin.risk.commons.cacheloader;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.youxin.risk.commons.adapter.di.ServiceAdapter;
import com.youxin.risk.commons.cache.CacheManager;
import com.youxin.risk.commons.cache.CacheType;
import com.youxin.risk.commons.cache.RedisCacheService;
import com.youxin.risk.commons.cacheloader.service.DiServiceService;
import com.youxin.risk.commons.constants.ConfigTableEnum;
import com.youxin.risk.commons.constants.ServiceTypeEnum;
import com.youxin.risk.commons.delayqueue.DelayTaskScheduler;
import com.youxin.risk.commons.kafkav2.sender.KafkaDynamicSender;
import com.youxin.risk.commons.model.DiService;
import com.youxin.risk.commons.model.DiServiceInput;
import com.youxin.risk.commons.model.DiServiceOutput;
import com.youxin.risk.commons.mongo.MongoDao;
import com.youxin.risk.commons.service.datacenter.SubscriptionDataService;
import com.youxin.risk.commons.service.di.DiKafkaMessageLogService;
import com.youxin.risk.commons.service.di.DiTaskService;
import com.youxin.risk.commons.service.engine.EngineAsyncRequestLogService;
import com.youxin.risk.commons.service.engine.ServiceDataIndexService;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.apache.commons.lang3.StringUtils;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Map;

/**
 * 字典配置缓存加载
 *
 * <AUTHOR>
 */
public class DiServiceCacheLoader extends BaseCacheLoader {

    private boolean invokeServiceClass = true;

    private DiServiceService diServiceService;
    private DiTaskService diTaskService;
    private MongoDao mongoDao;
    private RedisCacheService redisCacheService;

    private DiKafkaMessageLogService messageLogService;
    private KafkaTemplate kafkaTemplate;
    private KafkaTemplate kafkaMirrorTemplate;
    private KafkaDynamicSender kafkaDynamicSender;
    private ServiceDataIndexService serviceDataIndexService;
    private DelayTaskScheduler delayTaskScheduler;
    private EngineAsyncRequestLogService engineAsyncRequestLogService;
    private SubscriptionDataService subscriptionDataService;

   @Override
    @Scheduled(fixedDelay = 10000)
    public void load() {
        super.load(ConfigTableEnum.admin_di_service.name());
    }

    /**
     * 字典表缓存结构：Map<dict_code,Map<dict_value,Dictionary>>
     */
    @Override
    protected void loadAll() {
        List<DiService> diServices = this.diServiceService.selectAll();
        if (CollectionUtils.isEmpty(diServices)) {
            return;
        }
        Map<String, List<DiServiceInput>> inputMap = this.diServiceService.selectAllInput();
        Map<String, List<DiServiceOutput>> outputMap = this.diServiceService.selectAllOutput();
        Map<String, Object> serviceMap = Maps.newHashMap();
        Map<String, Object> serviceClassMap = Maps.newHashMap();
        Map<String, Object> taskTypeMap = Maps.newHashMap();
        HashSet<String> taskTypeSet = Sets.newHashSet();
        for (DiService service : diServices) {
            service.setInputs(inputMap.get(service.getServiceCode()));
            service.setOutputs(outputMap.get(service.getServiceCode()));
            serviceMap.put(service.getServiceCode(), service);
            if (ServiceTypeEnum.CLASS.name().equals(service.getServiceType())) {
                serviceClassMap.put(service.getServiceCode(), this.invokeServiceClass(service));
            }
            if (StringUtils.isNotBlank(service.getTaskType())) {
                taskTypeSet.add(service.getTaskType());
            }
        }
        taskTypeMap.put(CacheType.di_task_type.name(), taskTypeSet);
        CacheManager.setCache(CacheType.di_service_list_by_service_code, serviceMap);
        CacheManager.setCache(CacheType.di_service_class_list_by_service_code, serviceClassMap);
        CacheManager.setCache(CacheType.di_task_type, taskTypeMap);
    }

    private ServiceAdapter invokeServiceClass(DiService diService) {
        if (!invokeServiceClass) {
            return null;
        }
        try {
            Class<?> serviceClass = Class.forName(diService.getServiceClass());
            ServiceAdapter serviceAdapter = (ServiceAdapter) serviceClass.newInstance();
            serviceAdapter.setDiService(diService);
            serviceAdapter.setDiTaskService(diTaskService);
            serviceAdapter.setMongoDao(mongoDao);
            serviceAdapter.setRedisCacheService(redisCacheService);
            serviceAdapter.setDelayTaskScheduler(delayTaskScheduler);
            serviceAdapter.setMessageLogService(messageLogService);
            serviceAdapter.setKafkaTemplate(kafkaTemplate);
            serviceAdapter.setKafkaMirrorTemplate(kafkaMirrorTemplate);
            serviceAdapter.setServiceDataIndexService(serviceDataIndexService);
            serviceAdapter.setEngineAsyncRequestLogService(engineAsyncRequestLogService);
            serviceAdapter.setSubscriptionDataService(subscriptionDataService);
            serviceAdapter.setKafkaDynamicSender(kafkaDynamicSender);
            return serviceAdapter;
        } catch (ClassNotFoundException e) {
            LoggerProxy.error("serviceNotFound", this.logger, "service class not found, serviceCode={}", diService.getServiceCode(), e);
        } catch (InstantiationException e) {
            LoggerProxy.error("createServiceFailed", this.logger, "create service failed, serviceCode={}", diService.getServiceCode(), e);
        } catch (IllegalAccessException e) {
            LoggerProxy.error("serviceAccessIllegal", this.logger, "service access illegal, serviceCode={}", diService.getServiceCode(), e);
        } catch (Exception e) {
            LoggerProxy.error("createServiceError", this.logger, "create service error, serviceCode={}", diService.getServiceCode(), e);
        }
        return null;
    }

    @SuppressWarnings("unchecked")
    @Override
    protected int loadPart() {
        List<DiService> diServices = this.diServiceService.selectByUpdateTime(this.getCacheTime());
        if (!CollectionUtils.isEmpty(diServices)) {
            // 更新service后同步更新tasktype
            Map<String, Object> taskTypeMap = CacheManager.getCache(CacheType.di_task_type);
            HashSet<String> taskTypeSet = null;
            if (taskTypeMap == null) {
                taskTypeSet = Sets.newHashSet();
            } else if (!taskTypeMap.containsKey(CacheType.di_task_type.name())) {
                taskTypeSet = Sets.newHashSet();
            } else {
                taskTypeSet = (HashSet<String>) taskTypeMap.get(CacheType.di_task_type.name());
            }
            for (DiService service : diServices) {
                service.setInputs(this.diServiceService.selectInput(service.getServiceCode()));
                service.setOutputs(this.diServiceService.selectOutput(service.getServiceCode()));
                CacheManager.putToCache(CacheType.di_service_list_by_service_code, service.getServiceCode(), service);
                if (ServiceTypeEnum.CLASS.name().equals(service.getServiceType())) {
                    CacheManager.putToCache(CacheType.di_service_class_list_by_service_code, service.getServiceCode(), this.invokeServiceClass(service));
                }
                String taskType = service.getTaskType();
                taskTypeSet.add(taskType);
            }
            CacheManager.putToCache(CacheType.di_task_type, CacheType.di_task_type.name(), taskTypeSet);
            return diServices.size();
        }
        return 0;
    }

    public DiServiceService getDiServiceService() {
        return diServiceService;
    }

    public void setDiServiceService(DiServiceService diServiceService) {
        this.diServiceService = diServiceService;
    }

    public DiTaskService getDiTaskService() {
        return diTaskService;
    }

    public void setDiTaskService(DiTaskService diTaskService) {
        this.diTaskService = diTaskService;
    }

    public MongoDao getMongoDao() {
        return mongoDao;
    }

    public void setMongoDao(MongoDao mongoDao) {
        this.mongoDao = mongoDao;
    }

    public DiKafkaMessageLogService getMessageLogService() {
        return messageLogService;
    }

    public void setMessageLogService(DiKafkaMessageLogService messageLogService) {
        this.messageLogService = messageLogService;
    }

    public KafkaTemplate getKafkaTemplate() {
        return kafkaTemplate;
    }

    public void setKafkaTemplate(KafkaTemplate kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }

    public KafkaTemplate getKafkaMirrorTemplate() {
        return kafkaMirrorTemplate;
    }

    public void setKafkaMirrorTemplate(KafkaTemplate kafkaMirrorTemplate) {
        this.kafkaMirrorTemplate = kafkaMirrorTemplate;
    }

    public boolean isInvokeServiceClass() {
        return invokeServiceClass;
    }

    public void setInvokeServiceClass(boolean invokeServiceClass) {
        this.invokeServiceClass = invokeServiceClass;
    }

    public ServiceDataIndexService getServiceDataIndexService() {
        return serviceDataIndexService;
    }

    public void setServiceDataIndexService(ServiceDataIndexService serviceDataIndexService) {
        this.serviceDataIndexService = serviceDataIndexService;
    }

    public RedisCacheService getRedisCacheService() {
        return redisCacheService;
    }

    public void setRedisCacheService(RedisCacheService redisCacheService) {
        this.redisCacheService = redisCacheService;
    }

    public DelayTaskScheduler getDelayTaskScheduler() {
        return delayTaskScheduler;
    }

    public void setDelayTaskScheduler(DelayTaskScheduler delayTaskScheduler) {
        this.delayTaskScheduler = delayTaskScheduler;
    }

    public EngineAsyncRequestLogService getEngineAsyncRequestLogService() {
        return engineAsyncRequestLogService;
    }

    public void setEngineAsyncRequestLogService(EngineAsyncRequestLogService engineAsyncRequestLogService) {
        this.engineAsyncRequestLogService = engineAsyncRequestLogService;
    }

    public SubscriptionDataService getSubscriptionDataService() {
        return subscriptionDataService;
    }

    public void setSubscriptionDataService(SubscriptionDataService subscriptionDataService) {
        this.subscriptionDataService = subscriptionDataService;
    }

    public KafkaDynamicSender getKafkaDynamicSender() {
        return kafkaDynamicSender;
    }

    public void setKafkaDynamicSender(KafkaDynamicSender kafkaDynamicSender) {
        this.kafkaDynamicSender = kafkaDynamicSender;
    }
}
