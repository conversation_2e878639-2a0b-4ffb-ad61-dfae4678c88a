package com.youxin.risk.commons.concurrent;

import java.util.concurrent.*;

public class BoundedScheduledExecutorService {
    private ScheduledExecutorService scheduler;
    private Semaphore semaphore;

    public BoundedScheduledExecutorService(ScheduledExecutorService scheduler, int bound) {
        this.scheduler = scheduler;
        this.semaphore = new Semaphore(bound);
    }

    public void schedule(Runnable command, long delay, TimeUnit unit) throws InterruptedException {
        semaphore.acquire();
        try {
            scheduler.schedule(() -> {
                try {
                    command.run();
                } finally {
                    semaphore.release();
                }
            }, delay, unit);
        } catch (RejectedExecutionException e) {
            semaphore.release();
        }
    }

    public <T> ScheduledFuture<T> schedule(Callable<T> command, long delay, TimeUnit unit) throws InterruptedException {
        semaphore.acquire();
        try {
            return scheduler.schedule(() -> {
                try {
                    return command.call();
                } finally {
                    semaphore.release();
                }
            }, delay, unit);
        } catch (RejectedExecutionException e) {
            semaphore.release();
            throw e;
        }
    }

}
