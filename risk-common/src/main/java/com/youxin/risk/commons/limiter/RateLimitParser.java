package com.youxin.risk.commons.limiter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.springframework.scheduling.support.CronSequenceGenerator;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * {@link RateLimit}解析器
 *
 * <AUTHOR>
 */
public interface RateLimitParser {
    String TIME = "TIME";
    String DATETIME = "DATETIME";
    String CORN = "CORN";

    /**
     * 解析方法
     *
     * @return Map<String, List<RateLimit>>
     */
    Map<String, List<RateLimit>> parse();

    /**
     * 解析方法
     * @return List<RateLimit>
     */
    List<RateLimit> parseList();


    @Data
    class RateLimitMetaData {
        private Double rate;
        @Deprecated
        private String startTime;
        @Deprecated
        private String endTime;
        private String param;
        private String type;

        protected RateLimit transfer() {
            JSONObject params = JSON.parseObject(param);
            if (TIME.equalsIgnoreCase(type)) {
                LocalTime startTime;
                LocalTime endTime;
                if (params != null) {
                    startTime = LocalTime.parse(params.getString("startTime"));
                    endTime = LocalTime.parse(params.getString("endTime"));
                } else {
                    startTime = LocalTime.parse(this.startTime);
                    endTime = LocalTime.parse(this.endTime);
                }
                return new RateLimitWithTime(rate, startTime, endTime);
            }
            if (DATETIME.equalsIgnoreCase(type)) {
                LocalDateTime startTime;
                LocalDateTime endTime;
                if (params != null) {
                    startTime = LocalDateTime.parse(params.getString("startTime"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
                    endTime = LocalDateTime.parse(params.getString("endTime"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
                } else {
                    startTime = LocalDateTime.parse(this.startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
                    endTime = LocalDateTime.parse(this.endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
                }
                return new RateLimitWithDateTime(rate, startTime, endTime);
            }
            if (CORN.equalsIgnoreCase(type)) {
                String expression = params.getString("expression");
                return new RateLimitWithCornExpression(rate, new CronSequenceGenerator(expression));
            }
            throw new IllegalArgumentException("type=" + type + "is not supported");
        }
    }
}
