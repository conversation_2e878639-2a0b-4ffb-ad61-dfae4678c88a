package com.youxin.risk.commons.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * 值对比工具类
 * 用于对比驼峰格式和下划线格式的值差异，支持简单值和复杂JSON对象的对比
 */
public class ValueComparisonUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(ValueComparisonUtil.class);
    
    /**
     * 对比两个值的差异
     * 
     * @param camelKey 驼峰格式的键名
     * @param underscoreKey 下划线格式的键名
     * @param camelValue 驼峰格式的值
     * @param underscoreValue 下划线格式的值
     * @param userKey 用户标识
     * @param eventCode 事件代码
     * @param logTag 日志标签，用于区分不同的调用场景
     */
    public static void compareValues(String camelKey, String underscoreKey, Object camelValue, 
                                   Object underscoreValue, String userKey, String eventCode, String logTag) {
        try {
            // 如果两个值都为空，则不需要对比
            if (isEmptyValue(camelValue) && isEmptyValue(underscoreValue)) {
                return;
            }
            
            // 如果其中一个为空，记录差异
            if (isEmptyValue(camelValue) || isEmptyValue(underscoreValue)) {
                LoggerProxy.info(logTag, logger,
                    "Key format difference - camelKey: {}, underscoreKey: {}, camelValue: {}, underscoreValue: {}, userKey: {}, eventCode: {}", 
                    camelKey, underscoreKey, camelValue, underscoreValue, userKey, eventCode);
                return;
            }
            
            // 尝试将值转换为Map进行深度对比
            if (isJsonObject(camelValue) || isJsonObject(underscoreValue)) {
                compareJsonValues(camelKey, underscoreKey, camelValue, underscoreValue, userKey, eventCode, logTag);
            } else {
                // 简单值对比
                compareSimpleValues(camelKey, underscoreKey, camelValue, underscoreValue, userKey, eventCode, logTag);
            }
        } catch (Exception e) {
            LoggerProxy.warn(logTag + "Exception", logger, "Error comparing values for key: {} / {}, userKey: {}, eventCode: {}", 
                camelKey, underscoreKey, userKey, eventCode, e);
        }
    }
    
    /**
     * 判断值是否为空
     */
    private static boolean isEmptyValue(Object value) {
        return value == null || "".equals(value);
    }
    
    /**
     * 判断值是否为JSON对象
     */
    private static boolean isJsonObject(Object value) {
        if (value == null) {
            return false;
        }
        
        if (value instanceof Map || value instanceof List) {
            return true;
        }
        
        if (value instanceof String) {
            String str = (String) value;
            str = str.trim();
            return (str.startsWith("{") && str.endsWith("}")) || (str.startsWith("[") && str.endsWith("]"));
        }
        
        return false;
    }
    
    /**
     * 对比JSON格式的值
     */
    private static void compareJsonValues(String camelKey, String underscoreKey, Object camelValue, 
                                        Object underscoreValue, String userKey, String eventCode, String logTag) {
        try {
            Map<String, Object> camelMap = convertToMap(camelValue);
            Map<String, Object> underscoreMap = convertToMap(underscoreValue);
            
            if (camelMap == null || underscoreMap == null) {
                LoggerProxy.info(logTag, logger,
                    "JSON conversion failed - camelKey: {}, underscoreKey: {}, camelValue: {}, underscoreValue: {}, userKey: {}, eventCode: {}", 
                    camelKey, underscoreKey, camelValue, underscoreValue, userKey, eventCode);
                return;
            }
            
            MapDifference<String, Object> difference = Maps.difference(camelMap, underscoreMap);
            
            // 检查是否有差异
            if (!difference.areEqual()) {
                logMapDifferences(camelKey, underscoreKey, difference, userKey, eventCode, logTag);
            }
        } catch (Exception e) {
            LoggerProxy.warn(logTag + "JsonException", logger, "Error comparing JSON values for key: {} / {}, userKey: {}, eventCode: {}", 
                camelKey, underscoreKey, userKey, eventCode, e);
        }
    }
    
    /**
     * 对比简单值
     */
    private static void compareSimpleValues(String camelKey, String underscoreKey, Object camelValue, 
                                          Object underscoreValue, String userKey, String eventCode, String logTag) {
        // 转换为字符串进行对比
        String camelStr = String.valueOf(camelValue);
        String underscoreStr = String.valueOf(underscoreValue);
        
        if (!camelStr.equals(underscoreStr)) {
            // 尝试数值对比
            if (isNumeric(camelStr) && isNumeric(underscoreStr)) {
                try {
                    Double camelNum = Double.valueOf(camelStr);
                    Double underscoreNum = Double.valueOf(underscoreStr);
                    if (!camelNum.equals(underscoreNum)) {
                        LoggerProxy.info(logTag, logger,
                            "Numeric value difference - camelKey: {}, underscoreKey: {}, camelValue: {}, underscoreValue: {}, userKey: {}, eventCode: {}", 
                            camelKey, underscoreKey, camelValue, underscoreValue, userKey, eventCode);
                    }
                } catch (NumberFormatException e) {
                    LoggerProxy.info(logTag, logger,
                        "String value difference - camelKey: {}, underscoreKey: {}, camelValue: {}, underscoreValue: {}, userKey: {}, eventCode: {}", 
                        camelKey, underscoreKey, camelValue, underscoreValue, userKey, eventCode);
                }
            } else {
                LoggerProxy.info(logTag, logger,
                    "String value difference - camelKey: {}, underscoreKey: {}, camelValue: {}, underscoreValue: {}, userKey: {}, eventCode: {}", 
                    camelKey, underscoreKey, camelValue, underscoreValue, userKey, eventCode);
            }
        }
    }
    
    /**
     * 将对象转换为Map
     */
    private static Map<String, Object> convertToMap(Object value) {
        if (value == null) {
            return null;
        }
        
        if (value instanceof Map) {
            return (Map<String, Object>) value;
        }
        
        if (value instanceof String) {
            try {
                return JSON.parseObject((String) value, new TypeReference<Map<String, Object>>() {});
            } catch (Exception e) {
                return null;
            }
        }
        
        try {
            String jsonStr = JSON.toJSONString(value);
            return JSON.parseObject(jsonStr, new TypeReference<Map<String, Object>>() {});
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 记录Map差异日志
     */
    private static void logMapDifferences(String camelKey, String underscoreKey, MapDifference<String, Object> difference, 
                                        String userKey, String eventCode, String logTag) {
        // 记录值不同的键
        Map<String, MapDifference.ValueDifference<Object>> entriesDiffering = difference.entriesDiffering();
        for (Map.Entry<String, MapDifference.ValueDifference<Object>> entry : entriesDiffering.entrySet()) {
            MapDifference.ValueDifference<Object> valueDiff = entry.getValue();
            LoggerProxy.info(logTag, logger,
                "Map value difference - parentKey: {} / {}, subKey: {}, camelValue: {}, underscoreValue: {}, userKey: {}, eventCode: {}", 
                camelKey, underscoreKey, entry.getKey(), valueDiff.leftValue(), valueDiff.rightValue(), userKey, eventCode);
        }
        
        // 记录只在驼峰格式中存在的键
        Map<String, Object> entriesOnlyOnLeft = difference.entriesOnlyOnLeft();
        for (Map.Entry<String, Object> entry : entriesOnlyOnLeft.entrySet()) {
            LoggerProxy.info(logTag, logger,
                "Key only in camel format - parentKey: {} / {}, subKey: {}, value: {}, userKey: {}, eventCode: {}", 
                camelKey, underscoreKey, entry.getKey(), entry.getValue(), userKey, eventCode);
        }
        
        // 记录只在下划线格式中存在的键
        Map<String, Object> entriesOnlyOnRight = difference.entriesOnlyOnRight();
        for (Map.Entry<String, Object> entry : entriesOnlyOnRight.entrySet()) {
            LoggerProxy.info(logTag, logger,
                "Key only in underscore format - parentKey: {} / {}, subKey: {}, value: {}, userKey: {}, eventCode: {}", 
                camelKey, underscoreKey, entry.getKey(), entry.getValue(), userKey, eventCode);
        }
    }
    
    /**
     * 判断字符串是否为数字
     */
    private static boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}
