package com.youxin.risk.commons.model.datacenter;

import com.youxin.risk.commons.model.BaseModel;
import com.youxin.risk.commons.model.datacenter.common.VerifyCommonData;

import java.util.Date;

/**
 * verify基础数据推送新网信息表
 * 
 * <AUTHOR>
 * 
 * @date 2018-10-11
 */
public class DcSubmitXw extends BaseModel {

    /**
     * 贷款id
     */
    private Integer loanId;

    /**
     * 用户ID
     */
    private String userKey;

    /**
     * 来源系统
     */
    private String sourceSystem;

    /**
     * 电话号码
     */
    private String mobile;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String certNo;

    /**
     * 证件签发日期
     */
    private String certDate;

    /**
     * 证件到期日期
     */
    private String certExpiryDate;

    /**
     * 证件地址-省
     */
    private String certState;

    /**
     * 证件地址-市
     */
    private String certCity;

    /**
     * 证件地址-区
     */
    private String certDistCode;

    /**
     * 证件签发地
     */
    private String certPlace;

    /**
     * 证件签发机关
     */
    private String certAuthority;

    /**
     * 出生日期
     */
    private String birthDate;

    /**
     * 性别
     */
    private String gender;

    /**
     * 民族
     */
    private String ethnic;

    /**
     * 婚姻状态
     */
    private String maritalStatus;

    /**
     * 居住状态
     */
    private String livingCondition;

    /**
     * 地址类型
     */
    private String addressType;

    /**
     * 地址-省/州
     */
    private String provinceCode;

    /**
     * 地址-城市
     */
    private String cityCode;

    /**
     * 详细地址
     */
    private String addrStreet;

    /**
     * 银行卡号
     */
    private String bankCardno;

    /**
     * 银行卡预留手机号
     */
    private String bankMobile;

    /**
     * 借款金额
     */
    private String loanAmount;

    /**
     * 借款用途
     */
    private String loanPurpose;

    /**
     * 借款期限
     */
    private String loanTenor;

    /**
     * 借款期限单位 00-天 01-月 02-年
     */
    private String loanTenorUnit;

    /**
     * 借款利率 精确到小数点6位，如利率为0.123456 传123456
     */
    private String rate;

    private String professionalType;

    public String getIdcardF() {
        return idcardF;
    }

    public void setIdcardF(String idcardF) {
        this.idcardF = idcardF;
    }

    public String getIdcardB() {
        return idcardB;
    }

    public void setIdcardB(String idcardB) {
        this.idcardB = idcardB;
    }

    /**
     * 身份证正面图片URL
     */
    private String idcardF;

    /**
     * 身份证反面面图片URL
     */
    private String idcardB;

    /**
     * 银行卡类型 C-信用卡 D-储蓄卡
     */
    private String bankCardType;


    public Integer getLoanId() {
        return loanId;
    }

    public void setLoanId(Integer loanId) {
        this.loanId = loanId;
    }

    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey == null ? null : userKey.trim();
    }

    public String getSourceSystem() {
        return sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem == null ? null : sourceSystem.trim();
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile == null ? null : mobile.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo == null ? null : certNo.trim();
    }

    public String getCertDate() {
        return certDate;
    }

    public void setCertDate(String certDate) {
        this.certDate = certDate == null ? null : certDate.trim();
    }

    public String getCertExpiryDate() {
        return certExpiryDate;
    }

    public void setCertExpiryDate(String certExpiryDate) {
        this.certExpiryDate = certExpiryDate == null ? null : certExpiryDate.trim();
    }

    public String getCertState() {
        return certState;
    }

    public void setCertState(String certState) {
        this.certState = certState == null ? null : certState.trim();
    }

    public String getCertCity() {
        return certCity;
    }

    public void setCertCity(String certCity) {
        this.certCity = certCity == null ? null : certCity.trim();
    }

    public String getCertDistCode() {
        return certDistCode;
    }

    public void setCertDistCode(String certDistCode) {
        this.certDistCode = certDistCode == null ? null : certDistCode.trim();
    }

    public String getCertPlace() {
        return certPlace;
    }

    public void setCertPlace(String certPlace) {
        this.certPlace = certPlace == null ? null : certPlace.trim();
    }

    public String getCertAuthority() {
        return certAuthority;
    }

    public void setCertAuthority(String certAuthority) {
        this.certAuthority = certAuthority == null ? null : certAuthority.trim();
    }

    public String getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(String birthDate) {
        this.birthDate = birthDate ;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender == null ? null : gender.trim();
    }

    public String getEthnic() {
        return ethnic;
    }

    public void setEthnic(String ethnic) {
        this.ethnic = ethnic == null ? null : ethnic.trim();
    }

    public String getMaritalStatus() {
        return maritalStatus;
    }

    public void setMaritalStatus(String maritalStatus) {
        this.maritalStatus = maritalStatus == null ? null : maritalStatus.trim();
    }

    public String getLivingCondition() {
        return livingCondition;
    }

    public void setLivingCondition(String livingCondition) {
        this.livingCondition = livingCondition == null ? null : livingCondition.trim();
    }

    public String getAddressType() {
        return addressType;
    }

    public void setAddressType(String addressType) {
        this.addressType = addressType == null ? null : addressType.trim();
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    public String getAddrStreet() {
        return addrStreet;
    }

    public void setAddrStreet(String addrStreet) {
        this.addrStreet = addrStreet == null ? null : addrStreet.trim();
    }

    public String getBankCardno() {
        return bankCardno;
    }

    public void setBankCardno(String bankCardno) {
        this.bankCardno = bankCardno == null ? null : bankCardno.trim();
    }

    public String getBankMobile() {
        return bankMobile;
    }

    public void setBankMobile(String bankMobile) {
        this.bankMobile = bankMobile == null ? null : bankMobile.trim();
    }

    public String getLoanAmount() {
        return loanAmount;
    }

    public void setLoanAmount(String loanAmount) {
        this.loanAmount = loanAmount == null ? null : loanAmount.trim();
    }

    public String getLoanPurpose() {
        return loanPurpose;
    }

    public void setLoanPurpose(String loanPurpose) {
        this.loanPurpose = loanPurpose == null ? null : loanPurpose.trim();
    }

    public String getLoanTenor() {
        return loanTenor;
    }

    public void setLoanTenor(String loanTenor) {
        this.loanTenor = loanTenor == null ? null : loanTenor.trim();
    }

    public String getLoanTenorUnit() {
        return loanTenorUnit;
    }

    public void setLoanTenorUnit(String loanTenorUnit) {
        this.loanTenorUnit = loanTenorUnit == null ? null : loanTenorUnit.trim();
    }

    public String getRate() {
        return rate;
    }

    public void setRate(String rate) {
        this.rate = rate == null ? null : rate.trim();
    }


    public String getBankCardType() {
        return bankCardType;
    }

    public void setBankCardType(String bankCardType) {
        this.bankCardType = bankCardType == null ? null : bankCardType.trim();
    }

    public String getProfessionalType() {
        return professionalType;
    }

    public void setProfessionalType(String professionalType) {
        this.professionalType = professionalType;
    }
}