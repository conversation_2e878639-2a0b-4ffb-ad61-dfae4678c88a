package com.youxin.risk.commons.model;

import com.youxin.risk.commons.VariableValueTypeEnumNew;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.commons.constants.EngineAsyncRequestLogStatusEnum;
import com.youxin.risk.commons.utils.DateUtil;
import com.youxin.risk.commons.utils.StringUtils;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2023/10/13 14:26
 * @desc
 */
@Data
public class VariableAsyncRequestLog extends BaseModel {

    private static final long serialVersionUID = 3697975334578345445L;
    private String asyncRequestId;
    private String sessionId;
    private String loanKey;
    private String userKey;
    private String nodeId;
    private String step;
    /**
     * @see EngineAsyncRequestLogStatusEnum
     */
    private String status;
    private String variableCode;
    private String variableValue;
    private VariableValueTypeEnumNew variableValueType;
    private String errMsg;
    private Integer retryCount = 0;
    private Date retryTime;
    /**
     * 是否需要卡单
     */
    private Integer stuck;

    public void setRetry(String retryMsg) {
        this.setStatus(EngineAsyncRequestLogStatusEnum.RETRY.name());
        retryMsg = StringUtils.subString(retryMsg, 1000);
        this.setErrMsg(retryMsg);
        //如果不自动重试，则设置时间为 "2099-12-31 23:59:59"
        if (!isNeedAutoRetry(retryCount)) {
            this.setRetryTime(DateUtil.parse(DateUtil.FINAL_TIME, DateUtil.LONG_WEB_FORMAT));
        } else {
            this.setRetryTime(calNextRetryTime(30, this.getRetryCount()));
        }
        this.setRetryCount(this.getRetryCount() + 1);
    }

    @Override
    protected boolean isNeedAutoRetry(Number retryCount) {
        Integer variableRetryTimes = ApolloClientAdapter.getIntConfig(ApolloNamespaceEnum.ENGINE_SPACE, "variable" +
                ".retry.times", 3);
        return retryCount.intValue() < variableRetryTimes;
    }

    public static VariableAsyncRequestLog init(String requestId, String loanKey,String sessionId,String userKey,
                                               String prefetchVariable,
                                               String currentNodeId, String step, boolean needStuck) {
        //todo
        VariableAsyncRequestLog variableAsyncRequestLog = new VariableAsyncRequestLog();
        variableAsyncRequestLog.setAsyncRequestId(requestId);
        variableAsyncRequestLog.setLoanKey(loanKey);
        variableAsyncRequestLog.setSessionId(sessionId);
        variableAsyncRequestLog.setUserKey(userKey);
        variableAsyncRequestLog.setStatus(EngineAsyncRequestLogStatusEnum.INIT.name());
        variableAsyncRequestLog.setVariableCode(prefetchVariable);
        variableAsyncRequestLog.setNodeId(currentNodeId);
        variableAsyncRequestLog.setStep(step);
        variableAsyncRequestLog.setStuck(needStuck ? 1 : 0);
        return variableAsyncRequestLog;
    }
}
