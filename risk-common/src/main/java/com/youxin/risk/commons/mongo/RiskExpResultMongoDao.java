package com.youxin.risk.commons.mongo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCursor;
import com.mongodb.DBObject;
import com.mongodb.WriteResult;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.LimitOperation;
import org.springframework.data.mongodb.core.query.BasicQuery;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.text.ParseException;
import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @create 2023/3/31 9:20
 * @desc
 */
public abstract class RiskExpResultMongoDao<T> extends BaseMongoDao {
    protected static final Logger LOGGER = LoggerFactory.getLogger(RiskExpResultMongoDao.class);

    @Autowired
    @Qualifier("riskExpMongoTemplate")
    protected MongoTemplate riskExpMongoTemplate;

    @Override
    public void insert(Object obj) {
        LoggerProxy.info("insertToMongo", LOGGER, "insert data to mongo, collectionName={}", collectionName);
        long start = System.currentTimeMillis();
        riskExpMongoTemplate.insert(obj, collectionName);
        LOGGER.info("collectionName:{}; insertTime:{} ,insertToMongo time used:{}", collectionName,
                System.currentTimeMillis(), System.currentTimeMillis() - start);
    }

    abstract Set<String> getQueryKeys();

    /**
     * 获取返回对象的类信息
     *
     * @return
     */
    abstract Class<T> getResultVOClass();

    public  List<T> selectList(Map<String, Object> params) {
        BasicDBObject queryObject = new BasicDBObject();
        for (String queryKey : getQueryKeys()) {
            if (params.containsKey(queryKey) && params.get(queryKey) != null && !"".equals(params.get(queryKey))) {
                queryObject.put(queryKey, params.get(queryKey));
            }
        }
        Object startTime = params.get("startTime");
        Object endTime = params.get("endTime");
        if (startTime != null && endTime != null) {
            // 兼容老的查询
            try{
                Date startDate = new Date(Long.parseLong(String.valueOf(startTime)));
                Date endDate = new Date(Long.parseLong(String.valueOf(endTime)));
                queryObject.put("createTime", new BasicDBObject("$gt", startDate).append("$lte", endDate));
            }catch (Exception e){
                try {
                    Date startDate = DateUtils.parseDate((String) startTime, "yyyy-MM-dd HH:mm:ss");
                    Date endDate = DateUtils.parseDate((String) endTime, "yyyy-MM-dd HH:mm:ss");
                    queryObject.put("createTime", new BasicDBObject("$gt", startDate).append("$lte", endDate));
                } catch (ParseException ex) {
                    // nothing
                }
            }
        }
        BasicDBObject fieldObject = new BasicDBObject();
        fieldObject.put("request", false);
        fieldObject.put("response", false);
        fieldObject.put("productionResponse", false);
        fieldObject.put("diffResultList", false);
        int pageNo = params.containsKey("pageNum") ? ((Integer) params.get("pageNum")) - 1 : 0;
        int pageSize = params.containsKey("pageSize") ? (Integer) params.get("pageSize") : 20;
        Pageable pageable = new PageRequest(pageNo, pageSize);
        LOGGER.info("queryKeys ={}, isFinal={}", JSON.toJSONString(queryObject), queryObject.get("isFinal"));
        Query query = new BasicQuery(queryObject, fieldObject);
        query.with(pageable);
        query.with(new Sort(new Sort.Order(Sort.Direction.DESC, "createTime")));
        LOGGER.info("Generated MongoDB Query:{}", query.toString() );
        return riskExpMongoTemplate.find(query, getResultVOClass(), collectionName);
    }

    public List<T> selectListForExport(Map<String, Object> params, long totalCount) {
        List<T> allExperimentResults = new ArrayList<>();
        int batchSize = 1000;
        while (totalCount - batchSize >= 0) {
            buildParamsAndQuery(params, batchSize, allExperimentResults);
            totalCount = totalCount - batchSize;
        }
        if (totalCount > 0) {
            buildParamsAndQuery(params, totalCount, allExperimentResults);
        }
        return allExperimentResults;
    }

    private void buildParamsAndQuery(Map<String, Object> params, long limitSize,
                                     List<T> allStrategyExperimentResults) {
        params.put("limit", limitSize);
        params.put("skip", allStrategyExperimentResults.size());
        allStrategyExperimentResults.addAll(selectListForExportBatch(params));
    }

    public List<T> selectListForExportBatch(Map<String, Object> params) {
        BasicDBObject queryObject = new BasicDBObject();
        for (String queryKey : getQueryKeys()) {
            if (params.containsKey(queryKey) && params.get(queryKey) != null && !"".equals(params.get(queryKey))) {
                queryObject.put(queryKey, params.get(queryKey));
            }
        }
        Object startTime = params.get("startTime");
        Object endTime = params.get("endTime");
        if (startTime != null && endTime != null) {
            Date startDate = new Date(Long.valueOf(String.valueOf(startTime)));
            Date endDate = new Date(Long.valueOf(String.valueOf(endTime)));
            queryObject.put("createTime", new BasicDBObject("$gt", startDate).append("$lte", endDate));
        }
        BasicDBObject fieldObject = new BasicDBObject();
        fieldObject.put("request", false);
        fieldObject.put("response", false);
        fieldObject.put("productionResponse", false);

        Query query = new BasicQuery(queryObject, fieldObject);
        if (params.containsKey("skip")) {
            query.skip((int) params.get("skip"));
        }
        query.limit(((Long) params.get("limit")).intValue());
        query.with(new Sort(new Sort.Order(Sort.Direction.DESC, "createTime")));
        return riskExpMongoTemplate.find(query, getResultVOClass(), collectionName);
    }

    public List<T> selectListForExportNew(Map<String, Object> params) {
        BasicDBObject queryObject = new BasicDBObject();
        for (String queryKey : getQueryKeys()) {
            if (params.containsKey(queryKey) && params.get(queryKey) != null && !"".equals(params.get(queryKey))) {
                queryObject.put(queryKey, params.get(queryKey));
            }
        }
        Object startTime = params.get("startTime");
        Object endTime = params.get("endTime");
        if (startTime != null && endTime != null) {
            queryObject.put("createTime", new BasicDBObject("$gt", parseDate(startTime)).append("$lte", parseDate(endTime)));
        }
        BasicDBObject fieldObject = new BasicDBObject();
        fieldObject.put("request", false);
        int pageNo = params.containsKey("pageNum") ? ((Integer) params.get("pageNum")) - 1 : 0;
        int pageSize = params.containsKey("pageSize") ? (Integer) params.get("pageSize") : 20;
        Pageable pageable = new PageRequest(pageNo, pageSize);
        Query query = new BasicQuery(queryObject, fieldObject);
        query.with(pageable);
        query.with(new Sort(new Sort.Order(Sort.Direction.DESC, "createTime")));
        return riskExpMongoTemplate.find(query, getResultVOClass(), collectionName);
    }


    private Date parseDate(Object dateTime) {
        Date date = null;
        try{
            date = new Date(Long.parseLong(String.valueOf(dateTime)));
        }catch (Exception e){
            try {
                date = DateUtils.parseDate((String) dateTime, "yyyy-MM-dd HH:mm:ss");
            } catch (ParseException ex) {
                // nothing
            }
        }
        return date;
    }

    public long selectCount(Map<String, Object> params) {
        Query query = new Query();
        for (String queryKey : getQueryKeys()) {
            if (params.containsKey(queryKey) && params.get(queryKey) != null && !"".equals(params.get(queryKey))) {
                query.addCriteria(Criteria.where(queryKey).is(params.get(queryKey)));
            }
        }
        Object startTime = params.get("startTime");
        Object endTime = params.get("endTime");
        if (startTime != null && endTime != null) {
            query.addCriteria(Criteria.where("createTime").gt(parseDate(startTime)).lte(parseDate(endTime)));
        }
        return riskExpMongoTemplate.count(query, collectionName);
    }

    public T getById(String id) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(id)).limit(1);
        return riskExpMongoTemplate.findOne(query, getResultVOClass(), collectionName);
    }

    /**
     * 其中FeatureExperimentResult、StrategyExperimentResult 是固定集合 无需删除
     *
     * @param expCode
     */
    public void removeByExpCode(String expCode) {
        Query query = Query.query(Criteria.where("expCode").is(expCode));
        riskExpMongoTemplate.remove(query, getResultVOClass());
    }

    /**
     * 其中FeatureExperimentResult、StrategyExperimentResult 是固定集合 无需删除
     * @param date
     * @param limitNum
     * @return
     */
    @Deprecated
    public WriteResult deleteByTime(Date date, int limitNum) {
        Query query = new Query();
        Criteria criteria = Criteria.where("createTime").lt(date);
        query.addCriteria(criteria);
        query.limit(limitNum);
        return this.riskExpMongoTemplate.remove(query, collectionName);
    }

    /**
     * 根据expCode获取step
     *
     * @param expCode
     * @return
     */
    public List getDistinctStepByExpCode(String expCode) {
        if (StringUtils.isEmpty(expCode)) {
            return new ArrayList();
        }
        DBObject dbObject = new BasicDBObject();
        dbObject.put("expCode", expCode);

        DBObject fieldObject = new BasicDBObject();
        fieldObject.put("step", true);

        DBCursor dbCursor = riskExpMongoTemplate.getCollection(collectionName).find(dbObject, fieldObject).limit(100);
        Iterator<DBObject> iterator = dbCursor.iterator();
        Set<String> steps = new HashSet<>();
        while (iterator.hasNext()) {
            DBObject dbObjectItem = iterator.next();
            steps.add(dbObjectItem.get("step").toString());
        }

        return new ArrayList<>(steps);
    }

    public T getConditionResultById(String id, String[] noQuerys, String[] querys) {
        DBObject dbObject = new BasicDBObject();
        BasicDBObject fieldObject = new BasicDBObject();
        if (noQuerys != null) {
            for (String noQuery : noQuerys) {
                fieldObject.put(noQuery, false);
            }
        }
        if (querys != null) {
            for (String query : querys) {
                fieldObject.put(query, true);
            }
        }
        Query query = new BasicQuery(dbObject, fieldObject);
        query.addCriteria(Criteria.where("_id").is(id)).limit(1);
        return riskExpMongoTemplate.findOne(query, getResultVOClass(), collectionName);
    }

    public int countByExpCode(String expCode) {
        Aggregation aggregation = Aggregation.newAggregation(Aggregation.match(Criteria.where("expCode").is(expCode)),
                Aggregation.group("loanKey"),
                Aggregation.count().as("count"));
        AggregationResults<JSONObject> results = riskExpMongoTemplate.aggregate(aggregation, collectionName,
                JSONObject.class);
        LOGGER.info("根据实验编码查询loanKey的笔数：查询结果={}", JSON.toJSONString(results));
        if (CollectionUtils.isEmpty(results.getMappedResults())) {
            return -1;
        }
        return results.getMappedResults().get(0).getInteger("count");
    }

}
