package com.youxin.risk.commons.dao.admin;

import java.util.Date;
import java.util.List;

import com.youxin.risk.commons.model.SysUpdateTime;

/**
 * 配置通知表dao mapper
 * 
 * <AUTHOR>
 *
 */

public interface SysUpdateTimeMapper {

    int updateByTable(SysUpdateTime record);

    int updateTimeByTable(SysUpdateTime record);

    List<SysUpdateTime> getUpdateTimeByTable(String tableName);

    SysUpdateTime selectUpdateTimeByTable(String tableName);

    Date getMaxTimeByTable(String tableName);

}
