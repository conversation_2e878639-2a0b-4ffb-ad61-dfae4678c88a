package com.youxin.risk.commons.utils;

import org.apache.commons.lang.StringUtils;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.StringWriter;
import java.io.Writer;
import java.util.Map;

public class EmojiUtil {
    private static final String EMOJI_UNICODE = "[\\ud800\\udc00-\\udbff\\udfff\\ud800-\\udfff\\ufffe\\uffff]";
    private static final String EMOJI_REPLACE_MENT = "";

    private static void translate(final CharSequence input, final Writer out) throws IOException {
        if (out == null) {
            throw new IllegalArgumentException("The Writer must not be null");
        }
        if (input == null) {
            return;
        }
        int pos = 0;
        final int len = input.length();
        while (pos < len) {
            final int consumed = translate(input, pos, out);
            if (consumed == 0) {
                // inlined implementation of Character.toChars(Character.codePointAt(input, pos))
                // avoids allocating temp char arrays and duplicate checks
                char c1 = input.charAt(pos);
                out.write(c1);
                pos++;
                if (Character.isHighSurrogate(c1) && pos < len) {
                    char c2 = input.charAt(pos);
                    if (Character.isLowSurrogate(c2)) {
                        out.write(c2);
                        pos++;
                    }
                }
                continue;
            }
            // contract with translators is that they have to understand codepoints
            // and they just took care of a surrogate pair
            for (int pt = 0; pt < consumed; pt++) {
                pos += Character.charCount(Character.codePointAt(input, pos));
            }
        }
    }

    private static int translate(final CharSequence input, final int index, final Writer out) throws IOException {
        if (input.charAt(index) == '\\' && index + 1 < input.length() && input.charAt(index + 1) == 'u') {
            // consume optional additional 'u' chars
            int i = 2;
            while (index + i < input.length() && input.charAt(index + i) == 'u') {
                i++;
            }
            if (index + i < input.length() && input.charAt(index + i) == '+') {
                i++;
            }
            if (index + i + 4 <= input.length()) {
                // Get 4 hex digits
                final CharSequence unicode = input.subSequence(index + i, index + i + 4);
                try {
                    final int value = Integer.parseInt(unicode.toString(), 16);
                    if (isEmojiCharacter((char) value)) {
                        // 如果非emoji，则保持原样
                        out.write("\\u" + unicode);
                    } else {
                        // emoji标签替换
                        out.write(EMOJI_REPLACE_MENT);
                    }
                } catch (final NumberFormatException nfe) {
                    throw new IllegalArgumentException("Unable to parse unicode value: " + unicode, nfe);
                }
                return i + 4;
            }
            throw new IllegalArgumentException("Less than 4 hex digits in unicode value: '"
                    + input.subSequence(index, input.length()) + "' due to end of CharSequence");
        }
        return 0;
    }

    private static boolean isEmojiCharacter(char codePoint) {
        boolean ret = (codePoint == 0x0) || (codePoint == 0x9) || (codePoint == 0xA) || (codePoint == 0xD)
                || ((codePoint >= 0x20) && (codePoint <= 0xD7FF)) || ((codePoint >= 0xE000) && (codePoint <= 0xFFFD))
                || ((codePoint >= 0x10000) && (codePoint <= 0x10FFFF));
        return ret;
    }

    public static String emojiReplace(String source) {
        if (StringUtils.isEmpty(source)) {
            return source;
        }
        // 替换unicode
        source = source.replaceAll(EMOJI_UNICODE, EMOJI_REPLACE_MENT);
        String org = source;
        try {
            // 替换\\u****
            StringWriter writer = new StringWriter(source.length() * 2);
            translate(source, writer);
            String ret = writer.toString();
            // 替换\\u****产生的\[emoji]导致解析出错问题
            char[] cs = ret.toCharArray();
            StringBuilder sb = new StringBuilder();
            for (int i = 0, size = cs.length; i < size; ) {
                // 如果连续是\[e的字符，去掉\
                if ('\\' == cs[i] && (i + 2) < size && '[' == cs[i + 1] && 'e' == cs[i + 2]) {
                    sb.append("[e");
                    i += 3;
                } else {
                    sb.append(cs[i]);
                    i += 1;
                }

            }
            return sb.toString();
        } catch (Exception e) {
            // nothing
        }
        return org;
    }

    public static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        try {
            for (int i = 0; i < len; i += 2) {
                data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4) + Character.digit(s.charAt(i + 1), 16));
            }
        } catch (Exception e) {
            // Log.d("", "Argument(s) for hexStringToByteArray(String s)"+ "was not a hex string");
        }
        return data;
    }

    public static String unicode(String source) {
        StringBuffer sb = new StringBuffer();
        char[] sourceChar = source.toCharArray();
        String unicode = null;
        for (int i = 0; i < sourceChar.length; i++) {
            unicode = Integer.toHexString(sourceChar[i]);
            if (unicode.length() <= 2) {
                unicode = "00" + unicode;
            }
            sb.append("\\u" + unicode);
        }
        return sb.toString();
    }

    public static void main(String[] args) throws FileNotFoundException, IOException {
        String s = new String(hexStringToByteArray("F09F9096"));
        // System.out.println(s);
        // System.out.println(unicode(s));
        System.out
                .println("\\ud83d\\udc37\\u5b9d\\u5b9d---\ud83d\udc37\u5b9d\u5b9d----\\\\ud83d\\\\udc37\\u5b9d\\u5b9d");
        System.out.println("===" + emojiReplace(
                "\\ud83d\\udc37\\u5b9d\\u5b9d---\ud83d\udc37\u5b9d\u5b9d----\\\\ud83d\\\\udc37\\u5b9d\\u5b9d"));
        // String ss = emojiReplace("\\\\ud83d\\\\udc37\\u5b9d\\u5b9d\\[ee");
        // char[] cs = ss.toCharArray();
        // StringBuilder sb = new StringBuilder();
        // System.out.println(cs.length);
        // for (int i = 0, size = cs.length; i < size; ) {
        // if ('\\' == cs[i] && (i + 2) < size && '[' == cs[i + 1] && 'e' == cs[i + 2]) {
        // sb.append("[e");
        // i+=3;
        // } else {
        // sb.append(cs[i]);
        // i+=1;
        // }
        //
        // }
        // System.out.println(sb.toString());

        s = "{\"name\":\"fsfs\\\\dfs\\\\\\^111\"}";
        System.out.println(s);
        StringBuilder sb = new StringBuilder();
        char c;
        int count = 0;
        for (int i = 0; i < s.length(); i++) {
            c = s.charAt(i);
            if(c == '\\'){
                count++;
            }else{
                if(count != 0 && 0 != count%2){
                    sb.append("\\");
                }
                count = 0;
            }
            sb.append(c);
        }

        System.out.println(JacksonUtil.toObject(sb.toString(),Map.class));

    }
}