mode.name=prod
home.base=/opt/app/tomcat
app.home=${home.base}/webapps/ROOT
app.log.home=${catalina.base}/logs

tomcat.home=${home.base}/products/tomcat/tomcat_risk_admin
tomcat.port=8011
tomcat.shutdown.port=8012
tomcat.connection.timeout=5000
tomcat.doc.base=${app.home}
tomcat.allow.ips=172.*.*.*||127.0.0.1||10.*.*.*

java.opts=-Xmx3550m -Xms3550m -Xmn1500m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=128m -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xloggc:$CATALINA_HOME/logs/gc.log -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$CATALINA_HOME/logs/oom.log -Djava.nio.channels.spi.SelectorProvider=sun.nio.ch.EPollSelectorProvider

console.log.level=OFF

youxin.env=PROD
apollo.mate=http://risk-apollo-configservice.rrdbg.com

# mysql config
datasource.maxActive=5
datasource.initialSize=2
datasource.minIdle=2
datasource.maxWait=5000
datasource.testOnBorrow=true
datasource.defaultTransactionIsolation=4
datasource.timeBetweenEvictionRunsMillis=30000
datasource.minEvictableIdleTimeMillis=300000
datasource.timeBetweenLogStatsMillis=300000
datasource.druid.remove.abandoned=true
datasource.druid.remove.abandoned.timeout=300
datasource.druid.log.abandoned=false
datasource.connectProperties.connectTimeout=1000
datasource.connectProperties.socketTimeout=30000
datasource.logAbandoned=false
datasource.removeAbandoned=true
datasource.removeAbandonedTimeout=120
datasource.poolPreparedStatements=false
datasource.filters=stat,wall

datasource.admin.maxActive=10
datasource.admin.initialSize=2
datasource.admin.minIdle=2

datasource.engine.maxActive=1
datasource.engine.initialSize=1
datasource.engine.minIdle=1

risk.datasource.url=rm-2ze156g274ol8w8ik.mysql.rds.aliyuncs.com:3306

admin.datasource.url=jdbc:mysql://${risk.datasource.url}/risk_admin?${datasource.url.params}
admin.datasource.username=${SEC_RISK_ADMIN_DB_USERNAME}
admin.datasource.pwd=${SEC_RISK_ADMIN_DB_PASSWORD}

engine.datasource.url=jdbc:mysql://${risk.datasource.url}/risk_engine?${datasource.url.params}
engine.datasource.username=${SEC_RISK_ENGINE_DB_USERNAME}
engine.datasource.pwd=${SEC_RISK_ENGINE_DB_PASSWORD}

fs.datasource.url=jdbc:mysql://${risk.datasource.url}/risk_engine?${datasource.url.params}
fs.datasource.username=${SEC_RISK_ENGINE_DB_USERNAME}
fs.datasource.pwd=${SEC_RISK_ENGINE_DB_PASSWORD}

gw.datasource.url=jdbc:mysql://${risk.datasource.url}/risk_gateway?${datasource.url.params}
gw.datasource.username=${SEC_RISK_GATEWAY_DB_USERNAME}
gw.datasource.pwd=${SEC_RISK_GATEWAY_DB_PASSWORD}

alertlog.datasource.url=jdbc:mysql://${risk.datasource.url}/risk_alert?${datasource.url.params}
alertlog.datasource.username=${SEC_RISK_ALERT_DB_USERNAME}
alertlog.datasource.pwd=${SEC_RISK_ALERT_DB_PASSWORD}

channel.datasource.url=jdbc:mysql://${risk.datasource.url}/risk_channel?${datasource.url.params}
channel.datasource.username=${SEC_RISK_CHANNEL_DB_USERNAME}
channel.datasource.pwd=${SEC_RISK_CHANNEL_DB_PASSWORD}

verify.datasource.url=*******************************************************************************?${datasource.url.params}
verify.datasource.username=${SEC_RISK_PAYDAYLOAN_VERIFY_DB_USERNAME}
verify.datasource.pwd=${SEC_RISK_PAYDAYLOAN_VERIFY_DB_PASSWORD}

haohuan.verify.datasource.url=*******************************************************************************?${datasource.url.params}
haohuan.verify.datasource.username=${SEC_RISK_PAYDAYLOAN_VERIFY_DB_USERNAME}
haohuan.verify.datasource.pwd=${SEC_RISK_PAYDAYLOAN_VERIFY_DB_PASSWORD}

verify.sharding.datasource.url=********************************************************************************?${datasource.url.params}
verify.sharding.datasource.username=${SEC_RISK_PAYDAYLOAN_VERIFY2_DB_USERNAME}
verify.sharding.datasource.pwd=${SEC_RISK_PAYDAYLOAN_VERIFY2_DB_PASSWORD}

datacenter.datasource.url=*****************************************************************************?${datasource.url.params}
datacenter.datasource.username=${SEC_RISK_DATACENTER_DB_USERNAME}
datacenter.datasource.pwd=${SEC_RISK_DATACENTER_DB_PASSWORD}


rm.datasource.url=*************************************************************************?${datasource.url.params}
rm.datasource.username=${SEC_RISK_MANAGE_DB_USERNAME}
rm.datasource.pwd=${SEC_RISK_MANAGE_DB_PASSWORD}

mm.datasource.url=*******************************************************************************?${datasource.url.params}
mm.datasource.username=${SEC_RISK_MODEL_MANAGE_DB_USERNAME}
mm.datasource.pwd=${SEC_RISK_MODEL_MANAGE_DB_PASSWORD}

#rrd.verify.datasource.url=************************************************************************************
#rrd.verify.datasource.username=${SEC_RISK_RRD_VERIFY_DB_USERNAME}
#rrd.verify.datasource.pwd=${SEC_RISK_RRD_VERIFY_DB_PASSWORD}


cp.datasource.url=********************************************************************************?${datasource.url.params}
cp.datasource.username=${SEC_RISK_COMPUTING_PLATFORM_DB_USERNAME}
cp.datasource.pwd=${SEC_RISK_COMPUTING_PLATFORM_DB_PASSWORD}

di.datasource.url=*********************************************************************?${datasource.url.params}
di.datasource.username=${SEC_RISK_DI_DB_USERNAME}
di.datasource.pwd=${SEC_RISK_DI_DB_PASSWORD}
# mysql config end

# redis config start
redis.maxTotal=300
redis.maxIdle=30
redis.minIdle=10
redis.maxWaitMillis=5000
redis.testOnBorrow=true
redis.cluster.connectionTimeout=3000
redis.cluster.soTimeout=3000
redis.cluster.maxAttempts=1
redis.cluster.password=7b6abee0-05c1-43
redis.cluster.nodes=r-2ze7d1da6d634a94.redis.rds.aliyuncs.com:6379

riskDiNoRdbAof.redis.connectionTimeout=3000
riskDiNoRdbAof.redis.soTimeout=3000
riskDiNoRdbAof.redis.maxAttempts=1
riskDiNoRdbAof.redis.password=${redis.cluster.password}
riskDiNoRdbAof.redis.nodes=${redis.cluster.nodes}

fs.exp.redis.cluster.connectionTimeout=3000
fs.exp.redis.nodes=${redis.cluster.nodes}
fs.exp.redis.password=${redis.cluster.password}
fs.exp.redisson.maxPoolSize=128
fs.exp.redisson.minPoolSize=48

verify.redis.cluster.nodes=${redis.cluster.nodes}
verify.redis.cluster.password=${redis.cluster.password}
# redis config end

# mongo config start
mongo.authentication.database=admin
mongo.host=dds-2ze273fece514f241.mongodb.rds.aliyuncs.com:3717,dds-2ze273fece514f242.mongodb.rds.aliyuncs.com:3717
mongo.username=${SEC_RISK_ADMIN_14_92_TRANSFER_MONGODB_USERNAME}
mongo.password=${SEC_RISK_ADMIN_14_92_TRANSFER_MONGODB_PASSWORD}
mongo.database=transfer
mongo.credentials=${mongo.username}:${mongo.password}@${mongo.database}

mongo.risk.host=dds-2zea5b7d50dd08a41.mongodb.rds.aliyuncs.com:3717,dds-2zea5b7d50dd08a42.mongodb.rds.aliyuncs.com:3717
mongo.risk.username=${SEC_RISK_ADMIN_15_41_RISK_MONGODB_USERNAME}
mongo.risk.password=${SEC_RISK_ADMIN_15_41_RISK_MONGODB_PASSWORD}
mongo.risk.database=risk
mongo.risk.credentials=${mongo.risk.username}:${mongo.risk.password}@${mongo.risk.database}


#
mongo.event.host1=dds-2zec5d9386f87d442.mongodb.rds.aliyuncs.com:3717,dds-2zec5d9386f87d441.mongodb.rds.aliyuncs.com:3717
mongo.event.username1=${SEC_RISK_ENGINE_13_61_RISK_ENGINE1_MONGODB_USERNAME}
mongo.event.password1=${SEC_RISK_ENGINE_13_61_RISK_ENGINE1_MONGODB_PASSWORD}
mongo.event.database1=risk_engine
mongo.event.auth.database1=risk_engine
mongo.event.database1.write=true
mongo.event.database1.read=true
mongo.event.host1.init=true
#
mongo.event.host2=dds-2zea54289de13af41.mongodb.rds.aliyuncs.com:3717,dds-2zea54289de13af42.mongodb.rds.aliyuncs.com:3717
mongo.event.username2=${SEC_RISK_ENGINE_13_63_RISK_ENGINE2_MONGODB_USERNAME}
mongo.event.password2=${SEC_RISK_ENGINE_13_63_RISK_ENGINE2_MONGODB_PASSWORD}
mongo.event.database2=risk_engine
mongo.event.auth.database2=risk_engine
mongo.event.database2.write=false
mongo.event.database2.read=false
mongo.event.host2.init=false

mongo.riskExp.host=dds-2ze4fde7c7b405742.mongodb.rds.aliyuncs.com:3717,dds-2ze4fde7c7b405741.mongodb.rds.aliyuncs.com:3717
mongo.riskExp.username=${SEC_RISK_ADMIN_13_12_RISK_EXP_MONGODB_USERNAME}
mongo.riskExp.password=${SEC_RISK_ADMIN_13_12_RISK_EXP_MONGODB_PASSWORD}
mongo.riskExp.database=risk_exp
mongo.riskExp.credentials=${mongo.riskExp.username}:${mongo.riskExp.password}@${mongo.riskExp.database}

#
transfer.mongo.host=dds-2ze4fde7c7b405742.mongodb.rds.aliyuncs.com:3717,dds-2ze4fde7c7b405741.mongodb.rds.aliyuncs.com:3717
transfer.mongo.credentials=${mongo.username}:${mongo.password}@${mongo.database}
# mongo config end

# kafka config start
kafka.dp.hosts=alikafka-pre-cn-wwo3mzyef002-1-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-wwo3mzyef002-2-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-wwo3mzyef002-3-vpc.alikafka.aliyuncs.com:9092
kafka.mirror.dp.hosts=${kafka.dp.hosts}
kafka.cloud.host=kafka1.cdh.app.rrd:9092,kafka2.cdh.app.rrd:9092,kafka3.cdh.app.rrd:9092
kafka.library.host=${kafka.dp.hosts}
kafka.calculate.host=${kafka.dp.hosts}
# kafka config end

# metrics config start
metrics.stop=false
metrics.point.kafka.hosts=${kafka.dp.hosts}
metrics.point.mirror.kafka.hosts=${kafka.dp.hosts}

metrics.point.kafka.topic=metrics.point.kafka.topic.gateway
metrics.point.kafka.group.id=metrics.point.kafka.group
metrics.point.kafka.topic.list=metrics.point.kafka.topic,metrics.point.kafka.topic.gateway

#
metrics.remote.queue.server=${redis.cluster.nodes}
metrics.remote.queue.redis.password=${redis.cluster.password}
# metrics config end

# infulxDb config
metrics.influxdb.server=http://*************:8086
metrics.influxdb.server.username=risk_influx
metrics.influxdb.server.password=cVuyOkUwwlXzICqK

influx.url=http://*************:8086
influx.user=risk_influx
influx.password=cVuyOkUwwlXzICqK

metrics.influxdb2.server=http://*************:8086
metrics.influxdb2.server.username=risk_influx
metrics.influxdb2.server.password=cVuyOkUwwlXzICqK

# es config todo
es.server.cluster.name=es-icbg-cluster
es.server.cluster.server=***********:9200,***********:9200,***********:9200

#hbase
zookeeper.port=2181
zookeeper.quorum=***********,***********,***********,***********,***********
hadoop.user=hdfs

# xxljob config
xxl.job.admin.addresses=http://risk-xxl-job-manager.weicai.com.cn
xxl.job.accessToken=
xxl.job.executor.appname=${app.name}
xxl.job.executor.address=
xxl.job.executor.ip=
xxl.job.executor.port=-1
xxl.job.executor.logpath=/tmp/
xxl.job.executor.logretentiondays=-1
# xxljob config end

# mail config
mail.from=<EMAIL>
mail.host=mail.qf.youxin.com
mail.password=Jp_37Kc8MaHvCKbH
mail.port=25
mail.properties.mail.debug=false
mail.properties.mail.smtp.auth=true
mail.properties.mail.smtp.timeout=10000
mail.username=<EMAIL>
# mail config end

# service url
# wechat
wechat.robot.url = https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=
feature.wechat.bot.key=5bc5940f-7e07-4a1a-9095-a297f5080fb7
system.check.robotKey=52e99fd4-0f49-473d-ae8e-11b2762891d5
wechat.base.url=https://qyapi.weixin.qq.com/cgi-bin
wechat.credit.warn.url=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=
wechat.risk.plat.aesKey=Q1DYbd7Cvmvwq2C4o2GHSKh5OlLI39KEB3V8MAxaJ11
wechat.risk.plat.corpId=ww63478d6a674cf652
wechat.risk.plat.token=6GpB4bENhIYWN3ZWYvxaTE

# risk
risk.di.url=http://antifraud-risk-di.rrdbg.com
di.url=http://antifraud-risk-di.rrdbg.com
dc.url=http://antifraud-risk-datacenter.rrdbg.com
risk-datacenter.url=http://antifraud-risk-datacenter.rrdbg.com
risk.dc.url=http://antifraud-risk-datacenter.rrdbg.com
risk.dc.inside.url=http://antifraud-risk-datacenter-inside.weicai.com.cn
dc.inside.url=http://antifraud-risk-datacenter-inside.weicai.com.cn
dc.inside.url.heika=http://antifraud-risk-datacenter-inside.51hika.com
dc.inside.url.rongdan=http://antifraud-risk-datacenter-inside.xmwcrd.cn
risk.engine.url=http://antifraud-risk-process-engine.rrdbg.com
risk.service.url.engine=http://antifraud-risk-process-engine.rrdbg.com
risk.service.url.engine.batch=http://antifraud-risk-process-engine-batch.weicai.com.cn
risk.service.url.engine.mirror=http://antifraud-risk-process-engine-mirror.weicai.com.cn
engine=http://antifraud-risk-process-engine.rrdbg.com
process.engine=http://antifraud-risk-process-engine.rrdbg.com
risk.alert.send.url=http://antifraud-risk-alert.rrdbg.com/alert/api/event/riskAlert/v1
gw.service.url=http://antifraud-risk-gateway.rrdbg.com/risk/api/analyse/v2
risk.gateway.url=http://antifraud-risk-gateway.rrdbg.com
gateway.url=http://antifraud-risk-gateway.rrdbg.com
risk.service.url.gateway=http://antifraud-risk-gateway.rrdbg.com
risk.admin.url=http://antifraud-risk-admin.weicai.com.cn
block.qw.op.url=https://antifraud-risk-admin.we.cn/api/block/qw?
block.qw.robot.key=1eef945d-0813-4e74-bcb7-1efb99fb525f
rm.url=http://riskmanagement.rrdbg.com
fs.service.url=http://riskmanagement.rrdbg.com
rmBatch.url=http://antifraud-risk-fs-batch.weicai.com.cn
fp.url=http://antifraud-risk-fp.weicai.com.cn
alert.base.url=http://antifraud-risk-alert.rrdbg.com/
alert.collect.config.url=http://antifraud-risk-alert.rrdbg.com/alert/admin/
ra.url=http://api.riskanalysis.ucredit.com
ra.base.url=http://api.riskanalysis.ucredit.com
verify.url=http://antifraud-risk-verify.weicai.com.cn
verify.manage.baseurl=http://manage.kuaisujiekuan.com/
risk.verify.url=http://antifraud-risk-verify.weicai.com.cn
channel.forward.url=http://antifraud-risk-channel.rrdbg.com/fail
channel.url=http://antifraud-risk-channel.rrdbg.com
risk.approve.service.url=http://antifraud-risk-approve.weicai.com.cn
risk.assistant.url=http://risk-assistant.rrdbg.com
cd.url=http://antifraud-risk-credit-driver.rrdbg.com
cp.auth.host=http://cp-web.weicai.com.cn/api
cp.host=http://cp-web.weicai.com.cn
cp.util.getUserParent=http://************:80/api/user/getUserParent?sourceUser=
weicai.url=http://antifraud-risk-admin.weicai.com.cn/api
weicai_heika_gw_url=http://risk-api-gateway.rrdbg.com/heiKaApi
weicai_pudao_gw_url=http://risk-api-gateway.rrdbg.com/puDaoApi
weicai_rongdan_gw_url=http://risk-api-gateway.rrdbg.com/rongDanApi
heika.engine.url=http://antifraud-risk-process-engine-heika.weicai.com.cn
heika.fs.service.url=http://antifraud-risk-fs-heika.weicai.com.cn
heika.url=http://antifraud-risk-admin-heika.weicai.com.cn/api
heika_rongdan_gw_url=http://risk-api-gateway-heika.weicai.com.cn/rongDanApi
heika_weicai_gw_url=http://risk-api-gateway-heika.weicai.com.cn/weiCaiApi
pudao.engine.url=http://antifraud-risk-process-engine-pudao.weicai.com.cn
pudao.fs.service.url=http://antifraud-risk-fs-pudao.weicai.com.cn
pudao.url=http://risk-ui-pudao.weicai.com.cn/api
pudao_weicai_gw_url=http://risk-api-gateway-pudao.weicai.com.cn/weiCaiApi
rongdan.engine.url=http://antifraud-risk-process-engine-rongdan.weicai.com.cn
rongdan.fs.service.url=http://antifraud-risk-fs-rongdan.weicai.com.cn
rongdan.url=http://antifraud-risk-admin-rongdan.weicai.com.cn/api
rongdan_heika_gw_url=http://risk-api-gateway-rongdan.weicai.com.cn/heiKaApi
rongdan_weicai_gw_url=http://risk-api-gateway-rongdan.weicai.com.cn/weiCaiApi
system.calfeature.baseurl=http://cp-web.rrdbg.com
system.pythonframe.baseOnlineAkBatchURLPython3=http://risk-fp-feature-online-python3.weicai.com.cn
system.pythonframe.baseurl=http://feature-cal.rrdbg.com
system.pythonframe.baseurlAkBatchPython3=http://risk-fp-feature-python3-ack.weicai.com.cn
system.pythonframe.baseurlBatchPython3=http://antifraud-risk-management-python3-batch.weicai.com.cn
system.pythonframe.baseurlExpAkBatchPython3=http://risk-fp-feature-exp-python3.weicai.com.cn
system.pythonframe.baseurlFpBatchPython3=http://antifraud-risk-management-python3-fp-feature.weicai.com.cn
system.pythonframe.baseurlModelPython3=http://risk-model-python3.weicai.com.cn
system.pythonframe.fpUrlPython3=http://antifraud-risk-management-fp-python3.rrdbg.com
system.pythonframe.offlineurl=http://feature-cal-offline.rrdbg.com
system.pythonframe.onlineMc=http://antifraud-risk-mc-python3.weicai.com.cn
system.pythonframe.baseurlPython3=http://antifraud-risk-management-python3.rrdbg.com
url.dataplatform.get=https://babel.ucredit.com/babel/v1/record/%s?jobid=%s&systemid=%s&ignoreFinished=false
url.dataplatform.get.userkey=https://babel.ucredit.com/babel/v1/record/user/%s?userkey=%s&systemid=%s
url.dataplatform.get.list.userkey=https://babel.ucredit.com/babel/v1/record/user/all/%s?userkey=%s&systemid=%s
data.url.write.base.url=https://babel-write.ucredit.com
dp.trigger.callback.url=https://crawlers.ucredit.com/crawlers/v1/crawl/record/notify
variable.center.service.url=http://risk-variable-gateway.weicai.com.cn
variable.url=http://risk-variable-gateway.weicai.com.cn/
haofenqi.attribution.callback.url=http://haofenqi-attribution-server.weicai.com.cn
haofenqi.userCancel.url=http://haofenqi-user-server.haohuan.com/api/v1/user/cancel
app.url=http://api.m.kuaisujiekuan.com
app.submit.result=/user/audit-result
app.amount.result=/user/update-loan-amount
app.user.result=/user/verify-notice
app.haohuan.url=http://api-m.haohuan.com
app.haohuan.submit.result=/internal/v1/audit/audit-notice
app.haohuan.user.result=/internal/v1/audit/verify-notice
app.haohuan.amount.result=/internal/v1/audit/increase-amount-notice
app.haohuan.payoff.result=/internal/v1/audit/reloan-notice
app.haohuan.amount.update.result=/internal/v1/audit/update-account-info
account.salt.haohuan=93e2b3b1e63511e7b413000c290cc30f
account.url=http://account-proxy-internal.rrdbg.com
rrd.url=http://credit.rrdbg.com
rrd.service.url=http://antifraud-risk-rrd-verify-service.rrdbg.com
rrd.web.base.url=http://credit.rrdbg.com
rrd.updateAmount.url=/engine/updateUserAmount
manual.hh.url=http://audit-web.haohuan.com/order/receive
antifraud_mode.address=***********
antifraud_mode.port=9090
antifraud_mode.modelversion=model_20180227,model_20180424,model_20180425,model_20180528

ra.datasource.url=***************************************************************************?${datasource.url.params}
ra.datasource.username=fk_risk_x
ra.datasource.pwd=d2c17db64b23055c