<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.cp.RiskMissMetricMapper">
  <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.cp.RiskMissMetric">
    <result column="id" jdbcType="BIGINT" property="id"/>
    <result column="event_code" jdbcType="VARCHAR" property="eventCode"/>
    <result column="model_id" jdbcType="VARCHAR" property="modelId"/>
    <result column="miss_count" jdbcType="BIGINT" property="missCount"/>
    <result column="all_count" jdbcType="BIGINT" property="allCount"/>
    <result column="window_start" jdbcType="VARCHAR" property="windowStart"/>
    <result column="count_type" jdbcType="TINYINT" property="countType"/>
    <result column="status" jdbcType="TINYINT" property="status"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <sql id="table_name">
    risk_miss_metric
  </sql>

  <insert id="batchInsert" parameterType="list">
    INSERT INTO <include refid="table_name"/> (event_code, model_id, miss_count, all_count, window_start, count_type, status)
    VALUES
    <foreach collection="list" item="item" index="index" separator=",">
      (
      #{item.eventCode}, #{item.modelId}, #{item.missCount}, #{item.allCount}, #{item.windowStart}, #{item.countType}, #{item.status}
      )
    </foreach>
  </insert>

  <select id="getMissCountInfo" resultMap="BaseResultMap">
    select event_code, model_id, sum(miss_count) as miss_count, sum(all_count) as all_count, window_start
    from <include refid="table_name"/>
    where event_code in('haoHuanVerify', 'haoHuanLendAudit', 'haoHuanLendAuditReloan')
    and model_id in
    <foreach collection="features" index="index" item="feature" open="(" separator="," close=")">
      #{feature}
    </foreach>
    and window_start &gt;= #{windowStart} and window_start &lt; #{windowEnd}
    and count_type = 0 and status = 1
    group by event_code, model_id, window_start
  </select>

  <update id="updateStatusInvalid" >
    update <include refid="table_name"/> set status = 2, update_time = now()
    where event_code = #{eventCode} and model_id = #{modelId}
    and window_start = #{windowStart} and count_type = 0 and status = 1
  </update>

</mapper>