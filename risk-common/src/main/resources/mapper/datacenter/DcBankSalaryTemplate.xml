<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.datacenter.DcBankSalaryTemplateMapper" >

    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.datacenter.DcBankSalaryTemplate" >
        <result column="bank_name" property="bankName" jdbcType="VARCHAR" />
        <result column="salary_regex" property="salaryRegex" jdbcType="VARCHAR" />
        <result column="remain_regex" property="remainRegex" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,bank_name,salary_regex,
        remain_regex
    </sql>

    <sql id="table_name">
        dc_bank_salary_template
    </sql>

    <insert id="insert" parameterType="com.youxin.risk.commons.model.datacenter.DcBankSalaryTemplate">
        insert into
        <include refid="table_name"/>
        (
        bank_name,salary_regex,remain_regex
        )
        values
        (
        #{bankName},
        #{salaryRegex},
        #{remainRegex}
        )
    </insert>

    <select id="selectAll" resultMap="BaseResultMap">
        select bank_name,salary_regex,remain_regex
        from
        <include refid="table_name"/>
    </select>


</mapper>