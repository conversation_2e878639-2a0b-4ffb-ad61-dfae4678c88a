<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.datacenter.DcSubmitPlistMapper" >

    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.datacenter.DcSubmitPlist" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="operation_log_id" property="operationLogId" jdbcType="INTEGER" />
        <result column="user_key" property="userKey" jdbcType="VARCHAR" />
        <result column="package_name" property="packageName" jdbcType="VARCHAR" />
        <result column="app_name" property="appName" jdbcType="VARCHAR" />
        <result column="launch_time" property="launchTime" jdbcType="TIMESTAMP" />
        <result column="last_update_time" property="lastUpdateTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, operation_log_id, user_key, package_name, app_name, launch_time, last_update_time, 
        create_time, update_time
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select 
        <include refid="Base_Column_List" />
        from dc_submit_plist
        where id = #{id,jdbcType=BIGINT}
    </select>


    <insert id="insert" parameterType="com.youxin.risk.commons.model.datacenter.DcSubmitPlist" >
        <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
            SELECT LAST_INSERT_ID() AS id
        </selectKey>
        insert into dc_submit_plist (operation_log_id, user_key,
            package_name, app_name, launch_time, 
            last_update_time, create_time, update_time
            )
        values (#{operationLogId,jdbcType=INTEGER}, #{userKey,jdbcType=VARCHAR},
            #{packageName,jdbcType=VARCHAR}, #{appName,jdbcType=VARCHAR}, #{launchTime,jdbcType=TIMESTAMP}, 
            #{lastUpdateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
            )
    </insert>


    <!--批量插入  useGeneratedKeys="true"-->
    <insert id="insertBatch"   parameterType="java.util.List">
        insert into dc_submit_plist (id,operation_log_id, user_key,
        package_name, app_name, launch_time,
        last_update_time
        )
        values
        <!--item就是List里每一项的对象名，要用","分割每一条数据，最后要";"结尾-->
        <foreach collection="list" item="item" index="index" separator="," close=";">
            (#{item.id}, #{item.operationLogId,jdbcType=VARCHAR}, #{item.userKey,jdbcType=VARCHAR}, #{item.packageName,jdbcType=VARCHAR}
            , #{item.appName,jdbcType=VARCHAR}
            , #{item.launchTime,jdbcType=VARCHAR}
            , #{item.lastUpdateTime,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="getByUserKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from dc_submit_plist
        WHERE user_key = #{userKey}
        AND operation_log_id = (SELECT operation_log_id FROM dc_submit_plist  WHERE user_key = #{userKey}  ORDER BY id DESC LIMIT 1)
    </select>

     <select id="getAllByUserkey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from dc_submit_plist
        where user_key = #{userKey,jdbcType=VARCHAR}
        order by id desc
        limit 5000
    </select>


    <select id="selectAfterId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />  FROM dc_submit_plist
        where <![CDATA[ id > #{startId} ]]>
        limit #{limit}
    </select>


    <select id="selectMaxSyncId" resultType="java.lang.Long">
        <![CDATA[ select max(id) from
        (select max(id) as id  from dc_submit_plist_0 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_1 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_2 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_3 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_4 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_5 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_6 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_7 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_8 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_9 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_10 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_11 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_12 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_13 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_14 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_15 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_16 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_17 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_18 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_19 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_20 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_21 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_22 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_23 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_24 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_25 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_26 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_27 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_28 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_29 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_30 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_31 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_32 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_33 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_34 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_35 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_36 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_37 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_38 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_39 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_40 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_41 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_42 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_43 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_44 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_45 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_46 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_47 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_48 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_49 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_50 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_51 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_52 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_53 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_54 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_55 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_56 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_57 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_58 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_59 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_60 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_61 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_62 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_63 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_64 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_65 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_66 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_67 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_68 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_69 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_70 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_71 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_72 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_73 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_74 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_75 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_76 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_77 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_78 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_79 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_80 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_81 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_82 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_83 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_84 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_85 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_86 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_87 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_88 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_89 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_90 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_91 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_92 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_93 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_94 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_95 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_96 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_97 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_98 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_99 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_100 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_101 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_102 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_103 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_104 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_105 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_106 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_107 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_108 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_109 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_110 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_111 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_112 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_113 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_114 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_115 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_116 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_117 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_118 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_119 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_120 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_121 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_122 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_123 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_124 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_125 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_126 where id<=#{upBound}
        union select max(id) as id  from dc_submit_plist_127 where id<=#{upBound}) t
        ]]>
    </select>


</mapper>