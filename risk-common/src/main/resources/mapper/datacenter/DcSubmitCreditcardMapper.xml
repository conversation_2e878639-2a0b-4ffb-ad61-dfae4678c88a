<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.datacenter.DcSubmitCreditcardMapper" >

    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.datacenter.DcSubmitCreditcard" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="operation_log_id" property="operationLogId" jdbcType="INTEGER" />
        <result column="user_key" property="userKey" jdbcType="VARCHAR" />
        <result column="card_no" property="cardNo" jdbcType="VARCHAR" />
        <result column="bank_name" property="bankName" jdbcType="VARCHAR" />
        <result column="bank_address" property="bankAddress" jdbcType="VARCHAR" />
        <result column="reserved_mobile" property="reservedMobile" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, operation_log_id, user_key, card_no, bank_name, bank_address, reserved_mobile, 
        update_time, create_time
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select 
        <include refid="Base_Column_List" />
        from dc_submit_creditcard
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
        delete from dc_submit_creditcard
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.youxin.risk.commons.model.datacenter.DcSubmitCreditcard" >
        <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
            SELECT LAST_INSERT_ID() AS id
        </selectKey>
        insert into dc_submit_creditcard (operation_log_id, user_key,
            card_no, bank_name, bank_address, 
            reserved_mobile, update_time, create_time
            )
        values (#{operationLogId,jdbcType=INTEGER}, #{userKey,jdbcType=VARCHAR},
            #{cardNo,jdbcType=VARCHAR}, #{bankName,jdbcType=VARCHAR}, #{bankAddress,jdbcType=VARCHAR}, 
            #{reservedMobile,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
            )
    </insert>

    <insert id="insertSelective" parameterType="com.youxin.risk.commons.model.datacenter.DcSubmitCreditcard" >
        insert into dc_submit_creditcard
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="operationLogId != null" >
                operation_log_id,
            </if>
            <if test="userKey != null" >
                user_key,
            </if>
            <if test="cardNo != null" >
                card_no,
            </if>
            <if test="bankName != null" >
                bank_name,
            </if>
            <if test="bankAddress != null" >
                bank_address,
            </if>
            <if test="reservedMobile != null" >
                reserved_mobile,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=INTEGER},
            </if>
            <if test="operationLogId != null" >
                #{operationLogId,jdbcType=INTEGER},
            </if>
            <if test="userKey != null" >
                #{userKey,jdbcType=VARCHAR},
            </if>
            <if test="cardNo != null" >
                #{cardNo,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null" >
                #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="bankAddress != null" >
                #{bankAddress,jdbcType=VARCHAR},
            </if>
            <if test="reservedMobile != null" >
                #{reservedMobile,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.youxin.risk.commons.model.datacenter.DcSubmitCreditcard" >
        update dc_submit_creditcard
        <set >
            <if test="operationLogId != null" >
                operation_log_id = #{operationLogId,jdbcType=INTEGER},
            </if>
            <if test="userKey != null" >
                user_key = #{userKey,jdbcType=VARCHAR},
            </if>
            <if test="cardNo != null" >
                card_no = #{cardNo,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null" >
                bank_name = #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="bankAddress != null" >
                bank_address = #{bankAddress,jdbcType=VARCHAR},
            </if>
            <if test="reservedMobile != null" >
                reserved_mobile = #{reservedMobile,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.youxin.risk.commons.model.datacenter.DcSubmitCreditcard" >
        update dc_submit_creditcard
        set operation_log_id = #{operationLogId,jdbcType=INTEGER},
            user_key = #{userKey,jdbcType=VARCHAR},
            card_no = #{cardNo,jdbcType=VARCHAR},
            bank_name = #{bankName,jdbcType=VARCHAR},
            bank_address = #{bankAddress,jdbcType=VARCHAR},
            reserved_mobile = #{reservedMobile,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="getByUserKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from dc_submit_creditcard
        where user_key = #{userKey,jdbcType=VARCHAR} order  by create_time DESC limit 0,1
    </select>
</mapper>