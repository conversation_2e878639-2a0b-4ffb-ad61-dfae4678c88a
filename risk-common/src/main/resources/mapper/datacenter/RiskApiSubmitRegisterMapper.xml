<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.datacenter.RiskApiSubmitRegisterMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.datacenter.api.RiskApiSubmitRegister">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result column="api_source" property="apiSource" jdbcType="VARCHAR"/>
        <result column="source_system" property="sourceSystem" jdbcType="VARCHAR"/>
        <result column="user_key" property="userKey" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"
                typeHandler="com.youxin.risk.commons.mybatis.ApiMaskHandler"/>
        <result column="old_mobile" property="oldMobile" jdbcType="VARCHAR"
                typeHandler="com.youxin.risk.commons.mybatis.ApiMaskHandler"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, api_source, source_system, user_key, mobile, old_mobile, create_time, update_time
    </sql>

    <sql id="table_name">
        risk_api_submit_register
    </sql>

    <insert id="insert" parameterType="com.youxin.risk.commons.model.datacenter.api.RiskApiSubmitRegister">
        insert into <include refid="table_name"/> (
            api_source, source_system, user_key, mobile, old_mobile
        )
        values (
            #{apiSource,jdbcType=VARCHAR}, #{sourceSystem,jdbcType=VARCHAR}, #{userKey,jdbcType=VARCHAR},
            #{mobile,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.ApiMaskHandler},
            #{oldMobile,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.ApiMaskHandler}
        )
    </insert>

    <update id="updateById">
        update <include refid="table_name"/>
        set
        update_time = now()
        <if test="sourceSystem != null and sourceSystem != '' ">
            ,source_system = #{sourceSystem,jdbcType=VARCHAR}
        </if>
        <if test="mobile != null and mobile != '' ">
            ,mobile = #{mobile,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.ApiMaskHandler}
        </if>
        <if test="oldMobile != null and oldMobile != '' ">
            ,old_mobile = #{oldMobile,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.ApiMaskHandler}
        </if>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="getByUserKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from <include refid="table_name"/>
        where user_key = #{userKey,jdbcType=VARCHAR}
        <if test="apiSource != null and apiSource != '' ">
            and api_source = #{apiSource,jdbcType=VARCHAR}
        </if>
        order by id DESC limit 0,1
    </select>

    <select id="getListByUserKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from <include refid="table_name"/>
        where user_key = #{userKey,jdbcType=VARCHAR}
        <if test="apiSource != null and apiSource != '' ">
            and api_source = #{apiSource,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getByMobile" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from <include refid="table_name"/>
        where mobile = #{mobile,jdbcType=VARCHAR}
        order by id DESC limit 1
    </select>
</mapper>