<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.admin.AlertCollectConfMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.AlertCollectConf">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="collect_code" property="collectCode" jdbcType="VARCHAR"/>
        <result column="collect_desc" property="collectDesc" jdbcType="VARCHAR"/>
        <result column="datasource_name" property="datasourceName" jdbcType="VARCHAR"/>
        <result column="table_name" property="tableName" jdbcType="VARCHAR"/>
        <result column="function" property="function" jdbcType="VARCHAR"/>
        <result column="function_field" property="functionField" jdbcType="VARCHAR"/>
        <result column="collect_minutes" property="collectMinutes" jdbcType="BIGINT"/>
        <result column="tags" property="tags" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="save_Base_Column_List">
        collect_code, collect_desc, datasource_name, table_name, function, function_field, collect_minutes, tags, create_time, update_time
    </sql>

    <sql id="Base_Column_List">
        id, <include refid="save_Base_Column_List"/>
    </sql>

    <sql id="table_name">
        alert_admin_collect_conf
    </sql>


    <insert id="insert"  parameterType="com.youxin.risk.commons.model.AlertCollectConf">
        insert into <include refid="table_name" /> (<include refid="save_Base_Column_List" />) values (
        #{collectCode},#{collectDesc},#{datasourceName},#{tableName},
        #{function},#{functionField},#{collectMinutes},#{tags}, now(),now()
        )
    </insert>

    <select id="selectAllInMaster" resultMap="BaseResultMap">
        (select <include refid="Base_Column_List"/> from <include refid="table_name"/> order by id asc)
    </select>

    <select id="selectByUpdateTimeInMaster" resultMap="BaseResultMap" parameterType="java.util.Date">
        (select <include refid="Base_Column_List"/> from <include refid="table_name"/> where update_time >= #{updateTime})
    </select>
</mapper>