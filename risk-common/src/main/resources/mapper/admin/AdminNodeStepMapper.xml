<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.admin.AdminNodeStepMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.AdminNodeStep">
        <result column="event_code" property="eventCode" jdbcType="VARCHAR"/>
        <result column="node_code" property="nodeCode" jdbcType="VARCHAR"/>
        <result column="step" property="step" jdbcType="VARCHAR"/>
        <result column="event_name" property="eventName" jdbcType="VARCHAR"/>
    </resultMap>


    <select id="selectAllDataStep" resultMap="BaseResultMap">
        select event_code,node_code,step,event_name from admin_event_node_relation where task_name = 'dataTask' and step is not null
    </select>

</mapper>