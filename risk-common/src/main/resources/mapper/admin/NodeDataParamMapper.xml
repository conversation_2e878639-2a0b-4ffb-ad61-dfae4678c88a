<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youxin.risk.commons.dao.admin.NodeDataParamMapper">
  <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.NodeDataParam">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="node_id" jdbcType="VARCHAR" property="nodeId" />
    <result column="data_code" jdbcType="VARCHAR" property="dataCode" />
    <result column="expression" jdbcType="VARCHAR" property="expression" />
    <result column="param_name" jdbcType="VARCHAR" property="paramName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, node_id, data_code, expression, param_name, create_time, update_time
  </sql>

	<select id="selectByNodeCodeAndServiceCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from admin_node_data_param
    where node_id=#{nodeId,jdbcType=VARCHAR} and data_code=#{dataCode,jdbcType=VARCHAR}
  </select>

  <select id="selectUpdateNodeDataParam" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin_node_data_param
        where update_time >= #{updateTime} ORDER BY update_time desc
  </select>

  <select id="selectAllNodeDataParam" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from admin_node_data_param
  </select>
</mapper>