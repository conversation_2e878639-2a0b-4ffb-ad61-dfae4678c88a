<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.admin.AlertUserGroupMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.AlertUserGroup">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="group_name" property="groupName" jdbcType="VARCHAR"/>
        <result column="group_desc" property="groupDesc" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

  <sql id="table_name">
    alert_admin_user_group
  </sql>

  <sql id="Base_Columns">
    id, group_name, group_desc, email, create_time, update_time
  </sql>

    <select id="selectAllInMaster" resultMap="BaseResultMap" parameterType="java.util.Date">
        (select <include refid="Base_Columns"/> from <include refid="table_name"/>)
    </select>

    <select id="selectByUpdateTimeInMaster" resultMap="BaseResultMap" parameterType="java.util.Date">
        (select <include refid="Base_Columns"/> from <include refid="table_name"/> where update_time >= #{updateTime})
    </select>
</mapper>