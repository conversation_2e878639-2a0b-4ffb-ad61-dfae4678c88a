<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.admin.StatServiceMapper">


    <insert id="insert" parameterType="list">
        insert into stat_service (
        source_system,
        event_code,
        event_name,
        step,
        node_code,
        service_code,
        invoke_count,
        cache_count,
        stat_date
        ) values
        <foreach collection="list" item="item" index="index" open="(" separator="),(" close=")">
            #{item.sourceSystem},
            #{item.eventCode},
            #{item.eventName},
            #{item.step},
            #{item.nodeCode},
            #{item.serviceCode},
            #{item.invokeCount},
            #{item.cacheCount},
            #{item.statDate}
        </foreach>
    </insert>

    <select id="selectEventInfo" resultType="com.youxin.risk.commons.model.StatService">
     SELECT
	event_code AS eventCode,
	event_name AS eventName,
	step,
	node_code AS nodeCode
  FROM
	admin_event_node_relation
  WHERE
	node_type = 'DATA' AND node_code = #{nodeCode}
  LIMIT 1
    </select>

    <select id="selectLastEventInfoByNode" resultType="com.youxin.risk.commons.model.StatService">
    SELECT
    event_code AS eventCode,
    event_name AS eventName,
    step,
    node_code AS nodeCode
    FROM stat_service
    WHERE node_code =  #{nodeCode}
    ORDER BY id DESC LIMIT 1
    </select>

    <select id="selectEventInfoByEventCode" resultType="com.youxin.risk.commons.model.StatService">
        SELECT event_code AS eventCode,event_name AS eventName
        FROM risk_admin.admin_event_info
        WHERE event_code =#{eventCode}
        LIMIT 1
    </select>


    <delete id="deleteByStatDate">
        DELETE FROM stat_service WHERE stat_date = #{statDate}
    </delete>


</mapper>