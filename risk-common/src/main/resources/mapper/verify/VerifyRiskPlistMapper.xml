<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.verify.VerifyRiskPlistMapper">

    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.verify.VerifyRiskPlist">
        <id column="id" property="id"/>
        <result column="app_name" property="appName"/>
        <result column="package_name" property="packageName"/>
        <result column="tag1" property="tag1"/>
        <result column="tag2" property="tag2"/>
        <result column="tag3" property="tag3"/>
        <result column="tag4" property="tag4"/>
        <result column="tag5" property="tag5"/>
        <result column="tag6" property="tag6"/>
        <result column="tag7" property="tag7"/>
        <result column="tag8" property="tag8"/>
        <result column="tag9" property="tag9"/>
        <result column="tag10" property="tag10"/>

    </resultMap>

    <sql id="Base_Column_List">
        id, app_name, package_name, tag1, tag2, tag3, tag4, tag5, tag6, tag7, tag8, tag9, tag10
    </sql>

    <insert id="insertBatch"  parameterType="list">
        insert into verify_risk_plist(
        app_name, package_name, tag1, tag2, tag3, tag4, tag5, tag6, tag7, tag8, tag9, tag10
        ) VALUES
        <foreach collection="list" close="" index="index" item="item" open="" separator=",">
            (#{item.appName},#{item.packageName},#{item.tag1},#{item.tag2},#{item.tag3},#{item.tag4},#{item.tag5},#{item.tag6},#{item.tag7},#{item.tag8},#{item.tag9},#{item.tag10})
        </foreach>
    </insert>

    <insert id="insert"  parameterType="com.youxin.risk.commons.model.verify.VerifyRiskPlist">
        insert into verify_risk_plist(
        app_name, package_name, tag1, tag2, tag3, tag4, tag5, tag6, tag7, tag8, tag9, tag10
        ) VALUES
            (#{appName},#{packageName},#{tag1},#{tag2},#{tag3},#{tag4},#{tag5},#{tag6},#{tag7},#{tag8},#{tag9},#{tag10})
    </insert>

    <select id="countByAppAndPackage" resultType="integer">
        select count(*)
        from verify_risk_plist
        where app_name = #{appName} AND package_name = #{packageName}
    </select>

    <select id="queryByAppAndPackage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from verify_risk_plist
        where app_name = #{appName} AND package_name = #{packageName}
    </select>
</mapper>