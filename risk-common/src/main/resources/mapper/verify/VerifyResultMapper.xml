<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.verify.VerifyResultMapper" >

    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.verify.VerifyResult" >
        <id column="id" property="id" />
        <result column="user_key" property="userKey" />
        <result column="source_system" property="sourceSystem"  />
        <result column="loan_id" property="loanId"  />
        <result column="loan_key" property="loanKey"  />
        <result column="score" property="score"  />
        <result column="score2" property="score2"  />
        <result column="is_auto_passed" property="isAutoPassed"  />
        <result column="reason" property="reason"  />
        <result column="ra_report_id" property="raReportId"  />
        <result column="reason_code" property="reasonCode"  />
        <result column="auto_verify_time" property="autoVerifyTime"  />
        <result column="manual_verify_time" property="manualVerifyTime"  />
        <result column="final_verify_time" property="finalVerifyTime"  />
        <result column="is_final_passed" property="isFinalPassed"  />
        <result column="is_manual_passed" property="isManualPassed"  />
        <result column="is_manual" property="isManual"  />
        <result column="direct_pass_plan_id" property="directPassPlanId"  />
        <result column="update_time" property="updateTime"  />
        <result column="create_time" property="createTime"  />
        <result column="version" property="version"  />
        <result column="auto_lock_days" property="autoLockDays"  />
        <result column="manual_lock_days" property="manualLockDays"  />
        <result column="operator_id" property="operatorId"  />
        <result column="result_level" property="resultLevel"  />
        <result column="strategy_id" property="strategyId"  />
        <result column="model_feature" property="modelFeature"  />
        <result column="segment" property="segment"  />
        <result column="reason_code_user" property="reasonCodeUser"  />
        <result column="is_reduce_amount_pass" property="isReduceAmountPass"  />
        <result column="new_amount" property="newAmount"  />
        <result column="new_amount_expiry" property="newAmountExpiry"  />
        <result column="is_new_amount_accepted" property="isNewAmountAccepted"  />
        <result column="user_respond_time" property="userRespondTime"  />
        <result column="loan_amount" property="loanAmount"  />
        <result column="loan_period_nos" property="loanPeriodNos"  />
        <result column="loan_rate" property="loanRate"  />
        <result column="bt_amount" property="btAmount"  />
        <result column="bt_period_nos" property="btPeriodNos"  />
        <result column="bt_rate" property="btRate"  />
        <result column="total_amount" property="totalAmount"  />
        <result column="user_level_id" property="userLevelId"  />
        <result column="step" property="step"  />
        <result column="period_amount" property="periodAmount"  />
    </resultMap>

    <sql id="Base_Column_List">
        id,loan_id,loan_key,user_key,source_system,score,score2,is_auto_passed,reason,ra_report_id,reason_code,auto_verify_time,auto_lock_days,manual_verify_time,final_verify_time,is_final_passed,is_manual_passed,is_manual,direct_pass_plan_id,manual_lock_days,operator_id,result_level,strategy_id,update_time,create_time,version,model_feature,segment,reason_code_user,is_reduce_amount_pass,new_amount,new_amount_expiry,is_new_amount_accepted,user_respond_time,loan_amount,loan_period_nos,loan_rate,bt_amount,bt_period_nos,bt_rate,total_amount,user_level_id,step,period_amount
    </sql>

    <select id="getByUserKey" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from verify_result
        where user_key = #{userKey} AND  source_system = #{sourceSystem}  order by id DESC limit 0,1
    </select>


    <select id="queryUsersFirstResult" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from verify_result v1
                 inner join(
            select min(vr.id) as id2
            from verify_result vr
            where vr.user_key in
                <foreach collection="userKeyList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            group by vr.user_key) v2 on (v1.id = v2.id2)
        where v1.user_key in
            <foreach collection="userKeyList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>

    <select id="findResultsByUserKeyAndSys" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        FROM verify_result WHERE user_key = #{userKey} and source_system = #{sourceSystem}
        ORDER BY id DESC
    </select>
    <insert id="persist" parameterType="com.youxin.risk.commons.model.verify.VerifyResult">
        <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
            SELECT LAST_INSERT_ID()
        </selectKey>
         insert into verify_result(id,loan_id,loan_key,user_key,source_system,score,score2,is_auto_passed,reason,ra_report_id,reason_code,auto_verify_time,auto_lock_days,manual_verify_time,final_verify_time,is_final_passed,is_manual_passed,is_manual,direct_pass_plan_id,manual_lock_days,operator_id,result_level,strategy_id,update_time,create_time,version,model_feature,segment,reason_code_user,is_reduce_amount_pass,new_amount,new_amount_expiry,is_new_amount_accepted,user_respond_time,loan_amount,loan_period_nos,loan_rate,bt_amount,bt_period_nos,bt_rate,total_amount,user_level_id,step,period_amount
         )
         values (
        #{id},#{loanId},#{loanKey},#{userKey},#{sourceSystem},#{score},#{score2},#{isAutoPassed},#{reason},#{raReportId},#{reasonCode},#{autoVerifyTime},#{autoLockDays},#{manualVerifyTime},#{finalVerifyTime},#{isFinalPassed},#{isManualPassed},#{isManual},#{directPassPlanId},#{manualLockDays},#{operatorId},#{resultLevel},#{strategyId},#{updateTime},#{createTime},#{version},#{modelFeature},#{segment},#{reasonCodeUser},#{isReduceAmountPass},#{newAmount},#{newAmountExpiry},#{isNewAmountAccepted},#{userRespondTime},#{loanAmount},#{loanPeriodNos},#{loanRate},#{btAmount},#{btPeriodNos},#{btRate},#{totalAmount},#{userLevelId},#{step},#{periodAmount} )
 </insert>

    <update id="update" parameterType="com.youxin.risk.commons.model.verify.VerifyResult">
        update verify_result
        <trim prefix="SET" suffixOverrides=",">
            <if test="loanId != null"> loan_id = #{loanId},</if>
            <if test="loanKey != null"> loan_key = #{loanKey},</if>
            <if test="userKey != null"> user_key = #{userKey},</if>
            <if test="sourceSystem != null"> source_system = #{sourceSystem},</if>
            <if test="score != null"> score = #{score},</if>
            <if test="score2 != null"> score2 = #{score2},</if>
            <if test="isAutoPassed != null"> is_auto_passed = #{isAutoPassed},</if>
            <if test="reason != null"> reason = #{reason},</if>
            <if test="raReportId != null"> ra_report_id = #{raReportId},</if>
            <if test="reasonCode != null"> reason_code = #{reasonCode},</if>
            <if test="autoVerifyTime != null"> auto_verify_time = #{autoVerifyTime},</if>
            <if test="autoLockDays != null"> auto_lock_days = #{autoLockDays},</if>
            <if test="manualVerifyTime != null"> manual_verify_time = #{manualVerifyTime},</if>
            <if test="finalVerifyTime != null"> final_verify_time = #{finalVerifyTime},</if>
            <if test="isFinalPassed != null"> is_final_passed = #{isFinalPassed},</if>
            <if test="isManualPassed != null"> is_manual_passed = #{isManualPassed},</if>
            <if test="isManual != null"> is_manual = #{isManual},</if>
            <if test="directPassPlanId != null"> direct_pass_plan_id = #{directPassPlanId},</if>
            <if test="manualLockDays != null"> manual_lock_days = #{manualLockDays},</if>
            <if test="operatorId != null"> operator_id = #{operatorId},</if>
            <if test="resultLevel != null"> result_level = #{resultLevel},</if>
            <if test="strategyId != null"> strategy_id = #{strategyId},</if>
            <if test="updateTime != null"> update_time = #{updateTime},</if>
            <if test="createTime != null"> create_time = #{createTime},</if>
            <if test="version != null"> version = #{version},</if>
            <if test="modelFeature != null"> model_feature = #{modelFeature},</if>
            <if test="segment != null"> segment = #{segment},</if>
            <if test="reasonCodeUser != null"> reason_code_user = #{reasonCodeUser},</if>
            <if test="isReduceAmountPass != null"> is_reduce_amount_pass = #{isReduceAmountPass},</if>
            <if test="newAmount != null"> new_amount = #{newAmount},</if>
            <if test="newAmountExpiry != null"> new_amount_expiry = #{newAmountExpiry},</if>
            <if test="isNewAmountAccepted != null"> is_new_amount_accepted = #{isNewAmountAccepted},</if>
            <if test="userRespondTime != null"> user_respond_time = #{userRespondTime},</if>
            <if test="loanAmount != null"> loan_amount = #{loanAmount},</if>
            <if test="loanPeriodNos != null"> loan_period_nos = #{loanPeriodNos},</if>
            <if test="loanRate != null"> loan_rate = #{loanRate},</if>
            <if test="btAmount != null"> bt_amount = #{btAmount},</if>
            <if test="btPeriodNos != null"> bt_period_nos = #{btPeriodNos},</if>
            <if test="btRate != null"> bt_rate = #{btRate},</if>
            <if test="totalAmount != null"> total_amount = #{totalAmount},</if>
            <if test="userLevelId != null"> user_level_id = #{userLevelId},</if>
            <if test="step != null"> step = #{step},</if>
            <if test="periodAmount != null"> period_amount = #{periodAmount},</if>
        </trim>
        where id = #{id}
    </update>

    <select id="getByUserKeyAndLoanId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from verify_result
        where user_key = #{userKey} AND  loan_id = #{loanId}  order by id DESC limit 0,1
    </select>

    <select id="findLastResultBySysAndUserAndLoanId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from verify_result
        where user_key = #{userKey} AND  loan_id = #{loanId}  order by id DESC limit 0,1
    </select>

    <select id="findLastResultBySysAndUserAndLoanKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from verify_result
        where user_key = #{userKey} AND  loan_key = #{loanKey}  order by id DESC limit 1
    </select>

    <select id="findVerifyResultByUserKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from verify_result
        WHERE user_key = #{userKey} ORDER BY id DESC
        limit 1
    </select>

    <select id="findVerifyResultByLoanKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from verify_result
        WHERE loan_key = #{loanKey} ORDER BY id DESC
        limit 1
    </select>

    <select id="findVerifyResultsByLoanKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM verify_result T
        WHERE loan_key in
        <foreach collection="loanKeyList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND NOT EXISTS(
        SELECT 1
        FROM verify_result
        WHERE loan_key = T.loan_key
        AND id > T.id
        )
    </select>

    <select id="findVerifyResultByLoanId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from verify_result
        WHERE loan_id = #{loanId} ORDER BY id DESC
        limit 1
    </select>

    <select id="findPassedUserKey" resultType="string"><![CDATA[
        select
        user_key
        from verify_result
        WHERE
        id > #{startId}
        and
        id <= #{endId}
        and
        is_final_passed = 1
        ]]>
    </select>
</mapper>