<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youxin.risk.commons.dao.verify.VerifyLineRepaymentMapper" >

    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.verify.VerifyLineRepayment" >
        <id column="id" property="id" />
        <result column="user_key" property="userKey" />
        <result column="source_system" property="sourceSystem"  />
        <result column="loan_id" property="loanId"  />
        <result column="loan_key" property="loanKey"  />
        <result column="trans_id" property="transId"  />
        <result column="cur_order_id" property="curOrderId"  />
        <result column="repay_amount" property="repayAmount"  />
        <result column="repay_time" property="repayTime"  />
        <result column="is_finished" property="isFinished"  />
        <result column="remark" property="remark"  />
        <result column="create_time" property="createTime"  />
        <result column="update_time" property="updateTime"  />
        <result column="version" property="version"  />
    </resultMap>


    <insert id="insert" parameterType="com.youxin.risk.commons.model.verify.VerifyLineRepayment">
        <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into verify_line_repayment
        (
            user_key, source_system, loan_key, loan_id, trans_id, cur_order_id,
            repay_amount, repay_time, is_finished, remark, create_time, update_time, version
        )
        values
        (
            #{userKey}, #{sourceSystem}, #{loanKey}, #{loanId}, #{transId}, #{curOrderId},
            #{repayAmount}, #{repayTime}, #{isFinished}, #{remark}, #{createTime}, #{updateTime}, #{version}
        )
    </insert>

    <update id="update" parameterType="com.youxin.risk.commons.model.verify.VerifyLineRepayment">
        update verify_line_repayment
        <trim prefix="SET" suffixOverrides=",">
            <if test="userKey != null"> user_key = #{userKey},</if>
            <if test="sourceSystem != null"> source_system = #{sourceSystem},</if>
            <if test="loanKey != null"> loan_key = #{loanKey},</if>
            <if test="loanId != null"> loan_id = #{loanId},</if>
            <if test="transId != null"> trans_id = #{transId},</if>
            <if test="curOrderId != null"> cur_order_id = #{curOrderId},</if>
            <if test="repayAmount != null"> repay_amount = #{repayAmount},</if>
            <if test="repayTime != null"> repay_time = #{repayTime},</if>
            <if test="isFinished != null"> is_finished = #{isFinished},</if>
            <if test="remark != null"> remark = #{remark},</if>
            <if test="createTime != null"> create_time = #{createTime},</if>
            <if test="updateTime != null"> update_time = #{updateTime},</if>
            <if test="version != null"> version = #{version},</if>
        </trim>
        where id = #{id}
    </update>


    <select id="findRecordByTransId" resultMap="BaseResultMap">
        select
            id, user_key, source_system, loan_key, loan_id, trans_id, cur_order_id,
            repay_amount, repay_time, is_finished, remark, create_time, update_time, version
        from
            verify_line_repayment
        where
            user_key = #{userKey} AND  trans_id = #{transId}  order by create_time desc limit 0,1;
    </select>
</mapper>