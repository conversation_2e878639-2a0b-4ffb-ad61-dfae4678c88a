<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youxin.risk.commons.dao.verify.VerifyResultFLMapper">
	<!-- Result Map-->
	<resultMap id="resultMap" type="com.youxin.risk.commons.model.verify.VerifyResult">
		<id column="id" property="id"/>
		<result column="reason" property="reason"/>
		<result column="reason_code" property="reasonCode"/>
		<result column="loan_id" property="loanId"/>
		<result column="loan_key" property="loanKey"/>
		<result column="user_key" property="userKey"/>
		<result column="source_system" property="sourceSystem"/>
		<result column="score" property="score"/>
		<result column="score2" property="score2"/>
		<result column="is_auto_passed" property="isAutoPassed" />
		<result column="auto_verify_time" property="autoVerifyTime"/>
		<result column="final_verify_time" property="finalVerifyTime"/>
		<result column="is_final_passed" property="isFinalPassed"/>
		<result column="is_manual" property="isManual"/>
		<result column="auto_lock_days" property="autoLockDays"/>
		<result column="strategy_id" property="strategyId"/>
		<result column="segment" property="segment"/>
		<result column="is_reduce_amount_pass" property="isReduceAmountPass"/>
		<result column="new_amount" property="newAmount"/>
		<result column="new_amount_expiry" property="newAmountExpiry"/>
		<result column="loan_amount" property="loanAmount"/>
		<result column="bt_amount" property="btAmount"/>
		<result column="loan_period_nos" property="loanPeriodNos"/>
		<result column="loan_rate" property="loanRate"/>
		<result column="step" property="step"/>
		<result column="period_amount" property="periodAmount"/>
		<result column="bt_period_nos" property="btPeriodNos"/>
		<result column="bt_rate" property="btRate"/>
		<result column="total_amount" property="totalAmount"/>
		<result column="update_time" property="updateTime"/>
		<result column="create_time" property="createTime"/>
	</resultMap>

	<sql id="Base_Column_List">
        id,reason,reason_code,loan_id,loan_key,user_key,source_system,score,score2,is_auto_passed,auto_verify_time,final_verify_time,is_final_passed,is_manual,auto_lock_days,strategy_id,segment,is_reduce_amount_pass,new_amount,new_amount_expiry,loan_amount,bt_amount,loan_period_nos,loan_rate,step,period_amount,bt_period_nos,bt_rate,total_amount,update_time,create_time
    </sql>

	<!-- 获取用户信息 -->
	<insert id="insertVerifyResultFL" parameterType="com.youxin.risk.commons.model.verify.VerifyResult">
		insert into verify_result_fl(
			reason_code,loan_id,loan_key,user_key,source_system,score,score2,is_auto_passed,
			auto_verify_time,final_verify_time,is_final_passed,is_manual,auto_lock_days,strategy_id,
			segment,is_reduce_amount_pass,new_amount,new_amount_expiry,loan_amount,bt_amount,
			loan_period_nos,loan_rate,step,period_amount,bt_period_nos,bt_rate,total_amount,version,
			update_time,create_time
		)
		values
		(
			#{reasonCode},#{loanId},#{loanKey},#{userKey},#{sourceSystem},#{score},#{score2},#{isAutoPassed},
			#{autoVerifyTime},#{finalVerifyTime},#{isFinalPassed},#{isManual},#{autoLockDays},#{strategyId},
			#{segment},#{isReduceAmountPass},#{newAmount},#{newAmountExpiry},#{loanAmount},#{btAmount},
			#{loanPeriodNos},#{loanRate},#{step},#{periodAmount},#{btPeriodNos},#{btRate},#{totalAmount},#{version},
			#{updateTime},#{createTime}
		)
	</insert>

	<select id="findVerifyResultByLoanKey" resultMap="resultMap">
		select
		<include refid="Base_Column_List" />
		from verify_result_fl
		WHERE loan_key = #{loanKey} ORDER BY id DESC
		limit 1
	</select>

	<select id="findVerifyResultsByLoanKey" resultMap="resultMap">
		SELECT <include refid="Base_Column_List" />
		FROM verify_result_fl T
		WHERE loan_key in
		<foreach collection="loanKeyList" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		AND NOT EXISTS(
		SELECT 1
		FROM verify_result_fl
		WHERE loan_key = T.loan_key
		AND id > T.id
		)
	</select>

	<select id="lastMinuteRejectResult" resultMap="resultMap">
		select t2.id,t2.reason,t1.reason_code,t2.loan_id,t2.loan_key,t2.user_key,t2.source_system,t2.score,
		       t2.score2,t2.is_auto_passed,t2.auto_verify_time,t2.final_verify_time,t2.is_final_passed,
		       t2.is_manual,t2.auto_lock_days,t2.strategy_id,t2.segment,t2.is_reduce_amount_pass,t2.new_amount,
			   t2.new_amount_expiry,t2.loan_amount,t2.bt_amount,t2.loan_period_nos,t2.loan_rate,t2.step,
			   t2.period_amount,t2.bt_period_nos,t2.bt_rate,t2.total_amount,t2.update_time,t2.create_time
		from verify_result_fl24 t1, verify_result_fl t2
		where t1.loan_key=t2.loan_key
		and t1.`step` ='IRR_A_FL' and t2.`step` =#{rejectStep}
		and t1.`is_auto_passed` =0 and t1.`is_final_passed` =0
		and t2.`is_auto_passed` =0 and t1.`is_final_passed` =0
		and t1.`create_time` >now() - INTERVAL 1 MINUTE
		limit 1
	</select>

	<select id="getLastPassResultByUserKey" resultMap="resultMap">
		SELECT user_key, is_final_passed, create_time
		FROM verify_result_fl
		WHERE user_key = #{userKey} and is_final_passed = 1
		<if test="createTime != null">
			and create_time &gt; #{createTime}
		</if>
		order by id desc limit 1;
	</select>

</mapper>
