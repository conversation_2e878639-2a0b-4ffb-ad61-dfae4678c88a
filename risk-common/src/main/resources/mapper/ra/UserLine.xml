<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.ra.UserLineMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.verify.UserLine">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="user_key" jdbcType="VARCHAR" property="userKey"/>
        <result column="event_code" jdbcType="VARCHAR" property="eventCode"/>
        <result column="loan_line" jdbcType="DOUBLE" property="loanLine"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <insert id="insertUserLine" parameterType="com.youxin.risk.commons.model.verify.UserLine">
        insert into ${shardingTableName}
        (id,user_key,event_code,event_diff_mark,loan_application_time,apply_amount,apply_period,loan_avail_line,
        loan_line,loan_period,lift_type,lift_amount,tmp_expire_time,sfix_expire_time,loan_rate,create_time,update_time
        ) VALUES
        (
            #{id},#{userKey},#{eventCode},#{eventDiffMark},#{loanApplicationTime},#{applyAmount},#{applyPeriod},#{loanAvailLine},
            #{loanLine},#{loanPeriod},#{liftType},#{liftAmount},#{tmpExpireTime},#{sFixExpireTime},#{loanRate},NOW(),NOW()
        )
    </insert>

    <select id="selectLastVerifyByUserKey" resultMap="BaseResultMap">
        select id,user_key,event_code,loan_line,create_time from ${tableName}
        <where>
            user_key = #{userKey} and event_code in('haoHuanVerify','ApiVerify') order by id desc limit 1
        </where>
    </select>

    <select id="selectLastLoanLineByUserKey" resultType="java.lang.Double">
        select loan_line from ${tableName}
        <where>
            user_key = #{userKey} order by id desc limit 1
        </where>
    </select>
</mapper>