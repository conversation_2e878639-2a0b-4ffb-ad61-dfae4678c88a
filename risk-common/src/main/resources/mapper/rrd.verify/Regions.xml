<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.rrd.verify.RegionsMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.rrd.verify.Regions">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
        <result column="enabled" property="enabled" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="parentCode" property="parentCode" jdbcType="VARCHAR"/>
        <result column="sign" property="sign" jdbcType="VARCHAR"/>
        <result column="zipCode" property="zipCode" jdbcType="VARCHAR"/>
    </resultMap>


    <sql id="Base_Column_List">
        id, code, enabled, name, parentCode, sign, zipCode
    </sql>

    <select id="getNameByCode" resultType="string">
        SELECT name
        from regions
        where code = #{code};
    </select>
</mapper>