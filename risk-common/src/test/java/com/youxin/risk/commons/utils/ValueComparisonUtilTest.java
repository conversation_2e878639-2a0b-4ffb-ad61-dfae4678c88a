package com.youxin.risk.commons.utils;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ValueComparisonUtil 测试类
 */
public class ValueComparisonUtilTest {
    
    private static final Logger logger = LoggerFactory.getLogger(ValueComparisonUtilTest.class);
    
    private final ByteArrayOutputStream outContent = new ByteArrayOutputStream();
    private final PrintStream originalOut = System.out;
    
    @Before
    public void setUpStreams() {
        System.setOut(new PrintStream(outContent));
    }
    
    @After
    public void restoreStreams() {
        System.setOut(originalOut);
    }
    
    /**
     * 测试两个值都为空的情况
     */
    @Test
    public void testCompareValues_BothEmpty() {
        System.out.println("=== 测试两个值都为空的情况 ===");
        
        ValueComparisonUtil.compareValues(
            "testKey", "test_key", 
            null, null, 
            "testUser", "testEvent", "testLog"
        );
        
        ValueComparisonUtil.compareValues(
            "testKey", "test_key", 
            "", "", 
            "testUser", "testEvent", "testLog"
        );
        
        System.out.println("两个值都为空时，应该不输出任何日志");
    }
    
    /**
     * 测试其中一个值为空的情况
     */
    @Test
    public void testCompareValues_OneEmpty() {
        System.out.println("=== 测试其中一个值为空的情况 ===");
        
        ValueComparisonUtil.compareValues(
            "testKey", "test_key", 
            "value1", null, 
            "testUser", "testEvent", "testLog"
        );
        
        ValueComparisonUtil.compareValues(
            "testKey", "test_key", 
            null, "value2", 
            "testUser", "testEvent", "testLog"
        );
        
        ValueComparisonUtil.compareValues(
            "testKey", "test_key", 
            "value1", "", 
            "testUser", "testEvent", "testLog"
        );
        
        System.out.println("其中一个值为空时，应该记录差异日志");
    }
    
    /**
     * 测试简单字符串值对比
     */
    @Test
    public void testCompareValues_SimpleStrings() {
        System.out.println("=== 测试简单字符串值对比 ===");
        
        // 相同的字符串
        ValueComparisonUtil.compareValues(
            "testKey", "test_key", 
            "sameValue", "sameValue", 
            "testUser", "testEvent", "testLog"
        );
        
        // 不同的字符串
        ValueComparisonUtil.compareValues(
            "testKey", "test_key", 
            "value1", "value2", 
            "testUser", "testEvent", "testLog"
        );
        
        System.out.println("字符串对比完成");
    }
    
    /**
     * 测试数值对比
     */
    @Test
    public void testCompareValues_Numbers() {
        System.out.println("=== 测试数值对比 ===");
        
        // 相同的数值
        ValueComparisonUtil.compareValues(
            "testKey", "test_key", 
            "123", "123", 
            "testUser", "testEvent", "testLog"
        );
        
        // 不同的数值
        ValueComparisonUtil.compareValues(
            "testKey", "test_key", 
            "123", "456", 
            "testUser", "testEvent", "testLog"
        );
        
        // 数值类型不同但值相同
        ValueComparisonUtil.compareValues(
            "testKey", "test_key", 
            123, "123", 
            "testUser", "testEvent", "testLog"
        );
        
        // 浮点数对比
        ValueComparisonUtil.compareValues(
            "testKey", "test_key", 
            "123.45", "123.45", 
            "testUser", "testEvent", "testLog"
        );
        
        ValueComparisonUtil.compareValues(
            "testKey", "test_key", 
            "123.45", "123.46", 
            "testUser", "testEvent", "testLog"
        );
        
        System.out.println("数值对比完成");
    }
    
    /**
     * 测试布尔值对比
     */
    @Test
    public void testCompareValues_Booleans() {
        System.out.println("=== 测试布尔值对比 ===");
        
        ValueComparisonUtil.compareValues(
            "testKey", "test_key", 
            true, "true", 
            "testUser", "testEvent", "testLog"
        );
        
        ValueComparisonUtil.compareValues(
            "testKey", "test_key", 
            false, "true", 
            "testUser", "testEvent", "testLog"
        );
        
        System.out.println("布尔值对比完成");
    }
    
    /**
     * 测试简单 JSON 对象对比
     */
    @Test
    public void testCompareValues_SimpleJsonObjects() {
        System.out.println("=== 测试简单 JSON 对象对比 ===");
        
        // 相同的 JSON 对象
        Map<String, Object> map1 = new HashMap<>();
        map1.put("key1", "value1");
        map1.put("key2", 123);
        
        Map<String, Object> map2 = new HashMap<>();
        map2.put("key1", "value1");
        map2.put("key2", 123);
        
        ValueComparisonUtil.compareValues(
            "testKey", "test_key", 
            map1, map2, 
            "testUser", "testEvent", "testLog"
        );
        
        // 不同的 JSON 对象
        Map<String, Object> map3 = new HashMap<>();
        map3.put("key1", "value1");
        map3.put("key2", 456);
        
        ValueComparisonUtil.compareValues(
            "testKey", "test_key", 
            map1, map3, 
            "testUser", "testEvent", "testLog"
        );
        
        System.out.println("简单 JSON 对象对比完成");
    }
    
    /**
     * 测试复杂 JSON 对象对比
     */
    @Test
    public void testCompareValues_ComplexJsonObjects() {
        System.out.println("=== 测试复杂 JSON 对象对比 ===");
        
        // 创建复杂的 JSON 对象
        Map<String, Object> camelMap = new HashMap<>();
        camelMap.put("userName", "张三");
        camelMap.put("userAge", 25);
        camelMap.put("isActive", true);
        
        Map<String, Object> nestedMap1 = new HashMap<>();
        nestedMap1.put("city", "北京");
        nestedMap1.put("district", "朝阳区");
        camelMap.put("address", nestedMap1);
        
        List<String> hobbies1 = new ArrayList<>();
        hobbies1.add("读书");
        hobbies1.add("游泳");
        camelMap.put("hobbies", hobbies1);
        
        // 创建下划线格式的对象
        Map<String, Object> underscoreMap = new HashMap<>();
        underscoreMap.put("user_name", "张三");
        underscoreMap.put("user_age", 25);
        underscoreMap.put("is_active", true);
        
        Map<String, Object> nestedMap2 = new HashMap<>();
        nestedMap2.put("city", "北京");
        nestedMap2.put("district", "朝阳区");
        underscoreMap.put("address", nestedMap2);
        
        List<String> hobbies2 = new ArrayList<>();
        hobbies2.add("读书");
        hobbies2.add("游泳");
        underscoreMap.put("hobbies", hobbies2);
        
        ValueComparisonUtil.compareValues(
            "userInfo", "user_info", 
            camelMap, underscoreMap, 
            "testUser", "testEvent", "testLog"
        );
        
        System.out.println("复杂 JSON 对象对比完成");
    }
    
    /**
     * 测试 JSON 字符串对比
     */
    @Test
    public void testCompareValues_JsonStrings() {
        System.out.println("=== 测试 JSON 字符串对比 ===");
        
        String jsonStr1 = "{\"name\":\"张三\",\"age\":25,\"city\":\"北京\"}";
        String jsonStr2 = "{\"name\":\"张三\",\"age\":25,\"city\":\"北京\"}";
        String jsonStr3 = "{\"name\":\"李四\",\"age\":30,\"city\":\"上海\"}";
        
        // 相同的 JSON 字符串
        ValueComparisonUtil.compareValues(
            "userInfo", "user_info", 
            jsonStr1, jsonStr2, 
            "testUser", "testEvent", "testLog"
        );
        
        // 不同的 JSON 字符串
        ValueComparisonUtil.compareValues(
            "userInfo", "user_info", 
            jsonStr1, jsonStr3, 
            "testUser", "testEvent", "testLog"
        );
        
        System.out.println("JSON 字符串对比完成");
    }
    
    /**
     * 测试键存在性差异
     */
    @Test
    public void testCompareValues_KeyDifferences() {
        System.out.println("=== 测试键存在性差异 ===");
        
        Map<String, Object> map1 = new HashMap<>();
        map1.put("key1", "value1");
        map1.put("key2", "value2");
        map1.put("onlyInCamel", "camelValue");
        
        Map<String, Object> map2 = new HashMap<>();
        map2.put("key1", "value1");
        map2.put("key2", "value2");
        map2.put("onlyInUnderscore", "underscoreValue");
        
        ValueComparisonUtil.compareValues(
            "testKey", "test_key", 
            map1, map2, 
            "testUser", "testEvent", "testLog"
        );
        
        System.out.println("键存在性差异测试完成");
    }
    
    /**
     * 测试异常处理
     */
    @Test
    public void testCompareValues_ExceptionHandling() {
        System.out.println("=== 测试异常处理 ===");
        
        // 测试无效的 JSON 字符串
        String invalidJson = "{invalid json}";
        Map<String, Object> validMap = new HashMap<>();
        validMap.put("key", "value");
        
        ValueComparisonUtil.compareValues(
            "testKey", "test_key", 
            invalidJson, validMap, 
            "testUser", "testEvent", "testLog"
        );
        
        System.out.println("异常处理测试完成");
    }
    
    /**
     * 测试不同数据类型混合对比
     */
    @Test
    public void testCompareValues_MixedTypes() {
        System.out.println("=== 测试不同数据类型混合对比 ===");
        
        // Map 和 JSON 字符串对比
        Map<String, Object> map = new HashMap<>();
        map.put("name", "张三");
        map.put("age", 25);
        
        String jsonStr = "{\"name\":\"张三\",\"age\":25}";
        
        ValueComparisonUtil.compareValues(
            "userInfo", "user_info", 
            map, jsonStr, 
            "testUser", "testEvent", "testLog"
        );
        
        // List 和 JSON 数组字符串对比
        List<String> list = new ArrayList<>();
        list.add("item1");
        list.add("item2");
        
        String jsonArray = "[\"item1\",\"item2\"]";
        
        ValueComparisonUtil.compareValues(
            "items", "items", 
            list, jsonArray, 
            "testUser", "testEvent", "testLog"
        );
        
        System.out.println("混合类型对比完成");
    }

    /**
     * 测试边界情况
     */
    @Test
    public void testCompareValues_EdgeCases() {
        System.out.println("=== 测试边界情况 ===");

        // 测试空字符串和 null
        ValueComparisonUtil.compareValues(
            "testKey", "test_key",
            "", null,
            "testUser", "testEvent", "testLog"
        );

        // 测试空 Map
        Map<String, Object> emptyMap1 = new HashMap<>();
        Map<String, Object> emptyMap2 = new HashMap<>();

        ValueComparisonUtil.compareValues(
            "testKey", "test_key",
            emptyMap1, emptyMap2,
            "testUser", "testEvent", "testLog"
        );

        // 测试空 List
        List<Object> emptyList1 = new ArrayList<>();
        List<Object> emptyList2 = new ArrayList<>();

        ValueComparisonUtil.compareValues(
            "testKey", "test_key",
            emptyList1, emptyList2,
            "testUser", "testEvent", "testLog"
        );

        // 测试空 JSON 字符串
        ValueComparisonUtil.compareValues(
            "testKey", "test_key",
            "{}", "{}",
            "testUser", "testEvent", "testLog"
        );

        ValueComparisonUtil.compareValues(
            "testKey", "test_key",
            "[]", "[]",
            "testUser", "testEvent", "testLog"
        );

        System.out.println("边界情况测试完成");
    }

    /**
     * 测试特殊字符和中文
     */
    @Test
    public void testCompareValues_SpecialCharacters() {
        System.out.println("=== 测试特殊字符和中文 ===");

        // 测试中文字符
        ValueComparisonUtil.compareValues(
            "中文键", "中文_键",
            "中文值1", "中文值2",
            "测试用户", "测试事件", "testLog"
        );

        // 测试特殊字符
        ValueComparisonUtil.compareValues(
            "special@Key", "special_key",
            "value with spaces", "value_with_underscores",
            "<EMAIL>", "event#123", "testLog"
        );

        // 测试 JSON 中的特殊字符
        Map<String, Object> specialMap1 = new HashMap<>();
        specialMap1.put("key with spaces", "value with \"quotes\"");
        specialMap1.put("emoji", "😀😃😄");

        Map<String, Object> specialMap2 = new HashMap<>();
        specialMap2.put("key with spaces", "value with \"quotes\"");
        specialMap2.put("emoji", "😀😃😄");

        ValueComparisonUtil.compareValues(
            "specialData", "special_data",
            specialMap1, specialMap2,
            "testUser", "testEvent", "testLog"
        );

        System.out.println("特殊字符测试完成");
    }

    /**
     * 测试大数据量对比
     */
    @Test
    public void testCompareValues_LargeData() {
        System.out.println("=== 测试大数据量对比 ===");

        // 创建大的 Map
        Map<String, Object> largeMap1 = new HashMap<>();
        Map<String, Object> largeMap2 = new HashMap<>();

        for (int i = 0; i < 100; i++) {
            largeMap1.put("key" + i, "value" + i);
            largeMap2.put("key" + i, "value" + i);
        }

        // 在第50个位置添加差异
        largeMap2.put("key50", "differentValue50");

        long startTime = System.currentTimeMillis();

        ValueComparisonUtil.compareValues(
            "largeData", "large_data",
            largeMap1, largeMap2,
            "testUser", "testEvent", "testLog"
        );

        long endTime = System.currentTimeMillis();
        System.out.println("大数据量对比耗时: " + (endTime - startTime) + "ms");
    }

    /**
     * 测试嵌套对象对比
     */
    @Test
    public void testCompareValues_NestedObjects() {
        System.out.println("=== 测试嵌套对象对比 ===");

        // 创建深层嵌套的对象
        Map<String, Object> level3Map1 = new HashMap<>();
        level3Map1.put("level3Key", "level3Value");

        Map<String, Object> level2Map1 = new HashMap<>();
        level2Map1.put("level2Key", "level2Value");
        level2Map1.put("level3", level3Map1);

        Map<String, Object> level1Map1 = new HashMap<>();
        level1Map1.put("level1Key", "level1Value");
        level1Map1.put("level2", level2Map1);

        // 创建相似但有差异的嵌套对象
        Map<String, Object> level3Map2 = new HashMap<>();
        level3Map2.put("level3Key", "differentLevel3Value");

        Map<String, Object> level2Map2 = new HashMap<>();
        level2Map2.put("level2Key", "level2Value");
        level2Map2.put("level3", level3Map2);

        Map<String, Object> level1Map2 = new HashMap<>();
        level1Map2.put("level1Key", "level1Value");
        level1Map2.put("level2", level2Map2);

        ValueComparisonUtil.compareValues(
            "nestedData", "nested_data",
            level1Map1, level1Map2,
            "testUser", "testEvent", "testLog"
        );

        System.out.println("嵌套对象对比完成");
    }

    /**
     * 测试数组对比
     */
    @Test
    public void testCompareValues_Arrays() {
        System.out.println("=== 测试数组对比 ===");

        // 测试相同的数组
        List<String> list1 = new ArrayList<>();
        list1.add("item1");
        list1.add("item2");
        list1.add("item3");

        List<String> list2 = new ArrayList<>();
        list2.add("item1");
        list2.add("item2");
        list2.add("item3");

        ValueComparisonUtil.compareValues(
            "arrayData", "array_data",
            list1, list2,
            "testUser", "testEvent", "testLog"
        );

        // 测试不同的数组
        List<String> list3 = new ArrayList<>();
        list3.add("item1");
        list3.add("differentItem");
        list3.add("item3");

        ValueComparisonUtil.compareValues(
            "arrayData", "array_data",
            list1, list3,
            "testUser", "testEvent", "testLog"
        );

        // 测试包含对象的数组
        List<Map<String, Object>> objectList1 = new ArrayList<>();
        Map<String, Object> obj1 = new HashMap<>();
        obj1.put("id", 1);
        obj1.put("name", "object1");
        objectList1.add(obj1);

        List<Map<String, Object>> objectList2 = new ArrayList<>();
        Map<String, Object> obj2 = new HashMap<>();
        obj2.put("id", 1);
        obj2.put("name", "object1");
        objectList2.add(obj2);

        ValueComparisonUtil.compareValues(
            "objectArray", "object_array",
            objectList1, objectList2,
            "testUser", "testEvent", "testLog"
        );

        System.out.println("数组对比完成");
    }

    /**
     * 测试性能
     */
    @Test
    public void testCompareValues_Performance() {
        System.out.println("=== 测试性能 ===");

        // 创建复杂的测试数据
        Map<String, Object> complexMap1 = createComplexTestData();
        Map<String, Object> complexMap2 = createComplexTestData();

        // 添加一些差异
        complexMap2.put("modifiedKey", "modifiedValue");

        // 执行多次对比测试性能
        long totalTime = 0;
        int iterations = 10;

        for (int i = 0; i < iterations; i++) {
            long startTime = System.currentTimeMillis();

            ValueComparisonUtil.compareValues(
                "performanceTest", "performance_test",
                complexMap1, complexMap2,
                "testUser", "testEvent", "performanceLog"
            );

            long endTime = System.currentTimeMillis();
            totalTime += (endTime - startTime);
        }

        System.out.println("平均对比耗时: " + (totalTime / iterations) + "ms");
    }

    /**
     * 创建复杂的测试数据
     */
    private Map<String, Object> createComplexTestData() {
        Map<String, Object> complexMap = new HashMap<>();

        // 添加各种类型的数据
        complexMap.put("stringValue", "测试字符串");
        complexMap.put("intValue", 123);
        complexMap.put("doubleValue", 123.45);
        complexMap.put("booleanValue", true);

        // 添加嵌套对象
        Map<String, Object> nestedMap = new HashMap<>();
        nestedMap.put("nestedString", "嵌套字符串");
        nestedMap.put("nestedInt", 456);
        complexMap.put("nestedObject", nestedMap);

        // 添加数组
        List<String> stringList = new ArrayList<>();
        stringList.add("数组项1");
        stringList.add("数组项2");
        stringList.add("数组项3");
        complexMap.put("stringArray", stringList);

        // 添加对象数组
        List<Map<String, Object>> objectList = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            Map<String, Object> obj = new HashMap<>();
            obj.put("id", i);
            obj.put("name", "对象" + i);
            objectList.add(obj);
        }
        complexMap.put("objectArray", objectList);

        return complexMap;
    }
}
