package com.youxin.risk.commons.test.metrics;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.utils.JacksonUtil;

import java.util.Map;

public class JsonTest {

    public static void main(String[] args) {
        String str = "{\"type\":\"async\",\"requestId\":\"1548140887_A_3612801\",\"message\":{\"eventCode\":\"prepareHaoHuanApply\",\"userKey\":\"45f5e8d45c1df31c9db7adff64337faa\",\"appVersion\":\"451\",\"sourceSystem\":\"HAO_HUAN\",\"latitude\":\"103.908195\",\"wifiSSID\":\"\\602\",\"ip\":\"*************\",\"channel\":\"vivo\",\"deviceName\":\"vivo+X%E8%8E%93\",\"deviceId\":\"ffffffff-d790-93b9-0000-0000546ab9d4-fff317558bd1165f\",\"platform\":\"Android\",\"batteryPlugType\":\"0\",\"osVersion\":\"7.1.2\",\"mobileModel\":\"vivo X9s Plus\",\"taobaoStatus\":\"\",\"taobaoTime\":\"0\",\"alipayStatus\":\"0\",\"alipayTime\":\"0\",\"creditCardStatus\":\"0\",\"creditCardTime\":\"0\",\"phoneInfoStatus\":\"1\",\"isShowCreditCard\":\"0\",\"wifiMac\":\"8c:f2:28:e2:f9:6a\",\"wifiLevel\":\"4\",\"device\":\"vivo X9s Plus\",\"isCopyPackage\":\"0\",\"longitude\":\"28.96249\",\"batteryLevel\":\"24\",\"channelCode\":\"vivo\"}}";
        JSONObject jsonObject = JSONObject.parseObject(str);
        System.out.println(JSONObject.parseObject(str,Map.class));

        System.out.println(JacksonUtil.toObject(str, Map.class));
    }
}
