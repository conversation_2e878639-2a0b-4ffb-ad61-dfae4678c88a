package com.youxin.risk.commons.utils;

import com.alibaba.fastjson.JSON;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * ValueComparisonUtil 使用示例
 * 展示如何在实际场景中使用值对比工具类
 */
public class ValueComparisonUtilExample {
    
    /**
     * 示例1：回调服务中的值对比
     * 模拟 CallbackService 中的使用场景
     */
    @Test
    public void example1_CallbackServiceUsage() {
        System.out.println("=== 示例1：回调服务中的值对比 ===");
        
        // 模拟事件输出数据
        String camelKey = "userInfo";
        String underscoreKey = "user_info";
        
        // 驼峰格式的值
        Map<String, Object> camelValue = new HashMap<>();
        camelValue.put("userName", "张三");
        camelValue.put("userAge", 25);
        camelValue.put("isActive", true);
        
        // 下划线格式的值
        Map<String, Object> underscoreValue = new HashMap<>();
        underscoreValue.put("user_name", "张三");
        underscoreValue.put("user_age", 25);
        underscoreValue.put("is_active", true);
        
        // 进行对比
        ValueComparisonUtil.compareValues(
            camelKey, underscoreKey,
            camelValue, underscoreValue,
            "user123", "callbackEvent",
            "callbackValueDifference"
        );
        
        System.out.println("回调服务值对比完成");
    }
    
    /**
     * 示例2：API 响应对比
     * 对比新旧 API 响应格式的差异
     */
    @Test
    public void example2_ApiResponseComparison() {
        System.out.println("=== 示例2：API 响应对比 ===");
        
        // 旧版 API 响应
        String oldApiResponse = "{\n" +
            "  \"retCode\": \"0000\",\n" +
            "  \"retMsg\": \"成功\",\n" +
            "  \"data\": {\n" +
            "    \"userId\": \"12345\",\n" +
            "    \"userName\": \"张三\",\n" +
            "    \"userScore\": 85.5\n" +
            "  }\n" +
            "}";
        
        // 新版 API 响应（下划线格式）
        String newApiResponse = "{\n" +
            "  \"ret_code\": \"0000\",\n" +
            "  \"ret_msg\": \"成功\",\n" +
            "  \"data\": {\n" +
            "    \"user_id\": \"12345\",\n" +
            "    \"user_name\": \"张三\",\n" +
            "    \"user_score\": 85.5\n" +
            "  }\n" +
            "}";
        
        ValueComparisonUtil.compareValues(
            "apiResponse", "api_response",
            oldApiResponse, newApiResponse,
            "system", "apiMigration",
            "apiResponseDifference"
        );
        
        System.out.println("API 响应对比完成");
    }
    
    /**
     * 示例3：配置文件对比
     * 对比不同环境的配置差异
     */
    @Test
    public void example3_ConfigurationComparison() {
        System.out.println("=== 示例3：配置文件对比 ===");
        
        // 开发环境配置
        Map<String, Object> devConfig = new HashMap<>();
        devConfig.put("databaseUrl", "*****************************");
        devConfig.put("redisHost", "dev-redis");
        devConfig.put("logLevel", "DEBUG");
        devConfig.put("enableCache", true);
        
        // 生产环境配置
        Map<String, Object> prodConfig = new HashMap<>();
        prodConfig.put("database_url", "******************************");
        prodConfig.put("redis_host", "prod-redis");
        prodConfig.put("log_level", "INFO");
        prodConfig.put("enable_cache", true);
        
        ValueComparisonUtil.compareValues(
            "config", "config",
            devConfig, prodConfig,
            "admin", "configCheck",
            "configDifference"
        );
        
        System.out.println("配置文件对比完成");
    }
    
    /**
     * 示例4：数据迁移验证
     * 验证数据迁移前后的一致性
     */
    @Test
    public void example4_DataMigrationValidation() {
        System.out.println("=== 示例4：数据迁移验证 ===");
        
        // 迁移前的用户数据
        Map<String, Object> beforeMigration = new HashMap<>();
        beforeMigration.put("id", 12345);
        beforeMigration.put("name", "李四");
        beforeMigration.put("email", "<EMAIL>");
        beforeMigration.put("createTime", "2023-01-01 10:00:00");
        beforeMigration.put("isDeleted", false);
        
        // 迁移后的用户数据（字段名可能有变化）
        Map<String, Object> afterMigration = new HashMap<>();
        afterMigration.put("user_id", 12345);
        afterMigration.put("user_name", "李四");
        afterMigration.put("email_address", "<EMAIL>");
        afterMigration.put("created_at", "2023-01-01 10:00:00");
        afterMigration.put("deleted", false);
        
        ValueComparisonUtil.compareValues(
            "userData", "user_data",
            beforeMigration, afterMigration,
            "migrationTool", "dataMigration",
            "migrationValidation"
        );
        
        System.out.println("数据迁移验证完成");
    }
    
    /**
     * 示例5：A/B 测试结果对比
     * 对比不同版本的功能输出
     */
    @Test
    public void example5_ABTestComparison() {
        System.out.println("=== 示例5：A/B 测试结果对比 ===");
        
        // A 版本的风控结果
        Map<String, Object> versionA = new HashMap<>();
        versionA.put("riskScore", 75.5);
        versionA.put("riskLevel", "MEDIUM");
        versionA.put("isPassed", true);
        versionA.put("reason", "正常用户");
        
        Map<String, Object> detailA = new HashMap<>();
        detailA.put("deviceScore", 80);
        detailA.put("behaviorScore", 70);
        detailA.put("creditScore", 76);
        versionA.put("scoreDetail", detailA);
        
        // B 版本的风控结果
        Map<String, Object> versionB = new HashMap<>();
        versionB.put("risk_score", 78.2);
        versionB.put("risk_level", "MEDIUM");
        versionB.put("is_passed", true);
        versionB.put("reason", "正常用户");
        
        Map<String, Object> detailB = new HashMap<>();
        detailB.put("device_score", 82);
        detailB.put("behavior_score", 72);
        detailB.put("credit_score", 80);
        versionB.put("score_detail", detailB);
        
        ValueComparisonUtil.compareValues(
            "riskResult", "risk_result",
            versionA, versionB,
            "testUser001", "abTest",
            "abTestComparison"
        );
        
        System.out.println("A/B 测试结果对比完成");
    }
    
    /**
     * 示例6：错误场景处理
     * 展示如何处理各种异常情况
     */
    @Test
    public void example6_ErrorScenarios() {
        System.out.println("=== 示例6：错误场景处理 ===");
        
        // 场景1：一个值为 null
        ValueComparisonUtil.compareValues(
            "nullTest", "null_test",
            "normalValue", null,
            "testUser", "errorTest",
            "errorScenario"
        );
        
        // 场景2：无效的 JSON 字符串
        ValueComparisonUtil.compareValues(
            "invalidJson", "invalid_json",
            "{invalid json string}", "{}",
            "testUser", "errorTest",
            "errorScenario"
        );
        
        // 场景3：类型完全不匹配
        ValueComparisonUtil.compareValues(
            "typeMismatch", "type_mismatch",
            123, "string value",
            "testUser", "errorTest",
            "errorScenario"
        );
        
        System.out.println("错误场景处理完成");
    }
    
    /**
     * 示例7：实际业务场景 - 风控策略结果对比
     * 模拟风控引擎中策略结果的对比
     */
    @Test
    public void example7_RiskStrategyComparison() {
        System.out.println("=== 示例7：风控策略结果对比 ===");
        
        // 构建复杂的风控结果数据
        String strategyResultJson = "{\n" +
            "  \"isPassed\": true,\n" +
            "  \"riskScore\": 65.8,\n" +
            "  \"riskLevel\": \"LOW\",\n" +
            "  \"reasonCode\": \"R001\",\n" +
            "  \"ruleResults\": [\n" +
            "    {\"ruleId\": \"RULE_001\", \"result\": \"PASS\", \"score\": 20},\n" +
            "    {\"ruleId\": \"RULE_002\", \"result\": \"PASS\", \"score\": 25},\n" +
            "    {\"ruleId\": \"RULE_003\", \"result\": \"WARN\", \"score\": 15}\n" +
            "  ],\n" +
            "  \"userProfile\": {\n" +
            "    \"userId\": \"U123456\",\n" +
            "    \"userType\": \"NORMAL\",\n" +
            "    \"registrationDays\": 365\n" +
            "  }\n" +
            "}";
        
        // 下划线格式的结果
        Map<String, Object> underscoreResult = JSON.parseObject(strategyResultJson, Map.class);
        
        ValueComparisonUtil.compareValues(
            "strategyResult", "strategy_result",
            strategyResultJson, underscoreResult,
            "U123456", "riskStrategy",
            "strategyComparison"
        );
        
        System.out.println("风控策略结果对比完成");
    }
}
