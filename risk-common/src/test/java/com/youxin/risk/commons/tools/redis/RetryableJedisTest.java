package com.youxin.risk.commons.tools.redis;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Collections;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration({"classpath:spring-config.xml", "classpath:spring-redis.xml"})
public class RetryableJedisTest {

    @Autowired
    RetryableJedis retryableJedis;

    private static final String SCRIPT = "if (redis.call('get', KEYS[1]) == ARGV[1]) then " +
            " return redis.call('del', KEYS[1])" +
            " else" +
            " return redis.call('set', KEYS[1], ARGV[1])" +
            " end;";

    @Test
    public void testScriptLoad() {
        String key = "tttttttttttttt";
        retryableJedis.eval(SCRIPT, Collections.singletonList(key), Collections.singletonList("123"));
        String s = retryableJedis.get(key);
        System.out.println(s);
    }
}