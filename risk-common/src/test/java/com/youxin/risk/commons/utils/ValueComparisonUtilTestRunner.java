package com.youxin.risk.commons.utils;

/**
 * ValueComparisonUtil 测试运行器
 * 用于手动运行测试方法，验证功能是否正常
 */
public class ValueComparisonUtilTestRunner {
    
    public static void main(String[] args) {
        System.out.println("开始运行 ValueComparisonUtil 测试...");
        
        ValueComparisonUtilTest test = new ValueComparisonUtilTest();
        
        try {
            // 运行基础测试
            System.out.println("\n1. 运行基础测试...");
            test.setUpStreams();
            test.testCompareValues_BothEmpty();
            test.testCompareValues_OneEmpty();
            test.testCompareValues_SimpleStrings();
            test.restoreStreams();
            
            // 运行数值测试
            System.out.println("\n2. 运行数值测试...");
            test.setUpStreams();
            test.testCompareValues_Numbers();
            test.testCompareValues_Booleans();
            test.restoreStreams();
            
            // 运行 JSON 对象测试
            System.out.println("\n3. 运行 JSON 对象测试...");
            test.setUpStreams();
            test.testCompareValues_SimpleJsonObjects();
            test.testCompareValues_ComplexJsonObjects();
            test.testCompareValues_JsonStrings();
            test.restoreStreams();
            
            // 运行边界情况测试
            System.out.println("\n4. 运行边界情况测试...");
            test.setUpStreams();
            test.testCompareValues_EdgeCases();
            test.testCompareValues_SpecialCharacters();
            test.restoreStreams();
            
            // 运行高级测试
            System.out.println("\n5. 运行高级测试...");
            test.setUpStreams();
            test.testCompareValues_NestedObjects();
            test.testCompareValues_Arrays();
            test.restoreStreams();
            
            // 运行性能测试
            System.out.println("\n6. 运行性能测试...");
            test.setUpStreams();
            test.testCompareValues_Performance();
            test.restoreStreams();
            
            System.out.println("\n✅ 所有测试运行完成！");
            
        } catch (Exception e) {
            System.err.println("❌ 测试运行过程中出现异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
