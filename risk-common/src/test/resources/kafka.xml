<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
     http://www.springframework.org/schema/tx
     http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
     http://www.springframework.org/schema/jee
     http://www.springframework.org/schema/jee/spring-jee-3.0.xsd
     http://www.springframework.org/schema/context
     http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <!-- spring和kafka集成相关配置 -->
    <bean id="consumerProperties" class="java.util.HashMap">
        <constructor-arg>
            <map>
                <entry key="bootstrap.servers" value="172.16.1.90:9092,172.16.1.91:9092,172.16.2.148:9092"/>
                <entry key="group.id" value="riskGroup_di_ttttt_liujy"/>
                <entry key="enable.auto.commit" value="false"/>
                <entry key="auto.commit.interval.ms" value="1000"/>
                <entry key="session.timeout.ms" value="30000"/>
                <entry key="heartbeat.interval.ms" value="10000"/>
                <entry key="key.deserializer" value="org.apache.kafka.common.serialization.StringDeserializer"/>
                <entry key="value.deserializer" value="org.apache.kafka.common.serialization.StringDeserializer"/>
            </map>
        </constructor-arg>
    </bean>

    <bean id="consumerFactory" class="org.springframework.kafka.core.DefaultKafkaConsumerFactory">
        <constructor-arg>
            <ref bean="consumerProperties"/>
        </constructor-arg>
    </bean>

    <bean id="containerProperties" class="org.springframework.kafka.listener.config.ContainerProperties">
        <constructor-arg value="babel.crawl.result.test"/>
        <property name="messageListener" ref="dpMsgListener"/>
        <property name="ackMode" value="MANUAL_IMMEDIATE"/>
    </bean>

    <bean id="messageListenerContainer" class="org.springframework.kafka.listener.KafkaMessageListenerContainer"
          init-method="doStart">
        <constructor-arg ref="consumerFactory"/>
        <constructor-arg ref="containerProperties"/>
    </bean>

    <!-- risk kafka api -->
    <bean id="dpMsgListener" class="com.youxin.risk.commons.kafkav2.RiskKafkaAcknowledgingMessageListener">
        <property name="filters">
            <list>
                <ref bean="dpMsgPaserFilter"/>
            </list>
        </property>
        <property name="handler" ref="dpMsgHander"/>
    </bean>

    <!-- 消息解析filter，将消息反序列化对对象，默认使用fastjson，可自己实现 -->
    <bean id="dpMsgPaserFilter" class="com.youxin.risk.commons.kafkav2.filter.impl.ParseFilter">
        <property name="serializedBeanClassName" value="java.util.HashMap"/>
    </bean>

    <!-- 消息处理器，继承BaseKafKaMessageHandler，注入RetryableJedis，会进行判重操作，重复的请求不再处理 -->
    <bean id="dpMsgHander" class="com.youxin.risk.commons.kafkav2.handler.impl.DemoKafkaMessageHandler"></bean>


    <bean class="com.youxin.risk.commons.utils.ContextUtil" />
</beans>