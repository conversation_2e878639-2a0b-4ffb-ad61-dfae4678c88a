<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="
		http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.1.xsd">

    <bean id="genericObjectPoolConfig" class="org.apache.commons.pool2.impl.GenericObjectPoolConfig">
        <property name="maxTotal" value="8" />
        <property name="maxIdle" value="8"/>
        <property name="minIdle" value="4"/>
        <property name="maxWaitMillis" value="5000"/>
        <property name="testOnBorrow" value="true" />
    </bean>
    <bean id="jedisCluster" class="com.youxin.risk.commons.tools.redis.JedisClusterFactory">
        <property name="genericObjectPoolConfig" ref="genericObjectPoolConfig"/>
        <property name="connectionTimeout" value="3000"/>
        <property name="soTimeout" value="3000"/>
        <property name="maxAttempts" value="1"/>
        <property name="password" value="passwd456"/>
        <property name="nodes" value="172.16.3.191:7000,172.16.3.191:7001,172.16.3.191:7002,172.16.3.191:7100,172.16.3.191:7101,172.16.3.191:7102"/>
    </bean>

    <bean id="retryableJedis" class="com.youxin.risk.commons.tools.redis.RetryableJedis">
        <property name="jedisCluster" ref="jedisCluster"/>
    </bean>

    <bean id="redisLock" class="com.youxin.risk.commons.tools.lock.RedisLock">
        <property name="retryableJedis" ref="retryableJedis"/>
    </bean>

    <bean id="riskDiNoRdbAof" class="com.youxin.risk.commons.tools.redis.JedisClusterFactory">
        <property name="genericObjectPoolConfig" ref="genericObjectPoolConfig"/>
        <property name="connectionTimeout" value="3000"/>
        <property name="soTimeout" value="3000"/>
        <property name="maxAttempts" value="1"/>
        <property name="password" value="passwd456"/>
        <property name="nodes" value="172.16.3.191:7000,172.16.3.191:7001,172.16.3.191:7002,172.16.3.191:7100,172.16.3.191:7101,172.16.3.191:7102"/>
    </bean>

    <bean id="riskDiNoRdbAofRedis" class="com.youxin.risk.commons.tools.redis.RetryableJedis">
        <property name="jedisCluster" ref="riskDiNoRdbAof"/>
    </bean>

</beans>