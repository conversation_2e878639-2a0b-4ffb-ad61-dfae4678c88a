<?xml version="1.0" encoding="UTF-8" ?>
<!-- Mybatis config sample -->
<!DOCTYPE configuration
    PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">

<configuration>
    <environments default = "default">
        <environment id="default">
            <transactionManager type="JDBC"/>
            <dataSource type="UNPOOLED">
                <property name = "driver" value = "com.mysql.jdbc.Driver"/>
                <property name="url" value="*********************************************************************************************************************************************************************************************"/>
                <property name="username" value="root"/>
                <property name="password" value="youxin_risk"/>
            </dataSource>
        </environment>
    </environments>

    <mappers>
        <mapper resource="mapper/admin/ProcessNodeMapper.xml"/>
    </mappers>
</configuration>
