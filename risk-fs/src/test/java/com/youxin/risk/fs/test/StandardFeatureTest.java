package com.youxin.risk.fs.test;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.fs.service.impl.FeatureStandardCalServiceImpl;
import com.youxin.risk.fs.vo.SingleData;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2020/3/20
 */
public class StandardFeatureTest extends BaseTest {
    @Autowired
    private FeatureStandardCalServiceImpl featureStandardCalService;

    @Test
    public  void testParseData(){
        // 1
        String data="{\"aaa\":{\"bbb\":\"strData\"}}";
        String path="sss|aaa/bbc|aaa";

        SingleData result= featureStandardCalService.getSingleDataFromDataVoByPath(JSONObject.parseObject(data),path);
        System.out.println(result);

        path = "aaa|aaa/@bbb";
        result = featureStandardCalService.getSingleDataFromDataVoByPath(JSONObject.parseObject(data),path);

        data = "{\"aaa\":{\"bbb\":\"{\\\"jsonData\\\":\\\"strData\\\"}\"}}";
        path="sss|aaa/bbb/@jsonData";
        result= featureStandardCalService.getSingleDataFromDataVoByPath(JSONObject.parseObject(data),path);
        System.out.println(result);

        path = "sss|aaa/bbb/jsonData";
        result=featureStandardCalService.getSingleDataFromDataVoByPath(JSONObject.parseObject(data),path);
        System.out.println(result);
    }

}
