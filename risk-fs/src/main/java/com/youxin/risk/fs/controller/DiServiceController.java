package com.youxin.risk.fs.controller;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.cache.RedisCacheService;
import com.youxin.risk.commons.kafkav2.sender.KafkaSyncSender;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.mongo.MongoDao;
import com.youxin.risk.commons.tools.redis.RetryableJedis;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.commons.vo.StrategyInvokeVo;
import com.youxin.risk.commons.vo.TaskDataVo;
import com.youxin.risk.fs.mongo.dao.DataSubmitMongoDao;
import com.youxin.risk.fs.mongo.dao.StrategyMongoDao;
import com.youxin.risk.fs.service.DataSubmitService;
import com.youxin.risk.fs.service.DiSystemService;
import com.youxin.risk.fs.service.StrategyResultService;
import com.youxin.risk.fs.service.impl.FeatureServiceImpl;

import org.apache.commons.lang3.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

@RestController
@RequestMapping("/sharding")
public class DiServiceController {


	private static final Logger logger = LoggerFactory.getLogger(DiServiceController.class);

	@Autowired
	private MongoDao dao;

	@Autowired
	private DataSubmitMongoDao dataSubmitMongoDao;

    @Autowired
    private StrategyMongoDao strategyMongoDao;

	@Autowired
	private RetryableJedis retryableJedis;

	@Autowired
	private RedisCacheService redisCacheService;

	@Autowired
	private StrategyResultService strategyResultService;

	@Autowired
	private KafkaSyncSender strategyResultSender;

	@Resource
	private DataSubmitService dataSubmitService;

	@Autowired
	private FeatureServiceImpl featureService;

	@Autowired
	private DiSystemService diSystemService;

	@RequestMapping("event/sessionId/{sessionId}")
	public Event getEventBySessionId(@PathVariable String sessionId ){
		// Event event = eventService.getBySessionId(sessionId); TODO 替换接口
		Event event = diSystemService.getBySessionId(sessionId);
		return event;
	}

	@RequestMapping("col/query/userKey/{id}/{serviceCode}")
	public TaskDataVo queryByUserKey(@PathVariable String id,@PathVariable String serviceCode){
		return dao.getByUserKey(serviceCode,id,TaskDataVo.class);
	}

	@RequestMapping("col/query/loanKey/{id}/{serviceCode}")
	public TaskDataVo queryByLoanKey(@PathVariable String id,@PathVariable String serviceCode){
		return  dao.getByLoanKey(serviceCode,id,TaskDataVo.class);
	}

	/******** 模拟保存三方数据集合**********/
	@RequestMapping("col/create/{serviceCode}")
	public String createTaskDataVo(@PathVariable String serviceCode){
		String uid = UUID.randomUUID().toString();
		TaskDataVo vo = new TaskDataVo();
		vo.setServiceCode(serviceCode);
		vo.setCreateTime(new Date());
		vo.setLoanKey(uid);
		vo.setData(uid);
		vo.setJobId(uid);
		//vo.setJobId(uid);
		vo.setReason(uid);
		vo.setTaskId(RandomUtils.nextLong(1,9999999L));
		vo.setUserKey(uid);
		vo.setSourceSystem("HAO_HUAN");
		dao.insert(serviceCode,vo);
		return vo.getJobId();
	}

//	/******** 模拟保存三方数据集合**********/
//	@RequestMapping("col/create/{serviceCode}")
//	public String createTaskDataVo(@PathVariable String serviceCode,TaskDataVo vo){
//		dao.insert(serviceCode,vo);
//		return vo.getJobId();
//	}

	@RequestMapping("/dataSubmit")
	public Object getDataSubmit(@RequestParam(required = false) String userKey,
											@RequestParam(required = false) String eventCode,
											@RequestParam(required = false) String sourceSystem,
											@RequestParam(required = false) String loanKey,
											@RequestParam(required = false) String step,
											@RequestParam(required = false) Integer limit,
											@RequestParam(required = false) String createTimeStart,
											@RequestParam(required = false) String createTimeEnd,
											@RequestParam(required = false) boolean batchEvent) {

		List<String> loanKeyList = new ArrayList<>();
		if (StringUtils.isNotBlank(loanKey)) {
			String[] loanKeys = loanKey.split(",");
			loanKeyList.addAll(Arrays.asList(loanKeys));
		}
		return dataSubmitService.getMergedDataSubmitList(userKey, eventCode, sourceSystem, loanKeyList, step, limit,
				createTimeStart, createTimeEnd,batchEvent);

	}

	@RequestMapping("/dataSubmit/hbase")
	public Object getDataSubmit(@RequestParam String rowKey,@RequestParam(required = false) boolean batchEvent) {
		return dataSubmitService.getDataSubmit(rowKey,batchEvent);
	}


	@RequestMapping("/dataSubmit/es")
	public Object getDataSubmit(@RequestParam(required = false) String loanKey,
								@RequestParam(required = false) String userKey,
								@RequestParam(required = false) String eventCode,
								@RequestParam(required = false) String sourceSystem,
								@RequestParam(required = false) String step) {
		return dataSubmitService.getDataSubmitByES(loanKey,userKey,eventCode,sourceSystem,step);
	}

    @RequestMapping("/strategyInvoke")
    public Object getStrategyInvoke(@RequestParam(required = false) String userKey ,
											  @RequestParam(required = false) String eventCode,
											  @RequestParam(required = false) String sourceSystem,
											  @RequestParam(required = false) String loanKey,
											  @RequestParam(required = false) String step,
											  @RequestParam(required = false) Integer limit,
											  @RequestParam(required = false) String createTimeStart,
											  @RequestParam(required = false) String createTimeEnd,
											  @RequestParam(required = false) boolean batchEvent){
        List<String> loanKeyList = new ArrayList<>();
        if(StringUtils.isNotBlank(loanKey)){
            String[] loanKeys = loanKey.split(",");
            loanKeyList.addAll(Arrays.asList(loanKeys));
        }
		return strategyResultService.getMergedStrategyResustList(loanKeyList, userKey, eventCode, sourceSystem, step,
				limit, createTimeStart, createTimeEnd,batchEvent);
    }

	@RequestMapping("/strategyInvoke/hbase")
	public Object query(@RequestParam String rowKey,@RequestParam(required = false) boolean batchEvent) {
		return strategyResultService.getStrategyResult(rowKey,batchEvent);
	}

	@RequestMapping("/strategyInvoke/es")
	public Object query(@RequestParam(required = false) String loanKey,
						@RequestParam(required = false) String userKey,
						@RequestParam(required = false) String eventCode,
						@RequestParam(required = false) String sourceSystem,
						@RequestParam(required = false) String step) {
		return strategyResultService.getStrategyResultByES(loanKey,userKey,eventCode,sourceSystem,step);
	}





//	@RequestMapping("eventVo/last/userKey")
//	public String getLastEventByUserKey(@RequestParam(required = false) String userKey, @RequestParam(required = false) String eventCode,
//                                        @RequestParam(required = false) String sourceSystem,
//                                        @RequestParam(required = false) String loanKey,
//										@RequestParam(required = false) Integer limit){
//		List<String> loanKeyList = new ArrayList<>();
//		if(StringUtils.isNotBlank(loanKey)){
//			String[] loanKeys = loanKey.split(",");
//			loanKeyList.addAll(Arrays.asList(loanKeys));
//		}
//        List<EventVo> eventVoList = eventService.getLastEventByUserKey(userKey,eventCode,sourceSystem,loanKeyList,limit);
//		if(CollectionUtils.isNotEmpty(eventVoList)){
//			return JSONObject.toJSONString(eventVoList);
//		}else{
//			return "";
//			}
//	}




//	@RequestMapping("eventVo/last/userKey")
//	public String getLastEventByUserKey(@RequestParam(required = false) String userKey, @RequestParam(required = false) String eventCode,
//                                        @RequestParam(required = false) String sourceSystem,
//                                        @RequestParam(required = false) String loanKey,
//										@RequestParam(required = false) Integer limit){
//		List<String> loanKeyList = new ArrayList<>();
//		if(StringUtils.isNotBlank(loanKey)){
//			String[] loanKeys = loanKey.split(",");
//			loanKeyList.addAll(Arrays.asList(loanKeys));
//		}
//        List<EventVo> eventVoList = eventService.getLastEventByUserKey(userKey,eventCode,sourceSystem,loanKeyList,limit);
//		if(CollectionUtils.isNotEmpty(eventVoList)){
//			return JSONObject.toJSONString(eventVoList);
//		}else{
//			return "";
//			}
//	}

	@RequestMapping("strategyinvoke/result")
	public String getStrategyResultByLoanKeyAndStep(@RequestBody Map<String,Object> params){
		Map<String,Object> response = new HashMap<>();
		String loanKey = (String)params.get("loanKey");
		String stepStr = (String)params.get("stepArray");
		if(StringUtils.isNotBlank(loanKey) && StringUtils.isNotBlank(stepStr)){
			String[] steps = stepStr.split(",");
			for (String step : steps) {
				StrategyInvokeVo strategyInvokeVo = strategyResultService.getStrategyInvokeByLoanKeyAndStep(loanKey,step);
				if(StringUtils.isNotBlank(strategyInvokeVo.getResult())){
					response.put(step,strategyInvokeVo.getResult());
				}
			}
		}
		return JSONObject.toJSONString(response);
	}

}
