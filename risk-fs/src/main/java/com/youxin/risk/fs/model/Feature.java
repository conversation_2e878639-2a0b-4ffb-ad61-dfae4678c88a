package com.youxin.risk.fs.model;
import com.youxin.risk.fs.vo.FeaturePreReqVo;

import java.util.Date;

/**
 * <AUTHOR>
 * @date   2018年05月14日下午15:46:37
 */
public class Feature {
	
	
	//自增主键
	private Long id;

	//系统
	private String sourceSystem;

	//特征名称
	private String featureName;

	//特征版本
	private String featureVer;

	//路径
	private String nodePath;

	//步骤
	private String applyStep;

	//用户邮箱
	private String developer;

	//特征组名
	private String groupName;

	//特征组版本
	private String groupVer;
	
	//特征回调ID
    private Integer callBackId;

	//代码ID
	private Long featureCodeId;
	
	//串行特征小策略代码ID
	private Long strategyCodeId;

	//创建时间
	private Date createTime;

	//更新时间
	private Date updateTime;

	//版本
	private Integer version;

	// 特征类型
	private Integer preType;
	
	// 关联的特征详情id
	private Long submitDetailId;

	private String pythonVersion;

	private FeaturePreReqVo preReqVo;

	private Integer stepLevel;

	public FeaturePreReqVo getPreReqVo() {
		return preReqVo;
	}

	public void setPreReqVo(FeaturePreReqVo preReqVo) {
		this.preReqVo = preReqVo;
	}

	public Integer getCallBackId() {
        return callBackId;
    }

    public void setCallBackId(Integer callBackId) {
        this.callBackId = callBackId;
    }

    public Long getId() {
	    return this.id;
	}

	public void setId(Long id) {
	    this.id=id;
	}

	public String getSourceSystem() {
	    return this.sourceSystem;
	}

	public void setSourceSystem(String sourceSystem) {
	    this.sourceSystem=sourceSystem;
	}

	public String getFeatureName() {
	    return this.featureName;
	}

	public void setFeatureName(String featureName) {
	    this.featureName=featureName;
	}

	public String getFeatureVer() {
	    return this.featureVer;
	}

	public void setFeatureVer(String featureVer) {
	    this.featureVer=featureVer;
	}

	public String getNodePath() {
	    return this.nodePath;
	}

	public void setNodePath(String nodePath) {
	    this.nodePath=nodePath;
	}

	public String getApplyStep() {
	    return this.applyStep;
	}

	public void setApplyStep(String applyStep) {
	    this.applyStep=applyStep;
	}

	public String getDeveloper() {
	    return this.developer;
	}

	public void setDeveloper(String developer) {
	    this.developer=developer;
	}

	public String getGroupName() {
	    return this.groupName;
	}

	public void setGroupName(String groupName) {
	    this.groupName=groupName;
	}

	public String getGroupVer() {
	    return this.groupVer;
	}

	public void setGroupVer(String groupVer) {
	    this.groupVer=groupVer;
	}

	public Long getFeatureCodeId() {
	    return this.featureCodeId;
	}

	public void setFeatureCodeId(Long featureCodeId) {
	    this.featureCodeId=featureCodeId;
	}

	public Date getCreateTime() {
	    return this.createTime;
	}

	public void setCreateTime(Date createTime) {
	    this.createTime=createTime;
	}

	public Date getUpdateTime() {
	    return this.updateTime;
	}

	public void setUpdateTime(Date updateTime) {
	    this.updateTime=updateTime;
	}

	public Integer getVersion() {
	    return this.version;
	}

	public void setVersion(Integer version) {
	    this.version=version;
	}

	public Integer getPreType() {
		return preType;
	}

	public void setPreType(Integer preType) {
		this.preType = preType;
	}

	public Long getSubmitDetailId() {
		return submitDetailId;
	}

	public void setSubmitDetailId(Long submitDetailId) {
		this.submitDetailId = submitDetailId;
	}

	public Long getStrategyCodeId() {
		return strategyCodeId;
	}

	public void setStrategyCodeId(Long strategyCodeId) {
		this.strategyCodeId = strategyCodeId;
	}

	public String getPythonVersion() {
		return pythonVersion;
	}

	public void setPythonVersion(String pythonVersion) {
		this.pythonVersion = pythonVersion;
	}

    public Integer getStepLevel() {
        return stepLevel;
    }

    public void setStepLevel(Integer stepLevel) {
        this.stepLevel = stepLevel;
    }
}
