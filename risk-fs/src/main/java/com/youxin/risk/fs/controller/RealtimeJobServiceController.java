package com.youxin.risk.fs.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.fs.model.FeatureCode;
import com.youxin.risk.fs.model.FeaturePreRealtimeJob;
import com.youxin.risk.fs.model.FeaturePreRealtimeJobDataDepend;
import com.youxin.risk.fs.model.FeaturePreResult;
import com.youxin.risk.fs.service.FeaturePreRealtimJobService;
import com.youxin.risk.fs.vo.PreDataSubmitVo;
import com.youxin.risk.fs.web.JsonResultVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

@RestController
@RequestMapping("/realtime")
public class RealtimeJobServiceController {

	@Autowired
	private FeaturePreRealtimJobService featurePreRealtimJobService;

	private static final Logger logger = LoggerFactory.getLogger(RealtimeJobServiceController.class);



	@RequestMapping("/submit")
	public JsonResultVo submit(@RequestBody JSONObject params){
		logger.info("submit...data={}", JSON.toJSONString(params));
		FeaturePreRealtimeJob featurePreRealtimeJob = new FeaturePreRealtimeJob();
		featurePreRealtimeJob.setCpMainId(params.getLong("jobId"));
		featurePreRealtimeJob.setCpDetailId(params.getLong("detailId"));
		featurePreRealtimeJob.setVersion(params.getInteger("version"));
		featurePreRealtimeJob.setJobName(params.getString("jobName"));
		if(featurePreRealtimeJob.getCpDetailId()==null
				||featurePreRealtimeJob.getCpMainId()==null
				||featurePreRealtimeJob.getJobName()==null
				||featurePreRealtimeJob.getVersion()==null){
			String errMsg="jobId,detailId,version,jobName can not be null";
			logger.error(errMsg);
			return JsonResultVo.error(JsonResultVo.ERROR,errMsg);
		}
		FeatureCode featureCode = new FeatureCode();
		featureCode.setFeatureCode(params.getString("code"));
		featureCode.setVersion(featurePreRealtimeJob.getVersion().longValue());
		if(featureCode.getFeatureCode()==null){
			String errMsg="code can not be null";
			logger.error(errMsg);
			return JsonResultVo.error(JsonResultVo.ERROR,errMsg);
		}
		JSONArray dependArray = params.getJSONArray("dataDepends");
		if(dependArray==null || dependArray.size()==0){
			String errMsg="dataDepends can not be null or 0 size";
			logger.error(errMsg);
			return JsonResultVo.error(JsonResultVo.ERROR,errMsg);
		}
		List<FeaturePreRealtimeJobDataDepend> dependList =  new ArrayList();
		for (int i = 0; i < dependArray.size(); i++) {
			FeaturePreRealtimeJobDataDepend depend = new FeaturePreRealtimeJobDataDepend();
			JSONObject dependJson = dependArray.getJSONObject(i);
			depend.setDataCode(dependJson.getString("dataCode"));
			depend.setDataJoinPath(dependJson.getString("joinPath"));
			if(depend.getDataCode()==null || depend.getDataJoinPath()==null){
				String errMsg="dataDepend dataCode or joinPath can not be null";
				logger.error(errMsg);
				return JsonResultVo.error(JsonResultVo.ERROR,errMsg);
			}
			dependList.add(depend);
		}
		String result = featurePreRealtimJobService.submit(featurePreRealtimeJob,featureCode,dependList);
		if(result==null){
			return JsonResultVo.success();
		}else{
			logger.error(result);
			return JsonResultVo.error(JsonResultVo.ERROR,result);
		}
	}

	@RequestMapping("/cancel")
	public JsonResultVo cancel(@RequestBody JSONObject params){
		logger.info("cancel...data={}", JSON.toJSONString(params));
		Long cpDetailId = params.getLong("detailId");
		if(cpDetailId==null){
			String errMsg="detailId can not be null";
			logger.error(errMsg);
			return JsonResultVo.error(JsonResultVo.ERROR,errMsg);
		}
		String result = featurePreRealtimJobService.cancel(cpDetailId);
		if(result==null){
			return JsonResultVo.success();
		}else{
			logger.error(result);
			return JsonResultVo.error(JsonResultVo.ERROR,result);
		}
	}

	@RequestMapping("/start")
	public JsonResultVo start(@RequestBody JSONObject params){
		logger.info("start...data={}", JSON.toJSONString(params));
		Long cpDetailId = params.getLong("detailId");
		if(cpDetailId==null){
			String errMsg="detailId can not be null";
			logger.error(errMsg);
			return JsonResultVo.error(JsonResultVo.ERROR,errMsg);
		}
		String result = featurePreRealtimJobService.start(cpDetailId);
		if(result==null){
			return JsonResultVo.success();
		}else{
			logger.error(result);
			return JsonResultVo.error(JsonResultVo.ERROR,result);
		}
	}

	@RequestMapping("/stop")
	public JsonResultVo stop(@RequestBody JSONObject params){
		logger.info("stop...data={}", JSON.toJSONString(params));
		Long cpDetailId = params.getLong("detailId");
		if(cpDetailId==null){
			String errMsg="detailId can not be null";
			logger.error(errMsg);
			return JsonResultVo.error(JsonResultVo.ERROR,errMsg);
		}
		String result = featurePreRealtimJobService.stop(cpDetailId);
		if(result==null){
			return JsonResultVo.success();
		}else{
			logger.error(result);
			return JsonResultVo.error(JsonResultVo.ERROR,result);
		}

	}

	@RequestMapping("/data")
	public JsonResultVo data(@RequestBody JSONObject params){
		logger.info("processing realtime data={}", JSON.toJSONString(params));
		PreDataSubmitVo preDataSubmitVo = new PreDataSubmitVo();
		preDataSubmitVo.setDataCode(params.getString("dataCode"));
		preDataSubmitVo.setEventTime(params.getDate("eventTime"));
		preDataSubmitVo.setData(params.getString("data"));
		preDataSubmitVo.setUserKey(params.getString("key"));
		preDataSubmitVo.setCreateTime(params.getDate("createTime"));
		if(preDataSubmitVo.getData()==null
				||preDataSubmitVo.getEventTime()==null
				||preDataSubmitVo.getUserKey()==null
				||preDataSubmitVo.getDataCode()==null){
			String errMsg="data,eventTime,dataCode,key can not be null";
			logger.error(errMsg);
			return JsonResultVo.error(JsonResultVo.ERROR,errMsg);
		}
		List<FeaturePreResult> preResults = featurePreRealtimJobService.data(preDataSubmitVo);
		if(preResults != null){
			return JsonResultVo.success();
		}else{
			return JsonResultVo.error(JsonResultVo.ERROR,"调用Python计算realTimeFeature失败");
		}
	}


}
