package com.youxin.risk.fs.service;

import com.youxin.risk.fs.vo.DataSubmitVo;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DataSubmitService {

    /**
     * 将DataSubmitVo 保存至hbase
     * @param dataSubmitVo  DataSubmitVo 数据对象
     */
    void saveDataSubmitToHbase(DataSubmitVo dataSubmitVo);

    /**
     * 将DataSubmitVo 保存至ES
     */
    void saveDataSubmitToES(DataSubmitVo dataSubmitVo);

    /**
     * 根据rowkey 查询DataSubmitVo 记录
     * @param rowKey rowKey
     * @return DataSubmitVo
     */
    Object getDataSubmit(String rowKey,boolean batchEvent);


    /**
     * 查询DataSubmitVo 在ES中的 记录
     * @return DataSubmitVo
     */
    Object getDataSubmitByES(String loanKey,String userKey,String eventCode,String sourceSystem,String step);

    Object getMergedDataSubmitList(String userKey, String eventCode, String sourceSystem, List<String> loanKeyList, String step, Integer limit, String createTimeStart, String createTimeEnd,boolean batchEvent);
}
