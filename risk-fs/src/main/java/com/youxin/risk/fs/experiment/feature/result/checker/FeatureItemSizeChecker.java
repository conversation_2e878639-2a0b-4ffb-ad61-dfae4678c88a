package com.youxin.risk.fs.experiment.feature.result.checker;

import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.commons.vo.FeatureExperimentResultVo;
import com.youxin.risk.fs.vo.FeatureCalcDataVo;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 特征项输出大小
 *
 * <AUTHOR>
 */
@Slf4j
public class FeatureItemSizeChecker extends BaseFeatureCheckPoint {
    private static final Long DEFAULT_ITEM_SIZE = 1024L;

    @Override
    public String check(FeatureCalcDataVo featureCalcDataVo) {
        Long configMaxItemSize = getMaxConfigItemSize();
        try {
            String featureName = featureCalcDataVo.getFeatureName();
            String featureResultStr = String.format("<%s>%s</%s>", featureName, featureCalcDataVo.getValue(), featureName);
            List<Element> elements = DocumentHelper.parseText(featureResultStr).getRootElement().elements();
            return elements.stream()
                    .filter(element -> element.elements().size() == 0 &&
                            element.getData().toString().getBytes().length > configMaxItemSize)
                    .map(Element::getName)
                    .collect(Collectors.joining(","));
        } catch (DocumentException e) {
            log.error("featureItemSizeCheckError", e);
        }
        return null;
    }

    @Override
    public void buildExperimentResult(FeatureExperimentResultVo result, String errorPoints) {
        if (result.getNotPassReason() != null) {
            Long configItemSize = getMaxConfigItemSize();
            result.setNotPassReason(result.getNotPassReason() + JOINER + "特征项输出值超过" + configItemSize + " bytes");
        } else {
            result.setNotPassReason(errorPoints);
        }
    }

    private Long getMaxConfigItemSize() {
        try {
            return ApolloClientAdapter.getLongConfig(ApolloNamespaceEnum.FS_SPACE, "featureItemLimitLength", 1024L);
        } catch (Exception e) {
            // do nothing
            return DEFAULT_ITEM_SIZE;
        }
    }
}
