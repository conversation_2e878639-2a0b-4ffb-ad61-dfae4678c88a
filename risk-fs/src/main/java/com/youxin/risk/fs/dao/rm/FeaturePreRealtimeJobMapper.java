package com.youxin.risk.fs.dao.rm;

import com.youxin.risk.fs.model.FeaturePreRealtimeJob;
import com.youxin.risk.fs.model.FeaturePreRealtimeJobData;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/3
 */
public interface FeaturePreRealtimeJobMapper {
    int insert(FeaturePreRealtimeJob featurePreRealtimeJob);

    int delete(Long cpDetailId);

    Boolean isExist(Long cpDetailId);

    int updateStatus(@Param("cpDetailId") Long cpDetailId, @Param("status") Integer status);

    List<FeaturePreRealtimeJob> getAll();

    FeaturePreRealtimeJob getByCpDetailId(Long cpDetailId);

    FeaturePreRealtimeJobData getFeaturePreRealtimeJobDataMapByCpMainId(Long cpMainId);

    List<FeaturePreRealtimeJobData> getByDataCode(String dataCode);


}
