package com.youxin.risk.fs.controller;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.fs.service.ModelService;
import com.youxin.risk.fs.vo.ModelExecuteReq;
import com.youxin.risk.fs.web.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 模型控制层
 */
@RestController
@RequestMapping("/model")
public class ModelController {

    @Autowired
    private ModelService modelService;

    /**
     * 执行模型接口
     *
     * @param req
     * @return
     */
    @RequestMapping(value = "/{modelTag}", method = RequestMethod.POST)
    public JsonResultVo executeModel(@PathVariable("modelTag") String modelTag, @Valid @RequestBody ModelExecuteReq req) {
        JSONObject jsonObject = modelService.executeModel(modelTag, req);
        JsonResultVo resultVo = JsonResultVo.success("success");
        resultVo.setData(jsonObject);
        return resultVo;
    }

}
