package com.youxin.risk.fs.mongo.dao;

import com.mongodb.WriteResult;
import com.youxin.risk.commons.mongo.BaseMongoDao;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.fs.vo.DataSubmitVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2018/12/28 13:58
 */
@Repository
public class DataSubmitMongoDao extends BaseMongoDao {

    private static final Logger logger = LoggerFactory.getLogger(DataSubmitMongoDao.class);

    private String collection = "DataSubmit";

//    @Autowired
//    @Qualifier("shardingMongoTemplate")
//    private MongoTemplate shardingTemplate;


    public DataSubmitMongoDao() {
        collectionName = collection;
    }


    public void insert(Object obj){
        if (obj ==null) {
            LoggerProxy.error("insert",logger,"insert DataSubmit into  mongo,but obj is null");
            return ;
        }
        if(obj instanceof  DataSubmitVo){
            DataSubmitVo vo = (DataSubmitVo)obj;
            if(StringUtils.isBlank(vo.getUserKey())){
                vo.setUserKey("default-userKey-"+ UUID.randomUUID().toString());
                LoggerProxy.error("insert",logger,"insert DataSubmit into sharding mongo,but the sharding is null,default userKey:{}",vo.getUserKey());
            }
            LoggerProxy.info("insert",logger,"insert DataSubmit into sharding mongo");
//            this.insert(vo,shardingTemplate);
        }else {
            LoggerProxy.warn("insert",logger,"insert DataSubmit into mongo");
            template.insert(obj);
        }
    }


    public List<DataSubmitVo> getByUserKey(String userKey){
        Query query = new Query();
        Criteria criteria = Criteria.where("userKey").is(userKey);
        query.addCriteria(criteria);
        query.with(new Sort(new Sort.Order(Sort.Direction.ASC,"createTime")));
        query.limit(10);
//        return shardingTemplate.find(query,DataSubmitVo.class);
        return null;
    }


    public List<DataSubmitVo> findDataSubmitByCondition(String userKey,String eventCode,String sourceSystem,List<String> loanKeyList,String step,Integer limit){
        Query query = new Query();
        Criteria criteria = null;
        criteria = new Criteria();
        if(CollectionUtils.isNotEmpty(loanKeyList)){
            criteria.and("loanKey").in(loanKeyList);
        }
        if(StringUtils.isNotBlank(userKey)){
            criteria.and("userKey").is(userKey);
        }
        if(StringUtils.isNotBlank(eventCode)){
            criteria.and("eventCode").is(eventCode);
        }
        if(StringUtils.isNotBlank(step)){
            criteria.and("step").is(step);
        }
        if(StringUtils.isNotBlank(sourceSystem)){
            criteria.and("sourceSystem").is(sourceSystem);
        }
        query.addCriteria(criteria);
        query.with(new Sort(new Sort.Order(Sort.Direction.DESC,"createTime")));
        query.limit(limit);
//        return shardingTemplate.find(query,DataSubmitVo.class);
        return null;
    }

    //@Retryable(value = {Exception.class},exclude = {BsonSerializationException.class}, maxAttempts = MAX_ATTEMPTS,  backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    private void insert(Object vo,MongoTemplate template){
        template.insert(vo);
    }

    public WriteResult deleteByTime(Date date,int limitNum){
        Query query = new Query();
        Criteria criteria = Criteria.where("createTime").lt(date);
        query.addCriteria(criteria);
        query.limit(limitNum);
//        LoggerProxy.info("deleteByTime",logger,"deleteByTime DataSubmit from mongo,date={}",date);
//        return this.shardingTemplate.remove(query,collectionName);

        WriteResult ret = null;
        try {
            this.template.remove(query,collectionName);
//            ret = shardingTemplate.remove(query,collectionName);
        }catch (Exception ex){
            LoggerProxy.error("deleteByTime",logger,"deleteByTime DataSubmit error,date={}",date,ex);
        }
        return ret;
    }

    /**
     * 用于测试，不提供服务
     * @param userKey
     * @param loanKey
     * @param limit
     * @return
     */
    public List<DataSubmitVo> getDataSubmitVoList(String userKey , String loanKey,Integer limit){
        Query query = new Query();
        Criteria criteria = new Criteria();
        if(StringUtils.isNotBlank(userKey)){
            criteria.and("userKey").is(userKey);
        }else{
            criteria.and("loanKey").is(loanKey);
        }
        query.addCriteria(criteria);
        query.limit(limit);
//        return  shardingTemplate.find(query,DataSubmitVo.class, collectionName);
        return null;
    }



    public Date queryMinCreateTime(){
        Query query = new Query();
        query.with(new Sort(new Sort.Order(Sort.Direction.ASC,"createTime")));
        query.limit(1);

//        List<DataSubmitVo> results = template.find(query, DataSubmitVo.class);
//        return results.get(0).getCreateTime();

//        DataSubmitVo nvo = template.findOne(query, DataSubmitVo.class);
//        try {
//            nvo=shardingTemplate.findOne(query, DataSubmitVo.class);
//        }catch (Exception ex){
//            LoggerProxy.error("queryMinCreateTime",logger,"queryMinCreateTime DataSubmit from sharding mongo error",ex);
//        }
//        LoggerProxy.info("queryMinCreateTime",logger,"queryMinCreateTime DataSubmit from mongo");
//        DataSubmitVo ovo =template.findOne(query, DataSubmitVo.class);
//        if (nvo==null || nvo.getCreateTime() == null) {
//            return (ovo!=null && ovo.getCreateTime()!=null ) ? ovo.getCreateTime():null;
//        }
//        return nvo.getCreateTime().after(ovo.getCreateTime()) ?ovo.getCreateTime():nvo.getCreateTime();


//        LoggerProxy.info("queryMinCreateTime",logger,"queryMinCreateTime DataSubmit from sharding mongo");
//        DataSubmitVo nvo = shardingTemplate.findOne(query, DataSubmitVo.class);
//        return nvo.getCreateTime();

        List<DataSubmitVo> results = template.find(query, DataSubmitVo.class);
        if(CollectionUtils.isEmpty(results)){
            return null;
        }
        return results.get(0).getCreateTime();

    }

}
