package com.youxin.risk.fs.controller;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.fs.es.IndexService;
import com.youxin.risk.fs.hbase.utils.HbaseUtil;
import com.youxin.risk.fs.model.ESDataSubmit;
import com.youxin.risk.fs.model.ESStrategyResult;
import com.youxin.risk.fs.model.EsDataSubmitVo;
import com.youxin.risk.fs.model.EsStrategyResultVo;
import com.youxin.risk.fs.task.RollingEsIndexTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import static com.youxin.risk.fs.constants.IndexConstants.ES_DATA_SUBMIT;
import static com.youxin.risk.fs.constants.IndexConstants.ES_STRATEGY_RESULT;

//import com.youxin.risk.fs.es.TestIndexRepository;

/**
 * @ClassName IndexElasticsearchController
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/12/14 9:43 下午
 **/
@RestController
@RequestMapping("/index")
public class IndexElasticsearchController {

    @Autowired
    private IndexService indexService;
    @Autowired
    RollingEsIndexTask  rollingEsIndexTask;
    @RequestMapping("/isExist")
    public JSONObject isExist(){
        rollingEsIndexTask.execJobHandler("");
        return new JSONObject();
    }

    @RequestMapping("/createIndex")
    public JSONObject createIndex(){
        indexService.createIndex(ES_DATA_SUBMIT);
        indexService.createIndex(ES_STRATEGY_RESULT);
        return new JSONObject();
    }

    @RequestMapping("/deleteIndex")
    public JSONObject deleteIndex(){
        indexService.deleteIndex(ES_DATA_SUBMIT);
        indexService.deleteIndex(ES_STRATEGY_RESULT);
        return new JSONObject();
    }

    @RequestMapping("/insert")
    public JSONObject insert(){
        ESDataSubmit esDataSubmit = new ESDataSubmit();
        esDataSubmit.setId(HbaseUtil.getRowKey());
        esDataSubmit.setSourceSystem("HAO_HUAN");
        esDataSubmit.setUserKey("userkey1");
        esDataSubmit.setLoanKey("loankey2");
        esDataSubmit.setStep("A");
        esDataSubmit.setEventCode("HAO_HUAN");
        Date date = new Date();
        String strDateFormat = "yyyy-MM-dd HH:mm:ss";
        SimpleDateFormat sdf = new SimpleDateFormat(strDateFormat);
        esDataSubmit.setSearchCreateTime(date.getTime());
        esDataSubmit.setCreateTime(sdf.format(date));
        esDataSubmit.setData("aa");
        esDataSubmit.setEngineEventCreateTime(sdf.format(date));
        esDataSubmit.setSearchEngineEventCreateTime(date.getTime());
        try {
            indexService.addDataSubmit(esDataSubmit,esDataSubmit.getId());
        } catch (Exception e) {
            e.printStackTrace();
        }

//        ESStrategyResult esStrategyResult = new ESStrategyResult();
//        esStrategyResult.setId("3");
//        esStrategyResult.setSourceSystem("HAO_HUAN");
//        esStrategyResult.setUserKey("userkey1");
//        esStrategyResult.setLoanKey("loankey2");
//        esStrategyResult.setStep("A");
//        esStrategyResult.setEventCode("HAO_HUAN");
//        esStrategyResult.setSessionId("sessionId");
//        esStrategyResult.setStrategyCodeId(1000L);
//        esStrategyResult.setXml("xml");
//        esStrategyResult.setResult("result");
//        Date date = new Date();
//        String strDateFormat = "yyyy-MM-dd HH:mm:ss";
//        SimpleDateFormat sdf = new SimpleDateFormat(strDateFormat);
//        String format = sdf.format(date);
//        esStrategyResult.setCreateTime(format);
//        esStrategyResult.setSearchCreateTime(date.getTime());
//        try {
//            indexService.addStrategyResult(esStrategyResult,"3");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        return new JSONObject();
    }

    @RequestMapping("/insertStrategy")
    public JSONObject insertStrategy(){
        ESStrategyResult esStrategyResult = new ESStrategyResult();
       esStrategyResult.setId(HbaseUtil.getRowKey());
       esStrategyResult.setSourceSystem("HAO_HUAN");
        esStrategyResult.setUserKey("userkey1");
       esStrategyResult.setLoanKey("loankey2");
        esStrategyResult.setStep("A");
       esStrategyResult.setEventCode("HAO_HUAN");
       esStrategyResult.setSessionId("sessionId");
       esStrategyResult.setStrategyCodeId(1000L);
       esStrategyResult.setXml("xml");
        esStrategyResult.setResult("result");
       Date date = new Date();
        String strDateFormat = "yyyy-MM-dd HH:mm:ss";
       SimpleDateFormat sdf = new SimpleDateFormat(strDateFormat);
        String format = sdf.format(date);
       esStrategyResult.setCreateTime(format);
       esStrategyResult.setSearchCreateTime(date.getTime());
       esStrategyResult.setType("preloan");
       try {
          indexService.addStrategyResult(esStrategyResult,esStrategyResult.getId());
       } catch (Exception e) {
           e.printStackTrace();
        }
        return new JSONObject();
    }

    @RequestMapping("/search")
    public JSONObject search(){
        List<EsDataSubmitVo> dataSubmitVos = null;
        List<EsStrategyResultVo> strategyVos = null;
        try {
            dataSubmitVos = indexService.searchDataSubmit(null,null, null, null, null, 2, "2021-12-16 16:34:00", "2021-12-16 16:35:23");
            strategyVos = indexService.searchStrategy(null, null, null, null, null, 2, null, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data1",dataSubmitVos);
        jsonObject.put("data2",strategyVos);
        return jsonObject;
    }
}
