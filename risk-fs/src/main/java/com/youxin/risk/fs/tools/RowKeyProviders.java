package com.youxin.risk.fs.tools;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.fs.dao.rm.RmStrategyResultIndexMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class RowKeyProviders {
    @Autowired
    private RmStrategyResultIndexMapper rmStrategyResultIndexMapper;


    public RowKeyProvider getRowKeyProvider(JSONObject data) {
        String dataType = data.getString("dataType");
        if (dataType == null) {
            log.error("parse dataType is null, data={}", data);
            return null;
        }
        if ("loanKey".equals(dataType)) {
            List<String> data1 = JSON.parseArray(data.getString("data"), String.class);
            return new RowKeyProviderByLoanKey(new HashSet<>(data1));
        }
        return null;
    }


    public interface RowKeyProvider extends Supplier<List<String>> {

    }

    private class RowKeyProviderByLoanKey implements RowKeyProvider {
        private final Set<String> loanKeys;

        public RowKeyProviderByLoanKey(Set<String> loanKeys) {
            this.loanKeys = Optional.ofNullable(loanKeys).orElse(Collections.emptySet());
        }

        @Override
        public List<String> get() {
            List<String> result = new ArrayList<>();
            for (String loanKey : loanKeys) {
                List<String> rowKeysByConditions = rmStrategyResultIndexMapper.getRowKeysByConditionsNew(loanKey, null, null, null, null, 200, null, null);
                log.info("query rowKeys, loanKey={}, result={}", loanKey, String.join(",", rowKeysByConditions));
                result.addAll(rowKeysByConditions);
            }
            return result;
        }
    }
}
