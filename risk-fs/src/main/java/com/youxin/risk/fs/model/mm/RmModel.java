package com.youxin.risk.fs.model.mm;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * rm_model
 * <AUTHOR>
@Data
public class RmModel implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 系统
     */
    private String sourceSystem;

    /**
     * 模型标识
     */
    private String modelTag;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 模型状态,草稿:init,启用enable,禁用disable
     */
    private String modelStatus;

    /**
     * 模型文件标识,0:无,1:有
     */
    private Integer hasFile;

    /**
     * 负责人
     */
    private String owner;

    /**
     * 创建者
     */
    private String developer;

    /**
     * 备注
     */
    private String remark;

    /**
     * 模型服务url
     */
    private String modelUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 删除标识
     */
    private Integer delFlag;

    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        RmModel other = (RmModel) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSourceSystem() == null ? other.getSourceSystem() == null : this.getSourceSystem().equals(other.getSourceSystem()))
            && (this.getModelTag() == null ? other.getModelTag() == null : this.getModelTag().equals(other.getModelTag()))
            && (this.getModelName() == null ? other.getModelName() == null : this.getModelName().equals(other.getModelName()))
            && (this.getModelStatus() == null ? other.getModelStatus() == null : this.getModelStatus().equals(other.getModelStatus()))
            && (this.getHasFile() == null ? other.getHasFile() == null : this.getHasFile().equals(other.getHasFile()))
            && (this.getOwner() == null ? other.getOwner() == null : this.getOwner().equals(other.getOwner()))
            && (this.getDeveloper() == null ? other.getDeveloper() == null : this.getDeveloper().equals(other.getDeveloper()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getModelUrl() == null ? other.getModelUrl() == null : this.getModelUrl().equals(other.getModelUrl()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getDelFlag() == null ? other.getDelFlag() == null : this.getDelFlag().equals(other.getDelFlag()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSourceSystem() == null) ? 0 : getSourceSystem().hashCode());
        result = prime * result + ((getModelTag() == null) ? 0 : getModelTag().hashCode());
        result = prime * result + ((getModelName() == null) ? 0 : getModelName().hashCode());
        result = prime * result + ((getModelStatus() == null) ? 0 : getModelStatus().hashCode());
        result = prime * result + ((getHasFile() == null) ? 0 : getHasFile().hashCode());
        result = prime * result + ((getOwner() == null) ? 0 : getOwner().hashCode());
        result = prime * result + ((getDeveloper() == null) ? 0 : getDeveloper().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getModelUrl() == null) ? 0 : getModelUrl().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getDelFlag() == null) ? 0 : getDelFlag().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", sourceSystem=").append(sourceSystem);
        sb.append(", modelTag=").append(modelTag);
        sb.append(", modelName=").append(modelName);
        sb.append(", modelStatus=").append(modelStatus);
        sb.append(", hasFile=").append(hasFile);
        sb.append(", owner=").append(owner);
        sb.append(", developer=").append(developer);
        sb.append(", remark=").append(remark);
        sb.append(", modelUrl=").append(modelUrl);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", delFlag=").append(delFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}