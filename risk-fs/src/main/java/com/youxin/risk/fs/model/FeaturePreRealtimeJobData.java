/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.fs.model;


import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-12-03
 */
public class FeaturePreRealtimeJobData {


    private Long id;
    private Long cpMainId;
    private Long cpDetailId;
    private Integer version;
    private Long featureCodeId;
    private String jobName;
    private String dataJoinPath;
    private String dataCode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCpDetailId() {
        return cpDetailId;
    }

    public void setCpDetailId(Long cpDetailId) {
        this.cpDetailId = cpDetailId;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getFeatureCodeId() {
        return featureCodeId;
    }

    public void setFeatureCodeId(Long featureCodeId) {
        this.featureCodeId = featureCodeId;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getDataJoinPath() {
        return dataJoinPath;
    }

    public void setDataJoinPath(String dataJoinPath) {
        this.dataJoinPath = dataJoinPath;
    }

    public Long getCpMainId() {
        return cpMainId;
    }

    public void setCpMainId(Long cpMainId) {
        this.cpMainId = cpMainId;
    }

    public String getDataCode() {
        return dataCode;
    }

    public void setDataCode(String dataCode) {
        this.dataCode = dataCode;
    }
}