package com.youxin.risk.fs.service.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class LineDataHandler extends BaseFillDataSubmitHandler{
    private final static Logger LOG = LoggerFactory.getLogger(LineDataHandler.class);

    /**
     * {
     *     "line_data":[
     *         {
     *             "apply_rate":0.0179,
     *             "loan_line":2800,
     *             "create_time":"2021-04-16 16:02:40",
     *             "loan_period":9,
     *             "loan_actual_line":2800,
     *             "type":1,
     *             "apply_period":6,
     *             "loan_rate":0.0179,
     *             "borrow_time":1618560093,
     *             "is_passed":1,
     *             "real_amount":2800,
     *             "credit_line":4000,
     *             "loan_period_num":6,
     *             "loan_id":58208177,
     *             "apply_amount":2800,
     *             "shop_actual_line":4000
     *         }
     *     ]
     * }
     * @param data
     * @return
     */
    @Override
    public String thirdPartVulcanData(String data){
        if (!StringUtils.isEmpty(data)){
            JSONObject json = JSON.parseObject(data);
            JSONArray lineData = json.getJSONArray("line_data");
            /** 移除 **/
            for (int i= 0 ; i< lineData.size() ;i++){
                JSONObject jsonObject = lineData.getJSONObject(i);
                jsonObject.remove("loan_actual_line");
                jsonObject.remove("shop_actual_line");
                jsonObject.remove("loan_period");
                jsonObject.remove("loan_line");
                jsonObject.remove("credit_line");
            }
            return JSON.toJSONString(json);
        }
        return data;
    }
}
