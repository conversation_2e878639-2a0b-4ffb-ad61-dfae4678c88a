package com.youxin.risk.fs.dao.rm;

import com.youxin.risk.fs.model.FeatureCandidate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018年05月25日下午13:26:27
 */
public interface FeatureCandidateMapper {

    int insert(FeatureCandidate model);

    int update(FeatureCandidate model);

    /**
     * 按步骤获取计算特征
     *
     */
    List<FeatureCandidate> getFeatureByStep(@Param("sourceSystem") String sourceSystem, @Param("step") String step);

    int updateStatus(FeatureCandidate model);

    int deleteById(Long id);

    int deleteByFeatureName(@Param("sourceSystem") String sourceSystem, @Param("featureName") String featureName);

    Integer countUnFinished(Integer callBackId);

    List<FeatureCandidate> getFeatureByCallbackId(Integer callBackId);

}
