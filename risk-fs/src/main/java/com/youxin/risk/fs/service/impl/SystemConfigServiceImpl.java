package com.youxin.risk.fs.service.impl;

import com.youxin.risk.fs.dao.rm.SystemConfigMapper;
import com.youxin.risk.fs.model.SystemConfig;
import com.youxin.risk.fs.service.SystemConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2018年05月31日上午10:32:30
 */
@Service
public class SystemConfigServiceImpl implements SystemConfigService {

    private final static Logger LOG = LoggerFactory.getLogger(SystemConfigService.class);


    private static final String KEY_FEATURE_TEST_COUNT = "FEATURE_TEST_COUNT";

    @Autowired
    private SystemConfigMapper systemConfigMapper;

    @Override
    public Integer getFeatureTestCount() {

        SystemConfig config = this.systemConfigMapper.selectByKey(KEY_FEATURE_TEST_COUNT);
        return Integer.valueOf(config.getValue());
    }

}
