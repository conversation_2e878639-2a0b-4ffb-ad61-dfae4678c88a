package com.youxin.risk.fs.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.cache.CacheApi;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.constants.SourceSystemEnum;
import com.youxin.risk.commons.exception.RiskRuntimeException;
import com.youxin.risk.commons.model.DiService;
import com.youxin.risk.commons.remote.model.datacenter.DcRequest;
import com.youxin.risk.commons.service.dp.BabelDataService;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.fs.vo.DcResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class BackerService {
    private static Logger LOG = LoggerFactory.getLogger(BackerService.class);
    public static final String ASYNC_SMS_REPORT_SERVICE = "asyncSmsReportService";
    private final static String DC_SERVICE_CODE = "SMS_REPORT_LIST";

    @Resource
    private DatacenterSystemService dataCenterSystemService;

    @Resource
    protected DiSystemService diSystemService;

    @Autowired
    private BabelDataService babelDataService;


    public String backupSmsIfNeed(String userKey, String data) {
        if (hasRecord(data)) {
            return data;
        }

        DiService diService = CacheApi.getDiService(BackerService.ASYNC_SMS_REPORT_SERVICE);
        JSONArray smsReportList = smsReportList(userKey);
        for (Object o : smsReportList) {
            JSONObject smsReport = (JSONObject) o;
            String jobId = smsReport.getString("jobId");
            data = babelDataService.getDataFromDpByJobId(SourceSystemEnum.HAO_HUAN.name(), diService.getTaskType(), jobId);
            if (hasRecord(data)) {
                return data;
            }
        }

        return  data;
    }


    private JSONArray smsReportList(String userKey) {
        DcRequest dcRequest = dataCenterSystemService.buildDcRequest(userKey, BackerService.DC_SERVICE_CODE);
        DcResult dcResult = dataCenterSystemService.callDc(dcRequest);
        if (!RetCodeEnum.SUCCESS.equals(dcResult.getRetCode())) {
            LoggerProxy.error("payOff", LOG, "fail to payOff SMS_REPORT_LIST, userKey={}", userKey);
            throw new RiskRuntimeException("invoke dc SMS_REPORT_LIST error");
        }

        JSONArray array = JSON.parseArray((String) dcResult.getDataMap().get(BackerService.DC_SERVICE_CODE));
        if (array == null) {
            array = new JSONArray();
        }
        return array;
    }

    private boolean hasRecord(String data) {
        if (com.youxin.risk.commons.utils.StringUtils.isBlank(data)) {
            return false;
        }

        JSONObject record = JSON.parseObject(data);
        if (record != null) {
            return true;
        }
        return false;
    }

}
