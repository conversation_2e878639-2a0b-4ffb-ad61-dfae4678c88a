<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:mongo="http://www.springframework.org/schema/data/mongo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
	        			http://www.springframework.org/schema/data/mongo http://www.springframework.org/schema/data/mongo/spring-mongo-1.10.xsd">

    <!-- 去除集合里的_class属性 -->
    <bean id="mappingContext" class="org.springframework.data.mongodb.core.mapping.MongoMappingContext"/>
    <bean id="defaultMongoTypeMapper"
          class="org.springframework.data.mongodb.core.convert.DefaultMongoTypeMapper">
        <constructor-arg name="typeKey">
            <null/>
        </constructor-arg>
    </bean>
    <bean id="mappingMongoConverter"
          class="org.springframework.data.mongodb.core.convert.MappingMongoConverter">
        <constructor-arg name="mongoDbFactory" ref="mongoFactory"/>
        <constructor-arg name="mappingContext" ref="mappingContext"/>
        <property name="typeMapper" ref="defaultMongoTypeMapper"/>
    </bean>
    <mongo:mongo-client id="mongoDataSource" replica-set="${mongo.host}" credentials="${mongo.credentials}">
        <!-- #autoConnectRetry 		控制系统在发生连接错误时是否重试 -->
        <!-- #connectionsPerHost	每个主机的连接池大小 -->
        <!-- #slave-ok				是否可以在从节点读取数 -->
        <mongo:client-options connections-per-host="70" write-concern="SAFE" read-preference="PRIMARY_PREFERRED"
                              threads-allowed-to-block-for-connection-multiplier="50" heartbeat-connect-timeout="5000"
                              connect-timeout="30000" max-wait-time="1500" socket-keep-alive="true"
                              socket-timeout="10000" max-connection-idle-time="60000"/>
    </mongo:mongo-client>

    <!-- 定义MONGO ANTIFRAUD 连接工厂 -->
    <mongo:db-factory id="mongoFactory"
                      mongo-ref="mongoDataSource" dbname="${mongo.database}"/>

    <!-- 定义Template -->
    <bean id="mongoTemplate" class="org.springframework.data.mongodb.core.MongoTemplate">
        <constructor-arg name="mongoDbFactory" ref="mongoFactory"/>
        <constructor-arg name="mongoConverter" ref="mappingMongoConverter"></constructor-arg>
    </bean>
    <bean id= "RuleScoreResultMongoDao" class="com.youxin.risk.commons.mongo.RuleScoreResultMongoDao">
        <property name="template" ref="riskExpMongoTemplate"/>
    </bean>
    <bean id= "ruleScoreMirrorVarVoDao" class="com.youxin.risk.commons.mongo.RuleScoreMirrorVarVoDao">
        <property name="template" ref="riskExpMongoTemplate"/>
    </bean>

    <bean id="strategyExperimentResultMongoDao" class="com.youxin.risk.commons.mongo.StrategyExperimentResultMongoDao"/>
    <bean id="strategySplitFlowResultMongoDao" class="com.youxin.risk.commons.mongo.StrategySplitFlowResultMongoDao"/>
    <bean id="featureExperimentResultMongoDao" class="com.youxin.risk.commons.mongo.FeatureExperimentResultMongoDao"/>
    <bean id="featureSplitFlowResultMongoDao" class="com.youxin.risk.commons.mongo.FeatureSplitFlowResultMongoDao"/>
    <bean id="processFeatureExpMongoDao" class="com.youxin.risk.fs.mongo.dao.ProcessFeatureExpMongoDao"/>
    <bean id="DataSourceExperimentResultMongoDao" class="com.youxin.risk.commons.mongo.DataSourceExperimentResultMongoDao"/>

<!--    <bean id="eventDao" class="com.youxin.risk.commons.mongo.EventDao"/>-->
    <bean id="verifyResultDataDao" class="com.youxin.risk.commons.mongo.VerifyResultDataDao"/>
    <bean id="VerifyResultPuDaoDataDao" class="com.youxin.risk.commons.mongo.VerifyResultPuDaoDataDao"/>
    <!-- sharding -->
<!--    <mongo:mongo-client id="shardingMongoDataSource" replica-set="${mongo.sharding.host}"-->
<!--                        credentials="${mongo.sharding.credentials}">-->
<!--        <mongo:client-options connections-per-host="70" write-concern="W1" read-preference="SECONDARY_PREFERRED"-->
<!--                              threads-allowed-to-block-for-connection-multiplier="50" heartbeat-connect-timeout="5000"-->
<!--                              connect-timeout="30000" max-wait-time="1500" socket-keep-alive="true"-->
<!--                              max-connection-idle-time="10000"-->
<!--                              socket-timeout="10000"/>-->
<!--    </mongo:mongo-client>-->
<!--    <mongo:db-factory id="shardingMongoFactory" mongo-ref="shardingMongoDataSource"-->
<!--                      dbname="${mongo.sharding.database}"/>-->
<!--    <bean id="shardingMappingMongoConverter"-->
<!--          class="org.springframework.data.mongodb.core.convert.MappingMongoConverter">-->
<!--        <constructor-arg name="mongoDbFactory" ref="shardingMongoFactory"/>-->
<!--        <constructor-arg name="mappingContext" ref="mappingContext"/>-->
<!--        <property name="typeMapper" ref="defaultMongoTypeMapper"/>-->
<!--    </bean>-->

<!--    <bean id="shardingMongoTemplate" class="org.springframework.data.mongodb.core.MongoTemplate">-->
<!--        <constructor-arg name="mongoDbFactory" ref="shardingMongoFactory"/>-->
<!--        <property name="writeConcern" value="W1"/>-->
<!--    </bean>-->
    <!-- risk_exp -->
    <mongo:mongo-client id="riskExpMongoDataSource" replica-set="${mongo.riskExp.host}"
                        credentials="${mongo.riskExp.credentials}">
        <mongo:client-options connections-per-host="70" write-concern="W1" read-preference="PRIMARY_PREFERRED"
                              threads-allowed-to-block-for-connection-multiplier="50" heartbeat-connect-timeout="5000"
                              connect-timeout="30000" max-wait-time="1500" socket-keep-alive="true"
                              max-connection-idle-time="10000"
                              socket-timeout="30000"/>
    </mongo:mongo-client>
    <mongo:db-factory id="riskExpMongoFactory" mongo-ref="riskExpMongoDataSource"
                      dbname="${mongo.riskExp.database}"/>
    <bean id="mappingExpMongoConverter"
          class="org.springframework.data.mongodb.core.convert.MappingMongoConverter">
        <constructor-arg name="mongoDbFactory" ref="riskExpMongoFactory"/>
        <constructor-arg name="mappingContext" ref="mappingContext"/>
        <property name="typeMapper" ref="defaultMongoTypeMapper"/>
    </bean>
    <bean id="riskExpMongoTemplate" class="org.springframework.data.mongodb.core.MongoTemplate">
        <constructor-arg name="mongoDbFactory" ref="riskExpMongoFactory"/>
        <constructor-arg name="mongoConverter" ref="mappingExpMongoConverter"/>
    </bean>

</beans>
