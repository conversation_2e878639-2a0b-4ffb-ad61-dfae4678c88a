spring.profiles.active=@activeEnv@
mail.from=${mail.from}
mail.subject=${mail.subject}

system.pythonframe.baseurl=${system.pythonframe.baseurl}
system.pythonframe.offlineurl=${system.pythonframe.offlineurl}
system.pythonframe.baseurlPython3=${system.pythonframe.baseurlPython3}
system.pythonframe.fpUrlPython3=${system.pythonframe.fpUrlPython3}
system.pythonframe.baseurlBatchPython3=${system.pythonframe.baseurlBatchPython3}
system.pythonframe.baseurlFpBatchPython3=${system.pythonframe.baseurlFpBatchPython3}
system.pythonframe.baseurlAkBatchPython3=${system.pythonframe.baseurlAkBatchPython3}
system.pythonframe.baseurlExpAkBatchPython3=${system.pythonframe.baseurlExpAkBatchPython3}
system.pythonframe.baseOnlineAkBatchURLPython3=${system.pythonframe.baseOnlineAkBatchURLPython3}
system.pythonframe.baseurlModelPython3=${system.pythonframe.baseurlModelPython3}
fp.url=${fp.url}
engine=${engine}

transfer.delay.queue.prefix=transfer_delay_queue

system.calfeature.baseurl=${system.calfeature.baseurl}

system.pythonframe.onlineMc=${system.pythonframe.onlineMc}

kafka.fs.process.topic=${kafka.fs.process.topic}
kafka.fs.process.topic.experiment.group.id=${kafka.fs.process.topic.experiment.group.id}
kafka.topic.asyncarchive.asyncdata=${kafka.topic.asyncarchive.asyncdata}
kafka.topic.asyndatas=${kafka.topic.asyndatas}
kafka.topic.asyncarchive.featureevent=${kafka.topic.asyncarchive.featureevent}

url.dataplatform.get=${url.dataplatform.get}
url.dataplatform.get.userkey=${url.dataplatform.get.userkey}
url.dataplatform.get.list.userkey=${url.dataplatform.get.list.userkey}
di.url=${di.url}
dc.url=${dc.url}
dc.inside.url=${dc.inside.url}

es.server.cluster.name = ${es.server.cluster.name}
es.server.cluster.server = ${es.server.cluster.server}

#metrics
metrics.influxdb.server=${metrics.influxdb.server}
metrics.influxdb.server.username=${metrics.influxdb.server.username}
metrics.influxdb.server.password=${metrics.influxdb.server.password}


xxl.job.admin.addresses=${xxl.job.admin.addresses}
xxl.job.accessToken=${xxl.job.accessToken}
xxl.job.executor.appname=${xxl.job.executor.appname}
xxl.job.executor.address=${xxl.job.executor.address}
xxl.job.executor.ip=${xxl.job.executor.ip}
xxl.job.executor.port=${xxl.job.executor.port}
xxl.job.executor.logpath=${xxl.job.executor.logpath}
xxl.job.executor.logretentiondays=${xxl.job.executor.logretentiondays}

hbase.zookeeper.quorum=${hbase.zookeeper.quorum}
hbase.zookeeper.userName=${hbase.zookeeper.userName}
hbase.zookeeper.passWord=${hbase.zookeeper.passWord}
hbase.zookeeper.port=${hbase.zookeeper.port}

variable.center.service.url=${variable.center.service.url}
