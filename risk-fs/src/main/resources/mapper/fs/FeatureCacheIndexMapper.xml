<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youxin.risk.fs.dao.fs.FeatureCacheIndexMapper">
    <!-- Result Map-->
    <resultMap id="resultMap"
               type="com.youxin.risk.fs.model.FeatureCacheIndex">
        <id column="id" property="id"/>
        <result column="user_key" property="userKey"/>
        <result column="feature_name" property="featureName"/>
        <result column="feature_version" property="featureVersion"/>
        <result column="feature_value_id" property="featureValueId"/>
    </resultMap>

    <insert id="insert" parameterType="com.youxin.risk.fs.model.FeatureCacheIndex" useGeneratedKeys="true"
            keyColumn="id" keyProperty="id">
        insert into
        feature_cache_index (user_Key, feature_name, feature_version, feature_value_id)
        values (#{userKey}, #{featureName}, #{featureVersion}, #{featureValueId})
    </insert>
    <select id="selectFeatureValueId" resultType="java.lang.String">
        SELECT feature_value_id from feature_cache_index where
        user_key = #{userKey} AND feature_name = #{featureName} AND feature_version = #{featureVersion}
        order by id desc limit 1;
    </select>

</mapper>
