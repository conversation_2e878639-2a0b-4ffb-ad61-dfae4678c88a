<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youxin.risk.fs.dao.rm.FeatureCodeMapper">
	<!-- Result Map-->
	<resultMap id="resultMap"
		type="com.youxin.risk.fs.model.FeatureCode">
		<id column="id" property="id"/>
		<result column="feature_code" property="featureCode"/>
		<result column="create_time" property="createTime"/>
		<result column="version" property="version"/>
	</resultMap>
	       
	<!-- rm_feature_code table all fields -->
	<sql id="columnList">
		id,feature_code,create_time,version
	</sql>
	   
	<!-- 查询条件 -->
	<sql id="whereConditions">

		<where>
			<if test="id != null">
				and id = #{id}
			</if>
			<if test="featureCode != null">
				and feature_code = #{featureCode}
			</if>
			<if test="createTime != null">
				and create_time = #{createTime}
			</if>
			<if test="version != null">
				and version = #{version}
			</if>
		</where>
	</sql>
	   
	
	<!-- 插入记录 -->
	<insert id="insert"
		parameterType="com.youxin.risk.fs.model.FeatureCode"
		useGeneratedKeys="true" keyColumn="id" keyProperty="id">
		insert into
		rm_feature_code(feature_code,create_time,version)
		values(#{featureCode},now(),0)
	</insert>
	
	<!-- 根据id，修改记录-->
	<update id="update"
		parameterType="com.youxin.risk.fs.model.FeatureCode">
		update
		rm_feature_code set
		feature_code=#{featureCode},create_time=#{createTime},version=#{version}
		where id=#{id}
	</update>
	 
	<!-- 删除-->
	<delete id="delete"
		parameterType="com.youxin.risk.fs.model.FeatureCode">
		delete from rm_feature_code where id = #{id}
	</delete>
	 
	<!-- 根据ID查询 -->
	<select id="selectById" resultMap="resultMap"
		parameterType="long">
		select
		<include refid="columnList"/>
		from rm_feature_code where id = #{id}
	</select>
	  	
	<!-- 总数-->
	<select id="selectCountByConditions"
		resultType="java.lang.Integer"
		parameterType="com.youxin.risk.fs.model.FeatureCode">
		select count(1) from rm_feature_code
		<include refid="whereConditions"/>
	</select>
	  	
	<!-- 条件查询 -->
	<select id="selectByConditions" resultMap="resultMap"
		parameterType="com.youxin.risk.fs.model.FeatureCode">
		select
		<include refid="columnList"/>
		from rm_feature_code
		<include refid="whereConditions"/>
	</select>
	
	
	<!-- map入参查询总数-->
	<select id="selectCountByMap" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		select count(1) from rm_feature_code
		<include refid="whereConditions"/>
	</select>
	  	
	<!-- map入参查询 -->
	<select id="selectByMap" resultMap="resultMap"
		parameterType="java.util.Map">
		select
		<include refid="columnList"/>
		from rm_feature_code
		<include refid="whereConditions"/>
	</select>


</mapper>   
