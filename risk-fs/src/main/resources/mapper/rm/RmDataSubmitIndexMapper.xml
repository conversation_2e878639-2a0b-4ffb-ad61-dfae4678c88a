<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.fs.dao.rm.RmDataSubmitIndexMapper" >
  <resultMap id="BaseResultMap" type="com.youxin.risk.fs.model.RmDataSubmitIndex" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="user_key" property="userKey" jdbcType="VARCHAR" />
    <result column="loan_key" property="loanKey" jdbcType="VARCHAR" />
    <result column="source_system" property="sourceSystem" jdbcType="VARCHAR" />
    <result column="step" property="step" jdbcType="VARCHAR" />
    <result column="event_code" property="eventCode" jdbcType="VARCHAR" />
    <result column="row_key" property="rowKey" jdbcType="VARCHAR" />
    <result column="engine_event_create_time" property="engineEventCreateTime" jdbcType="TIMESTAMP" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />


  </resultMap>
  <sql id="Base_Column_List" >
    id, user_key, loan_key, source_system, step, event_code, row_key,
    engine_event_create_time, create_time
  </sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from rm_datasubmit_index
    where id = #{id,jdbcType=INTEGER}
  </select>
    <select id="getRowKeysByLoanKey" resultType="java.lang.String">
      select row_key from rm_datasubmit_index where loan_key = #{loanKey,jdbcType=VARCHAR}
    </select>
    <select id="getRowKeysByConditions" resultType="java.lang.String">
      select row_key from rm_datasubmit_index
      <where>
        <if test="loanKeyList != null and loanKeyList.size() > 0">
          and loan_key in
          <foreach collection="loanKeyList" item="loanKey" index="index" open="(" close=")" separator=",">
            #{loanKey}
          </foreach>
        </if>
        <if test="userKey!=null and userKey!=''">
          AND user_key = #{userKey}
        </if>

        <if test="eventCode!=null and eventCode!=''">
          AND event_code = #{eventCode}
        </if>
        <if test="sourceSystem!=null and sourceSystem!=''">
          AND source_system = #{sourceSystem}
        </if>

        <if test="step!=null and step!=''">
          AND step = #{step}
        </if>

        <if test="createTimeStart!=null">
          AND create_time <![CDATA[ >= ]]> #{createTimeStart,jdbcType=TIMESTAMP}
        </if>
        <if test="createTimeEnd != null">
          AND create_time <![CDATA[ <= ]]> #{createTimeEnd,jdbcType=TIMESTAMP}
        </if>

      </where>
      <choose>
        <when test="createTimeStart!=null or createTimeEnd != null">
          order by create_time desc
        </when>
        <otherwise>
          order by id desc
        </otherwise>
      </choose>
      limit #{limit}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
        delete from rm_datasubmit_index
        where id = #{id,jdbcType=INTEGER}
    </delete>


    <insert id="insert" parameterType="com.youxin.risk.fs.model.RmDataSubmitIndex" >
        insert into rm_datasubmit_index (user_key, loan_key,
          source_system, step, event_code, row_key,
          engine_event_create_time, create_time
          )
        values (#{userKey,jdbcType=VARCHAR}, #{loanKey,jdbcType=VARCHAR},
          #{sourceSystem,jdbcType=VARCHAR}, #{step,jdbcType=VARCHAR},
          #{eventCode,jdbcType=VARCHAR}, #{rowKey,jdbcType=VARCHAR},
          #{engineEventCreateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <delete id="deleteByCreateTimeAndLimit">
        <![CDATA[
            delete from rm_datasubmit_index
            where create_time < DATE_ADD( NOW(), INTERVAL - #{days, jdbcType=INTEGER} DAY )
            limit #{limit}
        ]]>
    </delete>
</mapper>