<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youxin.risk.fs.dao.rm.FeatureSubmitMapper">
    <resultMap id="DetailResultMap" type="com.youxin.risk.fs.model.FeatureSubmitDetail">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="feature_submit_code" property="featureSubmitCode" jdbcType="VARCHAR"/>
        <result column="feature_name" property="featureName" jdbcType="VARCHAR"/>
        <result column="feature_ver" property="featureVer" jdbcType="VARCHAR"/>
        <result column="node_path" property="nodePath" jdbcType="VARCHAR"/>
        <result column="feature_code_id" property="featureCodeId" jdbcType="BIGINT"/>
        <result column="strategy_code_id" property="strategyCodeId" jdbcType="BIGINT"/>
        <result column="pre_type" property="preType"/>
        <result column="step_level" property="stepLevel"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="python_version" property="pythonVersion" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , submit_code, source_system, apply_step, group_name, group_ver, status, developer, create_time, update_time,strategy_code_id,pre_type,python_version
    </sql>

    <sql id="Detail_Column_List">
        id
        , feature_submit_code, feature_name, feature_ver, node_path, feature_code_id, create_time, update_time,strategy_code_id,step_level,pre_type,python_version
    </sql>

    <select id="selectDetails" resultMap="DetailResultMap" parameterType="java.lang.String">
        select
        <include refid="Detail_Column_List"/>
        from rm_feature_submit_detail
        where feature_submit_code=#{featureSubmitCode}
    </select>

    <select id="selectDetail" resultMap="DetailResultMap" parameterType="java.lang.String">
        select
        <include refid="Detail_Column_List"/>
        from rm_feature_submit_detail
        where feature_submit_code=#{featureSubmitCode}
    </select>

    <select id="selectDetailsByIds" resultMap="DetailResultMap">
        select
        <include refid="Detail_Column_List"/>
        from rm_feature_submit_detail
        where id in
        <foreach item="item" index="" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getStepBySubmitCode" resultType="java.lang.String">
        SELECT apply_step FROM rm_feature_submit WHERE submit_code = #{featureSubmitCode}
    </select>
</mapper>