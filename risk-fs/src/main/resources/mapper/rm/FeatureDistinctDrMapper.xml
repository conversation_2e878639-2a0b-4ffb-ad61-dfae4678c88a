<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.fs.dao.rm.FeatureDistinctDrMapper" >
    <resultMap id="resultMap" type="com.youxin.risk.fs.model.FeatureDistinctDr">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="event" property="event" jdbcType="VARCHAR"/>
        <result column="step" property="step" jdbcType="VARCHAR"/>
        <result column="tag" property="tag" jdbcType="VARCHAR"/>
        <result column="output_info" property="outputInfo" jdbcType="VARCHAR"/>
        <result column="valid_time" property="validTime" jdbcType="TIMESTAMP"/>
        <result column="invalid_time" property="inValidTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="selectFeatureDistinctDr" resultMap="resultMap">
        select
            id,event,step,tag,output_info,valid_time,invalid_time,status,create_time,update_time
        from  feature_distinct_dr
        where event = #{event} and step = #{step} and status = 1
    </select>

    <select id="selectByPrimaryKey" resultMap="resultMap" parameterType="java.lang.Long" >
        select
            id,event,step,tag,output_info,valid_time,invalid_time,status,create_time,update_time
        from feature_distinct_dr
        where id = #{id,jdbcType=INTEGER}
    </select>
</mapper>
