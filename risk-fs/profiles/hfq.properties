mode.name=test
app.name=risk-fs

home.base=/home/<USER>

app.home=${home.base}/risk-control/${app.name}
app.log.home=${home.base}/logs/${app.name}

tomcat.home=${home.base}/products/tomcat/tomcat_risk_fs
tomcat.port=8121
tomcat.shutdown.port=8122
tomcat.connection.timeout=5000
tomcat.doc.base=${app.home}
tomcat.allow.ips=172.*.*.*||127.0.0.1||10.*.*.*

java.opts=-Xmx2000m -Xms2000m -Xmn1000m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=128m \
		-verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xloggc:$CATALINA_HOME/logs/gc.log \
		-XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$CATALINA_HOME/logs/oom.log \
		-Djava.nio.channels.spi.SelectorProvider=sun.nio.ch.EPollSelectorProvider \
        -Dfile.encoding=UTF8  -Duser.timezone=GMT+08

console.log.level=DEBUG

datasource.maxActive=200
datasource.initialSize=2
datasource.minIdle=2
datasource.maxWait=2000
datasource.testOnBorrow=true
datasource.defaultTransactionIsolation=4
datasource.timeBetweenEvictionRunsMillis=30000
datasource.minEvictableIdleTimeMillis=300000
datasource.timeBetweenLogStatsMillis=300000
datasource.druid.remove.abandoned=false
datasource.druid.remove.abandoned.timeout=300
datasource.druid.log.abandoned=false
datasource.connectProperties.connectTimeout=1000
datasource.connectProperties.socketTimeout=5000
datasource.logAbandoned=false
datasource.removeAbandoned=true
datasource.removeAbandonedTimeout=120
datasource.poolPreparedStatements=false
#datasource.filters=stat,wall,log4j
datasource.filters=stat,wall

datasource.url.params=characterEncoding=utf8&amp;autoReconnect=true&amp;zeroDateTimeBehavior=convertToNull&amp;useUnicode=true&amp;useOldAliasMetadataBehavior=true

admin.datasource.url=****************************************?${datasource.url.params}
admin.datasource.username=root
admin.datasource.pwd=aeagh12da45O2GBfoin

rm.datasource.url=*****************************************?${datasource.url.params}
rm.datasource.username=root
rm.datasource.pwd=aeagh12da45O2GBfoin

redis.maxTotal=8
redis.maxIdle=8
redis.minIdle=4
redis.maxWaitMillis=5000
redis.testOnBorrow=true
redis.cluster.connectionTimeout=3000
redis.cluster.soTimeout=3000
redis.cluster.maxAttempts=1
redis.cluster.password=passwd456
redis.cluster.nodes=************:7000,************:7001,************:7002,\
  ************:7100,************:7101,************:7102


mongo.host=***********:27017
mongo.username=test
mongo.password=test
mongo.database=risk
mongo.credentials=${mongo.username}:${mongo.password}@${mongo.database}

mongo.sharding.host=************:27017
mongo.sharding.username=risk
mongo.sharding.password=j8cWZbL9PHK3NUdAeNBB
mongo.sharding.database=risk
mongo.sharding.credentials=${mongo.sharding.username}:${mongo.sharding.password}@${mongo.sharding.database}


kafka.dp.hosts=hadoop-1:9092,hadoop-2:9092,hadoop-3:9092
kafka.mirror.dp.hosts=***********:9092,***********:9092,***********:9092
kafka.fs.strategy.topic=fs.strategy.experiment.hfq
kafka.fs.strategy.topic.experiment.group.id=fs_strategy_experiment_hfq
kafka.fs.strategy.topic.new.experiment.group.id=fs_strategy_new_experiment_hfq
kafka.fs.feature.topic=fs.feature.experiment.hfq
kafka.fs.feature.topic.experiment.group.id=fs_feature_experiment_hfq
kafka.fs.datasource.topic=fs.feature.experiment.hfq
kafka.fs.datasource.topic.experiment.group.id=fs_datasource_experiment_hfq
kafka.fs.process.topic=fs.process.experiment.hfq
kafka.fs.process.topic.experiment.group.id=fs_process_experiment_hfq
kafka.fs.datavo.xml.topic=fs_datavo_xml.hfq

kafka.fs.strategy.result.topic=fs.strategy.result.hfq
kafka.fs.strategy.result.group.id=fs_strategy_result_hfq

kafka.fs.datasubmit.topic=fs_datasubmit_hfq
kafka.fs.datasubmit.group.id=fs_datasubmit_hfq

kafka.topic.asyncarchive.asyncdata = risk.cp.asyncarchive.asyncdata
kafka.topic.asyncarchive.featureevent = risk.cp.asyncarchive.featureevent
kafka.topic.asyndatas = risk.cp.data.async.*
kafka.group.id.asyncdata = risk_cp_realtimefeature_asyncdata
kafka.group.id.asyncarchive.asyncdata= risk_cp_realtimefeature_asyncdata_archive
kafka.group.id.asyncarchive.featureevent= risk_cp_realtimefeature_event_archive

metrics.remote.queue.server=${redis.cluster.nodes}
metrics.remote.queue.redis.password=${redis.cluster.password}
metrics.stop=false

#python frame system
system.pythonframe.baseurl=http://antifraud-risk-management-python.test.rrdbg.com
system.pythonframe.offlineurl=http://antifraud-risk-management-python.test.rrdbg.com
system.pythonframe.baseurlPython3=http://antifraud-risk-management-python3.test.rrdbg.com
system.pythonframe.fpUrlPython3=http://antifraud-risk-management-python3.test.rrdbg.com
system.pythonframe.baseurlBatchPython3=http://antifraud-risk-management-python3.test.rrdbg.com
system.pythonframe.baseurlFpBatchPython3=http://antifraud-risk-management-python3.test.rrdbg.com
system.pythonframe.baseurlAkBatchPython3=http://antifraud-risk-management-python3.test.rrdbg.com
system.pythonframe.baseurlExpAkBatchPython3=http://antifraud-risk-management-python3.test.rrdbg.com
system.pythonframe.baseOnlineAkBatchURLPython3=http://antifraud-risk-management-python3.test.rrdbg.com
system.pythonframe.baseurlModelPython3=http://antifraud-risk-management-python3.test.rrdbg.com


#calculate feature system
system.calfeature.baseurl=http://************:12080

# mail
mail.defaultEncoding=UTF-8
mail.host=mail.qf.youxin.com
mail.username=<EMAIL>
mail.password=Jp_37Kc8MaHvCKbH
mail.smtp.auth=true
mail.smtp.timeout=25000
mail.debug=false
mail.from=<EMAIL>
mail.subject=TEST

youxin.env=DEV

zookeeper.port=2181
zookeeper.quorum=***********
hadoop.user=xujiajing

url.dataplatform.get=http://babel-read.test.rrdbg.com/babel/v1/record/%s?jobid=%s&systemid=ANTI_FRAUD&ignoreFinished=false
url.dataplatform.get.userkey=https://babel-read.test.rrdbg.com/babel/v1/record/user/%s?userkey=%s&systemid=ANTI_FRAUD
url.dataplatform.get.list.userkey=https://babel-read.test.rrdbg.com/babel/v1/record/user/all/%s?userkey=%s&systemid=ANTI_FRAUD
dc.url=http://antifraud-risk-datacenter-hfq.test.rrdbg.com
dc.inside.url=http://antifraud-risk-datacenter-inside-hfq.test.rrdbg.com
di.url=http://antifraud-risk-di-hfq.test.rrdbg.com

fs.datasource.url=*****************************************?${datasource.url.params}
fs.datasource.username=root
fs.datasource.pwd=aeagh12da45O2GBfoin


xxl.job.admin.addresses=http://risk-xxl-job-manager.test.rrdbg.com
xxl.job.accessToken=
xxl.job.executor.appname=risk-fs
xxl.job.executor.address=
xxl.job.executor.ip=
xxl.job.executor.port=-1
xxl.job.executor.logpath=/tmp/
xxl.job.executor.logretentiondays=-1

transfer.mongo.host=************:27017


metrics.point.kafka.hosts=hadoop-1:9092,hadoop-2:9092,hadoop-3:9092
metrics.point.kafka.topic=metrics.point.kafka.topic_hfq
metrics.point.kafka.group.id=metrics.point.kafka.group_hfq
metrics.point.kafka.topic.list=metrics.point.kafka.topic_hfq,metrics.point.kafka.topic.gateway_hfq
metrics.point.mirror.kafka.hosts=***********:9092,***********:9092,***********:9092

fp.url=http://antifraud-risk-fp.weicai.com.cn

kafka.fs.strategy.result.distinctDr.topic=distinctDr_test
kafka.fs.strategy.result.distinctDr..group.id=distinctDr_test_group

kafka.fs.model.invoke.topic=model_platform_invoke_result_test