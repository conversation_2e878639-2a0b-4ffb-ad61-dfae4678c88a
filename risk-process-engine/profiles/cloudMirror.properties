app.name=risk-process-engine

app.home=${home.base}/webapps/ROOT
app.log.home=${catalina.base}/logs

tomcat.home=${home.base}
tomcat.port=8091
tomcat.shutdown.port=8092

java.opts=-Xmx3550m -Xms3550m -Xmn1500m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=128m -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xloggc:$CATALINA_HOME/logs/gc.log -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$CATALINA_HOME/logs/oom.log -Djava.nio.channels.spi.SelectorProvider=sun.nio.ch.EPollSelectorProvider -Dfile.encoding=UTF8  -Duser.timezone=GMT+08

# mysql config
datasource.maxActive=30
datasource.initialSize=10
datasource.minIdle=10
datasource.maxWait=2000
datasource.druid.remove.abandoned=false
datasource.connectProperties.socketTimeout=5000
datasource.admin.maxActive=5

datasource.engine.maxActive=30
datasource.engine.initialSize=20
datasource.engine.minIdle=20

datasource.url.params=characterEncoding=utf8&amp;autoReconnect=true&amp;zeroDateTimeBehavior=convertToNull&amp;useUnicode=true&amp;useOldAliasMetadataBehavior=true

engine.datasource.url=********************************************************************************?${datasource.url.params}

# redisson
redisson.maxPoolSize=128
redisson.minPoolSize=48

# mongo config
# 风控数据集成库
mongo.host=dds-2ze4fde7c7b405742.mongodb.rds.aliyuncs.com:3717,dds-2ze4fde7c7b405741.mongodb.rds.aliyuncs.com:3717
mongo.username=${SEC_RISK_ENGINE_15_119_TRANSFER_MONGODB_USERNAME}
mongo.password=${SEC_RISK_ENGINE_15_119_TRANSFER_MONGODB_PASSWORD}
mongo.credentials=${mongo.username}:${mongo.password}@${mongo.database}

mongo.risk.username=${SEC_RISK_ENGINE_15_41_RISK_MONGODB_USERNAME}
mongo.risk.password=${SEC_RISK_ENGINE_15_41_RISK_MONGODB_PASSWORD}
mongo.risk.credentials=${mongo.risk.username}:${mongo.risk.password}@${mongo.risk.database}

mongo.sharding.username=${SEC_RISK_ENGINE_12_150_N_RISK_MONGODB_USERNAME}
mongo.sharding.password=${SEC_RISK_ENGINE_12_150_N_RISK_MONGODB_PASSWORD}
mongo.sharding.credentials=${mongo.sharding.username}:${mongo.sharding.password}@${mongo.sharding.database}

mongo.event.host1=${mongo.riskExp.host}
mongo.event.username1=${SEC_RISK_ENGINE_13_12_RISK_EXP_MONGODB_USERNAME}
mongo.event.password1=${SEC_RISK_ENGINE_13_12_RISK_EXP_MONGODB_PASSWORD}
mongo.event.database1=risk_exp
mongo.event.credentials1=${mongo.event.username1}:${mongo.event.password1}@${mongo.event.database1}
mongo.event.auth.database1=risk_exp

mongo.event.host2=${mongo.riskExp.host}
mongo.event.username2=${SEC_RISK_ENGINE_13_12_RISK_EXP_MONGODB_USERNAME}
mongo.event.password2=${SEC_RISK_ENGINE_13_12_RISK_EXP_MONGODB_PASSWORD}
mongo.event.database2=risk_exp
mongo.event.credentials2=${mongo.event.username2}:${mongo.event.password2}@${mongo.event.database2}
mongo.event.auth.database2=risk_exp
# 风控旧数据集成库
transfer.mongo.host=dds-2ze273fece514f241.mongodb.rds.aliyuncs.com:3717,dds-2ze273fece514f242.mongodb.rds.aliyuncs.com:3717

# kafka config
kafka.engine.monitor.topic=risk.engine.monitor.topic
kafka.engine.event.topic=risk.engine.event.topic
kafka.engine.trans.topic=antifraud.transaction
kafka.engine.graph.topic=antifraud.graph_verify
kafka.engine.graph.api.topic=antifraud.graph_api
kafka.engine.precachedata.result.topic=risk.engine.precachedata.result
kafka.engine.callback.topic=risk.engine.engine.topic
kafka.engine.callback.topic.group.id=youxin_risk_engine_engine_group
kafka.engine.shadow.topic=kafka.shadow.save.topic
kafka.engine.verify.result.writeback.topic=risk.engine.verify.result.save.topic
kafka.engine.delay.event.message.topic=kafka.engine.delay.event.message.topic
kafka.engine.shadow.monitor.topic=kafka.shadow.monitor.topic
kafka.engine.datamonitor.topic=risk.engine.engine.datamonitor
kafka.engine.datamonitor.topic.group.id=risk_engine_engine_datamonitor
risk.terminated.topic=risk_terminated_topic
risk.terminated.group=risk_terminated_group

kafka.fs.strategy.result.distinctDr.topic=distinctDr
kafka.fs.strategy.result.topic=fs.strategy.result
kafka.fs.strategy.result.json.topic=fs_strategy_variable_result
kafka.fs.strategy.topic=fs.strategy.experiment
kafka.api.event.strategy.variable.result.topic=api_event_strategy_variable_result

# metric config
metrics.point.kafka.topic=metrics.point.kafka.topic
metrics.point.kafka.group.id=metrics.point.kafka.group
metrics.point.kafka.topic.list=metrics.point.kafka.topic,metrics.point.kafka.topic.gateway

xxl.job.executor.appname=risk-process-engine-mirror

kafka.rta.hosts=alikafka-serverless-cn-rp63sv9r101-1000-vpc.alikafka.aliyuncs.com:9092,alikafka-serverless-cn-rp63sv9r101-2000-vpc.alikafka.aliyuncs.com:9092,alikafka-serverless-cn-rp63sv9r101-3000-vpc.alikafka.aliyuncs.com:9092
kafka.guiyin.event.strategy.result.topic=risk-attribution-result

kafka.quota.result.topic=quota_topic
kafka.reasoncode.dw.topic=kafka.reasoncode.dw.topic

verify.strategy.result.topic=verify.strategy.result.test

risk.quota.url=http://risk-quota-api.test.weicai.com.cn