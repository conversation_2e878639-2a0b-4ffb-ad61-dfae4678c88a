/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.ra.vo;

import com.youxin.risk.ra.utils.XmlEntity;
import com.youxin.risk.ra.utils.XmlNode;

@XmlEntity
public class ModelResultVo {
	@XmlNode(name="model_id")
	private String modelId;
	@XmlNode(name="loan_id")
	private String loanId;
	@XmlNode(name="prob")
	private Double prob;
	//@XmlNode(name="threshold")
	private String threshold;
	@XmlNode(name="model_feature")
	private String modelFeature;
	private Integer modelStatus;
	private String result;
	public String getModelId() {
		return this.modelId;
	}
	public void setModelId(String modelId) {
		this.modelId = modelId;
	}
	public String getLoanId() {
		return this.loanId;
	}
	public void setLoanId(String loanId) {
		this.loanId = loanId;
	}
	public Double getProb() {
		return this.prob;
	}
	public void setProb(Double prob) {
		this.prob = prob;
	}
	public String getThreshold() {
		return this.threshold;
	}
	public void setThreshold(String threshold) {
		this.threshold = threshold;
	}
	public String getModelFeature() {
		return this.modelFeature;
	}
	public void setModelFeature(String modelFeature) {
		this.modelFeature = modelFeature;
	}
	public Integer getModelStatus() {
		return modelStatus;
	}
	public void setModelStatus(Integer modelStatus) {
		this.modelStatus = modelStatus;
	}
	public String getResult() {
		return result;
	}
	public void setResult(String result) {
		this.result = result;
	}
}
