package com.youxin.risk.ra.vo;

import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class TongdunPolicyVo {
	private Integer id;
	private String policyUuid;
	private String policyMode;
	private Integer policyScore;
	private String policyName;
	private String riskType;
	private List<TongdunRulesVo> hitRules;
	private List<TongdunRulesVo> hitRulesConvert;
	private String tongdunFraudId;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getPolicyUuid() {
		return policyUuid;
	}

	public void setPolicyUuid(String policyUuid) {
		this.policyUuid = policyUuid;
	}

	public String getPolicyMode() {
		return policyMode;
	}

	public void setPolicyMode(String policyMode) {
		this.policyMode = policyMode;
	}

	public Integer getPolicyScore() {
		return policyScore;
	}

	public void setPolicyScore(Integer policyScore) {
		this.policyScore = policyScore;
	}

	public String getPolicyName() {
		return policyName;
	}

	public void setPolicyName(String policyName) {
		this.policyName = policyName;
	}

	public String getRiskType() {
		return riskType;
	}

	public void setRiskType(String riskType) {
		this.riskType = riskType;
	}

	public List<TongdunRulesVo> getHitRules() {
		return hitRules;
	}

	public void setHitRules(List<TongdunRulesVo> hitRules) {
		this.hitRules = hitRules;
	}

	public List<TongdunRulesVo> getHitRulesConvert() {
		return hitRulesConvert;
	}

	public void setHitRulesConvert(List<TongdunRulesVo> hitRulesConvert) {
		this.hitRulesConvert = hitRulesConvert;
	}

	public String getTongdunFraudId() {
		return tongdunFraudId;
	}

	public void setTongdunFraudId(String tongdunFraudId) {
		this.tongdunFraudId = tongdunFraudId;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this,
				ToStringStyle.SHORT_PREFIX_STYLE);
	}
}
