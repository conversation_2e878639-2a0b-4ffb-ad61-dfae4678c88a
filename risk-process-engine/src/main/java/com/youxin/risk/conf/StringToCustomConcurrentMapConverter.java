package com.youxin.risk.conf;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.commons.concurrent.CustomConcurrentMap;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.convert.ReadingConverter;
import org.springframework.data.convert.WritingConverter;

/**
 *<AUTHOR>
 *@create 2024/11/8 16:17
 *@desc
 */
@ReadingConverter
public class StringToCustomConcurrentMapConverter implements Converter<String, CustomConcurrentMap> {

    @Override
    public CustomConcurrentMap convert(String source) {
        return JSON.parseObject(source, CustomConcurrentMap.class);
    }
}
