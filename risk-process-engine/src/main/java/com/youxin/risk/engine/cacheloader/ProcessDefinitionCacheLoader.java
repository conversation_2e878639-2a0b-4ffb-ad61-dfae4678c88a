package com.youxin.risk.engine.cacheloader;

import com.youxin.risk.commons.cacheloader.BaseCacheLoader;
import com.youxin.risk.engine.activiti.runtime.ProcessEngines;
import com.youxin.risk.commons.cacheloader.service.ProcessDefinitionService;
import com.youxin.risk.commons.constants.ConfigTableEnum;
import com.youxin.risk.commons.model.ProcessDefinition;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/11/13 10:56
 */
public class ProcessDefinitionCacheLoader extends BaseCacheLoader {
    @Resource
    private ProcessDefinitionService processDefinitionService;

    @Override
    @Scheduled(fixedDelay = 10000)
    public void load() {
        super.load(ConfigTableEnum.admin_process_definition.toString());
    }

    @Override
    protected int loadPart() {
        //todo ，子流程更新后，应该更新依赖其的主流程，简便处理，直接更新所有的流程
        loadAll();

        return 0;
    }

    @Override
    protected void loadAll() {
        List<ProcessDefinition> processDefinitions = processDefinitionService.selectAll();
        if (CollectionUtils.isEmpty(processDefinitions)) {
            return;
        }
        //子流程
        registerProcessList(processDefinitions, true);

        //主流程
        registerProcessList(processDefinitions, false);
    }

    private void registerProcessList(List<ProcessDefinition> allProcessDefinitionList, boolean isSub) {
        List<ProcessDefinition> processDefList =
                allProcessDefinitionList.stream().filter(e -> isSub == e.getSubProcess())
                        .collect(Collectors.toList());
        for (ProcessDefinition pd : processDefList) {
            try {
                ProcessEngines.registerProcessEngine(pd.getProcessContent(), pd.getProcessJson(), pd.getProcessDefId());
            } catch (Exception e) {
                LoggerProxy.error("registerProcessError", logger,
                        "processDefId=" + pd.getProcessDefId(), e);
            }
        }
    }
}
