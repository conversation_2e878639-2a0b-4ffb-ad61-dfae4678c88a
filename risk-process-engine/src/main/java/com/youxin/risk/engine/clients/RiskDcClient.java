package com.youxin.risk.engine.clients;

import com.alibaba.fastjson.*;
import com.google.common.base.Stopwatch;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.exception.RiskRuntimeException;
import com.youxin.risk.commons.kafkav2.sender.KafkaSyncSender;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.model.ProcessNode;
import com.youxin.risk.commons.model.verify.ApiUserLine;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.remote.model.datacenter.DcRequest;
import com.youxin.risk.commons.remote.model.datacenter.DcRequestService;
import com.youxin.risk.commons.utils.*;
import com.youxin.risk.commons.vo.BooleanResult;
import com.youxin.risk.engine.utils.UserKeyLoader;
import com.youxin.risk.engine.vo.DcResult;
import com.youxin.risk.engine.vo.QuotaDwMessageVO;
import com.youxin.risk.engine.vo.QuotaMessageVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.youxin.risk.engine.conf.Apollo.logDetail;

/**
 * risk-datacenter服务客户端
 *
 * <AUTHOR>
 */
@Component
public class RiskDcClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(RiskDcClient.class);

    @Value("${dc.url}")
    private String url;
    @Value("${dc.inside.url}")
    private String dcInsideUrl;
    @Value("${risk.quota.url}")
    private String riskQuotaUrl;

    @Resource(name = "quotaDwSender")
    private KafkaSyncSender quotaDwSender;
    @Autowired
    private UserKeyLoader userKeyLoader;

    private static final int STATUS_SUCCESS = 0;
    private static final int TIMEOUT_MILLI_SECOND = 30 * 60 * 1000;

    private static final String QUERY_URL = "/dc/queryServices";
    private static final String DATA_VO_USER_URI = "/engine/dataVoByUser";
    private static final String JOBID_URI = "/thirdPartyFetch/jobidByLoanId";
    private static final String DATA_TASK_URI = "/engine/dataTask";
    private static final String AMOUNT_URI = "/verify/updateAmount";
    private static final String API_AMOUNT_URI = "/verify/updateApiAmount";
    private static final String SEARCH_USERLEVEL_URI = "/search/userLevel";
    private static final String SAVE_LEND_RESULT_URI = "/verify/lendResult";
    private static final String IS_WHITE_LIST_USER="/datacenter/whiteList/whiteListUser/";
    private static final String IS_WHITE_LIST_USER_NEW="/datacenter/whiteList/whiteListUserNew/";

    private static final String callBackDcVerifyDataUrl="/verifyData/callback";
    private static final String HFQ_LOAN_AMOUNT_RESULT_PATH = "currLineManagement";
    private static final String HFQ_LOAN_AMOUNT_UNDER_RESULT_PATH = "curr_line_management";

    public DcResult callDc(DcRequest request) {
        int timeout = 60000;
        String response = SyncHTTPRemoteAPI.postJson(dcInsideUrl + QUERY_URL, JsonUtils.toJson(request), timeout);
        if (StringUtils.isBlank(response)) {
            LoggerProxy.error("callDcError", LOGGER, "get data error, response is null,request={}", request);
            return new DcResult(RetCodeEnum.FAILED);
        }

        return buildDcResult(response);
    }


    private DcResult buildDcResult(String response) {
        JSONObject responseJson = JSONObject.parseObject(response);
        String retCode = responseJson.getString("retCode");
        if (!RetCodeEnum.SUCCESS.equals(retCode)) {
            LoggerProxy.error("getDataError", LOGGER, "get data error, retCode is error, response={}", response);
            return new DcResult(RetCodeEnum.FAILED);
        }

        JSONObject resultJson = responseJson.getJSONObject("result");
        JSONArray servicesJson = resultJson.getJSONArray("services");
        if (servicesJson == null || servicesJson.size() == 0) {
            LoggerProxy.error("getDataError", LOGGER, "get data error, service data is blank, response={}", response);
            return new DcResult(RetCodeEnum.FAILED);
        }

        Map<String, Object> resultMap = Maps.newHashMap();

        for (int i = 0; i < servicesJson.size(); i++) {
            JSONObject serviceJson = servicesJson.getJSONObject(i);
            String returnCode = serviceJson.getString("retCode");
            String serviceCode = serviceJson.getString("serviceCode");
            if (RetCodeEnum.SUCCESS.equals(returnCode)) {
                JSONObject dataResult = serviceJson.getJSONObject("result");
                if (dataResult != null) {
                    resultMap.put(serviceCode, dataResult.get("data"));
                }
            } else {
                LoggerProxy.warn("getDcDataWaring", LOGGER, "serviceCode={} retCode={} is not SUCCESS", serviceCode, returnCode);
            }
        }

        return new DcResult(RetCodeEnum.SUCCESS, resultMap);
    }

    public DcRequest buildDcRequest(Event event, String dcServiceCode, Map<String, Object> params) {
        DcRequestService serviceReq = new DcRequestService();
        serviceReq.setServiceCode(dcServiceCode);
        serviceReq.setParams(params);

        List<DcRequestService> services = ImmutableList.of(serviceReq);
        DcRequest request = new DcRequest();
        request.setServices(services);
        request.setUserKey(event.getUserKey());
        return request;
    }

    public Map<String, Object> getDataVoByUser(String sourceSystem, String userKey, Object apiLoanSource) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("sourceSystem", sourceSystem);
        params.put("userKey", userKey);
        if(Objects.nonNull(apiLoanSource)){
            params.put("apiLoanSource", apiLoanSource);
        }
        String requestStr = JSONObject.toJSONString(params);
        String response = SyncHTTPRemoteAPI.postJson(dcInsideUrl + DATA_VO_USER_URI, requestStr, TIMEOUT_MILLI_SECOND);
        JSONObject responseJson = JSONObject.parseObject(response);
        int status = responseJson.getInteger("status");
        if (status != STATUS_SUCCESS) {
            LoggerProxy.error("callGetDataVoByUserError", LOGGER, "status={},userKey={},message={}", responseJson.getString("status"), userKey, responseJson.getString("message"));
            throw new RiskRuntimeException(RetCodeEnum.FAILED, responseJson.getString("message"));
        }
        return responseJson.getJSONObject("data").getJSONObject("dataVo");
    }

    public Map<String, Object> getJobId(String userKey, String loanId, List<String> typss) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("userKey", userKey);
        params.put("loanId", loanId);
        params.put("jobTypes", typss);
        String requestStr = JSONObject.toJSONString(params);
        String response;
        //LoggerProxy.info("beforeGetJobId", LOGGER, "url={},request={}", dcInsideUrl + JOBID_URI, requestStr);
        response = SyncHTTPRemoteAPI.postJson(dcInsideUrl + JOBID_URI, requestStr, TIMEOUT_MILLI_SECOND);
        JSONObject responseJson = JSONObject.parseObject(response);
        int status = responseJson.getInteger("status");
        if (status != STATUS_SUCCESS) {
            LoggerProxy.error("callGetJobIdError", LOGGER, "url:{}, status={},message={}", dcInsideUrl, responseJson.getString("status"), responseJson.getString("message"));
            throw new RiskRuntimeException(RetCodeEnum.FAILED, responseJson.getString("message"));
        }
        return responseJson.getJSONObject("data").getJSONObject("loanJobId");
    }

    public JSONObject getDataTask(String sourceSystem, String userKey, String taskType) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("userKey", userKey);
        params.put("sourceSystem", sourceSystem);
        params.put("taskType", taskType);
        String requestStr = JSONObject.toJSONString(params);
        //LoggerProxy.info("beforeGetDataTask", LOGGER, "url={},request={}", dcInsideUrl + DATA_TASK_URI, requestStr);
        String response = SyncHTTPRemoteAPI.postJson(dcInsideUrl + DATA_TASK_URI, requestStr, TIMEOUT_MILLI_SECOND);
        JSONObject responseJson = JSONObject.parseObject(response);
        int status = responseJson.getInteger("status");
        if (status != STATUS_SUCCESS) {
            LoggerProxy.error("callGetDataTaskError", LOGGER, "url:{}, status={},message={}", dcInsideUrl, responseJson.getString("status"), responseJson.getString("message"));
            throw new RiskRuntimeException(RetCodeEnum.FAILED, responseJson.getString("message"));
        }
        return responseJson.getJSONObject("data").getJSONObject("dataTask");
    }

    public BooleanResult updateAmount(Event event, ProcessNode processNode, String xml, Object lineResult, boolean isNeedTransFlag) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        Map<String, Object> params = Maps.newHashMap();
        params.put("sourceSystem", event.getSourceSystem());
        params.put("userKey", event.getUserKey());
        params.put("loanKey", event.getLoanKey());
        /**
         * 好像已经无用，参考
         * @see com.youxin.risk.verify.service.impl.BaseAmountProcesser#saveUserAmount(com.alibaba.fastjson.JSONObject, java.lang.String, java.lang.String, java.lang.String, java.lang.Integer, java.lang.String)
         * 177行
         * / */
        params.put("xml", xml);
        params.put("strategyType", getStrategyType(event, processNode));
        params.put("verifyResult", lineResult);
        // 额度参数公共化, transId做幂等, 不可为空
        params.put("amountParams", event.getParams());
        params.put("amountType", event.getString("amountType"));
        params.put("eventCode", event.getEventCode());
        String requestStr = JSONObject.toJSONString(params);
        if (logDetail()) {
            LoggerProxy.info("beforeUpdateAmount", LOGGER, "url={},request={}", dcInsideUrl + AMOUNT_URI, requestStr);
        }

        // 发送额度信息到额度中心
        JSONObject newLineResult = sendQuotaResult(event, getStrategyType(event, processNode), lineResult);

        // 读取nacos配置获取更新event.verifyResult的结果的事件列表
        Boolean isSetVerifyResultEnabled = NacosClientAdapter.getBooleanConfig("isSetVerifyResultEnabled", false);
        String updateEventListStr = NacosClientAdapter.getStringConfig("updateEventList", "");
        List<String> updateEventList = Arrays.asList(updateEventListStr.split(","));
        if (updateEventList.contains(event.getEventCode())
                || updateEventList.contains("ALL")
                || (isSetVerifyResultEnabled && (userKeyLoader.getUserKeys().contains(event.getUserKey()) || isUpdateVerifyResultRequired(event.getUserKey())))
        ) {
            if(isNeedTransFlag){
                event.setVerifyResult(newLineResult);
            }else {
                JSONPath.set(event.getVerifyResult(), HFQ_LOAN_AMOUNT_RESULT_PATH, newLineResult);
                if(JsonGrayFeatureUtil.checkGrayFeature(event.getUserKey())){
                    JSONPath.set(event.getVerifyResult(), HFQ_LOAN_AMOUNT_UNDER_RESULT_PATH, newLineResult);
                }
            }
            LoggerProxy.info("sendQuotaResult", LOGGER, "userKey={}, loanKey={}, eventCode={}, update event.verifyResult success"
                    , event.getUserKey(), event.getLoanKey(), event.getEventCode());
        }

        String response = SyncHTTPRemoteAPI.postJson(dcInsideUrl + AMOUNT_URI, requestStr, TIMEOUT_MILLI_SECOND);
        long costTime = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
        LoggerProxy.info("afterUpdateAmount", LOGGER, "url:{}, userKey:{},response={}, cost={}", dcInsideUrl, event.getUserKey(), response, costTime);
        JSONObject responseJson = JSONObject.parseObject(response);
        int status = responseJson.getInteger("status");
        String message = responseJson.getString("message");
        if (status != STATUS_SUCCESS) {
            throw new RiskRuntimeException(RetCodeEnum.FAILED, message);
        }
        Boolean flag = responseJson.getJSONObject("data").getBoolean("result");
        if (!flag) {
            throw new RiskRuntimeException(RetCodeEnum.FAILED, "update amount failed, message:{}", message);
        }

        return new BooleanResult(flag, message);
    }

    public void updateVerifyAmount(Event event, ProcessNode processNode, Object lineResult) {
        // 发送额度信息到额度中心
        JSONObject newLineResult = sendQuotaResult(event, getStrategyType(event, processNode), lineResult);

        JSONPath.set(event.getVerifyResult(), HFQ_LOAN_AMOUNT_RESULT_PATH, newLineResult);
        if(JsonGrayFeatureUtil.checkGrayFeature(event.getUserKey())){
            JSONPath.set(event.getVerifyResult(), HFQ_LOAN_AMOUNT_UNDER_RESULT_PATH, newLineResult);
        }

        LoggerProxy.info("sendQuotaResult", LOGGER, "userKey={}, loanKey={}, eventCode={}, update event.verifyResult success"
                , event.getUserKey(), event.getLoanKey(), event.getEventCode());
        // todo 这里需要特别注意，策略如果输出api额度，则正常落库;不输出则不落库
        if(shouldSkipApiLineUpdate(newLineResult)){
            LoggerProxy.info("apiLineInfoNoValid", LOGGER
                    , "Skipping API line update - no api line info: userKey={}, loanKey={}, eventCode={}",
                    event.getUserKey(), event.getLoanKey(), event.getEventCode());
            return;
        }

        // 更新api额度信息
        Boolean isUpdateApiAmountEnabled = NacosClientAdapter.getBooleanConfig("isUpdateApiAmountEnabled", false);
        if (isUpdateApiAmountEnabled) {
            updateApiLineInfo(event, processNode, lineResult);
        }
    }

    public JSONObject updateQuotaAmount(Event event, ProcessNode processNode, Object lineResult) {
        // 请求额度中心
        JSONObject newLineResult = sendQuotaResult(event, getStrategyType(event, processNode), lineResult);

        // todo 这里需要特别注意，策略如果输出api额度，则正常落库;不输出则不落库
        if(shouldSkipApiLineUpdate(event.getVerifyResult())){
            LoggerProxy.info("apiLineInfoNoValid", LOGGER
                    , "Skipping API line update - no api line info: userKey={}, loanKey={}, eventCode={}",
                    event.getUserKey(), event.getLoanKey(), event.getEventCode());
            return newLineResult;
        }

        // 更新api额度信息
        Boolean isUpdateApiAmountEnabled = NacosClientAdapter.getBooleanConfig("isUpdateApiAmountEnabled", false);
        if (isUpdateApiAmountEnabled) {
            updateApiLineInfo(event, processNode, lineResult);
        }

        return newLineResult;
    }

    private void updateApiLineInfo(Event event, ProcessNode processNode, Object lineResult) {
        String requestStr = buildDcRequest(event, processNode, lineResult);
        sendUpdateRequest(event, requestStr);
    }

    private boolean shouldSkipApiLineUpdate(Map<String, Object> verifyResult) {
        String verifyResultStr = JSONObject.toJSONString(verifyResult);
        ApiUserLine info = JSON.parseObject(verifyResultStr, ApiUserLine.class);
        return isApiLineInfoInvalid(info);
    }

    private boolean isApiLineInfoInvalid(ApiUserLine info) {
        return Optional.ofNullable(info)
                .map(line -> Objects.isNull(line.getApiLine())
                        && Objects.isNull(line.getApiAvailLine())
                        && Objects.isNull(line.getApiUtilLine()))
                .orElse(true);
    }

    private String buildDcRequest(Event event, ProcessNode processNode, Object lineResult) {
        Map<String, Object> params = new HashMap<>();
        params.put("sourceSystem", event.getSourceSystem());
        params.put("userKey", event.getUserKey());
        params.put("loanKey", event.getLoanKey());
        params.put("strategyType", getStrategyType(event, processNode));
        params.put("verifyResult", lineResult);
        params.put("amountParams", event.getParams());
        params.put("amountType", event.getString("amountType"));
        params.put("eventCode", event.getEventCode());

        return JSONObject.toJSONString(params);
    }

    private void sendUpdateRequest(Event event, String requestStr) {
        try {
            Stopwatch stopwatch = Stopwatch.createStarted();
            String response = SyncHTTPRemoteAPI.postJson(dcInsideUrl + API_AMOUNT_URI, requestStr, TIMEOUT_MILLI_SECOND);
            long costTime = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
            LoggerProxy.info("afterUpdateApiAmount", LOGGER
                    , "userKey={}, response={}, cost={}", event.getUserKey(), response, costTime);
            validateResponse(response);
        } catch (Exception e) {
            throw new RiskRuntimeException(RetCodeEnum.FAILED, "Failed to update amount: " + e.getMessage());
        }
    }

    private void validateResponse(String response) {
        JSONObject responseJson = JSONObject.parseObject(response);
        int status = responseJson.getInteger("status");
        String message = responseJson.getString("message");

        if (status != STATUS_SUCCESS) {
            throw new RiskRuntimeException(RetCodeEnum.FAILED, message);
        }

        Boolean success = responseJson.getJSONObject("data").getBoolean("result");
        if (!success) {
            throw new RiskRuntimeException(RetCodeEnum.FAILED, "Update amount failed: " + message);
        }
    }

    private String getStrategyType(Event event, ProcessNode processNode) {
        String strategyType = event.getString("strategyType");
        try {
            if (processNode.getStrategy() != null) {
                strategyType = processNode.getStrategy().getType();
            }
        } catch (Exception e) {
            LoggerProxy.warn("getStrategyTypeError", LOGGER, "", e);
        }
        return strategyType;
    }

    public Map<String, Object> getUserLevel(String userKey) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        Map<String, Object> params = Maps.newHashMap();
        params.put("userKey", userKey);
        String requestStr = JSONObject.toJSONString(params);
        if (logDetail()) {
            LoggerProxy.info("beforeGetUserLevel", LOGGER, "url={},request={}", dcInsideUrl + SEARCH_USERLEVEL_URI, requestStr);
        }
        String response = SyncHTTPRemoteAPI.postJson(dcInsideUrl + SEARCH_USERLEVEL_URI, requestStr, TIMEOUT_MILLI_SECOND);
        long costTime = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
        LoggerProxy.info("afterGetUserLevel", LOGGER, "url:{}, response={}, cost={}", dcInsideUrl, response, costTime);
        JSONObject responseJson = JSONObject.parseObject(response);
        int status = responseJson.getInteger("status");
        if (status != STATUS_SUCCESS) {
            throw new RiskRuntimeException("user level not found");
        }

        return responseJson.getJSONObject("data").getJSONObject("userLevel");
    }

    public Boolean saveLendResult(Event event) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("is_passed", event.getVerifyResult().get("isPassed"));
        Object reasonCode = event.getVerifyResult().get("reasonCode");
        Object reason_code = event.getVerifyResult().get("reason_code");
        // 添加下划线
        reasonCode = JsonKeyFormatUtil
                .mergeResults(JSONObject.parseObject(JSONObject.toJSONString(reasonCode))
                        , JSONObject.parseObject(JSONObject.toJSONString(reason_code))
                        , event.getUserKey());

        params.put("reason_code", reasonCode);
        params.put("userKey", event.getUserKey());
        params.put("loanKey", event.getLoanKey());
        params.put("loanId", event.get("loanId"));
        params.put("loan", event.get("loan"));
        params.put("loanType", event.get("loanType"));
        params.put("fundChannel", event.get("fundChannel"));
        params.put("orderNo", event.get("orderNo"));
        params.put("featureXml", event.getXml());
        params.put("userLineId", null);

        params.put("Status", true);
        params.put("createTime", new Date());

        String requestStr = JSONObject.toJSONString(params);
        Stopwatch stopwatch = Stopwatch.createStarted();
        if (logDetail()) {
            LoggerProxy.info("beforeSaveLendResult", LOGGER, "url={},request={}", dcInsideUrl + SAVE_LEND_RESULT_URI, requestStr);
        }
        String response = SyncHTTPRemoteAPI.postJson(dcInsideUrl + SAVE_LEND_RESULT_URI, requestStr, TIMEOUT_MILLI_SECOND);

        long costTime = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
        LoggerProxy.info("afterSaveLendResult", LOGGER, "url:{}, response={}, cost={}", dcInsideUrl, response, costTime);
        JSONObject responseJson = JSONObject.parseObject(response);
        int status = responseJson.getInteger("status");
        return status == STATUS_SUCCESS;
    }

    /**
     * 是否命中黑/白名单
     * @param userKey 用户userKey
     * @param mobile 手机号
     * @param eventCode 事件code
     * @param key 黑白名单编码
     * @return true or false
     */
    public Boolean isWhiteListUser(String userKey,
                                   Optional<String> mobile,
                                   String eventCode,
                                   String key){
        String url=dcInsideUrl + IS_WHITE_LIST_USER;
        Map<String,String> param=new HashMap<>();
        param.put("userKey", userKey);
        param.put("eventCode", eventCode);
        mobile.ifPresent(s -> param.put("mobile", s));
        param.put("key", key);
        if (logDetail()) {
            LoggerProxy.info("beforeIsWhiteListUser", LOGGER, "params={}", JSONObject.toJSONString(param));
        }
        String response =SyncHTTPRemoteAPI.postJson(url,JSONObject.toJSONString(param),10*1000);
        if(StringUtils.isBlank(response)){
            return false;
        }
        JSONObject jsonObject = JSONObject.parseObject(response);
        LoggerProxy.info("afterIsWhiteListUser", LOGGER, "params={}", jsonObject.toString());
        if(jsonObject.getInteger("status")==0){
            JSONObject whiteListUser = jsonObject.getJSONObject("data").getJSONObject("whiteListUser");
            if (whiteListUser != null && !whiteListUser.isEmpty() && whiteListUser.getBoolean("isHit")) {
                return whiteListUser.getInteger("type") == 1 || whiteListUser.getInteger("type") == 2;
            }
        }
        return false;
    }

    /**
     * 是否命中黑/白名单
     * @param mobile  手机号
     * @param eventCode 事件code
     * @param whiteListKey 黑/白名单key
     * @param isBlackList 是否是黑名单
     * @return true or false
     */
    public Boolean isHit(String mobile, String eventCode, String whiteListKey, boolean isBlackList){
        String url=dcInsideUrl + IS_WHITE_LIST_USER_NEW;
        Map<String,String> param=new HashMap<>();
        param.put("mobile",mobile);
        param.put("eventCode",eventCode);
        param.put("whiteListKey",whiteListKey);
        param.put("isBlackList", String.valueOf(isBlackList));
        if (logDetail()) {
            LoggerProxy.info("beforeIsWhiteListUserNew", LOGGER, "params={}", JSONObject.toJSONString(param));
        }
        String response =SyncHTTPRemoteAPI.postJson(url,JSONObject.toJSONString(param),10*1000);
        if(StringUtils.isBlank(response)){
            return false;
        }
        JSONObject jsonObject = JSONObject.parseObject(response);
        LoggerProxy.info("afterIsWhiteListUserNew", LOGGER, "params={}", jsonObject.toString());
        return jsonObject.getInteger("status")==0 && jsonObject.getJSONObject("data").getBoolean("whiteListUser");
    }


    public void callBackDcVerifyData(Event event){
        String url=this.url+callBackDcVerifyDataUrl;
        JSONObject jsonObject=new JSONObject();
        jsonObject.put("loanKey", event.getLoanKey());
        String jsStr=jsonObject.toString();
        try {
            // 判断是否需要回调数据中心 ALL\HEIKA\RONGDAN 需要调用
            String targetSwitch = (String)event.getParams().get("targetSwitch");
            if (StringUtils.isEmpty(targetSwitch) ||
                    (!"ALL".equalsIgnoreCase(targetSwitch)
                    && !"HEIKA".equalsIgnoreCase(targetSwitch)
                    && !"RONGDAN".equalsIgnoreCase(targetSwitch))) {
                return;
            }
            //LoggerProxy.info("callBackDcVerifyData",LOGGER,"url:{},param:{}",url,jsStr);
            SyncHTTPRemoteAPI.postJson(url,jsStr,60000);
            LoggerProxy.info("callBackDcVerifyData",LOGGER,"success,param:{}",jsStr);
        }catch (Exception e){
            LoggerProxy.error("callBackDcVerifyData",LOGGER, String.format("param:%s,error:", jsStr),e);
        }
    }

    public JSONObject sendQuotaResult(Event event, String strategyType, Object lineResult) {
        LoggerProxy.info("sendQuotaResult", LOGGER, "userKey={}, loanKey={}, eventCode={}", event.getUserKey(), event.getLoanKey(), event.getEventCode());
        QuotaMessageVO quotaMessageVO = QuotaMessageVO.toQuotaObject(event, strategyType);
        String response = sendHttpRequestToQuotaCenter(riskQuotaUrl + "/quota/api/handleQuota", quotaMessageVO);

        JSONObject responseJson = JSONObject.parseObject(response);
        int status = responseJson.getInteger("status");
        if (status != STATUS_SUCCESS) {
            LoggerProxy.error("sendQuotaResult", LOGGER
                    , "userKey={}, loanKey={}, eventCode={}, sendQuotaResult failed:{}"
                    , event.getUserKey(), event.getLoanKey(), event.getEventCode(), responseJson.getString("message"));
            throw new RiskRuntimeException(RetCodeEnum.FAILED, responseJson.getString("message"));
        }

        JSONObject processedLineResult = responseJson.getJSONObject("data");
        // 取processedLineResult数据更新到newLineResult中
        JSONObject newLineResult = buildNewLineResult(lineResult, processedLineResult);
        compareData(lineResult, newLineResult, event.getEventCode());

        Boolean isSendReasonCodeToDw = NacosClientAdapter.getBooleanConfig("isSendReasonCodeToDw", false);
        if (isSendReasonCodeToDw) {
            try {
                LoggerProxy.info("sendReasonCode", LOGGER, "userKey={}, loanKey={}, eventCode={}", event.getUserKey(), event.getLoanKey(), event.getEventCode());
                //发送消息到数仓
                QuotaDwMessageVO quotaDwMessageVO = QuotaDwMessageVO.toQuotaDwObject(event);
                // 添加下划线key
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(quotaDwMessageVO));

                // 添加下划线key
                jsonObject = JsonKeyFormatUtil.mergeResults(jsonObject
                        , JsonKeyFormatUtil.convertToCamelCase(JSON.parseObject(JSON.toJSONString(jsonObject))), event.getUserKey());
                quotaDwSender.sendWithPartition(JSONObject.toJSONString(jsonObject), quotaDwMessageVO.getUserKey());
            } catch (Exception e) {
                LoggerProxy.error("sendReasonCode", LOGGER, "userKey={}, loanKey={}, eventCode={}, sendReasonCode failed:", event.getUserKey(), event.getLoanKey(), event.getEventCode(), e);
            }
        }

        return newLineResult;
    }

    public boolean isUpdateVerifyResultRequired(String userKey) {
        String url = String.format(riskQuotaUrl + "/quota/api/cache/hasKey/offline_batch_user_%s", userKey);
        int retryCount = 3;
        for (int i = 0; i < retryCount; i++) {
            try {
                String response = SyncHTTPRemoteAPI.get(url, TIMEOUT_MILLI_SECOND);
                JSONObject responseJson = JSONObject.parseObject(response);
                return responseJson.getBoolean("hasKey");
            } catch (Exception e) {
                LoggerProxy.error("isUpdateVerifyResultRequired"
                        , LOGGER, "Attempt {} failed: {}", i + 1, e.getMessage());
                if (i == retryCount - 1) {
                    throw new RiskRuntimeException(RetCodeEnum.FAILED, e);
                }
                SystemUtil.threadSleep(100);
            }
        }
        return false;
    }


    public JSONObject buildNewLineResult(Object originalLineResult, JSONObject processedLineResult) {
        try {
            Map<String, Object> originalMap = JSON.parseObject(JSON.toJSONString(originalLineResult)
                    , new TypeReference<Map<String, Object>>() {
                    });
            Map<String, Object> processedMap = JSON.parseObject(JSON.toJSONString(processedLineResult)
                    , new TypeReference<Map<String, Object>>() {
                    });

            Map<String, Object> newLineResultMap = new HashMap<>(originalMap);

            JsonKeyFormatUtil.setWithBothFormats(newLineResultMap::put, "creditLine", processedMap.get("totalAmount"));
            JsonKeyFormatUtil.setWithBothFormats(newLineResultMap::put, "availLine", processedMap.get("availAmount"));
            JsonKeyFormatUtil.setWithBothFormats(newLineResultMap::put, "utilLine", processedMap.get("usedAmount"));
            JsonKeyFormatUtil.setWithBothFormats(newLineResultMap::put, "loanLine", processedMap.get("totalAmount"));
            JsonKeyFormatUtil.setWithBothFormats(newLineResultMap::put, "loanAvailLine", processedMap.get("availAmount"));
            JsonKeyFormatUtil.setWithBothFormats(newLineResultMap::put, "loanActualLine", processedMap.get("actualAmount"));
            JsonKeyFormatUtil.setWithBothFormats(newLineResultMap::put, "loanUtilLine", processedMap.get("usedAmount"));
            JsonKeyFormatUtil.setWithBothFormats(newLineResultMap::put, "loanRate", processedMap.get("loanRate"));
            JsonKeyFormatUtil.setWithBothFormats(newLineResultMap::put, "accountStatus", processedMap.get("accountStatus"));
            JsonKeyFormatUtil.setWithBothFormats(newLineResultMap::put, "lineAssignTime", processedMap.get("lineAssignTime"));
            JsonKeyFormatUtil.setWithBothFormats(newLineResultMap::put, "ext1", JSON.toJSONString(processedMap.get("ext1")));
            JsonKeyFormatUtil.setWithBothFormats(newLineResultMap::put, "loanPeriod", processedMap.get("loanPeriod"));
            // 是否使用额度中心返回的api额度信息
            Boolean isUseQuotaApiAmountEnabled = NacosClientAdapter.getBooleanConfig("isUseQuotaApiAmountEnabled", false);
            if (isUseQuotaApiAmountEnabled) {
                if (processedMap.containsKey("apiTotalAmount")) {
                    JsonKeyFormatUtil.setWithBothFormats(newLineResultMap::put, "apiLine", processedMap.get("apiTotalAmount"));
                }
                if (processedMap.containsKey("apiUsedAmount")) {
                    JsonKeyFormatUtil.setWithBothFormats(newLineResultMap::put, "apiUtilLine", processedMap.get("apiUsedAmount"));
                }
                if (processedMap.containsKey("apiAvailAmount")) {
                    JsonKeyFormatUtil.setWithBothFormats(newLineResultMap::put, "apiAvailLine", processedMap.get("apiAvailAmount"));
                }
            }

            // newLineResultMap取出reasonCode，然后再取出processedMap中的touchCode
            Map<String, Object> reasonCode = (Map<String, Object>) newLineResultMap.get("reasonCode");
            Map<String, Object> touchCode = (Map<String, Object>) processedMap.get("touchCode");
            Map<String, Object> newReasonCode = new HashMap<>();
            for (Map.Entry<String, Object> entry : touchCode.entrySet()) {
                Map<String, Object> newEntry = new HashMap<>();
                newEntry.put("tag", entry.getValue());
                newReasonCode.put(entry.getKey(), newEntry);
            }
            reasonCode.put("events", newReasonCode);

            List<Map<String, Object>> periodLineRates = (List<Map<String, Object>>) processedMap.get("periodLineRates");
            List<Map<String, Object>> newPeriodLineRates = new ArrayList<>();
            for (Map<String, Object> rate : periodLineRates) {
                Map<String, Object> newRate = new HashMap<>();
                // newRate.put("rate_yn", rate.get("rate")); // 同rate
                newRate.put("min", rate.get("min"));
                newRate.put("max", rate.get("max"));
                newRate.put("period", rate.get("period"));
                newRate.put("rate", rate.get("rate"));
                newRate.put("type", "loan");
                // 增加临时费率相关字段
                if (rate.get("tmp_rate_end_time") != null) {
                    newRate.put("tmp_rate_end_time", rate.get("tmp_rate_end_time"));
                }
                if (rate.get("tmp_rate") != null) {
                    newRate.put("tmp_rate", rate.get("tmp_rate"));
                }
                newPeriodLineRates.add(newRate);
            }
            JsonKeyFormatUtil.setWithBothFormats(newLineResultMap::put, "periodLineRate", JSON.toJSONString(newPeriodLineRates));

            // apiPeriodLineRate返回给业务方字段需要转换成字符串，后续使用额度中心返回的apiPeriodLineRate
            Object apiPeriodLineRateObj = newLineResultMap.get("apiPeriodLineRate");
            if(null != apiPeriodLineRateObj){
                if (apiPeriodLineRateObj instanceof JSONArray) {
                    JSONArray apiPeriodLineRate = (JSONArray) apiPeriodLineRateObj;
                    JsonKeyFormatUtil.setWithBothFormats(newLineResultMap::put, "apiPeriodLineRate", JSON.toJSONString(apiPeriodLineRate));
                    if (isUseQuotaApiAmountEnabled) {
                        // 目前只有apiVerify、haoHuanAmountLend、haoHuanAmpuntRepay三个事件涉及api额度变更
                        // apiVerify策略直接输出apiPeriodLineRate，其他事件均为""
                        if  (processedMap.containsKey("apiPeriodLineRates")) {
                            JsonKeyFormatUtil.setWithBothFormats(newLineResultMap::put, "apiPeriodLineRate", JSON.toJSONString(processedMap.get("apiPeriodLineRates")));
                        }
                    }
                }
            }

            return new JSONObject(newLineResultMap);
        } catch (Exception e) {
            LoggerProxy.error("buildNewLineResult", LOGGER, "Error building new line result: ", e);
            throw new RiskRuntimeException(RetCodeEnum.FAILED, e);
        }
    }

    public String sendHttpRequestToQuotaCenter(String url, Object requestObject) {
        String requestStr = JSONObject.toJSONString(requestObject);
        int retryCount = 3;
        for (int i = 0; i < retryCount; i++) {
            try {
                return SyncHTTPRemoteAPI.postJson(url, requestStr, TIMEOUT_MILLI_SECOND);
            } catch (Exception e) {
                LoggerProxy.error("sendHttpRequestToQuotaCenter", LOGGER
                        , "Attempt {} failed: {}", i + 1, e.getMessage());
                if (i == retryCount - 1) {
                    throw new RiskRuntimeException(RetCodeEnum.FAILED, e);
                }
                SystemUtil.threadSleep(100);
            }
        }
        return null;
    }

    public void compareData(Object originalLineResult, JSONObject processedLineResult, String eventCode) {
        Boolean isCompareDataEnabled = NacosClientAdapter.getBooleanConfig("isCompareDataEnabled", true);
        if (!isCompareDataEnabled) {
            LoggerProxy.info("compareData", LOGGER, "Data comparison is disabled by configuration.");
            return;
        }

        // 指定事件才需要对比
        String noCompareDataEventList = NacosClientAdapter.getStringConfig("noCompareDataEventList", "");
        List<String> noCompareDataEvents = Arrays.asList(noCompareDataEventList.split(","));
        if (noCompareDataEvents.contains(eventCode)) {
            return;
        }

        try {
            Map<String, Object> originalMap = JSON.parseObject(JSON.toJSONString(originalLineResult), new TypeReference<Map<String, Object>>() {});
            Map<String, Object> processedMap = JSON.parseObject(JSON.toJSONString(processedLineResult), new TypeReference<Map<String, Object>>() {});

            MapDifference<String, Object> difference = Maps.difference(originalMap, processedMap);
            Map<String, MapDifference.ValueDifference<Object>> entriesDiffering = difference.entriesDiffering();
            List<Map<String, Object>> diffList = Lists.newArrayList();
            for (Map.Entry<String, MapDifference.ValueDifference<Object>> entry : entriesDiffering.entrySet()) {
                MapDifference.ValueDifference<Object> value = entry.getValue();
                String key = entry.getKey();

                if (key.equals("periodLineRate") || key.equals("ext1") || key.equals("apiPeriodLineRate")) {
                    List<Map<String, Object>> leftList = JSON.parseObject(value.leftValue().toString(), new TypeReference<List<Map<String, Object>>>() {});
                    List<Map<String, Object>> rightList = JSON.parseObject(value.rightValue().toString(), new TypeReference<List<Map<String, Object>>>() {});
                    if ((key.equals("periodLineRate")|| key.equals("apiPeriodLineRate")) && arePeriodLineRatesEqual(leftList, rightList)) {
                        continue;
                    }
                    if (key.equals("ext1") && areExt1Equal(leftList, rightList)) {
                        continue;
                    }
                } else if (key.equals("loanActualLine")) {
                    double leftValue = Double.parseDouble(String.valueOf(value.leftValue()));
                    double rightValue = Double.parseDouble(String.valueOf(value.rightValue()));
                    if (leftValue == 0 && rightValue < 0) {
                        continue;
                    }
                    if (areValuesEqual(value.leftValue(), value.rightValue())) {
                        continue;
                    }
                } else if (areValuesEqual(value.leftValue(), value.rightValue())) {
                    continue;
                }
                Map<String, Object> diff = new HashMap<>();
                diff.put("key", entry.getKey());
                diff.put("leftValue", value.leftValue());
                diff.put("rightValue", value.rightValue());
                diffList.add(diff);
            }

            Map<String, Object> entriesOnlyOnLeft = difference.entriesOnlyOnLeft();
            Map<String, Object> entriesOnlyOnRight = difference.entriesOnlyOnRight();

            if (!diffList.isEmpty() || !entriesOnlyOnLeft.isEmpty() || !entriesOnlyOnRight.isEmpty()) {
                diffList.forEach(diff -> LoggerProxy.info("compareData", LOGGER
                        , "Key: {}, Left Value: {}, Right Value: {}"
                        , diff.get("key"), diff.get("leftValue"), diff.get("rightValue")));

                for (Map.Entry<String, Object> entry : entriesOnlyOnLeft.entrySet()) {
                    LoggerProxy.info("compareData", LOGGER
                            , "Key only on left: {}, Value: {}", entry.getKey(), entry.getValue());
                }

                for (Map.Entry<String, Object> entry : entriesOnlyOnRight.entrySet()) {
                    LoggerProxy.info("compareData", LOGGER
                            , "Key only on right: {}, Value: {}", entry.getKey(), entry.getValue());
                }
            }
        } catch (Exception e) {
            LoggerProxy.error("compareData", LOGGER, "Error comparing data: ", e);
        }
    }

    private boolean areValuesEqual(Object leftValue, Object rightValue) {
        if (leftValue == null || rightValue == null) {
            return leftValue == rightValue;
        }
        try {
            Double leftDouble = Double.valueOf(String.valueOf(leftValue));
            Double rightDouble = Double.valueOf(String.valueOf(rightValue));
            return leftDouble.equals(rightDouble);
        } catch (NumberFormatException e) {
            return leftValue.equals(rightValue);
        }
    }

    private boolean arePeriodLineRatesEqual(List<Map<String, Object>> leftList, List<Map<String, Object>> rightList) {
        // 额度中心返回 那么需要打印不一致
        if(rightList.isEmpty()) return false;

        for (int i = 0; i < rightList.size(); i++) {
            // 线上额度策略结果为空 那么打印
            if(leftList.isEmpty()) return false;

            Map<String, Object> leftMap = leftList.get(i);
            Map<String, Object> rightMap = rightList.get(i);

            // 获取期数 9期不对比
            if (leftMap.get("period").equals(9) || rightMap.get("period").equals(9)) {
                continue;
            }

            for (String key : rightMap.keySet()) {
                if (!areValuesEqual(leftMap.get(key), rightMap.get(key))) {
                    return false;
                }
            }
        }
        return true;
    }

    private boolean areExt1Equal(List<Map<String, Object>> leftList, List<Map<String, Object>> rightList) {
        // 额度中心返回额 那么需要打印不一致
        if(rightList.isEmpty()) return false;

        for (int i = 0; i < rightList.size(); i++) {
            // 线上额度策略结果为空 那么打印
            if(leftList.isEmpty()) return false;

            Map<String, Object> leftMap = leftList.get(i);
            Map<String, Object> rightMap = rightList.get(i);

            // type=2 不对比
            if (leftMap.get("type").equals(2)) {
                continue;
            }

            for (String key : rightMap.keySet()) {
                if (!areValuesEqual(leftMap.get(key), rightMap.get(key))) {
                    return false;
                }
            }
        }
        return true;
    }
}