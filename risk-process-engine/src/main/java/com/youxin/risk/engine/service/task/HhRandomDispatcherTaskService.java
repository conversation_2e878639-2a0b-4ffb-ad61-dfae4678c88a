package com.youxin.risk.engine.service.task;


import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.commons.constants.EventVariableKeyEnum;
import com.youxin.risk.commons.constants.PointConstant;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.activiti.runtime.task.AbstractTaskService;
import com.youxin.risk.metrics.MetricsAPI;
import com.youxin.risk.metrics.enums.MetricsOpType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * 随机分流
 *
 * <AUTHOR>
 */
public class HhRandomDispatcherTaskService extends AbstractTaskService {
    private static final Logger LOGGER = LoggerFactory.getLogger(HhRandomDispatcherTaskService.class);
    public static final String IRR24 = "isIrr24";
    public static final String FL = "isFl";
    public static final String IRR24_SPLIT_FLOW = "irr24.split.flow";
    public static final String FL_SPLIT_FLOW = "fl.split.flow";

    @Override
    public void execute(ProcessContext processContext) {
        Event event = processContext.getEvent();
        boolean executeIrr24Process = false;
        event.set(IRR24, false);
        boolean executeFlProcess = false;
        event.set(FL, false);
        try {
            int irr24SplitFlow = getSplitFlow(IRR24_SPLIT_FLOW);
            int flSplitFlow = getSplitFlow(FL_SPLIT_FLOW);
            int random = Math.abs(event.getUserKey().hashCode()) % 100;
            event.set("serviceRandomNumber", getRandomNumber());
            if (irr24SplitFlow >= 0 && irr24SplitFlow < 100 && flSplitFlow >= 0 && flSplitFlow < 100) {
                if (random < irr24SplitFlow) {
                    if (!isHaoleCard(event)) {
                        LoggerProxy.info(LOGGER, "executeIrr24process, splitFlow={}, loanKey={}", irr24SplitFlow, event.getLoanKey());
                        executeIrr24Process = true;
                        event.set(IRR24, true);
                        point(event, true, false);
                    }
                } else if (random <= irr24SplitFlow + flSplitFlow) {
                    LoggerProxy.info(LOGGER, "executeFlProcess, splitFlow={}, loanKey={}", flSplitFlow, event.getLoanKey());
                    executeFlProcess = true;
                    event.set(FL, true);
                    point(event, false, true);
                } else {
                    LoggerProxy.info(LOGGER, "notExecuteIrr24ProcessAndFlProcess, splitFlow={}", irr24SplitFlow + flSplitFlow);
                    point(event, false, false);
                }
            }
            JSONObject processInfo = new JSONObject();
            processInfo.put("executeIrr24Process", executeIrr24Process);
            processInfo.put("executeFlProcess", executeFlProcess);
            event.getDataVo().put("processInfo", processInfo);
        } catch (Exception e) {
            event.set(IRR24, false);
            LoggerProxy.error("hhRandomDispatcherTaskServiceError", LOGGER, "", e);
        }
    }

    private int getSplitFlow(String key) {
        try {
            return ApolloClientAdapter.getIntConfig(ApolloNamespaceEnum.ENGINE_SPACE, key, 0);
        } catch (Exception e) {
            LoggerProxy.error("getSplitFlowError", LOGGER, "", e);
        }
        return 0;
    }

    // 生成一个10位的随机数，用作数据源分流
    private String getRandomNumber() {
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 10; i++) {
            sb.append(random.nextInt(10));
        }
        return sb.toString();
    }

    private boolean isHaoleCard(Event event) {
        try {
            String userAdditionalInfo = event.getString("userAdditionalInfo");
            Boolean isHaoleCardRight = JSONObject.parseObject(userAdditionalInfo).getBoolean("isHaoleCardRight");
            if (isHaoleCardRight != null) {
                return isHaoleCardRight;
            }
            return false;
        } catch (Exception e) {
            LoggerProxy.error("parseHaoleCardError", LOGGER, "loankey={}", event.getLoanKey(), e);
        }
        return false;
    }

    private void point(Event event, boolean isIrr24, boolean isFl) {
        try {
            Map<String, String> tags = new HashMap<>(2);
            tags.put(IRR24, String.valueOf(isIrr24));
            tags.put(FL, String.valueOf(isFl));
            tags.put(EventVariableKeyEnum.eventCode.name(), event.getEventCode());
            MetricsAPI.point(PointConstant.PE_HAO_HUAN_VERIFY_IRR24_POINT, tags,
                    1, false, MetricsOpType.timer);
        } catch (Exception e) {
            LoggerProxy.error("irr24AndFlPointError", LOGGER, "", e);
        }
    }
}
