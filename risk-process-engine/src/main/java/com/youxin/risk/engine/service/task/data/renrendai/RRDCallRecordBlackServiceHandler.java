package com.youxin.risk.engine.service.task.data.renrendai;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.utils.RRDDataVoUtils;
import com.youxin.risk.engine.service.task.data.ConfigurableDataHandler;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/12/10 22:26
 */
@Service
public class RRDCallRecordBlackServiceHandler extends ConfigurableDataHandler {
    @Override
    public Map<String, Object> buildRequestParams(Event event, String dataCode, String nodeCode) {
        Map<String, Object> params = super.buildRequestParams(event, dataCode, nodeCode);
        JSONObject userBasicInfo = RRDDataVoUtils.getUserBasicInfo(event);
        params.put("jobId", userBasicInfo.getString("callHistoryJobId"));
        return params;
    }
}
