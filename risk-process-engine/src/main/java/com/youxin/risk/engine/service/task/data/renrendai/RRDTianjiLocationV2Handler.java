package com.youxin.risk.engine.service.task.data.renrendai;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.constants.SourceSystemEnum;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.utils.RRDDataVoUtils;
import com.youxin.risk.engine.service.task.data.ConfigurableDataHandler;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/09/09 14:19
 */
@Service
public class RRDTianjiLocationV2Handler extends ConfigurableDataHandler {
    @Override
    public Map<String, Object> buildRequestParams(Event event, String dataCode, String nodeCode) {
        Map<String, Object> params = super.buildRequestParams(event, dataCode, nodeCode);
        JSONObject userBasicInfo = RRDDataVoUtils.getUserBasicInfo(event);
        params.put("systemId", SourceSystemEnum.REN_REN_DAI.name());
        params.put("mobile", userBasicInfo.getString("reserveMobile"));
        return params;
    }
}
