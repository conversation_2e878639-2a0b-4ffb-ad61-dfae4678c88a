package com.youxin.risk.engine.service.task.data.haohuan;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.engine.service.task.data.BaseDataHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class PenYuanServiceHandler extends BaseDataHandler {

    private final Logger logger = LoggerFactory.getLogger(PenYuanServiceHandler.class);

    @Override
    public Map<String, Object> buildRequestParams(Event event, String dataCode, String nodeCode) {
        Map<String, Object> params = super.buildRequestParams(event, dataCode, nodeCode);

        try {
            Map<String, Object> dataVo = event.getDataVo();
            JSONObject amountEc = JSONObject.parseObject(dataVo.get("AMOUNT_EC").toString());
            // ocr校验问题 暂时去掉
            // params.put("name", amountEc.get("name"));
            params.put("regNum", amountEc.get("regNum"));
        } catch (Exception e) {
            logger.error("get params error: loanKey=" + event.getLoanKey(), e);
        }
        return params;
    }
}
