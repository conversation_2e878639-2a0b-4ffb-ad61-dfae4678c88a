package com.youxin.risk.engine.cacheloader;

import com.youxin.risk.commons.cacheloader.BaseCacheLoader;
import com.youxin.risk.commons.model.ProcessDefinition;
import com.youxin.risk.engine.activiti.runtime.ProcessEngines;
import com.youxin.risk.commons.cacheloader.service.ProcessDefinitionCandidateService;
import com.youxin.risk.commons.constants.ConfigTableEnum;
import com.youxin.risk.commons.model.ProcessDefinitionCandidate;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/3/11 10:40
 */
public class ProcessDefinitionCandidateCacheLoader extends BaseCacheLoader {
    @Resource
    private ProcessDefinitionCandidateService processDefinitionCandidateService;

    @Override
    @Scheduled(fixedDelay = 10000)
    public void load() {
        super.load(ConfigTableEnum.admin_process_definition_candidate.toString());
    }

    @Override
    protected int loadPart() {
        //todo ，子流程更新后，应该更新依赖其的主流程，简便处理，直接更新所有的流程
        loadAll();
        return 0;
    }

    @Override
    protected void loadAll() {
        List<ProcessDefinitionCandidate> processDefinitionCandidates = processDefinitionCandidateService.selectAll();
        if (CollectionUtils.isEmpty(processDefinitionCandidates)) {
            return;
        }
        //子流程
        registerProcessList(processDefinitionCandidates, true);

        //主流程
        registerProcessList(processDefinitionCandidates, false);
    }

    private void registerProcessList(List<ProcessDefinitionCandidate> allProcessDefinitionList, boolean isSub) {
        List<ProcessDefinitionCandidate> processDefList =
                allProcessDefinitionList.stream().filter(e -> Boolean.compare(isSub, e.getSubProcess()) == 0)
                        .collect(Collectors.toList());
        for (ProcessDefinitionCandidate pd : processDefList) {
            try {
                ProcessEngines.registerProcessEngine(pd.getProcessContent(), pd.getProcessJson(), pd.getProcessDefId());
            } catch (Exception e) {
                LoggerProxy.error("registerProcessError", logger,
                        "processDefId=" + pd.getProcessDefId(), e);
            }
        }
    }
}
