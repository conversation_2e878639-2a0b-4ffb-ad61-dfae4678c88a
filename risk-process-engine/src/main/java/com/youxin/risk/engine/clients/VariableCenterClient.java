package com.youxin.risk.engine.clients;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.engine.vo.VariableCenterResponse;
import com.youxin.risk.engine.vo.VariableRequestParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class VariableCenterClient  {
    private final static Logger LOGGER = LoggerFactory.getLogger(VariableCenterClient.class);


    @Value("${variable.center.service.url}")
    private String variableCenterServiceUrl;

    private final static int HTTP_REQUEST_TIME_OUT = 30000;

    /**
     * 请求变量中心 计算变量
     *      * 接口链接：http://rrdloan-rap.renrendai.com/organization/repository/editor?id=46&mod=324
     * @param variableRequestParam
     * @return
     */
    public VariableCenterResponse requestVariableCenter(VariableRequestParams variableRequestParam) {

        String url = variableCenterServiceUrl + "/api/variable/process/v2/" + variableRequestParam.getEventCode();
        String responseStr = null;
        try {
            responseStr = SyncHTTPRemoteAPI.postJson(url, JSON.toJSONString(variableRequestParam),
                    HTTP_REQUEST_TIME_OUT);
        } catch (Exception e) {
            return VariableCenterResponse.error(e.getMessage());
        }
        return VariableCenterResponse.build(responseStr);
    }


}