package com.youxin.risk.engine.service.task;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.exception.RiskRuntimeException;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.activiti.runtime.task.AbstractTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 重审跑批打标事件定制节点
 */
@Service
public class HhRetrialTagTaskService extends AbstractTaskService {

    private static final Logger logger = LoggerFactory.getLogger(HhRetrialTagTaskService.class);

    @Value("${dc.inside.url}")
    private String dcInsideUrl;

    @Override
    public void execute(ProcessContext processContext) {
        Event event = processContext.getEvent();
        Map<String, Object> params = event.getParams();
        Map<String, Object> verifyResult = event.getVerifyResult();
        LoggerProxy.info("HhRetrialTagTaskService", logger, "loanKey={}, params={}", event.getLoanKey());
        this.saveDc(params, verifyResult);
    }

    private void saveDc(Map<String, Object> params, Map<String, Object> verifyResult) {
        HashMap<String, Object> request = new HashMap<>();
        request.put("userKey", params.get("userKey"));
        request.put("loanKey", params.get("loanKey"));
        request.put("userAccount", verifyResult.get("userAccount"));
        request.put("loanLine", verifyResult.get("loanLine"));
        request.put("fixLine", verifyResult.get("fixLine"));
        request.put("tempLine", verifyResult.get("tempLine"));
        request.put("createTime", verifyResult.get("createTime"));
        String response = SyncHTTPRemoteAPI.postJson(dcInsideUrl + "/amountRetrialAudit/submit", JSONObject.toJSONString(request), 10000);
        LoggerProxy.info("AmountRetrialAuditSaveDc", logger, "request={}, response={}", JSONObject.toJSONString(request), response);
        JSONObject json = JSONObject.parseObject(response);
        if (!"0".equals(json.getString("status"))) {
            throw new RiskRuntimeException(RetCodeEnum.FAILED, json.getString("message"));
        }
    }
}
