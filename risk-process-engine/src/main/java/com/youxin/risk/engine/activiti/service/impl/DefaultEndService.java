package com.youxin.risk.engine.activiti.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.commons.dao.engine.VariableAsyncRequestLogMapper;
import com.youxin.risk.commons.constants.EventVariableKeyEnum;
import com.youxin.risk.commons.event.impl.EventService;
import com.youxin.risk.commons.exception.RiskRuntimeException;
import com.youxin.risk.commons.kafkav2.sender.KafkaSyncSender;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.mongo.VerifyResultDataDao;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.service.di.ThirdPartyStatusService;
import com.youxin.risk.commons.utils.ContextUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.MirrorUtil;
import com.youxin.risk.commons.utils.NacosClientAdapter;
import com.youxin.risk.commons.vo.EventVo;
import com.youxin.risk.commons.vo.StrategyVo;
import com.youxin.risk.engine.activiti.constants.NodeTypeEnum;
import com.youxin.risk.engine.activiti.constants.ProcessInstanceStatusEnum;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.activiti.runtime.ProcessEngine;
import com.youxin.risk.engine.activiti.runtime.ProcessEngines;
import com.youxin.risk.engine.activiti.runtime.ProcessInstance;
import com.youxin.risk.engine.activiti.service.EndService;
import com.youxin.risk.engine.clients.RiskFsClient;
import com.youxin.risk.engine.service.EventSerialHandlerSupport;
import com.youxin.risk.engine.service.task.StrategyTaskServiceNew;
import com.youxin.risk.engine.service.task.mirror.MirrorServiceHandler;
import com.youxin.risk.engine.service.impl.ProcessInfoHandler;
import com.youxin.risk.engine.service.impl.RandomHandler;
import com.youxin.risk.engine.service.task.engine.EngineEventMongoService;
import com.youxin.risk.engine.utils.EngineNodeCostPointUtil;
import com.youxin.risk.engine.utils.EventUtils;
import com.youxin.risk.engine.vo.ApiEventStrategyResultForVulcanVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class DefaultEndService implements EndService {

    private Logger logger = LoggerFactory.getLogger(DefaultEndService.class);

    private ThirdPartyStatusService thirdPartyStatusService;
    private EngineEventMongoService engineEventMongoService;
    private VerifyResultDataDao verifyResultDataDao;
    private EventSerialHandlerSupport eventSerialHandlerSupport;
    private ProcessInfoHandler processInfoHandler;
    private RandomHandler randomHandler;
    private VariableAsyncRequestLogMapper variableAsyncRequestLogMapper;
    private KafkaSyncSender apiEventStrategyVariableResultSender;
    private EventService eventService;
    private MirrorServiceHandler mirrorServiceHandler;


    public DefaultEndService(){
        try{
            this.engineEventMongoService = (EngineEventMongoService)ContextUtil.getBean("engineEventMongoService");
            this.verifyResultDataDao = (VerifyResultDataDao) ContextUtil.getBean("verifyResultDataDao");
            this.thirdPartyStatusService = ContextUtil.getBean(ThirdPartyStatusService.class);
            this.eventSerialHandlerSupport = ContextUtil.getBean(EventSerialHandlerSupport.class);
            this.processInfoHandler = ContextUtil.getBean(ProcessInfoHandler.class);
            this.randomHandler = ContextUtil.getBean(RandomHandler.class);
            this.variableAsyncRequestLogMapper = ContextUtil.getBean(VariableAsyncRequestLogMapper.class);
            this.apiEventStrategyVariableResultSender =ContextUtil.getBean("apiEventStrategyVariableResultSender",KafkaSyncSender.class);
            this.eventService = ContextUtil.getBean(EventService.class);
            this.mirrorServiceHandler = ContextUtil.getBean(MirrorServiceHandler.class);
        }catch (Exception e){
            LoggerProxy.error("DefaultEndServiceInitError",logger,e.getMessage(),e);
        }
    }

    @Override
    public void execute(ProcessContext processContext) {
        Event event = processContext.getEvent();
        processContext.setCurrentNodeType(NodeTypeEnum.end);
        processContext.setSystemVarStatus(ProcessInstanceStatusEnum.COMPLETED);
        /** 流程信息处理 */
        processInfoHandler.processInfoHandle(processContext.getEvent());

        try {
            eventSerialHandlerSupport.releaseLock(event);
        }catch (Exception e) {
            logger.error("handleSerialReleaseLock error", e);
            throw new RiskRuntimeException("handleSerialReleaseLock error");
        }
        Map<String, Object> variables = processContext.getVariables();
        try{
            engineEventMongoService.updateEventToMongo(event);
            if (!event.isMirror()) {
                try {
                    String flag = thirdPartyStatusService.delCacheData(JSON.toJSONString(event.getDataId()));
//                    LoggerProxy.info("DefaultEndSGetDataId_finish",logger,"finish = {}",flag);
                }catch (Exception e){
                    LoggerProxy.info("DefaultEndSGetDataId_error",logger,"error",e);
                }
                verifyResultDataDao.updateVerifyResultMongo(EventVo.eventVo4Update(event));
            }
        }catch (Exception e){
            LoggerProxy.error("DefaultEndServiceLogError",logger,"sessionId={}",variables.get("eventForeignKey"), e);
        }
        try {
            //流程镜像处理
            handleProcessMirror(processContext);

            long start = System.currentTimeMillis();
            //删除变量请求日志
            variableAsyncRequestLogMapper.deleteByLoanKey(event.getLoanKey());
            LoggerProxy.info("流程执行结束", logger, "删除变量请求日志，sessionId={},耗时={}", event.getSessionId(),
                    (System.currentTimeMillis() - start));
        } catch (Exception e) {
            LoggerProxy.error("handleProcessMirrorError", logger, "sessionId={},errMsg=",
                    processContext.getEvent().getSessionId(), e);
        }

        try {
            if (("haoHuanVerify").equals(event.getEventCode())) {
                randomHandler.postRandomsToDc(event);
            }
        } catch (Exception e) {
            LoggerProxy.error("extractRandomError:{}-{}",logger, e.getMessage(), e);
        }
        try {
            // check architecture
            boolean checkArchitecture = false;
            if (StringUtils.equals(event.getEventCode(), "haoHuanLendAudit")
                    || StringUtils.equals(event.getEventCode(), "haoHuanLendAuditReloan")) {
                checkArchitecture = true;
            }
            // 需要屏蔽白名单数据，目前仅haoHuanVerify 老流程有特殊处理
            if (StringUtils.equals(event.getEventCode(), "haoHuanVerify")) {
                checkArchitecture = true;
                // 白名单用户不检查返回数据
                Object whiteListUser = event.getParams().get("whiteListUser");
                if (whiteListUser instanceof Boolean) {
                    boolean isWhiteListUser = (Boolean) whiteListUser;
                    if (isWhiteListUser) {
                        checkArchitecture = false;
                        LoggerProxy.error("validateArchitecture_ignored for whitelist: {}",logger, event.getLoanKey());
                    }
                }
            }
            if (checkArchitecture) {
                // 获取nacos配置
                String architectureString = NacosClientAdapter.getStringConfig(ApolloNamespaceEnum.ENGINE_SPACE, "event.architecture", "");
                if (StringUtils.isNotBlank(architectureString)) {
                    JSONObject architecture = JSONObject.parseObject(architectureString);
                    // 获取事件结果
                    Map<String, Object> verifyResultMap = event.getVerifyResult();
                    String verifyResult = (String) verifyResultMap.get("verifyResult");
                    // 没有配置对应的事件或者结果，则跳过
                    if (architecture.containsKey(event.getEventCode())
                            && architecture.getJSONObject(event.getEventCode()).containsKey(verifyResult)) {
                        // 获取对比的结构
                        JSONArray architectureVerifyResult = architecture.getJSONObject(event.getEventCode()).getJSONArray(verifyResult);
                        validateArchitecture(architectureVerifyResult, verifyResultMap, event.getEventCode(), event.getLoanKey());
                    }
                }
            }
        } catch (Exception e) {
            LoggerProxy.error("validateArchitecture_failed:{}-{}",logger, e.getMessage(), e);
        }

        if (!event.isMirror()) {
            EngineNodeCostPointUtil.pointNew(processContext);
            EngineNodeCostPointUtil.buildAndAddProcessNodeInfo(processContext, processContext.getCurrentNodeId());
            EngineNodeCostPointUtil.sendProcessNodeInfo(processContext);
        }
        if (event.isMirror()) {
            //镜像补发逻辑
            resendMissingMirrorSteps(event);
        }
        // api的策略结果发送数仓
        sendApiEventFinalStrategyResultToVulcan(event);

    }

    /**
     * 镜像补发逻辑
     * @param event
     */
    private void resendMissingMirrorSteps(Event event) {
        try {
            //根据流程id,查询一共有哪些步骤
            ProcessEngine processEngine = ProcessEngines.getProcessEngine(event.getProcessDefId());
            Set<String> allStepList = processEngine.getStepList();

            Set<String> notRunStepList = event.getMirrorNotRunSteps(allStepList);
            if (CollectionUtils.isEmpty(notRunStepList)) {
                LoggerProxy.info("镜像补发逻辑", logger, "loankey={},待补发的步骤集合为空，无需补发", event.getLoanKey());
                return;
            }
            LoggerProxy.info("镜像补发逻辑", logger, "loankey={},待补发的步骤集合={}", event.getLoanKey(),
                    JSON.toJSONString(notRunStepList));
            for (String step : notRunStepList) {
                StrategyVo strategyVo = buildStrategyVo(event, step);
                ContextUtil.getBean(StrategyTaskServiceNew.class).sendStrategyExpMessage(strategyVo);
            }
        } catch (Exception e) {
            LoggerProxy.warn("镜像补发逻辑异常", logger, e.getMessage(), e);
        }
    }

    private void sendApiEventFinalStrategyResultToVulcan(Event event) {
        try {
            String eventCode = event.getEventCode();
            if(NacosClientAdapter.getListConfig("sendToVulcanApiEvent",String.class).contains(eventCode)){
                eventService.fireAsynchronous(EventUtils.KAFKA_SEND_EVENT,apiEventStrategyVariableResultSender,ApiEventStrategyResultForVulcanVo.build(event).toJsonString());
            }
        }catch (Exception e){
            LoggerProxy.error("sendApiEventFinalStrategyResultToVulcan", logger,"error:", e);
        }
    }

    protected StrategyVo buildStrategyVo(Event event, String step) {
        StrategyVo strategyVo = new StrategyVo();
        strategyVo.setCreateTime(new Date());
        strategyVo.setUserKey(event.getUserKey());
        strategyVo.setType((String) event.get(EventVariableKeyEnum.strategyType.name()));
        strategyVo.setLoanKey(event.getLoanKey());
        strategyVo.setStep(step);
        strategyVo.setSourceSystem(event.getSourceSystem());
        strategyVo.setEventCode(event.getEventCode());
        strategyVo.setXml(event.getXml());
        String onlineLoanKey = MirrorUtil.getOnline(event.getLoanKey());
        StrategyVo onlineStrategyVo =
                ContextUtil.getBean(RiskFsClient.class).queryStrategyInvokeByLoanKey(onlineLoanKey,
                        step);
        strategyVo.setResult(onlineStrategyVo.getResult());
//        JSONObject onlineResultCopy = JSONObject.parseObject(onlineStrategyVo.getResult());
//        setDefaultValue(onlineResultCopy, "镜像侧无该步骤");
        strategyVo.setMirrorResult(JSON.toJSONString(new HashMap<>()));
        return strategyVo;
    }

    private  void setDefaultValue(Object onlineResultCopy, String defaultValue) {
        if (onlineResultCopy instanceof Map) {
            for (Map.Entry<String, Object> entry : ((Map<String,Object>)onlineResultCopy).entrySet()) {
                if (entry.getValue() instanceof Map || entry.getValue() instanceof List) {
                    setDefaultValue(entry.getValue(), defaultValue);
                } else {
                    entry.setValue(defaultValue);
                }

            }
        }
        if (onlineResultCopy instanceof List) {
            List onlineResultCopy1 = (List) onlineResultCopy;
            for (Object object : onlineResultCopy1) {
                setDefaultValue(object, defaultValue);
            }
        }
    }

    private void handleProcessMirror(ProcessContext processContext) {
        Event event = processContext.getEvent();
        // 事件中引用的流程为子流程时，且该子流程对应的策略类型 有策略镜像时， 发起子流程镜像 不发起流程镜像
        ProcessInstance processInstance = processContext.getProcessInstance();
        mirrorServiceHandler.startMirror(event, processInstance.getProcessDefId());
    }

    // architecture
    private boolean validateArchitecture(JSONArray jsonArray,
                                         Map<String, Object> verifyResultMap,
                                         String eventCode,
                                         String loanKey) {
        for (int i = 0; i < jsonArray.size(); i++) {
            String keyword = jsonArray.getString(i);
            boolean flag = validateIterator(keyword, verifyResultMap);
            if (!flag) {
//                System.out.println("event:"+ eventCode+",loanKey:"+loanKey+", architecture:"+keyword+" is not correct");
                LoggerProxy.error("validateArchitecture", logger,
                        "event:{},loanKey:{}, architecture:{} is not correct", eventCode, loanKey, keyword);
                // 添加企微报警
                sendWarning(eventCode, loanKey, keyword);
            }
        }
        return true;
    }

    /**
     * 递归验证verifyResult中是否存在对应的keyword
     */
    private boolean validateIterator(String keyword, Map<String, Object> verifyResult) {
        // 单词 keyword
        if (!StringUtils.contains(keyword, ".")) {
            return verifyResult.containsKey(keyword);
        }
        // 拼接 keyword
        int dotIndex = keyword.indexOf(".");
        String prefixWord = keyword.substring(0, dotIndex);
        String suffixWord = keyword.substring(dotIndex + 1);
        if (!verifyResult.containsKey(prefixWord)
                || Objects.isNull(verifyResult.get(prefixWord))
                || Objects.equals(verifyResult.get(prefixWord), "")) {
            return false;
        }
        return validateIterator(suffixWord, (Map<String, Object>)verifyResult.get(prefixWord));
    }

    private void sendWarning(String eventCode, String loanKey, String keyword) {
        JSONObject jsonObject=new JSONObject();
        StringBuilder stringBuilder=new StringBuilder();
        jsonObject.put("msgtype","text");
        if (StringUtils.equals(eventCode, "haoHuanVerify")) {
            stringBuilder.append("授信审核");
        } else if (StringUtils.equals(eventCode, "haoHuanLendAudit")) {
            stringBuilder.append("放款审核新客");
        } else {
            stringBuilder.append("放款审核老客");
        }
        stringBuilder.append("回调结果异常：\n")
                .append("loanKey:").append(loanKey).append("\n")
                .append("缺少字段:").append(keyword);
        JSONObject contentJson=new JSONObject();
        contentJson.put("content",stringBuilder.toString());
        jsonObject.put("text",contentJson);
        SyncHTTPRemoteAPI.postJson("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d2f76414-4873-45aa-ad50-a3d544943054",
                jsonObject.toString(), 60000);
    }
}
