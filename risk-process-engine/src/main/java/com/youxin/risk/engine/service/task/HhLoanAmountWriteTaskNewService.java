package com.youxin.risk.engine.service.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.apollo.client.NacosClient;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.commons.constants.ApolloNamespace;
import com.youxin.risk.commons.constants.EventVariableKeyEnum;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.model.ProcessNode;
import com.youxin.risk.commons.tools.lock.LockResult;
import com.youxin.risk.commons.tools.lock.RedisLock;
import com.youxin.risk.commons.utils.JsonKeyFormatUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.MapUtils;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.activiti.runtime.task.AbstractTaskService;
import com.youxin.risk.engine.clients.RiskDcClient;
import com.youxin.risk.engine.service.SplitFlowService;
import com.youxin.risk.engine.service.task.engine.EngineEventMongoService;
import com.youxin.risk.engine.utils.EnvUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.FutureTask;

/**
 * 额度更新节点
 */
@Component(value = "hhLoanAmountWriteTaskNewService")
public class HhLoanAmountWriteTaskNewService extends AbstractTaskService {

    protected static Logger logger = LoggerFactory.getLogger(HhLoanAmountWriteTaskNewService.class);

    @Resource(name = "redisLock")
    private RedisLock redisLock;
    @Resource
    private RiskDcClient riskDcClient;
    @Autowired
    private EngineEventMongoService engineEventMongoService;
    @Resource
    private SplitFlowService splitFlowService;
    @Resource(name = "syncBatchDataToFund")
    private ThreadPoolTaskExecutor syncBatchDataToFundThreadPool;
    @Value("${variable.center.service.url}")
    private String variableCenterServiceUrl;

    private static final String REDIS_KEY_HH_AMOUNT_DEAL = "VERIFY_USER_AMOUNT_";
    private static final int JOB_THRESHOLD_SECONDS = 30;
    private static final String HFQ_TRANSID_KEYWORD = "transId";

    @Override
    public void execute(ProcessContext processContext) {
        long start = System.currentTimeMillis();
        // 加锁,跑策略,check结果
        Event event = processContext.getEvent();
        String lockKey = REDIS_KEY_HH_AMOUNT_DEAL + event.getUserKey();
        LockResult lr = null;
        try {
            lr = redisLock.tryLock(lockKey, JOB_THRESHOLD_SECONDS);
            if (!lr.isSuccess()) {
                LoggerProxy.info("runAmountStrategyLockFailed", logger, "lockKey={}", lockKey);
                return;
            }

            // check transId, 异常重试流程
            // transId不存在,赋值sessionId
            if (null == event.getParams().get(HFQ_TRANSID_KEYWORD)) {
                event.getParams().put(HFQ_TRANSID_KEYWORD, event.getSessionId());
            }

            // 放款额度、还款额度事件取verifyResult，提额事件取原始结果
            Map<String, Object> verifyResult = transformVerifyResult(event, processContext);

            ProcessNode processNode = splitFlowService.getProcessNode(processContext);
            // 发送额度信息到额度中心
            JSONObject newLineResult = riskDcClient.updateQuotaAmount(event, processNode, verifyResult);
            event.setVerifyResult(newLineResult);

            // 通知黑卡和融担
            notifyFund(event, processNode);
            // 放款额度事件使用
            this.resetStrategyTypeIfNeed(processContext);

            engineEventMongoService.updateEventToMongo(event);
            LoggerProxy.info("updateAmountNewSuccess", logger, "userKey={},strategyType={}",
                    event.getUserKey(), event.getString("strategyType"));
        } finally {
            // 解锁
            if (lr != null && lr.isSuccess()) {
                redisLock.releaseLock(lockKey, lr.getLockId());
            }
        }
    }

    // todo 业务方兼容了下划线后，都使用event.getVerifyResult()
    private static Map<String, Object> transformVerifyResult(Event event, ProcessContext processContext) {
        String fromEventVerifyResult = processContext.getAttribute("fromEventVerifyResult");
        if("true".equals(fromEventVerifyResult)){
            return event.getVerifyResult(); // 因为合并问题，originResult中没有合并后的数据
        }
        // 兼容原有返回格式,返回业务方额度信息只第一层key转驼峰
        JSONObject originalResult = JSON.parseObject(event.getVerifyResult().get(EventVariableKeyEnum.originResult.name()).toString());
        JSONObject verifyOriResult = MapUtils.underLineKeyToCamelKeyV2(originalResult);
        return JsonKeyFormatUtil.mergeResults(verifyOriResult, originalResult);
    }



    private void resetStrategyTypeIfNeed(ProcessContext processContext) {
        String resetStrategyType = processContext.getAttribute("resetStrategyType");
        Event event = processContext.getEvent();
        String oldStrategyType = event.getString("strategyType");
        if(StringUtils.isNotBlank(resetStrategyType) && !StringUtils.equals(oldStrategyType,resetStrategyType)){
            // 添加下划线key
            JsonKeyFormatUtil.setWithBothFormats(event::set, "strategyType"
                    , resetStrategyType
                    , event.getUserKey());
        }
    }

    private void notifyFund(Event event, ProcessNode processNode) {
        // 处理跑批结果
        if (event.isBatch()) {
            // 判断是否配置的配置事件
            List<String> notifyEventCodeList = ApolloClientAdapter.getListConfig(
                    ApolloNamespaceEnum.ENGINE_SPACE,
                    "engine.eventcode.notify.fund", String.class);
            String eventCode = event.getEventCode();
            if (CollectionUtils.isNotEmpty(notifyEventCodeList) && notifyEventCodeList.contains(eventCode)) {
                String log = NacosClient.getByNameSpace(ApolloNamespace.engineSpace, "log", "0");
                if ("5".equals(log)){
                    LoggerProxy.info("startNotifyFund",logger,"userKey = {}", event.getUserKey());
                }
                // 线程池处理，判断用户是新老客户，新用户推黑卡，老用户推融担
                syncBatchDataToFundThreadPool.submit(new FutureTask<>(new AmountNotifyTaskService(EnvUtil.getTmpEvent(event), processNode,variableCenterServiceUrl)));
            }
        }
    }
}
