package com.youxin.risk.engine.service.task.data.haohuan;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.cache.CacheApi;
import com.youxin.risk.commons.model.DiService;
import com.youxin.risk.commons.model.DiServiceInput;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.engine.service.task.data.BaseDataHandler;
import com.youxin.risk.engine.service.task.data.ResolveParamService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


/**
 * @description: 进件归因数据处理类
 * @author: juxiang
 * @create: 2024-01-08 14:49
 **/
@Service
public class AttributionInfoVarDataHandler extends BaseDataHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(AttributionInfoVarDataHandler.class);
    @Resource
    private ResolveParamService resolveParamService;

    @Override
    public Map<String, Object> buildRequestParams(Event event, String dataCode, String nodeId) {
        Map<String, Object> params = new ConcurrentHashMap<>();
        Map<String, Object> eventParams = new ConcurrentHashMap<>(event.getParams());
        params.put("userKey",event.getUserKey());
        params.put("loanKey",event.getLoanKey());
        params.put("sourceSystem",event.getSourceSystem());
        super.buildBaseRequestParams(event,dataCode,nodeId,params);
        /*
          入参表达式
         */
        Map<String, Object> resolveParams = resolveParamService.resolveParams(event, dataCode, nodeId);
        if (!ObjectUtils.isEmpty(resolveParams) && resolveParams.size()!=0) {
            params.putAll(resolveParams);
        }
        params.put("serviceCode", dataCode);
        /**
         * 添加常量表达式
         */
        DiService diService = CacheApi.getDiService(getServiceCode(dataCode));
        List<DiServiceInput> inputs = diService.getInputs();
        if (CollectionUtils.isNotEmpty(inputs)) {
            inputs.forEach(input->{
                params.put(input.getInputCode(),input.getInputValue());
            });
        }
        Map<String, Object> dataInput = new ConcurrentHashMap<>((Map<String, Object>) eventParams.get("dataInput"));
        params.put("dataInput", dataInput);
        return params;
    }
}
