package com.youxin.risk.engine.scheduler;

import com.youxin.risk.commons.utils.EnvUtil;
import com.youxin.risk.engine.activiti.constants.ProcessInstanceStatusEnum;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.activiti.service.persist.ProcessInstanceService;
import com.youxin.risk.commons.model.EngineEvent;
import com.youxin.risk.commons.model.EngineProcessInstanceModel;
import com.youxin.risk.commons.scheduler.BaseScheduler;
import com.youxin.risk.commons.service.engine.EngineEventService;
import com.youxin.risk.commons.utils.LogUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.engine.service.ProcessWakeupService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-11-05
 */
public class EngineEventSuspendedTaskScheduler extends BaseScheduler implements InitializingBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(EngineEventSuspendedTaskScheduler.class);

    @Value("#{${constants.scheduler.suspended.lower.bound.offset}}")
    private long lowerBoundOffset;

    @Value("#{${constants.scheduler.suspended.upper.bound.offset}}")
    private long upperBoundOffset;

    @Value("#{${constants.scheduler.suspended.limit.bound.offset}}")
    private long limitBoundOffset;

    @Resource
    private EngineEventService engineEventService;

    @Resource
    ProcessInstanceService processInstanceService;

    @Resource
    private ProcessWakeupService processWakeupService;

    private String logClassName = "EngineEventSuspendedTask";

    @Override

    protected void process() {
        if(EnvUtil.isBatch()){
            dealBatchEvent();
        }else {
            dealNotBatchEvent();
        }
    }

    private void dealBatchEvent() {
        LoggerProxy.threadLog(LOGGER, this, "dealBatchEvent");
        long currentTimeMillis = System.currentTimeMillis();
        //3小时以前
//        Date fromTime = new Date(currentTimeMillis - limitBoundOffset);
        //2分钟以前
        Date toTime = new Date(currentTimeMillis - upperBoundOffset);
        //查询limit 500 条
        List<EngineEvent> engineEventList = engineEventService.selectBatchEventSuspendedCreateTimeBetween(null, toTime, sqlLimit);
        LoggerProxy.info("dealBatchEvent", LOGGER, "find batch suspended count: {}", engineEventList.size());
        handleEvents(engineEventList);
        LoggerProxy.info("dealBatchEvent", LOGGER, "deal batch suspended count: {}", engineEventList.size());
    }

    private void dealNotBatchEvent() {
        LoggerProxy.threadLog(LOGGER, this, "process");
        List<EngineEvent> events = findTimeoutSuspendedEngineEvents(true);
        LoggerProxy.info("processIn", LOGGER, "find events count: {}", events.size());
        handleEvents(events);
        LoggerProxy.info("executeCount", LOGGER, "important: {}", executeCount);
        if (executeCount < executeLimit) {
            LoggerProxy.info("retryNonImportant", LOGGER, "");
            events = findTimeoutSuspendedEngineEvents(false);
            LoggerProxy.info("retryNonImportant", LOGGER, "find events count: {}", events.size());
            handleEvents(events);
            LoggerProxy.info("executeCount", LOGGER, "total: {}", executeCount);
        }
    }

    private void handleEvents(List<EngineEvent> events) {
        for (EngineEvent event : events) {
            if (executeCount > executeLimit) {
                LoggerProxy.warn( "excessExecuteLimit", LOGGER, "{}", executeLimit);
                return;
            }
            handleSuspendedEngineEvent(event);
        }
    }

    /**
     * 前人不写注释，后人来补
     * @return List<EngineEvent>
     */
    private List<EngineEvent> findTimeoutSuspendedEngineEvents(boolean isImportant) {
        long currentTimeMillis = System.currentTimeMillis();
        //3小时以前
//        Date fromTime = new Date(currentTimeMillis - limitBoundOffset);
        //2分钟以前
        Date toTime = new Date(currentTimeMillis - upperBoundOffset);
        //查询limit 500 条
        List<EngineEvent> engineEventList = engineEventService.selectSuspendedCreateTimeBetween(null, toTime, isImportant, sqlLimit);
//        //如果一小时以内的数据小于50条，则再次查询-2h到-1h以内的数据，并将两者数据合并。这段代码，如果两次查库的时间比较长，可能会产生重复数据。
//        if (CollectionUtils.isEmpty(engineEventList) || engineEventList.size() < sqlLimit) {
//            //-2h
//            fromTime = new Date(currentTimeMillis - limitBoundOffset);
//            //-1h
//            toTime = new Date(currentTimeMillis - lowerBoundOffset);
//            List<EngineEvent> paddingEngineEvents = engineEventService.selectSuspendedCreateTimeBetween(fromTime, toTime, isImportant, sqlLimit);
//            if (CollectionUtils.isEmpty(engineEventList)) {
//                return paddingEngineEvents;
//            }
//            engineEventList.addAll(paddingEngineEvents);
//        }
        return engineEventList;
    }

    private void handleSuspendedEngineEvent(EngineEvent engineEvent) {
        if (!isExecute(engineEvent.getCreateTime(), engineEvent.getUpdateTime())) {
            return;
        }
        String mainLogId = LogUtil.getLogIdFromContext();
        LogUtil.bindLogId(engineEvent.getSessionId());
        try {
            EngineProcessInstanceModel engineProcessInstanceModel = processInstanceService.queryEngineProcessInstanceModelByProInsId(engineEvent.getProcessInstanceId());
            try{
                if(ProcessInstanceStatusEnum.SUSPENDED.name().equals(engineProcessInstanceModel.getStatus())){
                    ProcessContext processContext = processInstanceService.buildProcessContextByModel(engineProcessInstanceModel);
                    LoggerProxy.info(logClassName + "TryWakeup", LOGGER, "event sessionID={}, eventCode={}", engineEvent.getSessionId(), engineEvent.getEventCode());
                    processWakeupService.tryWakeupSession(engineEvent.getSessionId(), processContext.getCurrentNodeId(),"EngineEventSuspendedTaskScheduler");
                    executeCount++;
                    LoggerProxy.info("handleSuspendedEngineEventSuspendedInstance", LOGGER, "update event sessionId={}, eventCode={}",engineEvent.getSessionId(), engineEvent.getEventCode());
                }
            }catch (Exception e){
                LoggerProxy.error("handleSuspendedEngineEventSuspendedInstanceError", LOGGER, "sessionId={}, eventCode={}", engineEvent.getSessionId(), engineEvent.getEventCode(), e);
            }

            // 对于超时十分钟的processing直接更新engineEvent状态为失败
            try{
                if(ProcessInstanceStatusEnum.PROCESSING.name().equals(engineProcessInstanceModel.getStatus())
                        && isTimeOverTimeTenMinu(engineProcessInstanceModel.getUpdateTime())
                        && isTimeOverTimeTenMinu(engineEvent.getUpdateTime())
                ){
                    int count = engineEventService.updateEventFailed(engineEvent.getSessionId());
                    LoggerProxy.info("handleSuspendedEngineEventProcessingInstance", LOGGER,
                            "update event sessionId: {},result:{}",engineEvent.getSessionId(),count);
                }
            }catch (Exception e){
                LoggerProxy.error("handleSuspendedEngineEventProcessingInstanceError", LOGGER, "sessionId=" + engineEvent.getSessionId(), e);
            }
        } catch (Exception e) {
            LoggerProxy.error("handleSuspendedEngineEventError", LOGGER, "sessionId=" + engineEvent.getSessionId(), e);
        } finally {
            LogUtil.bindLogId(mainLogId);
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        if(EnvUtil.isBatch()){
            this.setLockName(this.getLockName()+"_batch");
            LOGGER.info("lock name : "+this.getLockName());
        }
    }
}
