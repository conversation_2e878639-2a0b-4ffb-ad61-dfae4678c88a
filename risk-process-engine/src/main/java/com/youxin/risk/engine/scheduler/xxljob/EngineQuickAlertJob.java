package com.youxin.risk.engine.scheduler.xxljob;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.service.WeChatService;
import com.youxin.risk.commons.service.engine.EngineEventService;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.xxl.job.XxlJobBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 *<AUTHOR>
 *@create 2024/7/1 16:33
 *@desc 引擎快速报警job
 * 目的：大量报错时，及时处理
 */
@Component
public class EngineQuickAlertJob implements XxlJobBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(EngineQuickAlertJob.class);

    @Autowired
    private EngineEventService engineEventService;

    @Autowired
    private WeChatService weChatService;

    @Value("${block.qw.robot.key}")
    private String qwBlockRobotKey;

    // 忽略的变量或者数据code
    List<String> ignoreVarOrDataCodeList = ApolloClientAdapter.getListConfig(
            ApolloNamespaceEnum.ENGINE_SPACE,
            "engine.alert.ignore.varOrData", String.class);

    @XxlJob(value = "engineQuickAlertJob")
    @Override
    public ReturnT<String> execJobHandler(String param) {
        try {
            LoggerProxy.info("引擎超过三分钟未回调报警", LOGGER, "开始执行");

            Map<String, Integer> eventCodeAndCountMap = new LinkedHashMap<>();
            Stopwatch stopwatch = Stopwatch.createStarted();
            List<Map<String, Object>> alertList = engineEventService.selectNotCallbackedMoreThan3Minutes(ignoreVarOrDataCodeList);
            for (Map<String, Object> entry : alertList) {
                eventCodeAndCountMap.put((String) entry.get("eventCode"), ((Number) entry.get("cnt")).intValue());
            }
            LoggerProxy.info("引擎超过三分钟未回调报警", LOGGER, "查询待报警数据={}，耗时={}ms", JSON.toJSONString(eventCodeAndCountMap),
                    stopwatch.elapsed(TimeUnit.MILLISECONDS));

            eventCodeAndCountMap = filter(eventCodeAndCountMap);
            LoggerProxy.info("引擎超过三分钟未回调报警", LOGGER, "过滤后结果={}", JSON.toJSONString(eventCodeAndCountMap));
            if (eventCodeAndCountMap.isEmpty()) {
                return ReturnT.SUCCESS;
            }

            String alertMessage = buildAlertMessage(eventCodeAndCountMap);
            LoggerProxy.info("引擎超过三分钟未回调报警", LOGGER, "待发送企微消息={}", alertMessage);
            weChatService.sendMarkdownMsg(alertMessage, qwBlockRobotKey);
        } catch (Exception e) {
            LoggerProxy.warn("引擎超过三分钟未回调报警", LOGGER, "异常，详细信息=", e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

    private Map<String, Integer> filter(Map<String, Integer> eventCodeAndCountMap) {
        Map<String, Integer> configMap = ApolloClientAdapter.getMapConfig(ApolloNamespaceEnum.ENGINE_SPACE, "engine" +
                        ".event.alert.config", Integer.class);
        Integer defaultAlertThreshold = configMap.getOrDefault("default", 10);
        Map<String, Integer> result = new LinkedHashMap<>();
        for (Map.Entry<String, Integer> entry : eventCodeAndCountMap.entrySet()) {
            // apiLendAudit、haoHuanLendAudit事件已排除掉因人审卡单
            if (entry.getValue() > configMap.getOrDefault(entry.getKey(), defaultAlertThreshold)) {
                result.put(entry.getKey(), entry.getValue());
            }
        }
        return result;
    }

    private String buildAlertMessage(Map<String, Integer> eventCodeAndCountMap) {
        int sum = eventCodeAndCountMap.values().stream().mapToInt(i -> i).sum();
        String title = "#### 引擎超过三分钟未回调报警(请及时处理) 总数量：" +  weChatService.warnMessage(String.valueOf(sum));
        StringBuilder alertMessage = new StringBuilder(title);
        for (Map.Entry<String, Integer> entry : eventCodeAndCountMap.entrySet()) {
            alertMessage.append("\n >").append(entry.getKey()).append("：").append(weChatService.warnMessage(String.valueOf(entry.getValue())));
        }
        return alertMessage.toString();
    }

}
