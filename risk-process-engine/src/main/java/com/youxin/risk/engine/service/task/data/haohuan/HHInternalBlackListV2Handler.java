/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.engine.service.task.data.haohuan;

import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.model.EngineAsyncRequestLog;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.utils.JsonUtils;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.engine.service.task.data.ConfigurableDataHandler;
import com.youxin.risk.engine.vo.DiResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class HHInternalBlackListV2Handler extends ConfigurableDataHandler {
    private static final Logger logger = LoggerFactory.getLogger(HHInternalBlackListV2Handler.class);
    private static final String DATA_CODE = "internalBlackListService";

    @Override
    public Map<String, Object> buildRequestParams(Event event, String dataCode, String nodeCode) {
        Map<String, Object> params = event.getParams();

        Map<String, Object> requestParams = Maps.newHashMap();
        requestParams.put("userKey", params.get("userKey"));
        requestParams.put("systemId", params.get("sourceSystem"));
        requestParams.put("idno", params.get("idcardNumber"));
        requestParams.put("mobile", params.get("registerMobile"));
        requestParams.put("contact", Lists.newArrayList());
        requestParams.put("GPS_lat", "");
        requestParams.put("GPS_lon", "");
        requestParams.put("ip", Lists.newArrayList());
        requestParams.put("deviceId", Lists.newArrayList());
        requestParams.put("wifiMac", Lists.newArrayList());
        this.buildAcquisitionTime(nodeCode,dataCode,event,requestParams);
        LoggerProxy.info("buildRequestParams", logger, "HHInternalBlackListV2Handler buildRequestParams, userKey={},params={}", params.get("userKey"),  JsonUtils.toJson(requestParams));
        return requestParams;
    }

    @Override
    public DiResult callDi(Event event, String dataCode, EngineAsyncRequestLog engineAsyncRequestLog) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        DiResult diResult = super.callDi(event, DATA_CODE, engineAsyncRequestLog);
        long costTime = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
        LoggerProxy.info("callServiceResult", logger, "call service result, result={}, cost={}", JsonUtils.toJson(diResult), costTime);
        return diResult;
    }

    @Override
    public String getServiceCode(String dataCode) {
        return DATA_CODE;
    }
}
