package com.youxin.risk.engine.mapper;

import com.youxin.risk.commons.model.EngineProcessInstanceModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/10/11 10:35
 */
public interface EngineProcessInstanceHistoryMapper {


    void insertBatch(List<EngineProcessInstanceModel> list);

    List<EngineProcessInstanceModel> selectAfterId(@Param("startId") Long startId);

    int deleteByIds(@Param("idList") List<Long> idList);

}
