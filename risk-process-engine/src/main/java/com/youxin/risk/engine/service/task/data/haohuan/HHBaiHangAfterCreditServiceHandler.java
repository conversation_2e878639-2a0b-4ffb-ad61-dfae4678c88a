package com.youxin.risk.engine.service.task.data.haohuan;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.vo.DataVo;
import com.youxin.risk.engine.service.task.data.ConfigurableDataHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/9/10 19:25
 * @desc
 */
@Service
public class HHBaiHangAfterCreditServiceHandler extends ConfigurableDataHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(HHBaiHangAfterCreditServiceHandler.class);


    @Override
    public String getServiceCode(String dataCode) {
        return "baiHangAfterCreditService";
    }

    @Override
    public Map<String, Object> buildRequestParams(Event event, String dataCode, String nodeCode) {
        Map<String, Object> params = super.buildRequestParams(event, dataCode, nodeCode);
        DataVo dataVo = JSONObject.parseObject(JSONObject.toJSONString(event.getDataVo()), DataVo.class);
        params.put("loanId", dataVo.getReportRequestVo().getLoanId());
        return params;
    }
}
