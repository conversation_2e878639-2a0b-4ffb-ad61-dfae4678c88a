package com.youxin.risk.engine.service.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.activiti.runtime.task.AbstractTaskService;
import com.youxin.risk.commons.constants.EventVariableKeyEnum;
import com.youxin.risk.commons.dao.verify.VerifyResultHitMapper;
import com.youxin.risk.commons.dao.verify.VerifyResultIrr24Mapper;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.model.verify.VerifyResult;
import com.youxin.risk.commons.model.verify.VerifyResultHit;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.ra.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 好还24流程结果回写
 */
public class HhIrr24RejectWriteBackTaskService extends AbstractTaskService {

    private static final Logger LOGGER = LoggerFactory.getLogger(HhIrr24RejectWriteBackTaskService.class);

    private static final String RONG_VERIFY = "rong360Verify";

    private static final String ZHAOLIAN_VERIFY_EVENT_CODE = "zhaolianVerify";

    private static final String RONG_360 = "RONG_360";

    private static final String STRATEGY_TYPE_RELOAN = "VERIFY_HAOHUAN_RELOAN_ALL";

    private static final String FLOW_TYPE = "flowType";
    //镜像标示
    private static final String IMAGE_FLOW = "image";

    private static final Double DEFAULT_REDUCE_AMOUNT = 500.0;

    private static final Integer DEFAULT_NEW_AMOUNT_VALID_DAYS = 30;

    @Autowired
    private VerifyResultIrr24Mapper verifyResultIrr24Mapper;

    @Autowired
    private VerifyResultHitMapper verifyResultHitMapper;


    @Override
    public void execute(ProcessContext processContext) {
        Event event = processContext.getEvent();
        Map<String, Object> verifyResult = event.getVerifyResult();
        String reasonCode = null;
        try {
            if (verifyResult != null && verifyResult.get(EventVariableKeyEnum.originResult.name()) != null) {
                JSONObject originResult = JSON.parseObject((String) verifyResult.get(EventVariableKeyEnum.originResult.name()));
                reasonCode = Objects.isNull(originResult) ? "" : originResult.get("reason_code").toString();
            }
        } catch (Exception e) {
            LoggerProxy.error("sendEventMessageError", LOGGER, "", e);
        }
        Map<String, Object> params = event.getParams();
        insertVerifyResult(params, JSON.parseObject(JSON.toJSONString(verifyResult)), reasonCode);
    }


    public void insertVerifyResult(Map<String, Object> params, JSONObject resultJson, String reasonCode) {
        VerifyResult verifyResult = new VerifyResult();
        verifyResult.setLoanId(Integer.valueOf(params.get("loanId").toString()));
        verifyResult.setLoanKey((String) params.get("loanKey"));
        verifyResult.setUserKey((String) params.get("userKey"));
        String sourceSystem = (String) params.get("sourceSystem");
        String eventCode = (String) params.get("eventCode");
        if (StringUtils.isNotBlank(eventCode) && RONG_VERIFY.equals(eventCode)) {
            sourceSystem = RONG_360;
        } else if (ZHAOLIAN_VERIFY_EVENT_CODE.equals(eventCode)) {
            sourceSystem = "ZHAOLIAN";
        }
        verifyResult.setSourceSystem(sourceSystem);

        verifyResult.setScore(Double.valueOf(resultJson.getString("score")));
        verifyResult.setScore2(Double.valueOf(resultJson.getString("score2")));
        verifyResult.setIsAutoPassed(resultJson.getBoolean("isPassed"));
        verifyResult.setAutoVerifyTime(new Date());
        verifyResult.setReasonCode(reasonCode);
        verifyResult.setIsManual(resultJson.getBoolean("isManual"));
        verifyResult.setStrategyId(resultJson.getInteger("strategyId"));
        verifyResult.setLevel(resultJson.getString("level"));
        verifyResult.setSegment(resultJson.getString("segment"));
        verifyResult.setStep((String) params.get("step"));
        verifyResult.setLoanAmount(Double.valueOf(resultJson.getString("loanAmount")));
        verifyResult.setLoanPeriodNos(resultJson.getInteger("loanPeriodNos"));
        verifyResult.setLoanRate(Double.valueOf(resultJson.getString("loanRate")));
        verifyResult.setPeriodAmount(resultJson.getString("periodAmount"));
        verifyResult.setBtAmount(Double.valueOf(resultJson.getString("btAmount")));
        verifyResult.setBtPeriodNos(resultJson.getInteger("btPeriodNos"));
        verifyResult.setBtRate(Double.valueOf(resultJson.getString("btRate")));
        verifyResult.setTotalAmount(Double.valueOf(resultJson.getString("totalAmount")));
        if (verifyResult.getIsManual() == null) {
            verifyResult.setIsManual(false);
        }

        Boolean isReduceAmountPass = resultJson.getBoolean("isReduceAmountPass");
        Double newAmount = null;
        Date newAmountExpiry = null;
        if (Boolean.TRUE.equals(isReduceAmountPass)) {
            newAmount = Double.valueOf(resultJson.getString("newAmount"));
            if (newAmount == null) {
                newAmount = DEFAULT_REDUCE_AMOUNT;
            }
            Integer validDays = resultJson.getInteger("validDays");
            if (validDays == null) {
                validDays = DEFAULT_NEW_AMOUNT_VALID_DAYS;
            }
            newAmountExpiry = DateUtils.getNextDayDate(new Date(), validDays);
        }

        Integer lockDays = resultJson.getInteger("lockDays");
        // 审核结果设置降额后额度和有效期
        verifyResult.setIsReduceAmountPass(isReduceAmountPass);
        verifyResult.setNewAmount(newAmount);
        verifyResult.setNewAmountExpiry(newAmountExpiry);
        Date lockDate = DateUtils.getNextDayDate(new Date(), lockDays);
        verifyResult.setAutoLockDays(lockDate);

        if (!verifyResult.getIsAutoPassed() || !verifyResult.getIsManual()) {
            LOGGER.info("irr process :userKey={},loanKey={} not need manual verify", verifyResult.getUserKey(), verifyResult.getLoanKey());
            verifyResult.setFinalVerifyTime(new Date());
            verifyResult.setIsFinalPassed(verifyResult.getIsAutoPassed());
        }
        verifyResult.setUpdateTime(new Date());
        verifyResult.setCreateTime(new Date());
        verifyResult.setVersion(0);
        this.verifyResultIrr24Mapper.insertVerifyResultIrr(verifyResult);

        // 插入到 verify_result_hit_irr 做触碰率监控用
        insertVerifyResultHitIrrMonitor(verifyResult);
    }


    private void insertVerifyResultHitIrrMonitor(VerifyResult verifyResult) {

        try {
            VerifyResultHit verifyResultHit = new VerifyResultHit();
            BeanUtils.copyProperties(verifyResult, verifyResultHit);
            String reasonCode = verifyResult.getReasonCode();
            if (StringUtils.isNotEmpty(reasonCode)) {
                JSONObject jsonObject = JSON.parseObject(reasonCode);
                if(jsonObject.containsKey("new_tag")){
                    verifyResultHit.setNewTag(jsonObject.getString("new_tag"));
                }
            }
            verifyResultHitMapper.insertVerifyResultHitIrr24(verifyResultHit);
        } catch (BeansException e) {
            LOGGER.error("insertVerifyResultHitIrrError , userKey={} ,loanKey={}", verifyResult.getUserKey(), verifyResult.getLoanKey(), e);
        }
    }


}
