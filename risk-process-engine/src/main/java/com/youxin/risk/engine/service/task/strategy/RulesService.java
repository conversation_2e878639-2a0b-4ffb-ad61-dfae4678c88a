package com.youxin.risk.engine.service.task.strategy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.dao.admin.RuleScoreCalMapper;
import com.youxin.risk.commons.exception.RiskRuntimeException;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.model.StrategyNodeRelationalDO;
import com.youxin.risk.commons.model.admin.RuleScoreStrategy;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.service.StrategyNodeRelationalService;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;

@Component
public class RulesService {
    private static final Logger logger = LoggerFactory.getLogger(RulesService.class);

    @Resource
    private StrategyNodeRelationalService strategyNodeRelationalService;
    @Resource
    private RuleScoreCalMapper ruleScoreCalMapper;
    @Resource(name = "ruleProcessingThreadPool")
    private ThreadPoolTaskExecutor ruleProcessingThreadPool;
    @Value("${system.pythonframe.baseurlPython3}")
    private String baseURLPython3;
    //调用策略
    private static final String URL_CALC_RULES= "/run_policy_async_rules";
    private final static int HTTP_REQUEST_TIME_OUT = 30000;

    /**
     * 规则分集合处理
     */
    public Map<String, JSONObject> rulesSetHandle(ProcessContext processContext) {
        Event event = processContext.getEvent();
        //获取策略代码id
        String strategyNodeId = processContext.getAttribute("step");
        StrategyNodeRelationalDO strategyNodeRelational = strategyNodeRelationalService.queryStrategyNodeRelational(strategyNodeId,
                event.isMirror());
        // 根据节点名称 查询规则分集合
        List<Rule> ruleSets = getRuleSets(strategyNodeRelational);
        if (ruleSets.isEmpty()) {
            return null;
        }

        // 按层级排序 1->2->3...
        Collections.sort(ruleSets, Comparator.comparingInt(Rule::getRuleLevel));

        Map<String, JSONObject> ruleResults = new HashMap<>();
        int currentLevel = ruleSets.get(0).getRuleLevel();
        List<CompletableFuture<RuleResult>> futures = new ArrayList<>();
        for (Rule rule : ruleSets) {
            if (rule.getRuleLevel() > currentLevel) {
                // 等待当前层级的所有任务完成
                try {
                    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                            .get(10, TimeUnit.SECONDS);
                } catch (InterruptedException | ExecutionException e) {
                    throw new RuntimeException("规则计算失败", e);
                } catch (TimeoutException e) {
                    throw new RuntimeException("规则计算超时", e);
                }
                // 获取当前层级的规则分结果
                for (CompletableFuture<RuleResult> future : futures) {
                    RuleResult result = future.join();
                    ruleResults.put(result.getRuleKey(), result.getRuleValue());
                }
                futures.clear();
                currentLevel = rule.getRuleLevel();
            }

            RuleProcessingTask task = new RuleProcessingTask(
                    buildRuleScoreCalcRequestVo(strategyNodeId, event, strategyNodeRelational, rule.getRuleKey(), rule.getRuleVersion())
                    ,rule
                    , ruleResults);
            CompletableFuture<RuleResult> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return task.call();
                } catch (Exception e) {
                    logger.error("规则处理异常", e);
                    throw new CompletionException(e);
                }
            }, ruleProcessingThreadPool);
            futures.add(future);
        }

        // 等待所有任务完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .get(10, TimeUnit.SECONDS);
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException("规则计算失败", e);
        } catch (TimeoutException e) {
            throw new RuntimeException("规则计算超时", e);
        }
        // 获取最后一层级的规则分结果
        for (CompletableFuture<RuleResult> future : futures) {
            RuleResult result = future.join();
            ruleResults.put(result.getRuleKey(), result.getRuleValue());
        }
        return ruleResults;
    }

    private List<Rule> getRuleSets(StrategyNodeRelationalDO strategyNodeRelational) {
        // 根据策略节点获取绑定的规则分
        List<RuleScoreStrategy> bindRuleScoreList = ruleScoreCalMapper
                .findBindRuleScoreByStrategyNode(strategyNodeRelational.getStrategyNodeId());
        List<Rule> ruleSets = new ArrayList<>();
        for (RuleScoreStrategy ruleScoreStrategy : bindRuleScoreList) {
            Rule rule = new Rule(ruleScoreStrategy.getRuleKey(), ruleScoreStrategy.getRuleVersion(), ruleScoreStrategy.getRuleLevel());
            ruleSets.add(rule);
        }
        return ruleSets;
    }

    class RuleProcessingTask implements Callable<RuleResult> {
        private RuleScoreCalcRequestVo ruleScoreCalcRequestVo;
        private Rule rule;

        public RuleProcessingTask(RuleScoreCalcRequestVo ruleScoreCalcRequestVo
                , Rule rule
                , Map<String, JSONObject> ruleSetResults) {
            this.ruleScoreCalcRequestVo = ruleScoreCalcRequestVo;
            this.rule = rule;
            // 设置规则分依赖的规则分结果
            this.ruleScoreCalcRequestVo.setRuleSetMap(ruleSetResults);
        }

        @Override
        public RuleResult call() throws Exception {
            String result = execPython(this.ruleScoreCalcRequestVo.getStep()
                    , this.ruleScoreCalcRequestVo.getLoanKey()
                    , this.ruleScoreCalcRequestVo.getMirror()
                    , this.ruleScoreCalcRequestVo);
            JSONObject ruleValue = JSON.parseObject(result);
            return new RuleResult(this.rule.getRuleKey(), ruleValue);
        }
    }

    /**
     * 规则分计算
     * @param strategyNodeId
     * @param loanKey
     * @param mirror
     * @param ruleScoreCalcRequestVo
     * @return
     */
    private String execPython(String strategyNodeId, String loanKey, Boolean mirror, RuleScoreCalcRequestVo ruleScoreCalcRequestVo) {
        String url = baseURLPython3 + URL_CALC_RULES;
        String request = JSON.toJSONString(ruleScoreCalcRequestVo);
        LoggerProxy.info("规则分", logger, "规则分代码计算请求" +
                        "，loanKey={}, strategyNodeId={}, isMirror={}, 请求数据={}",
                loanKey, strategyNodeId, mirror, StringUtils.subString(request, 2000));
        String responseStr = SyncHTTPRemoteAPI.postJson(url, request,
                HTTP_REQUEST_TIME_OUT);
        JSONObject response = JSON.parseObject(responseStr);
        Integer status = response.getInteger("status");
        if (status != 0) {
            LoggerProxy.error("规则分", logger, "规则分代码计算异常" +
                            "，loanKey={}, strategyNodeId={}, isMirror={}, 返回结果={}",
                    loanKey, strategyNodeId, mirror, responseStr);
            throw new RiskRuntimeException("规则分代码计算异常:" + responseStr);
        }
        return response.getString("result");
    }

    /**
     * 构建规则分计算请求vo
     * @param strategyNodeId
     * @param event
     * @param strategyNodeRelational
     * @return
     */
    private RuleScoreCalcRequestVo buildRuleScoreCalcRequestVo(String strategyNodeId
            , Event event
            , StrategyNodeRelationalDO strategyNodeRelational
            , String ruleKey
            , Integer ruleVersion) {
        RuleScoreCalcRequestVo ruleScoreCalcRequestVo = new RuleScoreCalcRequestVo();
        ruleScoreCalcRequestVo.setEventCode(event.getEventCode());
        ruleScoreCalcRequestVo.setLoanKey(event.getLoanKey());
        ruleScoreCalcRequestVo.setStep(strategyNodeId);
        ruleScoreCalcRequestVo.setType(strategyNodeRelational.getStrategyType());
        ruleScoreCalcRequestVo.setVariableMap(event.getVariableMap());
        ruleScoreCalcRequestVo.setSourceSystem(event.getSourceSystem());
        ruleScoreCalcRequestVo.setMirror(event.isMirror());
        ruleScoreCalcRequestVo.setRuleKey(ruleKey);
        ruleScoreCalcRequestVo.setRuleVersion(ruleVersion);
        return ruleScoreCalcRequestVo;
    }


    /**
     * 规则分计算请求vo
     */
    @Data
    public static class RuleScoreCalcRequestVo {
        private String sourceSystem;

        private String loanKey;

        private String eventCode;

        private String type;

        private String step;

        private Boolean mirror;

        private Map variableMap;

        private Map ruleSetMap;

        private String ruleKey;

        private Integer ruleVersion;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class Rule {
        // 规则key
        private String ruleKey;
        // 规则版本
        private Integer ruleVersion;
        // 规则层级
        private Integer ruleLevel;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private class RuleResult {
        String ruleKey;
        JSONObject ruleValue;
    }
}
