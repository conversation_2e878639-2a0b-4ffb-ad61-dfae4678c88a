package com.youxin.risk.engine.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.engine.service.BaseGwMsgCustomHandler;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class MiddleAutoGwMsgCustomHandler extends BaseGwMsgCustomHandler {

    @Override
    public String getEventCode(){
        return "haoHuanAmountMiddleAutoPayoff";
    }

    @Override
    public JSONObject buildGwMsg(Event event, String targetEventCode) {
        JSONObject gwMsg = super.buildGwMsg(event, targetEventCode);
        gwMsg.put("uid", event.get("uid"));
        return gwMsg;
    }
}
