package com.youxin.risk.engine.service.task;

import com.alibaba.fastjson.JSONPath;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.compare.CompareActuator;
import com.youxin.risk.commons.compare.DataTypeEnum;
import com.youxin.risk.commons.constants.PointConstant;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.exception.RiskRuntimeException;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.model.NodeFeature;
import com.youxin.risk.commons.model.ProcessNode;
import com.youxin.risk.commons.utils.ContextUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.activiti.runtime.task.AbstractTaskService;
import com.youxin.risk.engine.activiti.service.impl.DefaultUserTaskService;
import com.youxin.risk.engine.clients.RaClient;
import com.youxin.risk.engine.clients.RmClient;
import com.youxin.risk.engine.clients.RmClientNew;
import com.youxin.risk.engine.clients.VerifyClient;
import com.youxin.risk.engine.conf.Apollo;
import com.youxin.risk.engine.service.SplitFlowService;
import com.youxin.risk.engine.service.task.feature.FeatureHandler;
import com.youxin.risk.metrics.MetricsAPI;
import com.youxin.risk.ra.utils.RaXmlUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.TraceCrossThread;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2018/10/16 13:30
 */
public class FeatureTaskService extends AbstractTaskService {
    private static final Logger LOGGER = LoggerFactory.getLogger(FeatureTaskService.class);

    private static Map<String, String> handlerMap = Maps.newHashMap();
    private final static String PACKAGE_SPLIT = "/";

    @Resource
    private RaClient raClient;
    @Resource
    private RmClient rmClient;
    @Resource
    private RmClientNew rmClientNew;
    @Resource
    private SplitFlowService splitFlowService;
    @Resource
    private VerifyClient verifyClient;

    @Resource(name = "featureTaskThreadPool")
    private ThreadPoolTaskExecutor featureTaskThreadPool;

    private Method getRmFeatureResultMethod;

    private final static int REQUEST_TIMEOUT = 120;

    @Autowired
    private CompareActuator compareActuator;

    static {
        handlerMap.put("step", "stepHandler");
        handlerMap.put("rrdEventInfo", "rrdEventInfoHandler");
        handlerMap.put("lastStrategyResult", "lastStrategyResultHandler");
    }

    private String mappingSourceSystem(Event event) {
        if ("RONG360".equals(event.getString("channel"))) {
            return "RONG360";
        } else if ("ZHAOLIAN".equals(event.getString("channel"))) {
            return "ZHAOLIAN";
        }

        return event.getSourceSystem();
    }

    @Override
    public void execute(ProcessContext processContext) {

        ProcessNode processNode = splitFlowService.getProcessNode(processContext);
        if (processNode == null) {
            throw new RiskRuntimeException("processNode is null, nodeId=" + processContext.getCurrentNodeId());
        }
        Event event = processContext.getEvent();
        event.setCurrentStep(processContext.getAttribute("step"));

        LoggerProxy.info("featureTaskService_execute",LOGGER,"sessionId={}",event.getSessionId());

        // 兼容好分期授信审核DataVo步骤显示问题
        if(StringUtils.isNotBlank(processContext.getAttribute("realStep"))){
            event.set("realStep", processContext.getAttribute("realStep"));
        }

        List<String> features = Lists.newArrayList();
        for (NodeFeature nodeFeature : processNode.getFeatures()) {
            features.add(nodeFeature.getFeatureCode());
        }
        String xml = event.getXml();
        // 获取额度xml扩展amountType属性
        event.set("amountType", null == processContext.getAttribute("amountType") ? "" : processContext.getAttribute("amountType"));

        // 获取并合并特征结果
        xml = getAndMergeXml(features, event);
        features.remove("ra");
        features.remove("rrd");
        features.remove("verify");
        features.remove("rm");

        processContext.appendStep(event.getCurrentStep());
        // 如果不删除realStep， 此属性会继承到后续节点，影响后续Datavo步骤属性的显示。
        resetRealStep(event);

        point(event.getEventCode(), xml);
        Stopwatch started = Stopwatch.createStarted();
        if (!CollectionUtils.isEmpty(features)) {
            Document document = buildDocument(xml);
            Element root = document.getRootElement();
            for (String featureCode : features) {
                FeatureHandler handler = (FeatureHandler) ContextUtil.getBean(handlerMap.get(featureCode));
                String nodePath = handler.getNodePath();
                String[] nodes = nodePath.split(PACKAGE_SPLIT);
                if (nodes == null || nodes.length == 0) {
                    continue;
                }
                if (root == null) {
                    root = document.addElement(nodes[0]);
                }
                Element parent = root;
                for (int i = 1; i < nodes.length; i++) {
                    Element element = parent.element(nodes[i]);
                    if (element == null) {
                        element = parent.addElement(nodes[i]);
                    }
                    parent = element;
                }
                String featureXml = handler.handler(event);
                if (StringUtils.isNotBlank(featureXml)) {
                    try {
                        featureXml = featureXml.replaceAll("[\\x00-\\x08\\x0b-\\x0c\\x0e-\\x1f]", "");
                        Document featureDocument = new SAXReader().read(new ByteArrayInputStream(featureXml.getBytes()));
                        Element featureRoot = featureDocument.getRootElement();
                        Element oldElement = parent.element(handler.getNodeName());
                        if (oldElement != null) {
                            parent.remove(oldElement);
                        }
                        parent.add(featureRoot);
                    } catch (Exception e) {
                        LoggerProxy.error("getFeatureXmlError", LOGGER, "get feature xml error, featureCode={}, loanKey={}", featureCode, event.getLoanKey(), e);
                    }
                }
            }
            xml = document.asXML();
            xml = xml.replace("<?xml version=\"1.0\" encoding=\"UTF-8\"?>", "");
            xml = xml.replace("\r", "");
            xml = xml.replace("\n", "");
        }
        LoggerProxy.info("dealFeatureXmlInfo", LOGGER, "costTime:{},nodeId:{}", started.elapsed(TimeUnit.MILLISECONDS),processContext.getCurrentNodeId());
        String savePath = processContext.getAttribute(DefaultUserTaskService.SAVE_PATH);
        if (!StringUtils.isEmpty(savePath)) {
            saveXmlPointPath(event, xml, savePath);
        }
        event.setXml(xml);
//        eventService.updateEventToMongo(event);
    }

    private void mirrorComputing(Event event, String xml) {
        try {
            if (getRmFeatureResultMethod == null) {
                getRmFeatureResultMethod = this.getClass().getMethod("getRmFeatureResult", Event.class);
            }
            compareActuator.submitCompareTask(xml, "rmFeatureMirrorComputing", DataTypeEnum.XML.name(),
                    getRmFeatureResultMethod, this, event);
        } catch (Exception e) {
            LoggerProxy.warn("rmFeatureMirrorComputing", LOGGER, "error, loanKey={},step={},errMsg=", event.getLoanKey(),
                    event.getString("step"), e);
        }
    }

    public String getRmFeatureResult(Event event) {
        return rmClientNew.getFeatureXml(event);
    }

    public String getAndMergeXml(List<String> features, Event event) {
        String xml = event.getXml();
        FutureTask<String> raFeatureResultTask = null;
        FutureTask<String> verifyFeatureResultTask = null;
        FutureTask<String> rmFeatureResultTask = null;
        Stopwatch stopwatch = Stopwatch.createStarted();

        if (features.contains("ra")) {
            raFeatureResultTask = getRaFeatureTask(event);
        }
        if (features.contains("verify")) {
            verifyFeatureResultTask = getVerifyFeatureTask(event);
        }
        if (features.contains("rm")) {
            //添加日志，打印进件审核的
            rmFeatureResultTask = getRmFeatureTask(event);
        }
        // ra特征计算
        if (raFeatureResultTask != null) {
            xml = getFeatureResultAndMergeXml(raFeatureResultTask, "ra", xml, event, stopwatch);
        }

        // verify特征计算
        if (verifyFeatureResultTask != null) {
            xml = getFeatureResultAndMergeXml(verifyFeatureResultTask,  "verify", xml, event, stopwatch);
        }

        //rm特征计算
        if (rmFeatureResultTask != null) {
            xml = getFeatureResultAndMergeXml(rmFeatureResultTask,  "rm", xml, event, stopwatch);
        }
        if (Apollo.logDetail()) {
            LoggerProxy.info("featureComplete", LOGGER, "mirror, loanKey={}, step={},cost={}ms", event.getLoanKey(),
                    event.getString("step"), stopwatch.elapsed(TimeUnit.MILLISECONDS));
        }
        return xml;
    }

    public String getFeatureResultAndMergeXml(FutureTask<String> featureResultTask,
                                               String module, String xml, Event event, Stopwatch stopwatch) {
        
        String featureResult = null;
        try {
            featureResult = featureResultTask.get(REQUEST_TIMEOUT, TimeUnit.SECONDS);
//            LoggerProxy.info("getFeatureResult", LOGGER, "success, module={}, loanKey={}, step={},cost={}ms",
//                    module, event.getLoanKey(), event.getString("step"), stopwatch.elapsed(TimeUnit.MILLISECONDS));
            if ("rm".equals(module)) {
                mirrorComputing(event, featureResult);
            }
        } catch (Exception e) {
            LoggerProxy.error("getFeatureResult", LOGGER, "error, module={}, loanKey={}, step={}, err msg:",
                    module, event.getLoanKey(), event.getString("step"), e);
            throw new RiskRuntimeException(RetCodeEnum.FAILED, e.getMessage());
        }
        if (StringUtils.isBlank(featureResult)) {
            return xml;
        }
        
        return RaXmlUtils.mergeXml(xml, featureResult);
    }

    public FutureTask<String> getRmFeatureTask(Event event) {
        FutureTask<String> futureTask = new FutureTask<>(new RmFeatureTask(event));
        featureTaskThreadPool.submit(futureTask);
        return futureTask;
    }

    public FutureTask<String> getVerifyFeatureTask(Event event) {
        FutureTask<String> futureTask = new FutureTask<>(new VerifyFeatureTask(event));
        featureTaskThreadPool.submit(futureTask);
        return futureTask;
    }

    public FutureTask<String> getRaFeatureTask(Event event) {
        FutureTask<String> futureTask = new FutureTask<>(new RaFutureTask(event));
        featureTaskThreadPool.submit(futureTask);
        return futureTask;
    }


    private void saveXmlPointPath(Event event, String xml, String savePath) {
        try {
            // 自定义xml存储路径为event.verifyResult顶级目录开始, 不存在则创建
            JSONPath.set(event.getVerifyResult(), savePath, xml);
        } catch (Exception e) {
            LoggerProxy.error("saveXmlError", LOGGER, String.format("savePath=%s,xml=%s", savePath, xml), e);
        }
    }

    private void resetRealStep(Event event){
        try{
            event.set("realStep", "");
        }catch (Exception e){
            LoggerProxy.error("resetRealStep", LOGGER, "event loanKey={}", event.getLoanKey(), e);
        }
    }

    private Document buildDocument(String xml) {
        Document document = null;
        if (StringUtils.isNotBlank(xml)) {
            try {
                document = new SAXReader().read(new ByteArrayInputStream(xml.getBytes()));
            } catch (Exception e) {
                LoggerProxy.error("createDocumentError", LOGGER, "create document error", e);
            }
        }
        if (document == null) {
            document = DocumentHelper.createDocument();
        }
        return document;
    }

    private void point(String eventCode, String xml) {
        try {
            Map<String, String> tags = new HashMap<>();
            tags.put("eventCode", eventCode);
            Map<String, Number> values = new HashMap<>();
            int value;
            if (StringUtils.isNotBlank(xml)) {
                value = 1;
            } else {
                value = 0;
            }
            values.put("valueFlag", value);
            MetricsAPI.point(PointConstant.ra_datavo_feature, tags, values);
        } catch (Exception e) {
            LoggerProxy.error("pointException", LOGGER, "eventCode={}", eventCode, e);
        }
    }

    @TraceCrossThread
    private class RaFutureTask implements Callable<String> {
        private final Event event;

        private RaFutureTask(Event event) {
            this.event = event;
        }

        @Override
        public String call() {
            return raClient.getFeature(mappingSourceSystem(event),
                    event.getUserKey(),
                    event.getLoanKey(),
                    event.getString("step"),
                    event.getDataVo());
        }
    }

    @TraceCrossThread
    private class VerifyFeatureTask implements Callable<String> {
        private final Event event;

        private VerifyFeatureTask(Event event) {
            this.event = event;
        }

        @Override
        public String call() {
            return verifyClient.getFeature(event.getSourceSystem(),
                    event.getUserKey(), event.getLoanKey(), event.getString("step"), event.getParams(),
                    event.getXml(), event.getVerifyResult(), event.getString("amountType"));
        }
    }

    @TraceCrossThread
    private class RmFeatureTask implements Callable<String> {
        private final Event event;

        private RmFeatureTask(Event event) {
            this.event = event;
        }
        @Override
        public String call() {
            return rmClient.getFeatureXml(event);
        }
    }
}
