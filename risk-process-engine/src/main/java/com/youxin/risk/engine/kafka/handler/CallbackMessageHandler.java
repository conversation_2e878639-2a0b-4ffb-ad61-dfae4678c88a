package com.youxin.risk.engine.kafka.handler;

import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.kafkav2.KafkaContext;
import com.youxin.risk.commons.kafkav2.handler.impl.BaseKafKaMessageHandler;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.utils.LogUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.engine.service.CallbackService;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;

public class CallbackMessageHandler extends BaseKafKaMessageHandler {

    @Resource
    private CallbackService callbackService;

    @Override
    protected void handler0(KafkaContext context) {
        LoggerProxy.info("receivedCallbackMessage", logger, "callbackMessageHandler,message={}", context.getMessage());

        Event event;
        try {
            event = context.getMessageObject(Event.class);
        } catch (Exception e) {
            LoggerProxy.error("parseMessageError", logger, "parse message to sessionId error, message=" + context.getMessage(), e);
            context.setTerminated(true);
            return;
        }
        if (StringUtils.isBlank(event.getSessionId())) {
            LoggerProxy.error("notFoundSessionId", logger, "sessionId={}", event.getSessionId());
            context.setTerminated(true);
            return;
        }
        LogUtil.bindLogId(event.getSessionId());
        callbackService.handleReviewCallback(event);
        context.setRetCode(RetCodeEnum.SUCCESS);

        LogUtil.unbindLogId();
    }
}
