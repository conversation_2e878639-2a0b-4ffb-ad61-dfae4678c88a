package com.youxin.risk.engine.scheduler.xxljob;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.weicai.caesar.CaesarUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.youxin.risk.commons.dao.ra.VerifyBaseinfoSnapshotMapper;
import com.youxin.risk.commons.model.ra.VerifyBaseinfoSnapshot;
import com.youxin.risk.commons.tools.redis.RetryableJedis;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.xxl.job.XxlJobBase;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
public class UpdateVerifyBaseInfoSnapshotJob implements XxlJobBase {

    private static final Logger logger = LoggerFactory.getLogger(UpdateVerifyBaseInfoSnapshotJob.class);
    private static final int BATCH_SIZE = 100;
    private static final int DEFAULT_SLEEP_TIME = 10;
    private static final String REDIS_KEY_LAST_PROCESSED_ID = "UpdateVerifyBaseInfoSnapshotJob_lastProcessedId";

    @Resource
    private VerifyBaseinfoSnapshotMapper verifyBaseinfoSnapshotMapper;
    @Autowired
    private RetryableJedis retryableJedis;

    @XxlJob(value = "UpdateVerifyBaseInfoSnapshotJob")
    @Override
    public ReturnT<String> execJobHandler(String param) {
        LoggerProxy.info("清洗VerifyBaseinfoSnapshot表开始", logger, "");
        Stopwatch started = Stopwatch.createStarted();
        JSONObject jsonObject = JSONObject.parseObject(param);
        Integer batchSize = jsonObject.getInteger("batchSize");
        Long lastProcessedId = jsonObject.getLong("lastProcessedId");
        Integer sleepTime = jsonObject.getInteger("sleepTime");
        // 是否删除redis中的lastProcessedId
        boolean deleteFlag = jsonObject.getBoolean("deleteFlag") != null && jsonObject.getBoolean("deleteFlag");

        try {
            lastProcessedId = lastProcessedId != null ? lastProcessedId : getLastProcessedId();
            batchSize = batchSize != null ? batchSize : BATCH_SIZE;
            sleepTime = sleepTime != null ? sleepTime : DEFAULT_SLEEP_TIME;
            List<VerifyBaseinfoSnapshot> records = verifyBaseinfoSnapshotMapper.selectRecordsAfterId(lastProcessedId, batchSize);

            if (CollectionUtils.isEmpty(records)) {
                if(deleteFlag) {
                    deleteLastProcessedId();
                    logger.info("删除redis中的lastProcessedId");
                }
                LoggerProxy.info("修正VerifyBaseinfoSnapshot表", logger, "没有更多记录需要修正");
                return ReturnT.SUCCESS;
            }

            List<Long> idList = records.stream()
                    .map(record -> {
                        record.setMobile0(encryptMobile(record.getMobile0()));
                        record.setMobile1(encryptMobile(record.getMobile1()));
                        return record.getId();
                    })
                    .collect(Collectors.toList());

            verifyBaseinfoSnapshotMapper.updateBatch(records);
            Long newLastProcessedId = idList.get(idList.size() - 1);
            updateLastProcessedId(newLastProcessedId);

            if (sleepTime > 0) {
                Thread.sleep(sleepTime);
            }

            LoggerProxy.info("修正VerifyBaseinfoSnapshot表结束", logger
                    , "修正数量={} lastId={}, 耗时={}ms"
                    , idList.size(), newLastProcessedId, started.elapsed(TimeUnit.MILLISECONDS));
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LoggerProxy.error("清洗VerifyBaseinfoSnapshot表异常", logger, "异常信息=", e);
            return ReturnT.FAIL;
        }
    }

    private Long getLastProcessedId() {
        String value = retryableJedis.get(REDIS_KEY_LAST_PROCESSED_ID);
        return value != null ? Long.parseLong(value) : 0L;
    }

    private void updateLastProcessedId(Long lastProcessedId) {
        retryableJedis.set(REDIS_KEY_LAST_PROCESSED_ID, lastProcessedId.toString());
    }

    private void deleteLastProcessedId() {
        retryableJedis.del(REDIS_KEY_LAST_PROCESSED_ID);
    }

    /**
     * 加密手机号
     * @param mobile
     * @return
     */
    private static String encryptMobile(String mobile) {
        if (StringUtils.isNotBlank(mobile)) {
            mobile = CaesarUtil.isEncrypted(mobile) ? mobile : CaesarUtil.encode(mobile);
        }
        return mobile;
    }
}