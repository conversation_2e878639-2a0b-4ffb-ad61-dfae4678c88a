package com.youxin.risk.engine.utils;

import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.engine.service.task.HhRandomDispatcherTaskService;

/**
 * <AUTHOR>
 * @create 2023/10/27 14:52
 * @desc
 */
public class EnvUtil {
    public static String getCurrentEnv() {
        String batch = System.getenv("BATCH");
        if(StringUtils.isNoneBlank(batch) && "true".equals(batch)){
            return "process-engine-batch";
        }
        String mirror = System.getenv("MIRROR");
        if(StringUtils.isNoneBlank(mirror) && "true".equals(mirror)){
            return "process-engine-mirror";
        }
        return "process-engine";
    }

    public static Event getTmpEvent(Event event){
        Event eventTmp = new Event();
        String eventCode = event.getEventCode();
        eventTmp.setEventCode(eventCode);
        eventTmp.setSourceSystem(event.getSourceSystem());
        eventTmp.setUserKey(event.getUserKey());
        eventTmp.setLoanKey(event.getLoanKey());
        eventTmp.set("strategyType",event.getString("strategyType"));
        eventTmp.set("amountParams",event.getParams());
        eventTmp.set("amountType", event.getString("amountType"));
        if (event.get(HhRandomDispatcherTaskService.IRR24) != null){
            eventTmp.set("isIrr24", event.get(HhRandomDispatcherTaskService.IRR24));
        }
        eventTmp.setVerifyResult(event.getVerifyResult());
        eventTmp.getVerifyResult().remove("reason_code");
        return eventTmp;
    }
}
