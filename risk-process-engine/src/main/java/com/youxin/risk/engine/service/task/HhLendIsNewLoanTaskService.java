package com.youxin.risk.engine.service.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.activiti.runtime.task.AbstractTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022-09-08
 * 放款审核输出是否是首贷标识定制节点
 */
public class HhLendIsNewLoanTaskService extends AbstractTaskService {

    private static final Logger LOGGER = LoggerFactory.getLogger(HhLendIsNewLoanTaskService.class);

    @Override
    public void execute(ProcessContext processContext) {
        Event event = processContext.getEvent();
        //默认是贷中
        event.getDataVo().put("isNewLoan", false);
        try {
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(event.getDataVo())).getJSONObject("thirdPartyData");
            /*
             * hh_dz_applyrepay_data_dr
             * if len(his_bill_repay_plan) == 0 or his_bill_repay_plan == 'INVALID':
             * his_bill_repay_plan = json.loads(x['repayment_plan_sd']['loans'])
             * repayment_plan_sd 依赖loanHistoryByUserKeyService 数据源
             *  特征逻辑 loans = x.get("loans", [])
             */
            //如果数据源存在且不为空
            if (jsonObject.containsKey("loanHistoryByUserKeyService") && jsonObject.get("loanHistoryByUserKeyService") != null) {
                JSONObject loanHistoryByUserKeyService = jsonObject.getJSONObject("loanHistoryByUserKeyService");
                if (loanHistoryByUserKeyService.containsKey("loans") && loanHistoryByUserKeyService.get("loans") != null){
                    Object loan = jsonObject.getJSONObject("loanHistoryByUserKeyService").get("loans");
                    //如果loans的值是INVALID 或者 loan = []
                    if ("INVALID".equals(loan.toString()) || JSONObject.parseArray(loan.toString()).isEmpty()) {
                        event.getDataVo().put("isNewLoan", true);
                    }
                }else {
                    event.getDataVo().put("isNewLoan", true);
                }
            } else {
                event.getDataVo().put("isNewLoan", true);
            }
            LoggerProxy.info("HhLendIsNewLoanTask", LOGGER,"loanKey = {} isNewLoan = {}",event.getLoanKey(),event.getDataVo().get("isNewLoan").toString());
        } catch (Exception e) {
            LoggerProxy.error("HhLendIsNewLoanTaskService", LOGGER, "get loanHistoryByUserKeyService loan error:", e);
        }finally{
            // 供gateway打点使用
            setIsNewLoan(event);
        }
    }

    private void setIsNewLoan(Event event){
        try{
            event.set("isNewLoan", event.getDataVo().get("isNewLoan"));
        }catch(Exception e){
            LoggerProxy.error("setIsNewLoan", LOGGER, "", e);
        }
    }
}
