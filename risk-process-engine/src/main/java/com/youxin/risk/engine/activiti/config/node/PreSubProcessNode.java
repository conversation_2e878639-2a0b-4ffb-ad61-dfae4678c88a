package com.youxin.risk.engine.activiti.config.node;

import com.youxin.risk.engine.activiti.constants.NodeTypeEnum;
import com.youxin.risk.engine.activiti.service.Command;
import com.youxin.risk.engine.activiti.service.impl.DefaultPreSubProcessService;

/**
 * <AUTHOR>
 * @create 2023/9/21 15:53
 * @desc
 */
public class PreSubProcessNode extends Node{

    private String subProcessDefId;

    @Override
    public Command getCommand() {
        return new DefaultPreSubProcessService(subProcessDefId);
    }

    @Override
    public String getNodeType() {
        return NodeTypeEnum.preSubProcess.name();
    }

    public PreSubProcessNode(String subProcessDefId, String id) {
        this.subProcessDefId = subProcessDefId;
        this.setId(id);
        this.setName("子流程前置置节点" + id);
    }

    public String getSubProcessDefId() {
        return subProcessDefId;
    }

    public void setSubProcessDefId(String subProcessDefId) {
        this.subProcessDefId = subProcessDefId;
    }
}
