package com.youxin.risk.engine.activiti.config.node;

import com.youxin.risk.engine.activiti.constants.TaskTypeEnum;
import com.youxin.risk.engine.activiti.service.Command;
import com.youxin.risk.engine.activiti.service.impl.DefaultSystemTaskService;

public class SystemTaskNode extends TaskNode {

    @Override
    public String getTaskType() {
        return TaskTypeEnum.systemTask.name();
    }


    @Override
    public Command getCommand() {
        return new DefaultSystemTaskService();
    }
}
