package com.youxin.risk.engine.service.task.data.renrendai;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.EngineAsyncRequestLog;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.RRDDataVoUtils;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.engine.service.task.data.ConfigurableDataHandler;
import com.youxin.risk.engine.vo.DiResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/12/6 14:04
 */
@Service
public class RRDFayanyuanCompanyHandler extends ConfigurableDataHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(RRDFayanyuanCompanyHandler.class);

    @Override
    public Map<String, Object> buildRequestParams(Event event, String dataCode, String nodeCode) {
        Map<String, Object> params = super.buildRequestParams(event, dataCode, nodeCode);
        JSONObject userBasicInfo = RRDDataVoUtils.getUserBasicInfo(event);
        params.put("name", userBasicInfo.getString("company"));
        return params;
    }

    @Override
    public DiResult callDi(Event event, String dataCode, EngineAsyncRequestLog engineAsyncRequestLog) {
        JSONObject userBasicInfo = RRDDataVoUtils.getUserBasicInfo(event);
        if (!userBasicInfo.containsKey("company") || StringUtils.isBlank(userBasicInfo.getString("company"))) {
            LoggerProxy.warn("companyBlankWarn", LOGGER, "");
            return DiResult.error("companyBlankWarn");
        }
        return super.callDi(event, dataCode, engineAsyncRequestLog);
    }
}
