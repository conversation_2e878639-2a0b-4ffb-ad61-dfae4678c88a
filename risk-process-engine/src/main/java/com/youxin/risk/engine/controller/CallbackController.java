package com.youxin.risk.engine.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.remote.model.RpcResponse;
import com.youxin.risk.commons.utils.LogUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.vo.EventVo;
import com.youxin.risk.commons.web.controller.BaseController;
import com.youxin.risk.engine.service.CallbackService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.youxin.risk.commons.mongo.VerifyResultDataDao.VERIFY_RESULT_VAR;


/**
 * <AUTHOR>
 * @date 2018/10/26 14:41
 */
@RestController
@RequestMapping("/callback")
public class CallbackController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(CallbackController.class);
    public static final int MAX_WIDTH = 500;

    @Resource
    private CallbackService callbackService;

    @Autowired
    @Qualifier("mongoTemplate")
    protected MongoTemplate template;


    @ResponseBody
    @RequestMapping("/queryRefreshMongo")
    public String queryRefreshMongo(@RequestBody JSONObject requestJson){
        String userKey = requestJson.getString("userKey");
        Query query = new Query();
        query.addCriteria(Criteria.where("userKey").is(userKey).and("eventCode").is("haoHuanVerify_lastResult"));
        List<EventVo> eventVos = template.find(query, EventVo.class, VERIFY_RESULT_VAR);
        LoggerProxy.info("查询VERIFY_RESULT_VAR", LOGGER, "userKey ={} eventVos = {}",userKey, JSON.toJSONString(eventVos));
        return JSON.toJSONString(eventVos);
    }

    @ResponseBody
    @RequestMapping("/refreshMongo")
    public String refreshMongo(@RequestBody JSONObject requestJson){
        String userKey = requestJson.getString("userKey");
        String brApiCode = requestJson.getString("brApiCode");
        String alsM3IdNbankOrgnum = requestJson.getString("alsM3IdNbankOrgnum");
        String loanLine = requestJson.getString("loanCreditAmount");

        Query query = new Query();
        query.addCriteria(Criteria.where("userKey").is(userKey).and("eventCode").is("haoHuanVerify_lastResult"));
        List<EventVo> eventVos = template.find(query, EventVo.class, VERIFY_RESULT_VAR);

        if (CollectionUtils.isEmpty(eventVos)){
            LoggerProxy.info("插入新数据", LOGGER, "userKey ={} brApiCode = {} alsM3IdNbankOrgnum = {} loanLine = {}",userKey,brApiCode,alsM3IdNbankOrgnum,loanLine);
            Map<String,Object> map = new HashMap<>();
            map.put("userKey",userKey);
            map.put("eventCode","haoHuanVerify_lastResult");

            Map<String, Object> verifyResult = new HashMap<>();
            verifyResult.put("brApiCode",brApiCode);
            verifyResult.put("alsM3IdNbankOrgnum",alsM3IdNbankOrgnum);
            verifyResult.put("loanCreditAmount",loanLine);
            map.put("verifyResult",verifyResult);
            map.put("createTime",new Date());
            template.insert(map,VERIFY_RESULT_VAR);
        }else {
            LoggerProxy.info("数据存在", LOGGER, "userKey ={}",userKey);
        }

        return buildSuccessResponse();
    }

    @ResponseBody
    @RequestMapping("/di")
    public String diCallback(@RequestBody JSONObject requestJson) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        String requestId = requestJson.getString("requestId");
        LogUtil.bindLogId(requestId);
        String response = "";
        try {
            callbackService.diCallback(requestJson);
            response = buildSuccessResponse();
        } catch (Exception e) {
            LoggerProxy.error("dealCallbackError", LOGGER, "requestId=" + requestId, e);
            response = buildFailedResponse();
        }
        long cost = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
        LoggerProxy.info("finishedCallbackMessage", LOGGER, "callback di,request={} response={}, cost={}", StringUtils.abbreviate(requestJson.toJSONString(),MAX_WIDTH), StringUtils.abbreviate(response,MAX_WIDTH), cost);
        return response;
    }

    @ResponseBody
    @RequestMapping("/variable")
    public String variableCallback(@RequestBody JSONObject requestJson) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        String requestId = requestJson.getString("requestId");
        LogUtil.bindLogId(requestId);
        String response = "";
        try {
            callbackService.variableCallback(requestJson);
            response = buildSuccessResponse();
        } catch (Exception e) {
            LoggerProxy.error("dealCallbackError", LOGGER, "request = {}, errMsg = ", requestJson.toJSONString(), e);
            response = buildFailedResponse();
        }
        long cost = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
        LoggerProxy.info("receivedCallbackMessage", LOGGER, "callback variable, message={} cost={}",StringUtils.abbreviate(requestJson.toJSONString(), MAX_WIDTH),cost);
        LogUtil.unbindLogId();
        return response;
    }

    
    @ResponseBody
    @RequestMapping("/channel")
    public String channelCallback(@RequestBody JSONObject requestJson) {
        String requestId = requestJson.getString("requestId");
        LogUtil.bindLogId(requestId);
        LoggerProxy.info("receivedChannelCallbackMessage", LOGGER, "message={}", requestJson.toJSONString());
        String response = "";
        try {
            callbackService.channelCallback(requestJson);
            response = buildSuccessResponse();
        } catch (Exception e) {
            LoggerProxy.error("dealChannelCallbackError", LOGGER, "request=" + requestJson.toJSONString(), e);
            response = buildFailedResponse();
        }
        LoggerProxy.info("finishedChannelCallbackMessage", LOGGER, "response={}", response);
        return response;
    }

    @ResponseBody
    @RequestMapping("/reviewcallback")
    public RpcResponse reviewCallback(@RequestBody JSONObject requestJson) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        String requestId = requestJson.getString("requestId");
        LogUtil.bindLogId(requestId);
        LoggerProxy.info("receivedReviewCallbackMessage", LOGGER, "message={}", requestJson.toJSONString());
        RpcResponse response;
        try {
            callbackService.reviewCallback(requestJson);
            response = new RpcResponse(requestId, RetCodeEnum.SUCCESS);
        } catch (Exception e) {
            LoggerProxy.error("dealReviewCallbackError", LOGGER, "request=" + requestJson.toJSONString(), e);
            response = this.generatorErrorResponse2(e);
        }
        long cost = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
        LoggerProxy.info("finishedReviewCallbackMessage", LOGGER, "response={}, cost={}", response, cost);
        return response;
    }

    private String buildSuccessResponse() {
        Map<String, Object> responseMap = Maps.newHashMap();
        responseMap.put("status", 0);
        return JSONObject.toJSONString(responseMap);
    }

    private String buildFailedResponse() {
        Map<String, Object> responseMap = Maps.newHashMap();
        responseMap.put("status", -1);
        return JSONObject.toJSONString(responseMap);
    }
}
