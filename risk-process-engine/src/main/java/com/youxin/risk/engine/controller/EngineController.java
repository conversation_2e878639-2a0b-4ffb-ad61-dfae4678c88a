package com.youxin.risk.engine.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.constants.PointConstant;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.kafkav2.KafkaContext;
import com.youxin.risk.commons.kafkav2.filter.impl.ParseFilter;
import com.youxin.risk.commons.model.EngineEvent;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.mongo.MongoDao;
import com.youxin.risk.commons.service.engine.EngineEventService;
import com.youxin.risk.commons.service.engine.EventService;
import com.youxin.risk.commons.utils.GlobalUtil;
import com.youxin.risk.commons.utils.LogUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.vo.EventVo;
import com.youxin.risk.commons.vo.TaskDataVo;
import com.youxin.risk.commons.web.controller.BaseController;
import com.youxin.risk.engine.clients.RiskDcClient;
import com.youxin.risk.engine.scheduler.EngineEventFailedTaskScheduler;
import com.youxin.risk.engine.service.EventHandler;
import com.youxin.risk.engine.service.EventProcessHandler;
import com.youxin.risk.engine.service.task.HhLendResultTaskService;
import com.youxin.risk.engine.service.task.engine.EngineEventMongoService;
import com.youxin.risk.metrics.MetricsAPI;
import com.youxin.risk.metrics.enums.MetricsOpType;
import com.youxin.risk.ra.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.youxin.risk.commons.mongo.VerifyResultDataDao.VERIFY_RESULT_VAR;

/**
 * <AUTHOR>
 * @date 2020/05/21 14:41
 */
@RestController
@RequestMapping("/engine")
public class EngineController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(EngineController.class);


    @Resource
    private EventService eventService;
    @Autowired
    private EngineEventMongoService engineEventMongoService;
    @Resource
    private EventProcessHandler eventProcessHandler;
    @Resource
    private EngineEventService engineEventService;
    @Resource(name = "hhLendResultTask")
    private HhLendResultTaskService hhLendResultTask;
    @Autowired
    private EventHandler eventHandler;

    @Resource
    @Qualifier("gatewayMessagePaserFilter")
    private ParseFilter parseFilter;

    @Autowired
    private MongoDao dao;

    @Autowired
    @Qualifier("mongoTemplate")
    protected MongoTemplate template;

    @Autowired
    EngineEventFailedTaskScheduler engineEventFailedTaskScheduler;

    @Resource
    private RiskDcClient riskDcClient;

    @ResponseBody
    @RequestMapping("/call")
    public void call(@RequestBody String message) {
        KafkaContext context = new KafkaContext();
        String logid = GlobalUtil.getSessionId();
        LogUtil.bindLogId(logid);
        context.setMessage(message);
        context.setLogId(logid);
        parseFilter.doFilter(context);

        Stopwatch stopwatch = Stopwatch.createStarted();
        // 组装event
        Event event;
        try {
            event = context.getMessageObject(Event.class);
        } catch (Exception e) {
            LoggerProxy.error("parseMessageError", logger, "parse message to event error, message=" + context.getMessage(), e);
            context.setTerminated(true);
            return;
        }
        if (StringUtils.isBlank(event.getSessionId())) {
            LoggerProxy.error("notFoundSessionId", logger, "event={}", event);
            context.setTerminated(true);
            return;
        }
        LogUtil.bindLogId(event.getSessionId());
        // 幂等逻辑：先把Event保存到Mongo，再把EngineEvent保存到MySQL。
        // 如果EngineEvent在MySQL里存在，则Event肯定也保存到了Mongo。
        try {
            EngineEvent engineEvent = engineEventService.getBySessionId(event.getSessionId());
            if (engineEvent != null) {
                LoggerProxy.warn("messageRepeat", logger, "");
                context.setTerminated(true);
                return;
            }
        } catch (Exception e) {
            LoggerProxy.warn("checkRepeatError", logger, "", e);
        }
        // 执行流程
        try {
            eventProcessHandler.initTask(event);
            eventHandler.asyncHandleEvent(event);
            context.setRetCode(RetCodeEnum.SUCCESS);
        } finally {
            long costTime = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
            // 埋点
            Map<String, String> tags = Maps.newHashMap();
            tags.put("sourceSystem", event.getSourceSystem());
            tags.put("eventCode", event.getEventCode());
            MetricsAPI.point(PointConstant.PE_HANDLER_POINT, tags,
                    costTime, true, MetricsOpType.timer);
            //LoggerProxy.info("finishedProcess", logger, "eventCode={}, cost={}", event.getEventCode(), costTime);
        }
        LogUtil.unbindLogId();
    }

    @ResponseBody
    @RequestMapping("/sendLendShadow")
    public void sendShadow(@RequestParam("sessionId") String sessionId){
        try{
//            Event event = eventService.getBySessionId(sessionId); todo 替换
            Event event = engineEventMongoService.getBySessionId(sessionId);
            hhLendResultTask.sendLendResult(event);
        }catch (Exception e){
            LoggerProxy.error("sendLendShadowError", logger, "", e);
        }
    }

    /**
     * FP调用引擎Engine
     * @param id
     * @param serviceCode
     * @param cacheTTL
     * @return
     */
    @RequestMapping("col/query/userKey/{id}/{serviceCode}/{cacheTTL}")
    public TaskDataVo queryByUserKey(@PathVariable String id, @PathVariable String serviceCode,@PathVariable Integer cacheTTL){
        //LoggerProxy.info("queryByUserKey",logger,"id = {}, serviceCode = {}, cacheTTL={}",id,serviceCode,cacheTTL);
        TaskDataVo tv = dao.getByUserKey(serviceCode,id,TaskDataVo.class);
        if(null == tv){
            return null;
        }

        if (cacheTTL!=0) {
            /** 当限制了缓存的时间的时候,相差范围内为有效数据 **/
            if (DateUtils.getDistDates(tv.getCreateTime(), new Date()) <= cacheTTL){
                return tv;
            }else {
                return null;
            }
        }else {
            return  tv;
        }
    }

    /**
     * 用于回溯事件结果变量表的数据
     * @param jsonObject
     * @return
     */
    @RequestMapping("/refreshResultVar")
    public String refreshResultVar(@RequestBody JSONObject jsonObject){
        List<String> uniqueKeys = JSONArray.parseArray(Optional.ofNullable(jsonObject.getString("uniqueKeys")).orElse("[]"),String.class);
        if(uniqueKeys.isEmpty()){
            return "uniqueKeys can not be empty!";
        }
        String operation = jsonObject.getString("operation");
        JSONObject data = jsonObject.getJSONObject("data");
        if(StringUtils.equals("insert",operation)){
            Map<String,Object> map = new HashMap<>();
            uniqueKeys.forEach(e->{
                map.put(e,data.remove(e));
            });
            Map<String, Object> verifyResult = new HashMap<>(data);
            map.put("verifyResult",verifyResult);
            map.put("createTime",new Date());
            template.insert(map,VERIFY_RESULT_VAR);
        }else if (StringUtils.equals("update",operation)){
            Criteria criteria=null;
            for (String key:uniqueKeys){
                if(Objects.isNull(criteria)){
                    criteria=Criteria.where(key).is(data.remove(key));
                }else {
                    criteria=criteria.and(key).is(data.remove(key));
                }
            }
            Update update = new Update();
            update.set("verifyResult",data);
            /** 增加更新时间 **/
            update.set("createTime",new Date());
            this.template.upsert(new Query(criteria),update, VERIFY_RESULT_VAR);
        }else if(StringUtils.equals("insertIfNotExist",operation)){
            Criteria criteria=null;
            for (String key:uniqueKeys){
                if(Objects.isNull(criteria)){
                    criteria=Criteria.where(key).is(data.get(key));
                }else {
                    criteria=criteria.and(key).is(data.get(key));
                }
            }
            Query query = new Query();
            query.addCriteria(criteria);
            List<EventVo> eventVos = template.find(query, EventVo.class, VERIFY_RESULT_VAR);
            if(eventVos.isEmpty()){
                Map<String,Object> map = new HashMap<>();
                uniqueKeys.forEach(e->{
                    map.put(e,data.remove(e));
                });
                Map<String, Object> verifyResult = new HashMap<>(data);
                map.put("verifyResult",verifyResult);
                map.put("createTime",new Date());
                template.insert(map,VERIFY_RESULT_VAR);
            }
        }
        return "success!";
    }

    /**
     * 测试环境定时任务不好用户，该接口用来手动唤醒指定事件
     */
    @RequestMapping("/wakeUpBySessionId")
    public void wakeUp(@RequestBody JSONObject jsonObject) {
        engineEventFailedTaskScheduler.handleEventBySessionId(jsonObject.getString("sessionId"));
    }

    /**
     * 发送额度信息到额度中心，测试使用
     * @param params
     * @return
     */
    @RequestMapping("/sendQuotaResult")
    public String sendQuotaResult(@RequestBody JSONObject params) {
        Event event = params.getObject("event", Event.class);
        JSONObject lineResult = params.getJSONObject("lineResult");
        try {
            // 发送额度信息到额度中心
            riskDcClient.sendQuotaResult(event, event.getString("strategyType"), lineResult);
            return "success";
        } catch (Exception e) {
            logger.error("sendQuotaResult error", e);
            return "error";
        }
    }
}
