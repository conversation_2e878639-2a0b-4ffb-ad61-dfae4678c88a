package com.youxin.risk.engine.service.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.DataVoUtils;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.activiti.runtime.task.AbstractTaskService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

/**
 * @description: 朴道审核结果与微财审核结果差异告警定制节点
 * @author: juxiang
 * @create: 2023-02-08 14:42
 **/
@Service
public class PuDaoDifferenceResultWarn extends AbstractTaskService {
    private static final Logger LOGGER = LoggerFactory.getLogger(PuDaoDifferenceResultWarn.class);
    private static final List<String> LEND_AUDIT_EVENTS = Arrays.asList("haoHuanLendAudit", "haoHuanLendAuditReloan", "fPudaoVerifyEmit");
    private static final String PU_DAO_VERIFY_SERVICE = "PuDaoVerifyService";
    private static final String IS_PASSED = "isPassed";

    private static final String TITLE="朴道fPudaoVerify事件结果差异告警:";
    private static final char ENTER='\n';

    private static final String robotKey="ce04dcd7-67d3-49cb-8cf1-db15a332123b";

    @Value("${wechat.robot.url}")
    private String weChatUrl;

    @Override
    public void execute(ProcessContext processContext) {
        try {
            Event event=processContext.getEvent();
            String eventCode = event.getEventCode();
            String weiCaiResult = null;
            String puDaoResult = null;
            // 获取朴道审核结果
            String puDaoVerifyServiceStr = String.valueOf(Optional.ofNullable(DataVoUtils.getThirdPartyData(event, PU_DAO_VERIFY_SERVICE)).orElse(""));
            if(StringUtils.isNotEmpty(puDaoVerifyServiceStr)){
                JSONObject puDaoVerifyService = JSON.parseObject(puDaoVerifyServiceStr);
                puDaoResult = puDaoVerifyService.getString("result");
            }
            if(LEND_AUDIT_EVENTS.contains(eventCode)){
                // 从上下文中获取结果，来比较朴道结果
                weiCaiResult = String.valueOf(Optional.ofNullable(event.getVerifyResult()).orElse(new HashMap<>()).getOrDefault(IS_PASSED,""));
            }

            if(!StringUtils.equals(weiCaiResult, puDaoResult)){
                this.sendWarn(event.getLoanKey(),event.getUserKey());
                LoggerProxy.info("PuDaoDifferenceResultWarn",LOGGER,"weiCaiResult:{},puDaoResult:{}",weiCaiResult,puDaoResult);
            }
        }catch (Exception e){
            LoggerProxy.error("PuDaoDifferenceResultWarn",LOGGER,"error:",e);
        }
    }

    private void sendWarn(String loanKey, String userKey) {
        JSONObject jsonObject=new JSONObject();
        StringBuilder stringBuilder=new StringBuilder();
        jsonObject.put("msgtype","text");
        stringBuilder.append(TITLE).append(ENTER).append("user_key:").append(userKey).append(ENTER).append("loan_key:").append(loanKey);
        JSONObject contentJson=new JSONObject();
        contentJson.put("content",stringBuilder.toString());
        jsonObject.put("text",contentJson);
        SyncHTTPRemoteAPI.postJson(weChatUrl + robotKey, jsonObject.toString(), 60000);
    }
}
