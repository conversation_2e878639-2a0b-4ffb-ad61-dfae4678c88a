package com.youxin.risk.engine.clients;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.youxin.risk.commons.constants.AppName;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.remote.model.RpcRequest;
import com.youxin.risk.commons.remote.model.RpcResponse;
import com.youxin.risk.commons.utils.JsonUtils;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * risk_channel服务客户端
 *
 * <AUTHOR>
 */
@Component
public class RiskChannelClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(RiskChannelClient.class);

    @Value("${channel.url}")
    private String url;

    private static final String HANDLER_URL = "/handler";

    private static int TIMEOUT_MILLIS = 60000;

    public RpcResponse callChannel(String requestId,Object params) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        RpcRequest request = this.buildRequest(requestId, params);
        LoggerProxy.info("beforeCallChannel", LOGGER, "request={}",request);
        String response = SyncHTTPRemoteAPI.postJson(url + HANDLER_URL, JsonUtils.toJson(request), TIMEOUT_MILLIS);
        long costTime = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
        LoggerProxy.info("afterCallChannel", LOGGER, "response={}, cost={}", response, costTime);
        if (StringUtils.isBlank(response)) {
            LoggerProxy.error("callChannelError", LOGGER, "get data error, response is null,request={}", request);
            RpcResponse rpcResponse = new RpcResponse();
            rpcResponse.setRetCode(RetCodeEnum.FAILED.getValue());
            return rpcResponse;
        }

        return JsonUtils.toObject(response, RpcResponse.class);
    }

    public RpcRequest buildRequest(String requestId, Object params) {
        RpcRequest<Object> request = new RpcRequest<>();
        request.setAppName(AppName.risk_process_engine.name());
        request.setRequestId(requestId);
        JSONObject data = new JSONObject();
        data.put("params", params);
        request.setData(data);

        return  request;
    }

}