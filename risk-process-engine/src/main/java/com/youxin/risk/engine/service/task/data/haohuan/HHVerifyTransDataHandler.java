package com.youxin.risk.engine.service.task.data.haohuan;

import com.youxin.risk.commons.model.Event;
import com.youxin.risk.engine.service.task.data.ConfigurableDataHandler;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-12-29
 */
@Service
public class HHVerifyTransDataHandler extends ConfigurableDataHandler {

    @Override
    public Map<String, Object> buildRequestParams(Event event, String dataCode, String nodeCode) {
        Map<String, Object> params = super.buildRequestParams(event, dataCode, nodeCode);
        params.put("loanId", event.get("loanId"));
        params.put("userKey", event.get("userKey"));
        return params;
    }

}
