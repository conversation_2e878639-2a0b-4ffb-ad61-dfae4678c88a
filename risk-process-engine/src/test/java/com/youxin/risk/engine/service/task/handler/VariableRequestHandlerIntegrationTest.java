package com.youxin.risk.engine.service.task.handler;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.model.VariableAsyncRequestLog;
import com.youxin.risk.engine.service.VariableRequestLogService;
import com.youxin.risk.engine.service.VariableRequestService;
import com.youxin.risk.engine.service.context.VariableExecutionContext;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import static org.junit.Assert.*;

/**
 * VariableRequestHandler集成测试
 * 使用真实依赖组件而非mock对象进行测试
 *
 * <AUTHOR>
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class VariableRequestHandlerIntegrationTest {

    @Autowired
    private VariableRequestHandler variableRequestHandler;


    private VariableExecutionContext context;
    private Event event;
    private List<VariableAsyncRequestLog> requestLogs;
    private String requestId;

    @Before
    public void setUp() {
        // 生成唯一请求ID
        requestId = UUID.randomUUID().toString().replace("-", "");
        
        // 初始化事件
        event = new Event();
        event.setCurrentStep("test_step");
        event.setMirror(false);
        event.setLoanKey("test_loan_key_" + System.currentTimeMillis());
        event.setUserKey("test_user_key");
        event.setEventCode("test_event_code");
        
        // 设置dataInput
        Map<String, Object> dataInput = new HashMap<>();
        dataInput.put("test_input", "test_value");
        dataInput.put("apiSource", "TEST");
        event.set("dataInput", dataInput);

        // 初始化上下文
        context = VariableExecutionContext.builder()
                .strategyNodeId("test_node")
                .currentNodeId("test_node")
                .isAutoRetry(false)
                .event(event)
                .requestId(requestId)
                .build();

        // 初始化变量列表
        List<String> prefetchVariableList = new ArrayList<>();
        prefetchVariableList.add("test_var1");
        prefetchVariableList.add("test_var2");
        context.setPrefetchVariableList(prefetchVariableList);
        
//        // 初始化缓存变量集合
//        Set<String> cachedVarCodes = new HashSet<>();
//        cachedVarCodes.add("test_var1");
//        context.setCachedVarCodes(cachedVarCodes);

        // 初始化请求日志
        requestLogs = new ArrayList<>();
        VariableAsyncRequestLog log = new VariableAsyncRequestLog();
        log.setAsyncRequestId(requestId);
        log.setStep("test_step");
        log.setVariableCode("test_var1");
        requestLogs.add(log);
        
        VariableAsyncRequestLog log2 = new VariableAsyncRequestLog();
        log2.setAsyncRequestId(requestId);
        log2.setStep("test_step");
        log2.setVariableCode("test_var2");
        requestLogs.add(log2);
    }

    /**
     * 测试正常发送变量请求
     * 集成测试环境下，验证与真实依赖组件的交互
     */
    @Test
    public void testSendVariableRequest_Normal() {
        // 执行测试
        boolean result = variableRequestHandler.sendVariableRequest(context);

        // 验证结果
        // 注意：在集成测试环境中，实际结果取决于真实组件的行为
        // 这里我们主要验证方法执行不抛异常，并检查上下文状态变化
        assertNotNull("数据源配置应该被设置", context.getDatasourceConfig());
        if (!result) {
            System.out.println("变量请求失败，这可能是由于测试环境中变量中心服务不可用");
            System.out.println("数据源配置: " + JSON.toJSONString(context.getDatasourceConfig()));
        }
        
        // 验证请求参数字符串是否被设置（非自动重试场景）
        String paramsStr = event.clearVariableRequestParamsStr();
        assertNotNull("变量请求参数字符串应该被设置", paramsStr);
        System.out.println("变量请求参数: " + paramsStr);
    }

    /**
     * 测试自动重试场景下的变量请求
     * 集成测试环境下，验证与真实依赖组件的交互
     */
    @Test
    public void testSendVariableRequest_AutoRetry() {
        // 设置为自动重试场景
        context = VariableExecutionContext.builder()
                .strategyNodeId("test_node")
                .currentNodeId("test_node")
                .isAutoRetry(true)
                .event(event)
                .requestId(requestId)
                .build();
        
        // 设置变量列表和缓存变量集合
        List<String> prefetchVariableList = new ArrayList<>();
        prefetchVariableList.add("test_var1");
        prefetchVariableList.add("test_var2");
        context.setPrefetchVariableList(prefetchVariableList);
        
//        Set<String> cachedVarCodes = new HashSet<>();
//        cachedVarCodes.add("test_var1");
//        context.setCachedVarCodes(cachedVarCodes);

        // 执行测试
        boolean result = variableRequestHandler.sendVariableRequest(context);

        // 验证结果
        assertNotNull("数据源配置应该被设置", context.getDatasourceConfig());
        if (!result) {
            System.out.println("变量请求失败，这可能是由于测试环境中变量中心服务不可用");
            System.out.println("数据源配置: " + JSON.toJSONString(context.getDatasourceConfig()));
        }
        
        // 自动重试场景下不应设置变量请求参数字符串
        assertNull("自动重试场景下不应设置变量请求参数字符串", event.clearVariableRequestParamsStr());
    }

    /**
     * 测试基于事件的变量请求（重试场景）
     * 集成测试环境下，验证与真实依赖组件的交互
     */
    @Test
    public void testSendVariableRequest_WithEvent() {
        // 执行测试
        boolean result = variableRequestHandler.sendVariableRequest(event, requestLogs);

        // 验证结果
        if (!result) {
            System.out.println("变量请求失败，这可能是由于测试环境中变量中心服务不可用");
        }
        
        // 验证方法执行不抛异常
        System.out.println("基于事件的变量请求执行完成，结果: " + result);
    }

    /**
     * 测试基于事件的变量请求 - 空日志列表
     * 集成测试环境下，验证与真实依赖组件的交互
     */
    @Test
    public void testSendVariableRequest_WithEvent_EmptyLogs() {
        // 准备空日志列表
        List<VariableAsyncRequestLog> emptyLogs = new ArrayList<>();

        // 执行测试
        boolean result = variableRequestHandler.sendVariableRequest(event, emptyLogs);

        // 验证结果
        assertFalse("空日志列表应该返回false", result);
    }
}