package com.youxin.risk.engine.service.task;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.Event;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.JUnitCore;
import org.junit.runner.Result;
import org.junit.runner.RunWith;
import org.junit.runner.notification.Failure;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

/**
 * StrategyTaskBaseNewService的单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class StrategyTaskBaseNewServiceTest {

    @InjectMocks
    private TestStrategyTaskBaseNewService strategyTaskBaseNewService;

    private Event event;

    @Before
    public void setUp() {
        event = new Event();
    }

    /**
     * 测试transfer方法处理普通JSON结果
     */
    @Test
    public void testTransferWithRegularResult() {
        // 设置
        String result = "{\"is_passed\": true, \"score\": 85, \"reason_code\": {\"code\": \"001\", \"description\": \"Test reason\"}}";
        event.setEventCode("testEvent");

        // 执行
        JSONObject verifyResult = strategyTaskBaseNewService.transfer(result, event);

        // 验证
        assertNotNull(verifyResult);
        assertEquals("ACCEPT", verifyResult.getString("verifyResult"));
        assertTrue(verifyResult.getBoolean("isPassed"));
        assertEquals(85, verifyResult.getIntValue("score"));
        assertNotNull(verifyResult.getJSONObject("reasonCode"));
        assertEquals("001", verifyResult.getJSONObject("reasonCode").getString("code"));
        assertEquals("Test reason", verifyResult.getJSONObject("reasonCode").getString("description"));
    }

    /**
     * 测试transfer方法处理haoHuanLendAudit事件代码
     */
    @Test
    public void testTransferWithHaoHuanLendAudit() {
        // 设置
        String result = "{\"is_passed\": true, \"score\": 85, \"reason_code\": {\"code\": \"001\", \"description\": \"Test reason\", \"diversion_para\": {\"param1\": \"value1\"}}}";
        event.setEventCode("haoHuanLendAudit");

        // 执行
        JSONObject verifyResult = strategyTaskBaseNewService.transfer(result, event);

        // 验证
        assertNotNull(verifyResult);
        assertEquals("ACCEPT", verifyResult.getString("verifyResult"));
        assertTrue(verifyResult.getBoolean("isPassed"));
        assertEquals(85, verifyResult.getIntValue("score"));
        assertNotNull(verifyResult.getJSONObject("reasonCode"));
        assertNotNull(verifyResult.getJSONObject("reasonCode").getJSONObject("diversion_para"));
        assertEquals("value1", verifyResult.getJSONObject("reasonCode").getJSONObject("diversion_para").getString("param1"));
    }

    /**
     * 测试transfer方法处理haoHuanLendAuditReloan事件代码
     */
    @Test
    public void testTransferWithHaoHuanLendAuditReloan() {
        // 设置
        String result = "{\"is_passed\": false, \"score\": 65, \"reason_code\": {\"code\": \"002\", \"description\": \"Reject reason\", \"diversion_para\": {\"param1\": \"value1\"}}}";
        event.setEventCode("haoHuanLendAuditReloan");

        // 执行
        JSONObject verifyResult = strategyTaskBaseNewService.transfer(result, event);

        // 验证
        assertNotNull(verifyResult);
        assertEquals("REJECT", verifyResult.getString("verifyResult"));
        assertFalse(verifyResult.getBoolean("isPassed"));
        assertEquals(65, verifyResult.getIntValue("score"));
        assertNotNull(verifyResult.getJSONObject("reasonCode"));
        assertNotNull(verifyResult.getJSONObject("reasonCode").getJSONObject("diversion_para"));
        assertEquals("value1", verifyResult.getJSONObject("reasonCode").getJSONObject("diversion_para").getString("param1"));
    }

    /**
     * 测试transfer方法处理没有isPassed字段的结果
     */
    @Test
    public void testTransferWithoutIsPassedField() {
        // 设置
        String result = "{\"score\": 85, \"reason_code\": {\"code\": \"001\", \"description\": \"Test reason\"}}";
        event.setEventCode("testEvent");

        // 执行
        JSONObject verifyResult = strategyTaskBaseNewService.transfer(result, event);

        // 验证
        assertNotNull(verifyResult);
        assertFalse(verifyResult.containsKey("verifyResult"));
        assertEquals(85, verifyResult.getIntValue("score"));
        assertNotNull(verifyResult.getJSONObject("reasonCode"));
    }

    /**
     * 用于测试的StrategyTaskBaseNewService实现类
     */
    private static class TestStrategyTaskBaseNewService extends StrategyTaskBaseNewService {
        @Override
        public void transferStrategyResult(com.youxin.risk.engine.activiti.context.ProcessContext processContext, Event event, java.util.Map<String, Object> result) {
            // 不需要实现
        }

        protected JSONObject transfer(String result, Event event) {
            return super.transfer(result, event, 1111L);
        }
    }

    /**
     * 直接运行测试的主方法
     */
    public static void main(String[] args) {
        Result result = JUnitCore.runClasses(StrategyTaskBaseNewServiceTest.class);

        for (Failure failure : result.getFailures()) {
            System.out.println(failure.toString());
        }

        System.out.println("测试运行数: " + result.getRunCount());
        System.out.println("测试失败数: " + result.getFailureCount());
        System.out.println("测试忽略数: " + result.getIgnoreCount());
        System.out.println("测试是否成功: " + (result.wasSuccessful() ? "是" : "否"));
    }
}
