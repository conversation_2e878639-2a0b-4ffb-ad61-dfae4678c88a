package com.youxin.risk.engine.service.task.datasource;

import com.youxin.risk.commons.constants.EngineProcessType;
import com.youxin.risk.commons.dao.admin.NodeConfigMapper;
import com.youxin.risk.commons.model.DatasourceConfigDO;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.engine.service.context.VariableExecutionContext;
import com.youxin.risk.engine.service.task.datasource.strategy.MirrorDataSourceConfigStrategy;
import com.youxin.risk.engine.service.task.datasource.strategy.OnlineDataSourceConfigStrategy;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * DataSourceConfigService单元测试
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class DataSourceConfigServiceTest {

    
    @InjectMocks
    private OnlineDataSourceConfigStrategy onlineDataSourceConfigStrategy;
    
    @InjectMocks
    private MirrorDataSourceConfigStrategy mirrorDataSourceConfigStrategy;

    @Mock
    private NodeConfigMapper nodeConfigMapper;


    String strategyNodeId = "test_node_id";

    private VariableExecutionContext context;

    @Before
    public void setUp() {
        // 配置策略工厂返回Mock策略
        Event event = new Event();
        context = VariableExecutionContext.builder()
                .event(event)
                .strategyNodeId(strategyNodeId).build();
        
        // 配置策略工厂返回对应的策略实例

    }

    /**
     * 测试正常流程 - 非镜像环境
     */
    @Test
    public void testBuildDatasourceConfig_Normal() {
        // 准备测试数据
        List<DatasourceConfigDO> configList = createTestConfigList();

        // 配置Mock行为
        when(nodeConfigMapper.selectDatasourceConfigByStrategyNode(strategyNodeId)).thenReturn(configList);

        // 执行测试
        Map<String, Object> result = onlineDataSourceConfigStrategy.buildDatasourceConfig(context);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(100L, result.get("datasource1"));
        assertEquals(101L, result.get("datasource2"));
    }

    /**
     * 测试镜像环境
     */
    @Test
    public void testBuildDatasourceConfig_Mirror() {
        // 准备测试数据
        List<DatasourceConfigDO> configList = createTestConfigList();
        context.getEvent().setMirror(true);

        // 配置Mock行为
        when(nodeConfigMapper.selectDatasourceConfigByStrategyNode(strategyNodeId)).thenReturn(configList);


        // 执行测试
        Map<String, Object> result = mirrorDataSourceConfigStrategy.buildDatasourceConfig(context);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(MirrorDataSourceConfigStrategy.DEFAULT_VARIABLE_CENTER_DATASOURCE_TEMPLATE_ID, result.get("datasource1"));
        assertEquals(MirrorDataSourceConfigStrategy.DEFAULT_VARIABLE_CENTER_DATASOURCE_TEMPLATE_ID, result.get("datasource2"));
    }

    /**
     * 测试空配置列表场景
     */
    @Test
    public void testBuildDatasourceConfig_EmptyList() {
        // 准备测试数据

        List<DatasourceConfigDO> emptyList = new ArrayList<>();

        // 配置Mock行为
        when(nodeConfigMapper.selectDatasourceConfigByStrategyNode(strategyNodeId)).thenReturn(emptyList);

        // 执行测试
        Map<String, Object> result = onlineDataSourceConfigStrategy.buildDatasourceConfig(context);


        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }



    // 辅助方法 - 创建测试配置列表
    private List<DatasourceConfigDO> createTestConfigList() {
        List<DatasourceConfigDO> configList = new ArrayList<>();

        DatasourceConfigDO config1 = new DatasourceConfigDO();
        config1.setDatasourceCode("datasource1");
        config1.setVariableCenterId(100L);
        configList.add(config1);

        DatasourceConfigDO config2 = new DatasourceConfigDO();
        config2.setDatasourceCode("datasource2");
        config2.setVariableCenterId(101L);
        configList.add(config2);

        return configList;
    }
}