package com.youxin.risk.engine.service.task.mirror.impl;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.model.StrategyExp;
import com.youxin.risk.engine.service.ApolloClientAdapterWrapper;
import com.youxin.risk.engine.service.task.mirror.MirrorServiceHandler;
import com.youxin.risk.engine.service.task.mirror.impl.EventMirrorServiceImpl;
import com.youxin.risk.engine.service.task.mirror.impl.StrategyTypeMirrorServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;

/**
 * MirrorServiceHandler Spring集成测试
 * 使用真实的Spring上下文和依赖组件测试镜像服务处理器
 *
 * <AUTHOR>
 */
@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration("classpath:spring/spring-config.xml")
@ContextConfiguration(locations = {"classpath:spring/spring-config.xml",
        "classpath:spring/spring-config-test-new.xml"})
public class MirrorServiceHandlerSpringIntegrationTest {

    @Autowired
    private MirrorServiceHandler mirrorServiceHandler;

    @Autowired
    private ApolloClientAdapterWrapper apolloClientAdapterWrapper;

    @Autowired
    private EventMirrorServiceImpl eventMirrorServiceImpl;

    @Autowired
    private StrategyTypeMirrorServiceImpl strategyTypeMirrorServiceImpl;

    private Event event;
    private String processDefId;

    @Before
    public void setUp() {
        // 初始化Mockito注解
//        MockitoAnnotations.initMocks(this);
        
        // 初始化测试数据
        processDefId = UUID.randomUUID().toString();

        // 创建事件对象
        event = new Event();
        event.setLoanKey("test_loan_key_" + System.currentTimeMillis());
        event.setUserKey("test_user_key");
        event.setEventCode("test_event_code");
        event.setMirror(false);
        // 创建流程定义对象 - 在每个测试方法中根据需要设置或从数据库获取
        
//        // 获取EventMirrorServiceImpl实例并注入mock的apolloClientAdapterWrapper
//        try {
//            // 通过反射获取mirrorServiceHandler中的mirrorServiceFactory
//            java.lang.reflect.Field factoryField = MirrorServiceHandler.class.getDeclaredField("mirrorServiceFactory");
//            factoryField.setAccessible(true);
//            MirrorServiceFactory factory = (MirrorServiceFactory) factoryField.get(mirrorServiceHandler);
//
//            // 获取EVENT类型的MirrorService实现（EventMirrorServiceImpl）
//            MirrorService eventMirrorService = factory.getMirrorService(MirrorService.MirrorType.EVENT);
//
//            // 注入mock的apolloClientAdapterWrapper
//            java.lang.reflect.Field wrapperField = eventMirrorService.getClass().getDeclaredField("apolloClientAdapterWrapper");
//            wrapperField.setAccessible(true);
//            wrapperField.set(eventMirrorService, apolloClientAdapterWrapper);
//        } catch (Exception e) {
//            System.err.println("Failed to inject mock: " + e.getMessage());
//            e.printStackTrace();
//        }
    }

    /**
     * 测试正常启动镜像 - 主流程场景
     * 使用真实的Spring上下文和依赖组件验证镜像启动流程
     */
    @Test
    public void testStartMirror_processNotExist() {
        // 准备测试数据 - 从数据库获取真实的流程定义或创建测试数据
        // 注意：此处需要确保数据库中存在对应的流程定义，或者在测试前插入测试数据
        
        // 执行测试
        boolean result = mirrorServiceHandler.startMirror(event, processDefId);
        
        // 验证结果 - 根据实际情况调整断言
        // 如果数据库中不存在对应的流程定义，则预期结果为false
        // 如果存在且镜像服务正常，则预期结果为true
        // 此处仅作示例，实际测试需要根据环境调整
        assertFalse("如果流程定义不存在，应该返回false", result);
    }

    /**
     * 集成测试 - 使用数据库中的真实流程定义
     * 注意：此测试需要数据库中存在有效的流程定义数据
     */
    @Test
    public void testStartMirror_WithRealProcessDefinition_sub_false() {
        // 从数据库获取一个真实存在的流程定义ID
        // 此处仅作示例，实际测试需要根据环境调整
        String realProcessDefId = "AnotherNewEngineSub#1";
        event.setCurrentStrategyType("ANOTHER_NEW_ENGINE");
        Mockito.when(strategyTypeMirrorServiceImpl.findEffectiveExperiment(anyString())).thenReturn(null);

        
        // 执行测试
        boolean result = mirrorServiceHandler.startMirror(event, realProcessDefId);
        
        // 验证结果 - 根据实际情况调整断言
        // 此处不做断言，因为结果取决于实际环境
        assertFalse("没有启用状态的策略镜像，应该返回false", result);
        System.out.println("使用真实流程定义测试结果: " + result);
    }

    /**
     * 集成测试 - 使用数据库中的真实流程定义
     * 注意：此测试需要数据库中存在有效的流程定义数据
     */
    @Test
    public void testStartMirror_WithRealProcessDefinition_sub_true() {
        // 从数据库获取一个真实存在的流程定义ID
        // 此处仅作示例，实际测试需要根据环境调整
        String realProcessDefId = "NewEngineProcess#11";
        event.setCurrentStrategyType("NewEngineTestType");
        StrategyExp strategyExp = new StrategyExp();
        strategyExp.setId(1L);
        Mockito.when(strategyTypeMirrorServiceImpl.findEffectiveExperiment(anyString())).thenReturn(strategyExp);


        // 执行测试
        boolean result = mirrorServiceHandler.startMirror(event, realProcessDefId);

        // 验证结果 - 根据实际情况调整断言
        // 此处不做断言，因为结果取决于实际环境
        assertTrue("存在启用状态的策略镜像，应该返回true", result);
        System.out.println("使用真实流程定义测试结果: " + result);
    }

    /**
     * 集成测试 - 使用数据库中的真实流程定义
     * 注意：此测试需要数据库中存在有效的流程定义数据
     */
    @Test
    public void testStartMirror_WithRealProcessDefinition_main_false() {
        // 从数据库获取一个真实存在的流程定义ID
        // 此处仅作示例，实际测试需要根据环境调整
        String realProcessDefId = "AnotherNewEngineMain#10";

        // 执行测试
        boolean result = mirrorServiceHandler.startMirror(event, realProcessDefId);

        // 验证结果 - 根据实际情况调整断言
        // 此处不做断言，因为结果取决于实际环境
        assertFalse("没有启用状态的策略镜像，应该返回false 且 nacos中没有配置流程镜像", result);
        System.out.println("使用真实流程定义测试结果: " + result);
    }

    /**
     * 集成测试 - 使用数据库中的真实流程定义
     * 注意：此测试需要数据库中存在有效的流程定义数据
     */
    @Test
    public void testStartMirror_WithRealProcessDefinition_main_true() {
        // 从数据库获取一个真实存在的流程定义ID
        // 此处仅作示例，实际测试需要根据环境调整
        String realProcessDefId = "AnotherNewEngineMain#10";
        event.setEventCode("test_event_code_true");
        
        Map<String, JSONObject> processMirrorConfig = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("processDefId", "NewEngineMainprocess#15");
        jsonObject.put("beginTime", "2025-04-01 10:00:00");
        jsonObject.put("endTime", "2025-05-31 00:00:00");
        jsonObject.put("ratio", 100);
        processMirrorConfig.put("test_event_code_true", jsonObject);
        
        // 使用包装类而不是直接模拟静态方法
        Mockito.when(apolloClientAdapterWrapper.getMapConfig(ApolloNamespaceEnum.ENGINE_SPACE,
                "process.mirror.config",
                JSONObject.class)).thenReturn(processMirrorConfig);

        StrategyExp strategyExp = new StrategyExp();
        strategyExp.setId(1L);
        Mockito.when(eventMirrorServiceImpl.findEffectiveExperiment(anyString())).thenReturn(strategyExp);

        // 执行测试
        boolean result = mirrorServiceHandler.startMirror(event, realProcessDefId);

        // 验证结果 - 根据实际情况调整断言
        // 此处不做断言，因为结果取决于实际环境
        assertTrue("存在启用状态的策略镜像， 且 nacos中配置流程镜像，应该返回true", result);
        System.out.println("使用真实流程定义测试结果: " + result);
    }
}