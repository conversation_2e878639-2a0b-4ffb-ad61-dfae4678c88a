~~~mermaid
graph TD;
    IRRAFL --> route343357b2;
    hhUserLevel --> hhDataVo;
    preSubProcessNodeForFUNDROUTENEW#5 --> HHFUNDROUTEvar;
    route8aa1017d --> |eventverifyResultverifyResult == ACCEPT| FLForIrr24AcceptWriteBack;
    route8aa1017d --> |eventverifyResultverifyResult == REJECT| FLirr36ForIrr24AcceptWriteBack;
    IRREFL --> route458a3f09;
    whiteListCheckTask --> |eventparamswhiteListUser == false| preSubProcessNodeForANTIFRAUDVERIFY#0;
    whiteListCheckTask --> |eventparamswhiteListUser == true| preSubProcessNodeForWHITELISTVERIFY#0;
    postSubProcessNodeForWHITELISTVERIFY#0 --> route7e98680f;
    postSubProcessNodeForWHITELISTVERIFYAMOUNT#2 --> whiteListWriteBack;
    FLirr36ForIrrReject36WriteBack --> route756520ee;
    verifyEndNotifyTaskService --> hhVerifyBaseinfo;
    FLIrrAccept36WriteBack --> route756520ee;
    startNode --> hhUserBasicData;
    IRRAMOUNTFL36 --> Irr36VerifyLoanAmountWrite;
    IRRAMOUNTFLvar --> IRRAMOUNTFL;
    postSubProcessNodeForANTIFRAUDVERIFY#0 --> route2ff2dc3a;
    route756520ee --> hhResultTransform;
    HHFUNDROUTEvar --> HHFUNDROUTE;
    lastVerifyResultVarWrite --> route756520ee;
    route458a3f09 --> |eventverifyResultverifyResult == ACCEPT| IRRFFLvar;
    route458a3f09 --> |eventverifyResultverifyResult == REJECT| postSubProcessNodeForIRR36VERIFY#1;
    IRRBFLvar --> IRRBFL;
    hhResultTransform --> verifyEndNotifyTaskService;
    IRRBFL --> routebf84d917;
    preSubProcessNodeForIRR36VERIFY#1 --> IRRPDFLvar;
    IRRFFL --> postSubProcessNodeForIRR36VERIFY#1;
    routed2f644e4 --> |eventverifyResultverifyResult == ACCEPT| IRRDFLvar;
    routed2f644e4 --> |eventverifyResultverifyResult == REJECT| postSubProcessNodeForIRR36VERIFY#1;
    whiteListAvar --> whiteListA;
    FLIrr24AcceptWriteBack --> FLIrrAccept36WriteBack;
    Irr24VerifyLoanAmountWrite --> postSubProcessNodeForIRR24VERIFYAMOUNT#2;
    whiteListAmountAvar --> whiteListAmountA;
    hhDataVo --> whiteListCheckTask;
    route7e98680f --> |eventverifyResultverifyResult == ACCEPT| preSubProcessNodeForWHITELISTVERIFYAMOUNT#2;
    route7e98680f --> |eventverifyResultverifyResult == REJECT| whiteListWriteBack;
    preSubProcessNodeForIRR24VERIFY#2 --> IRRPAFLvar;
    postSubProcessNodeForIRR36VERIFYAMOUNT#2 --> preSubProcessNodeForFUNDROUTENEW#5;
    route7d189dbc --> |eventverifyResultverifyResult == ACCEPT| IRRAFLvar;
    route7d189dbc --> |eventverifyResultverifyResult == REJECT| postSubProcessNodeForIRR24VERIFY#2;
    IRRCFL --> postSubProcessNodeForIRR24VERIFY#2;
    route8fa5e9c1 --> |eventverifyResultverifyResult == ACCEPT| FLirr36ForIrrAccept36WriteBack;
    route8fa5e9c1 --> |eventverifyResultverifyResult == REJECT| FLirr36ForIrrReject36WriteBack;
    route343357b2 --> |eventverifyResultverifyResult == ACCEPT| IRRBFLvar;
    route343357b2 --> |eventverifyResultverifyResult == REJECT| postSubProcessNodeForIRR24VERIFY#2;
    postSubProcessNodeForFUNDROUTENEW#5 --> lastVerifyResultVarWrite;
    HHFUNDROUTE --> postSubProcessNodeForFUNDROUTENEW#5;
    WhiteListVerifyLoanAmountWrite --> postSubProcessNodeForWHITELISTVERIFYAMOUNT#2;
    whiteListWriteBack --> route756520ee;
    hhUserBasicData --> hhUserLevel;
    IRRPAFLvar --> IRRPAFL;
    IRRPDFLvar --> IRRPDFL;
    FLirr36ForIrr24AcceptWriteBack --> preSubProcessNodeForIRR36VERIFY#1;
    FLForIrr24AcceptWriteBack --> FLForIrrAccept36WriteBack;
    IRREFLvar --> IRREFL;
    IRRFFLvar --> IRRFFL;
    IRRPAFL --> route7d189dbc;
    preSubProcessNodeForWHITELISTVERIFY#0 --> whiteListAvar;
    IRRDSFLvar --> IRRDSFL;
    IRRDFLvar --> IRRDFL;
    IRRPAFLAFvar --> IRRPAFLAF;
    hhVerifyBaseinfo --> endNode;
    preSubProcessNodeForWHITELISTVERIFYAMOUNT#2 --> whiteListAmountAvar;
    routec0a21179 --> |eventverifyResultverifyResult == ACCEPT| IRRDSFLvar;
    routec0a21179 --> |eventverifyResultverifyResult == REJECT| postSubProcessNodeForIRR36VERIFY#1;
    IRRAFLvar --> IRRAFL;
    IRRCFLvar --> IRRCFL;
    whiteListA --> postSubProcessNodeForWHITELISTVERIFY#0;
    IRRAMOUNTFL36var --> IRRAMOUNTFL36;
    IRRDSFL --> route28869af0;
    IRRPAFLAF --> postSubProcessNodeForANTIFRAUDVERIFY#0;
    IRRDFL --> routec0a21179;
    preSubProcessNodeForIRR36VERIFYAMOUNT#2 --> IRRAMOUNTFL36var;
    preSubProcessNodeForIRR24VERIFYAMOUNT#2 --> IRRAMOUNTFLvar;
    routebf84d917 --> |eventverifyResultverifyResult == ACCEPT| IRRCFLvar;
    routebf84d917 --> |eventverifyResultverifyResult == REJECT| postSubProcessNodeForIRR24VERIFY#2;
    preSubProcessNodeForANTIFRAUDVERIFY#0 --> IRRPAFLAFvar;
    whiteListAmountA --> WhiteListVerifyLoanAmountWrite;
    postSubProcessNodeForIRR36VERIFY#1 --> route8fa5e9c1;
    route2ff2dc3a --> |eventverifyResultverifyResult == REJECT| FLIrr24AcceptWriteBack;
    route2ff2dc3a --> |eventverifyResultverifyResult == ACCEPT| preSubProcessNodeForIRR24VERIFY#2;
    FLirr36ForIrrAccept36WriteBack --> preSubProcessNodeForIRR36VERIFYAMOUNT#2;
    route28869af0 --> |eventverifyResultverifyResult == ACCEPT| IRREFLvar;
    route28869af0 --> |eventverifyResultverifyResult == REJECT| postSubProcessNodeForIRR36VERIFY#1;
    FLForIrrAccept36WriteBack --> preSubProcessNodeForIRR24VERIFYAMOUNT#2;
    Irr36VerifyLoanAmountWrite --> postSubProcessNodeForIRR36VERIFYAMOUNT#2;
    postSubProcessNodeForIRR24VERIFY#2 --> route8aa1017d;
    IRRAMOUNTFL --> Irr24VerifyLoanAmountWrite;
    IRRPDFL --> routed2f644e4;
    postSubProcessNodeForIRR24VERIFYAMOUNT#2 --> preSubProcessNodeForFUNDROUTENEW#5;

~~~