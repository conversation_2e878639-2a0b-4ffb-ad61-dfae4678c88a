package com.youxin.risk.channel.service.builder.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.channel.service.DataPlatformMessageHandler;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.constants.SourceSystemEnum;
import com.youxin.risk.commons.exception.RiskRuntimeException;
import com.youxin.risk.commons.model.ChannelRequestAgency;
import com.youxin.risk.commons.model.ChannelRequestModel;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.mongo.MongoDao;
import com.youxin.risk.commons.utils.DateUtil;
import com.youxin.risk.commons.utils.GlobalUtil;
import com.youxin.risk.commons.utils.JacksonUtil;
import com.youxin.risk.commons.utils.LoggerProxy;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.*;

import javax.annotation.Resource;

@Service("BHXTMessageBuilder")
public class BHXTMessageBuilder extends AbstractAgencyMessageBuilder implements InitializingBean {

//    private Map<String, String> bankInfoModels;
//    private Map<String, String> idAreaModels;

    private static final Logger LOGGER = LoggerFactory.getLogger(BHXTMessageBuilder.class);

	@Resource
	private DataPlatformMessageHandler dataPlatformMessageHandler;
	@Resource
	private MongoDao mongoDao;

    @Override
    public void afterPropertiesSet() throws Exception {

    }

    @Override
    public String build(ChannelRequestModel model, ChannelRequestAgency agencyModel) {

        Event request = JacksonUtil.toObject(model.getRequestMessage(), Event.class);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("systemId", "HAO_HUAN");
        jsonObject.put("nextMethod", "riskCtrl");

        if (StringUtils.isBlank(agencyModel.getRequestAgencyId())) {
            String id = GlobalUtil.getGlobalId().substring(1);
            // yyyyMMdd{seq}
            agencyModel.setRequestAgencyId(DateUtil.formatCurrent(DateUtil.SHORT_FORMAT) + id);
        }
        jsonObject.put("outTradeNo", agencyModel.getRequestAgencyId());

		// 需根据jobId自行去DP获取相关信息组装对应参数, lengthOfNetworkTime & contactsSum
		buildDpData(request, model);
		jsonObject.put("userInfo", buildUserInfo(request));
		jsonObject.put("loanInfo", buildLoannfo(request));
		jsonObject.put("relevantInfo", buildEmptyJson(request));

		jsonObject.put("deviceInfo", buildDeviceInfo(request));
		jsonObject.put("creditInfo", buildCreditInfo(request));

		// 数组
		jsonObject.put("contacts", buildContacts(request));
		jsonObject.put("houses", buildEmptyJsonArray(request));
		jsonObject.put("vehicles", buildEmptyJsonArray(request));

		jsonObject.put("idCardNo", request.getString("idNo"));


        return jsonObject.toJSONString();
    }

	private void buildDpData(Event request, ChannelRequestModel model) {
		String sourceSystem = model.getSourceSystem();
		if (StringUtils.isEmpty(sourceSystem) || SourceSystemEnum.RA.name().equals(sourceSystem)) {
			return;
		}
		// 需根据jobId自行去DP获取相关信息组装对应参数, lengthOfNetworkTime & contactsSum
		int contactSum = 0;
		int lengthOfNetworkTime = 0;
		// 获取联系人num
		String phoneBookJobId = request.getString("phoneBookJobId");
		if (StringUtils.isNotEmpty(phoneBookJobId)) {
			String phoneBookResp = dataPlatformMessageHandler.getDataFromDp("PHONE_BOOK", phoneBookJobId);
			JSONObject phoneBookRespObj = JSONObject.parseObject(phoneBookResp);
			if (phoneBookRespObj != null) {
				List phoneBooks = phoneBookRespObj.getJSONArray("phoneBooks");
				if (CollectionUtils.isNotEmpty(phoneBooks)) {
					contactSum = phoneBooks.size();
				}
			}
		}
		// 获取在网时长
		String callHistoryJobId = request.getString("callHistoryJobId");
        if (StringUtils.isNotBlank(callHistoryJobId)) {
            List<JSONObject> result = this.getCallRecords(callHistoryJobId);
            if (CollectionUtils.isNotEmpty(result)) {
                JSONObject callHistoryRec = result.get(0);
                if (callHistoryRec.getJSONObject("userInfo")!=null &&
                        callHistoryRec.getJSONObject("userInfo").getDate("inNetDate")!=null) {
                    lengthOfNetworkTime = DateUtil.getDistMonths(callHistoryRec.getJSONObject("userInfo").getDate("inNetDate"), new Date());
                }
            }
        }

		request.set("contactsSum", ""+contactSum);
		request.set("lengthOfNetworkTime", ""+lengthOfNetworkTime);
	}

	private List<JSONObject> getCallRecords(String jobId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("job.jobID").is(jobId));
        query.with(new Sort(Sort.Direction.DESC, "_id"));
        query.limit(1);
        List<JSONObject> result = null;
        try {
            result = mongoDao.getByPointQuery("CallRecord", query, JSONObject.class);
        } catch (Exception e) {
            LoggerProxy.warn("queryMongoFailed",LOGGER,"query mongo failed,jobId={}",jobId,e);
        }

        return result;
    }

	@Override
    public RetCodeEnum parseResponse(ChannelRequestAgency agency, String response) {
        JSONObject map = JSONObject.parseObject(response);
        String status = String.valueOf(map.getInteger("status"));
        if ("6".contains(status)) {
            // 重复提交的认为成功
            agency.setDpJobId(map.getString("jobId"));
            return RetCodeEnum.SUCCESS;
        }
        throw new RiskRuntimeException(RetCodeEnum.FAILED, "dbstatus=" + status);
    }

    @Override
    public String parseResponseMessage(ChannelRequestAgency agency, String data) {
        return data;
    }

    private JSONArray buildContacts(Event request) {
        JSONArray arr = new JSONArray();
        List contacts = request.get("contacts", List.class);
        if (CollectionUtils.isEmpty(contacts)) {
            return arr;
        }
        for (Object item : contacts) {
            if (item instanceof Map) {
                Map itemMap = (Map) item;
                JSONObject obj = new JSONObject();
                obj.put("relationType", bhxtRelationType((String) itemMap.get("relationType")));
                obj.put("name", itemMap.get("name"));
                obj.put("mobile", itemMap.get("mobile"));
                arr.add(obj);
            }
        }
        return arr;
    }

    private String bhxtRelationType(String innerRelationType) {
        if (StringUtils.isBlank(innerRelationType) || "其他".equals(innerRelationType)) {
            return "7";
        }
        if ("8_3_父母".equals(innerRelationType) || "父母".equals(innerRelationType)) {
            // 对方父母是分开定义的，和业务方确认都传"2(父亲)"
            return "2";
        }
        if ("9_1_朋友".equals(innerRelationType) || "朋友".equals(innerRelationType)) {
            return "11";
        }
        if ("8_5_其他亲属".equals(innerRelationType) || "其他亲属".equals(innerRelationType)) {
            return "7";
        }
        if ("9_2_同事".equals(innerRelationType) || "同事".equals(innerRelationType)) {
            return "7";
        }
        if ("8_1_配偶".equals(innerRelationType) || "配偶".equals(innerRelationType)) {
            return "10";
        }
        if ("8_2_子女".equals(innerRelationType) || "子女".equals(innerRelationType)) {
            return "3";
        }
        if ("8_4_兄弟姐妹".equals(innerRelationType) || "兄弟姐妹".equals(innerRelationType)) {
            return "6";
        }
        return "7";
    }

    private JSONObject buildCreditInfo(Event request) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("lengthOfNetworkTime", request.getString("lengthOfNetworkTime"));
        jsonObject.put("HundredThousandth", request.getString("HundredThousandth"));
        jsonObject.put("score", request.getString("score"));
        jsonObject.put("grade", request.getString("grade"));
        return jsonObject;
    }

    private JSONObject buildDeviceInfo(Event request) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("imeiNo", request.getString("imei"));
        jsonObject.put("system", request.getString("system"));
        jsonObject.put("deviceType", request.getString("deviceType"));
        jsonObject.put("contactsSum", request.getString("contactsSum"));
        return jsonObject;
    }


    private JSONObject buildEmptyJson(Event request) {
        JSONObject jsonObject = new JSONObject();
        return jsonObject;
    }

    private JSONArray buildEmptyJsonArray(Event request) {
        JSONArray jsonObject = new JSONArray();
        return jsonObject;
    }

    private JSONObject buildLoannfo(Event request) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("loanIssue", request.getString("period"));
        jsonObject.put("loanAmount", request.getString("loanAmount"));
        jsonObject.put("loanTime", request.getString("loanTime"));
        jsonObject.put("loanNum", request.getString("loanNo"));

//        jsonObject.put("repaymentType", "01");
        jsonObject.put("repaymentType", request.getString("repaymentType"));
//        jsonObject.put("applySource", "02");
        jsonObject.put("applySource", request.getString("applySource"));
//        jsonObject.put("interestRateType", "10");
        jsonObject.put("interestRateType", request.getString("interestRateType"));
//        jsonObject.put("interestRate", "12%");
        jsonObject.put("interestRate", request.getString("interestRate"));
//        jsonObject.put("loanuse", "03");
        jsonObject.put("loanuse", request.getString("loanuse"));
        jsonObject.put("useInstructions", request.getString("loanUsage"));
//        jsonObject.put("payType", "10");
        jsonObject.put("payType", request.getString("payType"));
//        jsonObject.put("certificateType", "02");
        jsonObject.put("certificateType", request.getString("certificateType"));
//        jsonObject.put("certificateNo", request.getString("idNo"));
        jsonObject.put("certificateNo", request.getString("certificateNo"));
//        jsonObject.put("payName", "");
//        jsonObject.put("payAccount", "");
//        jsonObject.put("payPhone", "");
//        jsonObject.put("payBank", "");
        return jsonObject;
    }

    private JSONObject buildUserInfo(Event request) {

        JSONObject userInfo = new JSONObject();
        userInfo.put("customerName", request.getString("name"));
        userInfo.put("certId", request.getString("idNo"));
        userInfo.put("phone", request.getString("mobile"));
        userInfo.put("reservePhone", request.getString("reservedMobile"));

        userInfo.put("bankCode", request.getString("bhxtBankCode"));
        userInfo.put("accountNo", request.getString("bankCardNo"));

        int sexNum = Integer.parseInt(request.getString("idNo").substring(16, 17));
        // 0-男，1-女
        userInfo.put("sex", 1 == sexNum % 2 ? 0 : 1);

        // 学历
        userInfo.put("eduexperience", request.getString("eduexperience"));
        userInfo.put("marriage", marriage(request.getString("marriage")));
        // 职业类型, 业务转
        userInfo.put("occupationType", request.getString("occupationType"));
        return userInfo;
    }

    private String marriage(String marriage) {
        if (StringUtils.isBlank(marriage)) {
            return "90";
        }
        if ("2_4_离异".equals(marriage)) {
            return "40";
        } else if ("2_5_丧偶".equals(marriage)) {
            return "30";
        } else if ("2_1_未婚".equals(marriage)) {
            return "10";
        } else if ("2_3_已婚未育".equals(marriage)) {
            return "29";
        } else if ("2_2_已婚已育".equals(marriage)) {
            return "28";
        } else {
            return "90";
        }
    }
}
