package com.youxin.risk.channel.task;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.channel.constants.FundPlatChannel;
import com.youxin.risk.channel.service.CallBackFromHeikaService;
import com.youxin.risk.commons.model.ChannelRequestAgency;
import com.youxin.risk.commons.model.ChannelRequestModel;
import com.youxin.risk.commons.service.channel.ChannelRequestAgencyService;

@Service
public class ChannelRequestHeikaCallbackTask extends AbstractChannelTask {

	@Resource
	private ChannelRequestAgencyService channelRequestAgencyService;

	@Resource
	private CallBackFromHeikaService callBackFromHeikaService;

    @Override
    protected ChannelRequestModel queryOne() {
        return queryOneWaitSubmit();
    }

    @Override
    protected int lock(long id) {
        return lockWaitCallback(id);
    }

    @Override
    protected void process(ChannelRequestModel record) {
		JSONObject requset = new JSONObject();
		ChannelRequestAgency reqAgencyRes = channelRequestAgencyService.selectByAgencyCode(record.getRequestId(),FundPlatChannel.HEIKA.name());
		if (reqAgencyRes != null ) {
			requset.put("jobId", reqAgencyRes.getDpJobId());
			callBackFromHeikaService.dealHeikaCallBackMsg(requset.toJSONString());
		}
    }

    private int lockWaitCallback(long id) {
        return channelRequestModelService.lockPointAgencyWaitCallback(id);
    }

    private ChannelRequestModel queryOneWaitSubmit() {
        // 批量查10条，只返回一条，降低锁概率
        List<ChannelRequestModel> list = channelRequestModelService.selectPointAgencyWaitCallback(DEFAULT_QUERY_SIZE,
				FundPlatChannel.HEIKA.name());
        return getRandom(list);
    }
}