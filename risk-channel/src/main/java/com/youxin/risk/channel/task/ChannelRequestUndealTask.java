/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.channel.task;

import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.youxin.risk.channel.service.CallBackMsgProxy;
import com.youxin.risk.channel.service.DataPlatformMessageHandler;
import com.youxin.risk.commons.constants.RedisKeyEnum;
import com.youxin.risk.commons.model.ChannelRequestAgency;
import com.youxin.risk.commons.service.channel.ChannelRequestAgencyService;
import com.youxin.risk.commons.tools.lock.LockResult;
import com.youxin.risk.commons.tools.lock.RedisLock;
import com.youxin.risk.commons.utils.GlobalUtil;
import com.youxin.risk.commons.utils.LogUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;

/**
 *  channel 处理未回调补偿任务
 */
@Service
public class ChannelRequestUndealTask {
    private static Logger logger = LoggerFactory.getLogger(ChannelRequestUndealTask.class);

    private static final String REDIS_LOCK_KEY = RedisKeyEnum.channelRequestUndealTaskLockKey.toString();

    private static final int REDIS_LOCK_TIMEOUT_SECS = 1 * 60;

    @Resource
    private ChannelRequestAgencyService channelRequestAgencyService;

    @Resource
	private DataPlatformMessageHandler dataPlatformMessageHandler;

    @Resource
	private CallBackMsgProxy callBackMsgProxy;

    @Resource
    private RedisLock redisLock;

    public void doJob() {
        if (!GlobalUtil.isRunning()) {
            LoggerProxy.warn("GlobalRunningIsFalse", logger, "");
            return;
        }
        try {
            LogUtil.buildAndBindLog();
            LockResult lockResult = redisLock.tryLock(REDIS_LOCK_KEY, REDIS_LOCK_TIMEOUT_SECS);
            if (lockResult == null || !lockResult.isSuccess()) {
				LoggerProxy.info("tryLockFailed", logger, "");
                return;
            }
			List<ChannelRequestAgency> list = channelRequestAgencyService.selectWaitCallbackList();
            if (CollectionUtils.isEmpty(list)) {
				LoggerProxy.info("undealListIsNull", logger, "");
                return;
            }
            for (ChannelRequestAgency channelRequestAgency: list) {
				String dpJobId = channelRequestAgency.getDpJobId();
				if (StringUtils.isEmpty(dpJobId)){
            		return;
				}
            	String recordType = "BHXT_RISK_RECORD";
				String dataFromDp = dataPlatformMessageHandler.getDataFromDp(recordType, dpJobId);
				if (StringUtils.isEmpty(dataFromDp)) {
					return;
				}
				callBackMsgProxy.dealMsg(dpJobId, dataFromDp, recordType);
			}
        } catch (Exception e) {
        	LoggerProxy.error("ChannelRequestUndealTaskException", logger, "", e);
        } finally {
            LogUtil.unbindLogId();
        }
    }

}