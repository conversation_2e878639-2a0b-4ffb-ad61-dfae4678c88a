package com.youxin.risk.channel.service;

import java.util.Date;
import java.util.Map;

import javax.annotation.Resource;

import com.youxin.risk.metrics.enums.MetricsOpType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Maps;
import com.youxin.risk.channel.constants.ChannelConstant;
import com.youxin.risk.channel.service.builder.AgencyMessageBuilder;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.kafkav2.KafkaContext;
import com.youxin.risk.commons.kafkav2.handler.impl.BaseKafKaMessageHandler;
import com.youxin.risk.commons.model.ChannelRequestAgency;
import com.youxin.risk.commons.model.ChannelRequestModel;
import com.youxin.risk.commons.mongo.MongoDao;
import com.youxin.risk.commons.service.channel.ChannelRequestAgencyService;
import com.youxin.risk.commons.service.channel.ChannelRequestModelService;
import com.youxin.risk.commons.utils.LogUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.vo.TaskDataVo;
import com.youxin.risk.metrics.MetricsAPI;

@Service
public class CallBackMsgProxy extends BaseKafKaMessageHandler {

    @Resource
    private ChannelRequestAgencyService channelRequestAgencyService;

    @Resource
    private ChannelRequestModelService channelRequestModelService;

    @Resource
    private MongoDao mongoDao;

    @Resource
    private CallbackService callbackService;

    @Resource(name = "agencyMessageBuilderProxy")
    private AgencyMessageBuilder agencyMessageBuilder;


    public Boolean dealMsg(String jobId, String data, String collectionName) {
        if (StringUtils.isEmpty(jobId) || StringUtils.isEmpty(collectionName)) {
            return false;
        }
        if (StringUtils.isEmpty(data)) {
            // 回调上游信息为空时,报警但不阻断流程
			LoggerProxy.warn("callbackDataInvalid", logger, "jobID=" + jobId);
        }
        // 查找原记录
        ChannelRequestAgency agencyLog = this.channelRequestAgencyService.selectByJobId(jobId);
        if (agencyLog == null) {
            //task not found just save message
            LoggerProxy.error("callbackUpButNotFoundRecord", logger, "jobID=" + jobId);
            return false;
        }
        // logId绑定问题是否与请求requestId保持一致
        LogUtil.bindLogId(agencyLog.getRequestId());

        LoggerProxy.info("selectRequestModel", logger, "jobID={},requestId={}", jobId, agencyLog.getRequestId());

        ChannelRequestModel requestModel = this.channelRequestModelService.selectByRequestId(agencyLog.getRequestId());
        if (requestModel == null) {
            LoggerProxy.error("selectRequestModelError", logger, "requestId={}", agencyLog.getRequestId());
            return false;
        }
        Boolean result = false;
		try {
			TaskDataVo taskDataVo = new TaskDataVo();
			taskDataVo.setUserKey(requestModel.getUserKey());
			taskDataVo.setLoanKey(requestModel.getLoanKey());
			taskDataVo.setJobId(agencyLog.getDpJobId());
			taskDataVo.setCreateTime(new Date());
			// 解析DP返回消息
			taskDataVo.setData(agencyMessageBuilder.parseResponseMessage(agencyLog, data));

			//save data
			boolean saveSuccess = this.mongoDao.insert(collectionName, taskDataVo);
			//        LoggerProxy.info("saveDataToMongo", logger, "data={}, result={}, cost={}", taskDataVo,
			// saveSuccess, context.getSplitTimeAndSplit());
			if (!saveSuccess) {
				return result;
			}
			callbackService.callback(requestModel, agencyLog, taskDataVo.getData());
			result = true;
			return result;
		} finally {
        	long costTime = System.currentTimeMillis() - agencyLog.getCreateTime().getTime();
			// 埋点
			Map<String, String> tags = Maps.newHashMap();
			tags.put("retCode", result ? RetCodeEnum.SUCCESS.getValue(): RetCodeEnum.FAILED.getValue());
			tags.put("callbackFundplat", agencyLog.getAgencyCode());
			MetricsAPI.point(ChannelConstant.FUNDPLAT_CALL_BACK_AVG_COST_TIME_POINT, tags,
                    costTime,true, MetricsOpType.timer);
		}
    }


    @Override
    protected void handler0(KafkaContext context) {
        
    }

}