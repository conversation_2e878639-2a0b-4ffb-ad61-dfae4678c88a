<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration	PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-config.dtd">

<configuration>
	<properties>
		<property name="dialect" value="mysql" />
		<property name="pageSqlId" value=".*Page$" />
	</properties>
	
	<settings>
		<!--打印sql到控制台-->
		<!--<setting name="logImpl" value="STDOUT_LOGGING" />-->
		<setting name="cacheEnabled" value="true" />
		<setting name="lazyLoadingEnabled" value="false" />
		<setting name="aggressiveLazyLoading" value="false" />
		<setting name="multipleResultSetsEnabled" value="true" />
		<setting name="useColumnLabel" value="true" />
		<setting name="useGeneratedKeys" value="false" />
		<setting name="defaultExecutorType" value="SIMPLE" />
		<setting name="defaultStatementTimeout" value="25000" />
	</settings>
</configuration>
