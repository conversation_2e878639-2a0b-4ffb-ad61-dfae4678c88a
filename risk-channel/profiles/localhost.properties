mode.name=dev
app.name=risk-channel

home.base=/home/<USER>

app.home=${home.base}/risk-control/${app.name}
app.log.home=${home.base}/logs/${app.name}

tomcat.home=${home.base}/products/tomcat/tomcat_risk_channel
tomcat.port=8061
tomcat.shutdown.port=8062
tomcat.connection.timeout=5000
tomcat.doc.base=${app.home}
tomcat.allow.ips=172.*.*.*||127.0.0.1||10.*.*.*

java.opts=-Xmx2000m -Xms2000m -Xmn1000m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=128m \
		-verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xloggc:$CATALINA_HOME/logs/gc.log \
		-XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$CATALINA_HOME/logs/oom.log \
		-Djava.nio.channels.spi.SelectorProvider=sun.nio.ch.EPollSelectorProvider \
        -Dfile.encoding=UTF8  -Duser.timezone=GMT+08

console.log.level=DEBUG

datasource.maxActive=200
datasource.initialSize=2
datasource.minIdle=2
datasource.maxWait=2000
datasource.testOnBorrow=true
datasource.defaultTransactionIsolation=4
datasource.timeBetweenEvictionRunsMillis=30000
datasource.minEvictableIdleTimeMillis=300000
datasource.timeBetweenLogStatsMillis=300000
datasource.druid.remove.abandoned=false
datasource.druid.remove.abandoned.timeout=300
datasource.druid.log.abandoned=false
datasource.connectProperties.connectTimeout=1000
datasource.connectProperties.socketTimeout=5000
datasource.logAbandoned=false
datasource.removeAbandoned=true
datasource.removeAbandonedTimeout=120
datasource.poolPreparedStatements=false
#datasource.filters=stat,wall,log4j
datasource.filters=stat,wall

datasource.url.params=characterEncoding=utf8&amp;autoReconnect=true&amp;zeroDateTimeBehavior=convertToNull&amp;useUnicode=true&amp;useOldAliasMetadataBehavior=true

admin.datasource.url=jdbc:mysql://***********:3306/risk_admin?${datasource.url.params}
admin.datasource.username=root
admin.datasource.pwd=youxin_risk

channel.datasource.url=******************************************?${datasource.url.params}
channel.datasource.username=root
channel.datasource.pwd=youxin_risk

redis.maxTotal=8
redis.maxIdle=8
redis.minIdle=4
redis.maxWaitMillis=5000
redis.testOnBorrow=true
redis.cluster.connectionTimeout=3000
redis.cluster.soTimeout=3000
redis.cluster.maxAttempts=1
redis.cluster.password=passwd123
redis.cluster.nodes=***********:7000,***********:7001,***********:7002,\
  ***********:7100,***********:7101,***********:7102

mongo.host=************:27017
mongo.username=test
mongo.password=test
mongo.database=transfer

mongo.sharding.host=************:27017
mongo.sharding.username=risk
mongo.sharding.password=j8cWZbL9PHK3NUdAeNBB
mongo.sharding.database=risk
mongo.sharding.credentials=${mongo.sharding.username}:${mongo.sharding.password}@${mongo.sharding.database}

kafka.dp.hosts=hadoop-1:9092,hadoop-2:9092,hadoop-3:9092
kafka.mirror.dp.hosts=***********:9092,***********:9092,***********:9092
kafka.dp.topic=babel.crawl.result.test
kafka.dp.topic.group.id=youxin_risk_channel_Group_40
kafka.dc.topic.default=risk.dc.channel.default.topic.40
kafka.dc.topic.default.group.id=dc_channel_group_40

metrics.remote.queue.server=${redis.cluster.nodes}
metrics.remote.queue.redis.password=${redis.cluster.password}
metrics.stop=false


url.dataplatform.get=https://babel-read.test.rrdbg.com/babel/v1/record/%s?jobid=%s&systemid=%s&ignoreFinished=false
url.dataplatform.get.userkey=https://babel-read.test.rrdbg.com/babel/v1/record/user/%s?userkey=%s&systemid=%s
url.dataplatform.get.list.userkey=https://babel-read.test.rrdbg.com/babel/v1/record/user/all/%s?userkey=%s&systemid=%s

url.dataplatform.bhtx=http://crawlers.test.rrdbg.com/crawlers/v2/crawl/bhxt

url.dataplatform.xwyh=https://crawlers.test.rrdbg.com/crawlers/v1/crawl

url.fundplatform.ynxt=http://**************:8081/server_fund_api_env_150
#url.fundplatform.ynxt=http://core-fund-api-test_env_150.test.rrdbg.com

url.callback.ra=http://***********:13080/thirdPartyFetch/channelNotify
url.callback.default=http://***********:8091/callback/channel
url.callback.engine=http://***********:8091/callback/channel

url.datavisor.post =http://datavisor-hfq-haojie.rrdbg.com/renrendai2/clientEvent_test



url.getreqmsg.datacenter=http://***********:8071/dc/handler/query

url.getreqmsg.datacenter.inside=http://***********:8071/dc/handler/query

productkey.haohuan.borrow=BD7B09DF47EF407C9CB3094BE50B345F

salt.fund.platform= 123456

url.di=http://***********:8021

youxin.env=DEV

transfer.mongo.host=************:27017


metrics.point.kafka.hosts=hadoop-1:9092,hadoop-2:9092,hadoop-3:9092
metrics.point.kafka.topic=metrics.point.kafka.topic_local
metrics.point.kafka.group.id=metrics.point.kafka.group_local
metrics.point.kafka.topic.list=metrics.point.kafka.topic_local,metrics.point.kafka.topic.gateway_local
metrics.point.mirror.kafka.hosts=***********:9092,***********:9092,***********:9092
