mode.name=test
app.name=risk-calculate

home.base=/home/<USER>

app.home=${home.base}/risk-control/${app.name}
app.log.home=${home.base}/logs/${app.name}

tomcat.home=${home.base}/products/tomcat/tomcat_risk_calculate
tomcat.port=8111
tomcat.shutdown.port=8112
tomcat.connection.timeout=5000
tomcat.doc.base=${app.home}
tomcat.allow.ips=172.*.*.*||127.0.0.1||10.*.*.*

java.opts=-Xmx2000m -Xms2000m -Xmn1000m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=128m \
		-verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xloggc:$CATALINA_HOME/logs/gc.log \
		-XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$CATALINA_HOME/logs/oom.log \
		-Djava.nio.channels.spi.SelectorProvider=sun.nio.ch.EPollSelectorProvider \
        -Dfile.encoding=UTF8  -Duser.timezone=GMT+08

console.log.level=DEBUG

datasource.maxActive=200
datasource.initialSize=2
datasource.minIdle=2
datasource.maxWait=2000
datasource.testOnBorrow=true
datasource.defaultTransactionIsolation=4
datasource.timeBetweenEvictionRunsMillis=30000
datasource.minEvictableIdleTimeMillis=300000
datasource.timeBetweenLogStatsMillis=300000
datasource.druid.remove.abandoned=false
datasource.druid.remove.abandoned.timeout=300
datasource.druid.log.abandoned=false
datasource.connectProperties.connectTimeout=1000
datasource.connectProperties.socketTimeout=5000
datasource.logAbandoned=false
datasource.removeAbandoned=true
datasource.removeAbandonedTimeout=120
datasource.poolPreparedStatements=false
#datasource.filters=stat,wall,log4j
datasource.filters=stat,wall

datasource.url.params=characterEncoding=utf8&amp;autoReconnect=true&amp;zeroDateTimeBehavior=convertToNull&amp;useUnicode=true&amp;useOldAliasMetadataBehavior=true

admin.datasource.url=*****************************************?${datasource.url.params}
admin.datasource.username=test
admin.datasource.pwd=cfx4TxzSdTXOXuyUOcL1

library.datasource.url=*******************************************?${datasource.url.params}
library.datasource.username=test
library.datasource.pwd=cfx4TxzSdTXOXuyUOcL1

verify.datasource.url=************************************************?${datasource.url.params}
verify.datasource.username=test
verify.datasource.pwd=cfx4TxzSdTXOXuyUOcL1


redis.maxTotal=8
redis.maxIdle=8
redis.minIdle=4
redis.maxWaitMillis=5000
redis.testOnBorrow=true
redis.cluster.connectionTimeout=3000
redis.cluster.soTimeout=3000
redis.cluster.maxAttempts=1
redis.cluster.password=passwd456
redis.cluster.nodes=************:7000,************:7001,************:7002,\
  ************:7100,************:7101,************:7102

kafka.dp.hosts=hadoop-1:9092,hadoop-2:9092,hadoop-3:9092
kafka.mirror.dp.hosts=***********:9092,***********:9092,***********:9092
kafka.engine.calculate.topic=risk.engine.event.topic.test
kafka.engine.calculate.topic.group.id=youxin_risk_calculate_Group_test

metrics.remote.queue.server=${redis.cluster.nodes}
metrics.remote.queue.redis.password=${redis.cluster.password}
metrics.stop=false

url.notify.hfq=http://************/internal/v1/audit/update-account-info
notify.encrypt.key.hfq=HAOHUAN_PASSWORD


metrics.point.kafka.hosts=hadoop-1:9092,hadoop-2:9092,hadoop-3:9092
metrics.point.kafka.topic=metrics.point.kafka.topic_test
metrics.point.kafka.group.id=metrics.point.kafka.group_test
metrics.point.kafka.topic.list=metrics.point.kafka.topic_test,metrics.point.kafka.topic.gateway_test
metrics.point.mirror.kafka.hosts=***********:9092,***********:9092,***********:9092

youxin.env=DEV