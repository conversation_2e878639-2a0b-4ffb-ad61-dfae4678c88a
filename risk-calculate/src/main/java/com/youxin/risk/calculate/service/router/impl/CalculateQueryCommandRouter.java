/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.calculate.service.router.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.calculate.service.router.CalculateRouter;
import com.youxin.risk.calculate.vo.CalculateVo;
import com.youxin.risk.commons.cache.CacheApi;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.model.LibListData;
import com.youxin.risk.commons.model.LibraryConf;
import com.youxin.risk.commons.model.PolicyLibraryConf;
import com.youxin.risk.commons.service.calculate.LibListDataService;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;

@Service("calculateQueryCommandRouter")
public class CalculateQueryCommandRouter extends BaseCalculateRouter implements CalculateRouter {

    private Logger logger = LoggerFactory.getLogger(CalculateQueryCommandRouter.class);

	@Resource
	private LibListDataService libListDataService;

	@Override
	public void route(CalculateVo calculateVo) {
		// todo 查询服务
		String policyCode = calculateVo.getPolicyCode();
		String[] variableCodes = getVariableCodes(policyCode);
		if (variableCodes.length < 1 ){
			LoggerProxy.error("findLibraryVariableFailed", logger, String.format("policyCode=%s",
					policyCode));
			calculateVo.setRetCode(RetCodeEnum.ILLEGAL_ARGUMENT);
			return;
		}
		PolicyLibraryConf policyLibraryConf = CacheApi.getPolicyLibraryInfo(policyCode);
		LibraryConf libraryConf = CacheApi.getLibraryInfo(policyLibraryConf.getLibraryCode());
		Map<String, Object> libraryData = calculateVo.getLibraryData();
		List<Long> record = new ArrayList<>();
		for (String variableCode: variableCodes) {
			String variableValue = libraryData.get(variableCode).toString();
			if (StringUtils.isNotEmpty(variableValue)){
				record.add(getDataHashcode(libraryConf.getLibraryCode(), variableCode, variableValue));
			}
		}
		if (CollectionUtils.isEmpty(record)) {
			LoggerProxy.error("findLibraryVariableValueFailed", logger, String.format("policyCode=%s,variableCodes=%s",
					policyCode, variableCodes.toString()));
			calculateVo.setRetCode(RetCodeEnum.FAILED);
			return;
		}
		List<LibListData> result = libListDataService.getByHashCodes(record);
		calculateVo.setRetCode(RetCodeEnum.SUCCESS);
		calculateVo.setRetMsg(JSON.toJSONString(result));
	}
}