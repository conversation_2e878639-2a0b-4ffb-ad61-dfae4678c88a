/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.calculate.vo;

import java.util.Map;

import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.model.BaseEntity;

/**
 *
 */
public class CalculateDataVo extends BaseEntity {

    /**
     * 二级字段，解密dataPlaintext后
     */
    private String type;
    private String retCode = RetCodeEnum.SUCCESS.getValue();
    private String requestId;
    private String agencyRequestId;
    private Map<String, Object> message;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public Map<String, Object> getMessage() {
        return message;
    }

    public void setMessage(Map<String, Object> message) {
        this.message = message;
    }

    public String getAgencyRequestId() {
        return agencyRequestId;
    }

    public void setAgencyRequestId(String agencyRequestId) {
        this.agencyRequestId = agencyRequestId;
    }


    public String getRetCode() {
        return retCode;
    }

}