package com.youxin.risk.calculate.tools;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.youxin.risk.calculate.constants.CalculateConstant;
import com.youxin.risk.calculate.constants.PolicyType;
import com.youxin.risk.commons.utils.LoggerProxy;

public class PolicyConfigTools {

    private static final Logger LOGGER = LoggerFactory.getLogger(PolicyConfigTools.class);

    public static PolicyType getPolicyType(String policyCode) {
        try {
            String policyCodeData = policyCode.substring(policyCode.indexOf("_")+1, policyCode.length());
            String policyCodeCalData = policyCodeData.substring(0,3);
            if (CalculateConstant.POLICY_TYPE_LIBRARY.equals(policyCodeCalData)){
                return PolicyType.LIBRARY;
            } else if(CalculateConstant.POLICY_TYPE_AMOUNT.equals(policyCodeCalData)){
                return PolicyType.AMOUNT;
            } else if(CalculateConstant.POLICY_TYPE_ACCOUNT.equals(policyCodeCalData)){
                return PolicyType.ACCOUNT;
            } else if(CalculateConstant.POLICY_TYPE_NOTIFY.equals(policyCodeCalData)){
                return PolicyType.NOTIFY;
            }
        } catch (Exception e) {
            LoggerProxy.warn("getPolicyTypeFailed", LOGGER, "policyCode={}",policyCode);
        }
        return null;
    }
}