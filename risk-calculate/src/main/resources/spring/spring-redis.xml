<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="
		http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.1.xsd">

    <bean id="genericObjectPoolConfig" class="org.apache.commons.pool2.impl.GenericObjectPoolConfig">
        <property name="maxTotal" value="${redis.maxTotal}" />
        <property name="maxIdle" value="${redis.maxIdle}"/>
        <property name="minIdle" value="${redis.minIdle}"/>
        <property name="maxWaitMillis" value="${redis.maxWaitMillis}"/>
        <property name="testOnBorrow" value="${redis.testOnBorrow}" />
    </bean>
    <bean id="jedisCluster" class="com.youxin.risk.commons.tools.redis.JedisClusterFactory">
        <property name="genericObjectPoolConfig" ref="genericObjectPoolConfig"/>
        <property name="connectionTimeout" value="${redis.cluster.connectionTimeout}"/>
        <property name="soTimeout" value="${redis.cluster.soTimeout}"/>
        <property name="maxAttempts" value="${redis.cluster.maxAttempts}"/>
        <property name="password" value="${redis.cluster.password}"/>
        <property name="nodes" value="${redis.cluster.nodes}"/>
    </bean>

    <bean id="retryableJedis" class="com.youxin.risk.commons.tools.redis.RetryableJedis">
        <property name="jedisCluster" ref="jedisCluster"/>
    </bean>
</beans>