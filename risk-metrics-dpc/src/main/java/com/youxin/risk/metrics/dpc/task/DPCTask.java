package com.youxin.risk.metrics.dpc.task;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.metrics.config.ConfigUtils;
import com.youxin.risk.metrics.constants.Constent;
import com.youxin.risk.metrics.constants.PropertiesKey;
import com.youxin.risk.metrics.dpc.check.Checker;
import com.youxin.risk.metrics.dpc.check.impl.DefaultChecker;
import com.youxin.risk.metrics.dpc.helpers.DPCUtils;
import com.youxin.risk.metrics.dpc.influxdb.InfluxDBAPI;
import com.youxin.risk.metrics.dpc.queue.QueueListener;
import com.youxin.risk.metrics.dpc.queue.QueueListenerFactory;
import com.youxin.risk.metrics.enums.MetricsOpType;
import com.youxin.risk.metrics.helpers.LoggerUtils;
import com.youxin.risk.metrics.helpers.ThreadUtils;
import com.youxin.risk.metrics.helpers.Utils;
import com.youxin.risk.metrics.model.PointModel;

import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

public class DPCTask {

    private QueueListener listener;

    private Executor tasks;

    private boolean isOK;

    private Checker checker;

    private static String seperator;

    private static volatile Map<String, PointModel> cache;

    private static Object lock;

    public DPCTask() {

    }

    public void init() {
        lock = new Object();
        seperator = "#";
        cache = new HashMap<>();
        try {
            isOK = true;
            checker = new DefaultChecker();
            initListener();
            initTask();
        } catch (Exception e) {
            LoggerUtils.report(DPCTask.class.getName() + " init exception", e);
            isOK = false;
        }
        LoggerUtils.report("metrics-dpc init end, isOK=" + isOK);
    }

    private void initListener() throws Exception {
        listener = QueueListenerFactory.buildQListener();
    }

    private void initTask() {

        int taskSize = ConfigUtils.getInteger(PropertiesKey.DPC_REMOTE_QUEUE_TASK_SIZE);
        if (taskSize <= 0) {
            taskSize = Runtime.getRuntime().availableProcessors() + 1;
        }

        String queueKeys = ConfigUtils.getString(PropertiesKey.DPC_REMOTE_QUEUE_REDIS_KEYS);
        if (null == queueKeys || "".equals(queueKeys)) {
            throw new IllegalArgumentException("'" + PropertiesKey.DPC_REMOTE_QUEUE_REDIS_KEYS + "'");
        }

        String[] queueArr = queueKeys.split(",");
        int thSize = queueArr.length * taskSize;

        tasks = new ThreadPoolExecutor(thSize, thSize,
                1L, TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(thSize),
                ThreadUtils.defaultThreadFactory(), ThreadUtils.defaultHandler());

        for (String queueKey : queueArr) {
            if (null == queueKey || "".equals(queueKey)) {
                continue;
            }
            for (int i = 0; i < taskSize; i++) {
                tasks.execute(new ReadAndWrite(queueKey));
            }
        }
    }

    private class ReadAndWrite implements Runnable {

        private String queueKey;

        private long step = 1000*3L;

        private long times;

        private ReadAndWrite(String queueKey) {
            this.queueKey = queueKey;
        }

        @Override
        public void run() {
            boolean isRun;
            for (; ; ) {
                if (!isOK) {
                    break;
                }
                isRun = DPCUtils.isIsDPCRunning();
                if (!isRun) {
                    continue;
                }
                String msg = null;
                try {
                    msg = listener.pop(queueKey);
                    write(msg);
                } catch (Exception e) {
                    LoggerUtils.report("ReadAndWriteException" + e.getMessage());
                } finally {
                    if (!isRun) {
                        Utils.threadSleep(3000);
                    } else if (null == msg) {
                        Utils.threadSleep(50);
                        long temp = System.currentTimeMillis()/step;
                        if(temp != times){
                            times = temp;
                            LoggerUtils.report("redis-queue is empty ; queueKey =" + queueKey);
                        }
                    }
                }
            }
        }
    }



    private void write(String msg) {
        if (null == msg || "".equals(msg)) {
            return;
        }
        PointModel point = null;
        try {
            point = JSONObject.parseObject(msg, PointModel.class);
        } catch (JSONException e) {
            LoggerUtils.report("JSONException, msg=" + msg);
            return;
        }
        try {
            if (!checker.check(point)) {
                LoggerUtils.reportPoint("write to influxdb, but check false, msg=" + msg);
                return;
            }
            //手动执行聚合操作
            if(point.isToBeCollected()){
                reorganizePoint(point);
                return;
            }
//            InfluxDBAPI.write(point);
            InfluxDBAPI.write(point);
            LoggerUtils.reportPoint("write to influxdb, msg=" + msg);
        } catch (Exception e) {
            LoggerUtils.report("WriteMsgException" + e.getMessage());
        }
    }

    private void addCache(PointModel point,String key){
        synchronized (lock){
            if(cache.get(key) == null){
                PointModel newPoint = new PointModel();
                newPoint.setNamespace(point.getNamespace());
                newPoint.setValue(point.getValue());
                newPoint.getValue().put(Constent.INFLUXDB_VALUE_FIELD,0);
                newPoint.setTags(point.getTags());
                newPoint.setOpType(point.getOpType());
                newPoint.setToBeCollected(point.isToBeCollected());
                newPoint.setTime(point.getTime());
                newPoint.setCount(new AtomicLong(0));
                newPoint.setTimeSum(new AtomicLong(0));
                newPoint.setPoint(point.getPoint());

                HashMap<String,PointModel> newCache = new HashMap<>(cache);
                newCache.put(key,newPoint);
                cache = Collections.unmodifiableMap(newCache);
            }
        }
    }


    private void reorganizePoint(PointModel point){
        String key = buildCacheKey(point);
        PointModel old = cache.get(key);
        if(old == null){
            addCache(point,key);
            old = cache.get(key);
        }
        //合并一下
        old.getCount().incrementAndGet();
        if(MetricsOpType.timer.name().equals(point.getOpType())){
            long addTime = point.getValue().get(Constent.INFLUXDB_VALUE_FIELD).longValue();
            old.getTimeSum().addAndGet(addTime);
        }
    }


    public static String buildCacheKey(PointModel point){
        StringBuilder cacheKey = new StringBuilder();
        cacheKey.append(point.getNamespace()).append(seperator)
                .append(point.getPoint()).append(seperator);
        Map<String,String> tagMap = point.getTags();
        if(tagMap != null){
            TreeMap<String,String> sort = new TreeMap();
            for(String tagkey : tagMap.keySet()){
                sort.put(tagkey,null);
            }
            for(String tagkey : sort.keySet()){
                cacheKey.append(tagkey).append(seperator).append(tagMap.get(tagkey)).append(seperator);
            }
        }
        Map<String,Number> fildMap = point.getValue();
        if(fildMap != null){
            TreeMap<String,String> sort = new TreeMap();
            for(String fieldKey : fildMap.keySet()){
                sort.put(fieldKey,null);
            }
            for(String fieldKey : sort.keySet()){
                cacheKey.append(fieldKey).append(seperator);
            }
        }
        return cacheKey.toString();
    }

    public static Collection<PointModel> getCacheData(){
        return Collections.unmodifiableList(new ArrayList<>(cache.values()));
    }


}
