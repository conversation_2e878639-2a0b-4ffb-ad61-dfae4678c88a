package com.youxin.risk.metrics.dpc.service;

import com.youxin.risk.metrics.dpc.influxdb.InfluxDBAPI;

public class ShowDatabasesService {

    private static final ShowDatabasesService instance = new ShowDatabasesService();

    private ShowDatabasesService() {

    }

    public static ShowDatabasesService getInstance() {
        return instance;
    }

    public String handler() {
        return InfluxDBAPI.showDatabases();
    }
}
