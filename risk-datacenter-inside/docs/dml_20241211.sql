# 策略审核白名单-userkey粒度
insert into `risk_datacenter`.`dc_black_white_verify`(business_type, list_type, dimension, dimension_value, creator, modifier) values ('STRATEGY', 'WHITE', 'USER_KEY', '44497b46a8b6d2e13a4cbe16f9770f95', 'migrate', 'migrate');

# 查征白名单-userkey粒度
insert into `risk_datacenter`.`dc_black_white_verify`(business_type, list_type, dimension, dimension_value, creator, modifier) values ('PBOC', 'WHITE', 'USER_KEY', '44497b46a8b6d2e13a4cbe16f9770f95', 'migrate', 'migrate');