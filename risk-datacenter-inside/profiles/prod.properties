mode.name=prod
app.name=risk-datacenter

home.base=/opt/app/tomcat

app.home=${home.base}/webapps/ROOT
app.log.home=${catalina.base}/logs

tomcat.home=${home.base}
tomcat.port=8071
tomcat.shutdown.port=8072
tomcat.connection.timeout=5000
tomcat.doc.base=${app.home}
tomcat.allow.ips=172.*.*.*||127.0.0.1||10.*.*.*

java.opts=-Xmx3550m -Xms3550m -Xmn1500m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=128m \
		-verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xloggc:$CATALINA_HOME/logs/gc.log \
		-XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$CATALINA_HOME/logs/oom.log \
		-Djava.nio.channels.spi.SelectorProvider=sun.nio.ch.EPollSelectorProvider \
        -Dfile.encoding=UTF8  -Duser.timezone=GMT+08

console.log.level=OFF

#admin 连接配置
datasource.admin.maxActive=50
datasource.admin.initialSize=2
datasource.admin.minIdle=2

datasource.maxActive=50
datasource.initialSize=3
datasource.minIdle=3
datasource.maxWait=15000
datasource.testOnBorrow=true
datasource.defaultTransactionIsolation=4
datasource.timeBetweenEvictionRunsMillis=25000
datasource.minEvictableIdleTimeMillis=300000
datasource.timeBetweenLogStatsMillis=300000
datasource.druid.remove.abandoned=false
datasource.druid.remove.abandoned.timeout=300
datasource.druid.log.abandoned=false
datasource.connectProperties.connectTimeout=1000
datasource.connectProperties.socketTimeout=20000
datasource.logAbandoned=false
datasource.removeAbandoned=true
datasource.removeAbandonedTimeout=120
datasource.poolPreparedStatements=false
#datasource.filters=stat,wall,log4j
datasource.filters=stat,wall

datasource.url.params=characterEncoding=utf8&amp;autoReconnect=true&amp;zeroDateTimeBehavior=convertToNull&amp;useUnicode=true&amp;useOldAliasMetadataBehavior=true&amp;useAffectedRows=true

admin.datasource.url=****************************************?${datasource.url.params}
admin.datasource.username=${SEC_RISK_ADMIN_DB_USERNAME}
admin.datasource.pwd=${SEC_RISK_ADMIN_DB_PASSWORD}

datacenter.datasource.url=**********************************************?${datasource.url.params}
datacenter.datasource.username=${SEC_RISK_DATACENTER_DB_USERNAME}
datacenter.datasource.pwd=${SEC_RISK_DATACENTER_DB_PASSWORD}

verify.datasource.url=***********************************************?${datasource.url.params}
verify.datasource.username=${SEC_RISK_PAYDAYLOAN_VERIFY_DB_USERNAME}
verify.datasource.pwd=${SEC_RISK_PAYDAYLOAN_VERIFY_DB_PASSWORD}

verify.sharding.datasource.url=**************************************************?${datasource.url.params}
verify.sharding.datasource.username=${SEC_RISK_PAYDAYLOAN_VERIFY2_DB_USERNAME}
verify.sharding.datasource.pwd=${SEC_RISK_PAYDAYLOAN_VERIFY2_DB_PASSWORD}

haohuan.datasource.url=****************************************?${datasource.url.params}
haohuan.datasource.username=${SEC_RISK_HAOHUAN_DB_DB_USERNAME}
haohuan.datasource.pwd=${SEC_RISK_HAOHUAN_DB_DB_PASSWORD}

ra.datasource.url=*******************************************?${datasource.url.params}
ra.datasource.username=${SEC_RISK_ANALYSIS_DB_USERNAME}
ra.datasource.pwd=${SEC_RISK_ANALYSIS_DB_PASSWORD}

shadow.datasource.url=******************************************?${datasource.url.params}
shadow.datasource.username=${SEC_RISK_SHADOW_DB_USERNAME}
shadow.datasource.pwd=${SEC_RISK_SHADOW_DB_PASSWORD}

odsReport.datasource.url=*****************************************?${datasource.url.params}
odsReport.datasource.username=${SEC_RISK_ODS_REPORT_DB_USERNAME}
odsReport.datasource.pwd=${SEC_RISK_ODS_REPORT_DB_PASSWORD}

hive.datasource.url=**************************************
hive.datasource.username=com_plat
hive.datasource.pwd=JDNuywdj62Acnb6awHkg

black.datasource.url=***************************************?${datasource.url.params}
black.datasource.username=${SEC_RISK_BLACKLIST_DB_USERNAME}
black.datasource.pwd=${SEC_RISK_BLACKLIST_DB_PASSWORD}

mybatis.type-handlers-package=com.youxin.risk.commons.mybatis

redis.maxTotal=300
redis.maxIdle=30
redis.minIdle=10
redis.maxWaitMillis=5000
redis.testOnBorrow=true
redis.cluster.connectionTimeout=3000
redis.cluster.soTimeout=3000
redis.cluster.maxAttempts=1
redis.cluster.password=1c1zOc9cHCGE9xmOZ51jzM
redis.cluster.nodes=***********:6398,***********:6395,***********:6396,************:6393,************:6394,***********:6399

redisson.maxPoolSize=128
redisson.minPoolSize=48

#redis cluster
ra.redis.cluster.node1.host=************
ra.redis.cluster.node1.port=6389
ra.redis.cluster.node2.host=************
ra.redis.cluster.node2.port=6389
ra.redis.cluster.node3.host=************
ra.redis.cluster.node3.port=6390
ra.redis.cluster.node4.host=************
ra.redis.cluster.node4.port=6389
ra.redis.cluster.node5.host=************
ra.redis.cluster.node5.port=6390
ra.redis.cluster.node6.host=************
ra.redis.cluster.node6.port=6390
ra.redis.cluster.password=Wu2tDFYIUlLGnmiU

kafka.dp.hosts=kafka1.cdh.app.rrd:9092,kafka2.cdh.app.rrd:9092,kafka3.cdh.app.rrd:9092
kafka.dp.topic=babel.crawl.result
kafka.dc.topic.default=risk.dc.channel.default.topic
kafka.verify.shadow.topic=kafka.shadow.save.topic



#dp��ѯ����ص�kafka��Ϣ����
kafka.dp.result.topic=babel.crawl.result
kafka.dp.result.topic.group.id=riskGroup_ra


kafka.dc.host=kafka1.cdh.app.rrd:9092,kafka2.cdh.app.rrd:9092,kafka3.cdh.app.rrd:9092



metrics.point.kafka.hosts=kafka1.cdh.app.rrd:9092,kafka2.cdh.app.rrd:9092,kafka3.cdh.app.rrd:9092
metrics.point.kafka.topic=metrics.point.kafka.topic
metrics.point.kafka.group.id=metrics.point.kafka.group
metrics.point.mirror.kafka.hosts=kafka5.cdh:9092,kafka6.cdh:9092,kafka7.cdh:9092

metrics.remote.queue.server=${redis.cluster.nodes}
metrics.remote.queue.redis.password=${redis.cluster.password}
metrics.stop=false

url.dataplatform.get=https://babel.ucredit.com/babel/v1/record/%s?jobid=%s&systemid=%s&ignoreFinished=false
url.dataplatform.get.userkey=https://babel.ucredit.com/babel/v1/record/user/%s?userkey=%s&systemid=%s
url.dataplatform.get.list.userkey=https://babel.ucredit.com/babel/v1/record/user/all/%s?userkey=%s&systemid=%s

mongo.host=rs1.riskmongo.app.rrd:27017,rs2.riskmongo.app.rrd:27017,arbiter1.riskmongo.app.rrd:27017
mongo.username=${SEC_RISK_DATACENTER_INSIDE_14_92_TRANSFER_MONGODB_USERNAME}
mongo.password=${SEC_RISK_DATACENTER_INSIDE_14_92_TRANSFER_MONGODB_PASSWORD}
mongo.database=transfer

mongo.account.host=rs1.smsmongo.app.rrd:27017,rs2.smsmongo.app.rrd:27017,rs3.smsmongo.app.rrd:27017,rs4.smsmongo.app.rrd:27017
mongo.account.username=${SEC_RISK_DATACENTER_INSIDE_14_100_RISK_DATACENTER_MONGODB_USERNAME}
mongo.account.password=${SEC_RISK_DATACENTER_INSIDE_14_100_RISK_DATACENTER_MONGODB_PASSWORD}
mongo.account.database=ibg_account_proxy
mongo.account.credentials=${mongo.account.username}:${mongo.account.password}@${mongo.account.database}


youxin.env=PROD

redis.cluster.node1.host=************
redis.cluster.node1.port=6389
redis.cluster.node2.host=************
redis.cluster.node2.port=6389
redis.cluster.node3.host=************
redis.cluster.node3.port=6390
redis.cluster.node4.host=************
redis.cluster.node4.port=6389
redis.cluster.node5.host=************
redis.cluster.node5.port=6390
redis.cluster.node6.host=************
redis.cluster.node6.port=6390
redis.cluster.verify.password=Wu2tDFYIUlLGnmiU

#�����û��ύ���ݵ�redis
redis.cluster.cache.node1.host=************
redis.cluster.cache.node1.port=6398
redis.cluster.cache.node2.host=************
redis.cluster.cache.node2.port=6398
redis.cluster.cache.node3.host=************
redis.cluster.cache.node3.port=6399
redis.cluster.cache.node4.host=************
redis.cluster.cache.node4.port=6397
redis.cluster.cache.node5.host=************
redis.cluster.cache.node5.port=6398
redis.cluster.cache.node6.host=************
redis.cluster.cache.node6.port=6399
redis.cluster.cache.datacenter.password=Wu2tDFYIUlLGnmiU

data.url.base=http://babel.ucredit.com/babel
data.url.record.job.callhistory=/v1/record/CALL_HISTORY
data.url.record.job.tb=/v1/record/TB
data.url.record.call.record=/v1/record/callrecord
data.url.record.call.record.idnumber=/v1/record/callrecord/idnumber
data.url.crawler.callhistory=/v1/crawl/CALL_HISTORY
data.url.crawler.tb=/v1/crawl/TB
data.expiry.callhistory=30
data.expiry.tb=30
data.mobile.base=http://crawlers.ucredit.com
data.mobile.url = /v1/mobile/area
data.mobile.batch.url = /v1/mobile/area/batch
data.url.write.base=https://babel-write.ucredit.com
data.url.record.userinfo=/v1/record/USER_INFO
data.url.record.hermes=/v1/record/HERMES_RESULT
data.expiry.phonebook=-1
data.url.crawler.phonebook=/v1/crawl/PHONE_BOOK
data.url.record.job.phonebook=/v1/record/PHONE_BOOK
data.url.record.user.phonebook=/v1/record/user/PHONE_BOOK


#api.properties
account.url.base=http://account-proxy-internal.rrdbg.com
account.url.baseNew=http://core-system-api-gateway.rrdbg.com
account.url.userKey=/account/loan/getLoanHistoryInfo
account.url.userKey.new=/account/loan/getLoanHistoryInfoNew
account.url.userKey.getLoanSource=/account/loan/getLoanSource
account.url.userKey.new.delay.check=/account/loan/getLoanHistoryInfoNewWithDelayCheck
account.url.mobile=/account/loan/getLoanHistoryInfoOfAllPartner
account.url.loan=/account/loan/getPayPlan
account.url.loan.list=/account/loan/getLoanHistoryInfoByLoanKeys
account.url.loanStatus.list=/account/loan/getLoanStatusByLoanKeys
account.service.version=1.0
account.partner=PAY_DAY_LOAN
account.salt=584a0919cd1045cc2cb608b8
account.salt.RRD=8800170A64430F4A7566045c
account.salt.haohuan=93e2b3b1e63511e7b413000c290cc30f
account.salt.antifraud=9A88148340A84EA4B4C2D7976D6971B2

app.url=http://api.m.kuaisujiekuan.com
app.submit.result=/user/audit-result
app.amount.result=/user/update-loan-amount
app.user.result=/user/verify-notice
app.haohuan.url=http://api-m.haohuan.com
app.haohuan.submit.result=/internal/v1/audit/audit-notice
app.haohuan.user.result=/internal/v1/audit/verify-notice
app.haohuan.amount.result=/internal/v1/audit/increase-amount-notice
app.haohuan.payoff.result=/internal/v1/audit/reloan-notice
app.haohuan.amount.update.result=/internal/v1/audit/update-account-info

risk.di.url=http://antifraud-risk-di.rrdbg.com
risk.dc.url.base=http://antifraud-risk-datacenter.rrdbg.com
risk.rm.url.base=http://riskmanagement.rrdbg.com
risk.spider.url=http://risk-spider.rrdbg.com
# ????????
heika.amount.callback=https://athena-risk-api.51hika.com
# ????????
rongdan.amount.callback=https://soter-risk-api.xmwcrd.cn

#loan info
data.loan.base=https://babel-yellowpage.ucredit.com
data.url.loan.loanhistory=/v2/loan/loanhistory
data.url.loan.blacklist=/v2/loan/blacklist
data.url.loan.loanrepay=/v2/loan/repay


#rong360
data.url.record.job.rongrecord=/v1/record/RONG_RECORD
data.url.record.job.rongreport=/v1/record/RONG_REPORT

#sms
data.url.record.sms=/v1/record/SMS_REPORT
data.url.record.sms.idcard=/v1/record/general/SMS_REPORT

#crawlers
data.url.crawlers.base=http://crawlers.ucredit.com/crawlers
data.url.crawlers.crawl=/v1/crawl
data.url.record.creditx.fraud=/v1/record/CREDITX_FRAUD_RECORDS
data.url.record.creditx.score=/v1/record/CREDITX_SCORE_RECORDS
data.url.record.shumei=/v1/record/SHUMEI_REPORT
data.url.record.callrecord=/v1/record/PHONE_CALLRECORD
data.url.record.taobao=/v1/record/ALITAOBAO
data.url.record.alipay=/v1/record/ALIPAY
data.url.record.unionpay=/v1/record/UNIONPAYSMART_PERSONAL
data.url.record.creditCardBill=/v1/record/EMAIL_ACCOUNT
data.url.crawlers.v2=https://crawlers.we.cn/v2/crawl

zookeeper.port=2181
zookeeper.quorum=***********,***********,***********,***********,***********
slave.zookeeper.quorum=***********,***********,***********

hadoop.user=hdfs

risk.gateway.url=http://antifraud-risk-gateway.rrdbg.com
risk.di.url.base=http://antifraud-risk-di.rrdbg.com
risk.alert.url=http://antifraud-risk-alert.rrdbg.com/alert/api/event/riskAlert/v1

xxl.job.admin.addresses=http://risk-xxl-job-manager.weicai.com.cn
xxl.job.accessToken=
xxl.job.executor.appname=risk-datacenter-inside
xxl.job.executor.address=
xxl.job.executor.ip=
xxl.job.executor.port=-1
xxl.job.executor.logpath=/tmp/
xxl.job.executor.logretentiondays=-1

#�÷��ڻ�ȡ�û���Ϣ�ӿ�
hfq.user.url=http://haofenqi-user-server.haohuan.com/api/v1/risk/user

transfer.mongo.host=************:27017,***********:27017,***********:27017


accountProxy.datasource.url=********************************************?${datasource.url.params}
accountProxy.datasource.username=we_fk_accprox_s
accountProxy.datasource.pwd=598b72bbc632b3db

datastatistics.url=http://label-quality-monitor.weicai.com.cn

## 风控报警内部群
wechat.inside.alert.url=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=27ee0fab-171f-47e9-889c-8cbb4cf8d32a

## 多头历史数据离线特征报批异常报警群
wechat.duotou.alert.url=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a0025f59-eb7b-4f15-86f6-0e1029aa0686

## 质量平台
variable.api.url=http://risk-variable-gateway.weicai.com.cn/api

mongo.event.host1=***********:27017
mongo.event.username1=risk_engine1
mongo.event.password1=rtZnMJwtkgGqtDTV
mongo.event.database1=risk_engine
mongo.event.credentials1=${mongo.event.username1}:${mongo.event.password1}@${mongo.event.database1}

mongo.event.host2=***********:27017
mongo.event.username2=risk_engine2
mongo.event.password2=PtZnMJwtkgGqtsz7
mongo.event.database2=risk_engine
mongo.event.credentials2=${mongo.event.username2}:${mongo.event.password2}@${mongo.event.database2}

blacklist.checkphonebook.url=http://api.blacklist.rrdbg.com/checkphonebook
