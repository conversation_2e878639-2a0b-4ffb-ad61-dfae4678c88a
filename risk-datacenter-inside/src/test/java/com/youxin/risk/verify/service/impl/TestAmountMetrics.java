package com.youxin.risk.verify.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.verify.VerifyUserLineManagement;
import com.youxin.risk.datacenter.service.TongdunService;
import com.youxin.risk.datacenter.service.impl.DcReportRequestServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Random;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class TestAmountMetrics {

    static String[] eventCodes = {"haoHuanVerify","haoHuanLendAudit","haoHuanAmountRepay"};
    static String[] strategyTypes = {"StrategyA","StrategyB","StrategyC"};

    @Resource
    private AmountMetricsService amountMetricsService;

    @Resource
    private DcReportRequestServiceImpl dcReportRequestService;

    @Resource
    private TongdunService tongdunService;
    @Test
    public void testAmountMetrics() throws InterruptedException {
//        for (int i = 0; i < Integer.MAX_VALUE; i++) {
        for (int i = 0; i < 10; i++) {
            Thread.sleep(100);
            String eventCode = eventCodes[new Random().nextInt(eventCodes.length)];
            String strategyType = strategyTypes[new Random().nextInt(strategyTypes.length)];
            VerifyUserLineManagement userLineManagement = new VerifyUserLineManagement();
            userLineManagement.setAccountStatus("A");
            userLineManagement.setCreditLine(new Random().nextDouble() * new Random().nextInt(10000));
            userLineManagement.setAvailLine(new Random().nextDouble() * new Random().nextInt(10000));
            userLineManagement.setUtilLine(new Random().nextDouble() * new Random().nextInt(10000));
            userLineManagement.setLoanLine(new Random().nextDouble() * new Random().nextInt(10000));
            userLineManagement.setLoanAvailLine(new Random().nextDouble() * new Random().nextInt(10000));
            userLineManagement.setLoanUtilLine(new Random().nextDouble() * new Random().nextInt(10000));
            userLineManagement.setLoanActualLine(new Random().nextDouble() * new Random().nextInt(10000));
            userLineManagement.setShopLine(new Random().nextDouble() * new Random().nextInt(10000));
            userLineManagement.setShopAvailLine(new Random().nextDouble() * new Random().nextInt(10000));
            userLineManagement.setShopUtilLine(new Random().nextDouble() * new Random().nextInt(10000));
            userLineManagement.setShopActualLine(new Random().nextDouble() * new Random().nextInt(10000));
            amountMetricsService.metricsAmount(userLineManagement,eventCode,strategyType);
        }
    }

    public static void main(String[] args) {
        for (int i = 0; i < 100; i++) {

            System.out.println(new Random().nextDouble());
        }
    }

    @Test
    public void testGetLastNotNullPlatform(){
        String platform = dcReportRequestService.getLastNotNullPlatform("ddc2edb7ce2c45df3cd91a362402a04e");
        System.out.println(platform);
    }


    @Test
    public void testGetLastNotNullAndEmptyPlatform(){
        String platform = dcReportRequestService.getLastNotNullAndEmptyPlatform("fc31ac2413b5d38ca15cf540e84e3677");
        System.out.println(platform);
    }

    @Test
    public void testTongdunInput(){
        JSONObject param = new JSONObject();
        param.put("userKey","b082ca6d7b0b92d8e0882a37089f8c59");
        param.put("loanKey", "hh_6bdefddba16a436780df3bc8b9726a8d");
        tongdunService.buildParams(param);
    }
}
