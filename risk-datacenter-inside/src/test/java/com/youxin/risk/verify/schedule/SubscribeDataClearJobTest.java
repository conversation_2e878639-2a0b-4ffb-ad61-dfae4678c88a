package com.youxin.risk.verify.schedule;

import junit.framework.TestCase;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class SubscribeDataClearJobTest extends TestCase {

    @Autowired
    private SubscribeDataClearJob subscribeDataClearJob;

    @Autowired
    private DcOperationLogClearJob dcOperationLogClearJob;

    @Test
    public void testJob() {
        subscribeDataClearJob.execJobHandler("{\"tableName\":\"dp_subscribe\",\"daysAgo\": 3,\"limit\": 100}");
        subscribeDataClearJob.execJobHandler("{\"tableName\":\"dc_subscribe\",\"daysAgo\": 3,\"limit\": 100}");
    }

    @Test
    public void testJob1() {
        dcOperationLogClearJob.execJobHandler("{\"clearRedisKey\":\"clearDcOperationLog\",\"step\": 100000,\"operationType\":\"ZHONG_BANG,CAR,HOUSE\",\"limit\": 100}");
    }

}