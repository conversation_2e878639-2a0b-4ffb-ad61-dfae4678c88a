package com.youxin.risk.verify.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.verify.VerifyUserLineManagement;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class ApiUserLineManagementServiceTest {

    @Autowired
    private ApiUserLineManagementService apiUserLineManagementService;

    @Test
    public void testGetByUserKey() {
        String param = "{\n" +
                "    \"amountType\": \"AMOUNT_DEFAULT\",\n" +
                "    \"eventCode\": \"haoHuanAmountIdentify\",\n" +
                "    \"sourceSystem\": \"HAO_HUAN\",\n" +
                "    \"verifyResult\": {\n" +
                "        \"apiPeriodLineRate\": \"[{\\\"period\\\":3,\\\"min\\\":500.0,\\\"max\\\":2260.0,\\\"rate\\\":0.02,\\\"rate_yn\\\":0.02,\\\"type\\\":\\\"loan\\\",\\\"guarantee_rate\\\":0.006},{\\\"lh_rate_period\\\":4,\\\"period\\\":6,\\\"min\\\":500.0,\\\"max\\\":2260.0,\\\"rate\\\":0.0179,\\\"rate_yn\\\":0.0179,\\\"lh_rate\\\":0.0179,\\\"is_lh_rate\\\":0,\\\"type\\\":\\\"loan\\\",\\\"guarantee_rate\\\":0.006},{\\\"lh_rate_period\\\":4,\\\"period\\\":9,\\\"min\\\":500.0,\\\"max\\\":2260.0,\\\"rate\\\":0.0173,\\\"rate_yn\\\":0.0173,\\\"lh_rate\\\":0.0173,\\\"is_lh_rate\\\":0,\\\"type\\\":\\\"loan\\\",\\\"guarantee_rate\\\":0.006},{\\\"lh_rate_period\\\":4,\\\"period\\\":12,\\\"min\\\":500.0,\\\"max\\\":2260.0,\\\"rate\\\":0.0171,\\\"rate_yn\\\":0.0171,\\\"lh_rate\\\":0.0171,\\\"is_lh_rate\\\":0,\\\"type\\\":\\\"loan\\\",\\\"guarantee_rate\\\":0.006},{\\\"lh_rate_period\\\":4,\\\"period\\\":6,\\\"min\\\":1.0,\\\"max\\\":2000.0,\\\"rate\\\":0.0179,\\\"rate_yn\\\":0.0179,\\\"lh_rate\\\":0.0179,\\\"is_lh_rate\\\":0,\\\"type\\\":\\\"shop\\\",\\\"guarantee_rate\\\":0.006}]\",\n" +
                "        \"apiOutputTag\": 0,\n" +
                "        \"testCode\": {\n" +
                "\n" +
                "        },\n" +
                "        \"antifraudResult\": {\n" +
                "            \"company_ts_rule_result\": {\n" +
                "                \"lock_days\": 0,\n" +
                "                \"is_reject\": false,\n" +
                "                \"reject_rules\": {\n" +
                "                    \"N_ANTIFRAUD_002_RULE\": {\n" +
                "                        \"is_black\": -999,\n" +
                "                        \"is_death\": -999\n" +
                "                    }\n" +
                "                }\n" +
                "            },\n" +
                "            \"af_tools\": {\n" +
                "                \"af_sx_rules_score\": {\n" +
                "                    \"sx_grey_score_fin\": -999,\n" +
                "                    \"sx_score_flow\": \"v1\",\n" +
                "                    \"grey_flag\": 0,\n" +
                "                    \"sx_white_score_fin\": -999\n" +
                "                },\n" +
                "                \"af_jy_rules_score\": {\n" +
                "                    \"grey_flag\": 0,\n" +
                "                    \"jy_grey_score_fin\": -999,\n" +
                "                    \"jy_grey_flow\": \"v2\"\n" +
                "                }\n" +
                "            },\n" +
                "            \"all_results\": [\n" +
                "                {\n" +
                "                    \"lock_days\": 0,\n" +
                "                    \"is_reject\": false,\n" +
                "                    \"reject_rules\": {\n" +
                "                        \"N_ANTIFRAUD_001_RULE\": {\n" +
                "                            \"ts_score_new\": -999,\n" +
                "                            \"ts_score_new_act\": -999,\n" +
                "                            \"grey_flag\": -999\n" +
                "                        }\n" +
                "                    }\n" +
                "                },\n" +
                "                {\n" +
                "                    \"lock_days\": 0,\n" +
                "                    \"is_reject\": false,\n" +
                "                    \"reject_rules\": {\n" +
                "                        \"N_ANTIFRAUD_002_RULE\": {\n" +
                "                            \"is_black\": -999,\n" +
                "                            \"is_death\": -999\n" +
                "                        }\n" +
                "                    }\n" +
                "                },\n" +
                "                {\n" +
                "                    \"lock_days\": 0,\n" +
                "                    \"is_reject\": false,\n" +
                "                    \"reject_rules\": {\n" +
                "                        \"N_ANTIFRAUD_003_RULE\": {\n" +
                "                            \"screenrecording_flag\": -999,\n" +
                "                            \"rp_random20\": 0.524502882054,\n" +
                "                            \"step\": \"HH_SIGN_A\"\n" +
                "                        }\n" +
                "                    }\n" +
                "                },\n" +
                "                {\n" +
                "                    \"is_reject\": false,\n" +
                "                    \"reject_rules\": {\n" +
                "                        \"N_ANTIFRAUD_004_RULE\": {\n" +
                "                            \"fxq_hit_flag\": -999\n" +
                "                        }\n" +
                "                    }\n" +
                "                },\n" +
                "                {\n" +
                "                    \"lock_days\": 0,\n" +
                "                    \"is_reject\": false,\n" +
                "                    \"reject_rules\": {\n" +
                "                        \"N_ANTIFRAUD_005_RULE\": {\n" +
                "                            \"grey_flag\": -999,\n" +
                "                            \"ts_score_old\": -999,\n" +
                "                            \"ts_score_old_act\": -999\n" +
                "                        }\n" +
                "                    }\n" +
                "                },\n" +
                "                {\n" +
                "                    \"is_reject\": false,\n" +
                "                    \"reject_rules\": {\n" +
                "                        \"N_ANTIFRAUD_006_RULE\": {\n" +
                "                            \"hm_hitcnt\": -999,\n" +
                "                            \"fl_hitcnt\": -999\n" +
                "                        }\n" +
                "                    }\n" +
                "                },\n" +
                "                {\n" +
                "                    \"is_reject\": false,\n" +
                "                    \"reject_rules\": {\n" +
                "                        \"N_ANTIFRAUD_007_RULE\": {\n" +
                "                            \"if_debt_app_flag\": 0,\n" +
                "                            \"step\": \"HH_SIGN_A\"\n" +
                "                        }\n" +
                "                    }\n" +
                "                }\n" +
                "            ],\n" +
                "            \"fxq_hit_rule_result\": {\n" +
                "                \"is_reject\": false,\n" +
                "                \"reject_rules\": {\n" +
                "                    \"N_ANTIFRAUD_004_RULE\": {\n" +
                "                        \"fxq_hit_flag\": -999\n" +
                "                    }\n" +
                "                }\n" +
                "            },\n" +
                "            \"oldcust_ts_rule_result\": {\n" +
                "                \"lock_days\": 0,\n" +
                "                \"is_reject\": false,\n" +
                "                \"reject_rules\": {\n" +
                "                    \"N_ANTIFRAUD_005_RULE\": {\n" +
                "                        \"grey_flag\": -999,\n" +
                "                        \"ts_score_old\": -999,\n" +
                "                        \"ts_score_old_act\": -999\n" +
                "                    }\n" +
                "                }\n" +
                "            },\n" +
                "            \"grey_hit_ts_rule_result\": {\n" +
                "                \"is_reject\": false,\n" +
                "                \"reject_rules\": {\n" +
                "                    \"N_ANTIFRAUD_006_RULE\": {\n" +
                "                        \"hm_hitcnt\": -999,\n" +
                "                        \"fl_hitcnt\": -999\n" +
                "                    }\n" +
                "                }\n" +
                "            },\n" +
                "            \"screen_ts_rule_result\": {\n" +
                "                \"lock_days\": 0,\n" +
                "                \"is_reject\": false,\n" +
                "                \"reject_rules\": {\n" +
                "                    \"N_ANTIFRAUD_003_RULE\": {\n" +
                "                        \"screenrecording_flag\": -999,\n" +
                "                        \"rp_random20\": 0.524502882054,\n" +
                "                        \"step\": \"HH_SIGN_A\"\n" +
                "                    }\n" +
                "                }\n" +
                "            },\n" +
                "            \"newcust_ts_rule_result\": {\n" +
                "                \"lock_days\": 0,\n" +
                "                \"is_reject\": false,\n" +
                "                \"reject_rules\": {\n" +
                "                    \"N_ANTIFRAUD_001_RULE\": {\n" +
                "                        \"ts_score_new\": -999,\n" +
                "                        \"ts_score_new_act\": -999,\n" +
                "                        \"grey_flag\": -999\n" +
                "                    }\n" +
                "                }\n" +
                "            },\n" +
                "            \"final_results\": {\n" +
                "                \"antifraud_reject_label_fin\": 0\n" +
                "            },\n" +
                "            \"debt_app_rule_result\": {\n" +
                "                \"is_reject\": false,\n" +
                "                \"reject_rules\": {\n" +
                "                    \"N_ANTIFRAUD_007_RULE\": {\n" +
                "                        \"if_debt_app_flag\": 0,\n" +
                "                        \"step\": \"HH_SIGN_A\"\n" +
                "                    }\n" +
                "                }\n" +
                "            }\n" +
                "        },\n" +
                "        \"amountPeriodRateManagement\": {\n" +
                "            \"amount\": {\n" +
                "                \"loan\": {\n" +
                "                    \"expire_date\": \"\",\n" +
                "                    \"limit\": 20,\n" +
                "                    \"type\": \"fix\"\n" +
                "                },\n" +
                "                \"shop\": {\n" +
                "                    \"expire_date\": \"\",\n" +
                "                    \"limit\": \"\",\n" +
                "                    \"type\": \"\"\n" +
                "                }\n" +
                "            },\n" +
                "            \"period\": {\n" +
                "                \"loan\": {\n" +
                "                    \"flag\": \"\",\n" +
                "                    \"expire_date\": \"\",\n" +
                "                    \"period_rate\": {\n" +
                "\n" +
                "                    },\n" +
                "                    \"type\": \"\"\n" +
                "                },\n" +
                "                \"shop\": {\n" +
                "                    \"flag\": \"\",\n" +
                "                    \"expire_date\": \"\",\n" +
                "                    \"period_rate\": {\n" +
                "\n" +
                "                    },\n" +
                "                    \"type\": \"\"\n" +
                "                }\n" +
                "            },\n" +
                "            \"rate\": {\n" +
                "                \"loan\": {\n" +
                "                    \"flag\": \"\",\n" +
                "                    \"expire_date\": \"\",\n" +
                "                    \"period_rate\": {\n" +
                "\n" +
                "                    },\n" +
                "                    \"type\": \"\"\n" +
                "                },\n" +
                "                \"shop\": {\n" +
                "                    \"flag\": \"\",\n" +
                "                    \"expire_date\": \"\",\n" +
                "                    \"period_rate\": {\n" +
                "\n" +
                "                    },\n" +
                "                    \"type\": \"\"\n" +
                "                }\n" +
                "            }\n" +
                "        },\n" +
                "        \"btActualLine\": 0,\n" +
                "        \"lastId\": 1420651433,\n" +
                "        \"isActive\": true,\n" +
                "        \"loanLine\": 2260,\n" +
                "        \"btRate\": 0.0179,\n" +
                "        \"loanUtilLine\": 1511.11,\n" +
                "        \"id\": 3575532167,\n" +
                "        \"shopRate\": 0.0179,\n" +
                "        \"executeCommonModule\": true,\n" +
                "        \"ext1\": \"[{\\\"tmp_line_status\\\":0,\\\"tmp_actual_line\\\":0.0,\\\"tmp_line_end_time\\\":\\\" \\\",\\\"fix_line\\\":2260.0,\\\"tmp_line\\\":0.0,\\\"tmp_avail_line\\\":0.0,\\\"type\\\":1,\\\"tmp_util_line\\\":0.0},{\\\"tmp_line_status\\\":0,\\\"tmp_actual_line\\\":0.0,\\\"tmp_line_end_time\\\":\\\" \\\",\\\"fix_line\\\":2000.0,\\\"tmp_line\\\":0.0,\\\"tmp_avail_line\\\":0.0,\\\"type\\\":2,\\\"tmp_util_line\\\":0.0}]\",\n" +
                "        \"lineAssignTime\": 1716260127000,\n" +
                "        \"btLine\": 0,\n" +
                "        \"shardingTableName\": \"verify_user_line_management_45\",\n" +
                "        \"btAvailLine\": 0,\n" +
                "        \"version\": 26,\n" +
                "        \"userKey\": \"9d976351daa1d9b4223e68b297dcad70\",\n" +
                "        \"operatingAmountChange\": 0,\n" +
                "        \"shopPeriod\": 6,\n" +
                "        \"availLine\": 748.89,\n" +
                "        \"tmpLoanLineEndTime\": \"19700101080000\",\n" +
                "        \"periodLineRate\": \"[{\\\"period\\\":3,\\\"min\\\":500.0,\\\"max\\\":2260.0,\\\"rate\\\":0.02,\\\"rate_yn\\\":0.02,\\\"type\\\":\\\"loan\\\",\\\"guarantee_rate\\\":0.006},{\\\"lh_rate_period\\\":4,\\\"period\\\":6,\\\"min\\\":500.0,\\\"max\\\":2260.0,\\\"rate\\\":0.0179,\\\"rate_yn\\\":0.0179,\\\"lh_rate\\\":0.0179,\\\"is_lh_rate\\\":0,\\\"type\\\":\\\"loan\\\",\\\"guarantee_rate\\\":0.006},{\\\"lh_rate_period\\\":4,\\\"period\\\":9,\\\"min\\\":500.0,\\\"max\\\":2260.0,\\\"rate\\\":0.0173,\\\"rate_yn\\\":0.0173,\\\"lh_rate\\\":0.0173,\\\"is_lh_rate\\\":0,\\\"type\\\":\\\"loan\\\",\\\"guarantee_rate\\\":0.006},{\\\"lh_rate_period\\\":4,\\\"period\\\":12,\\\"min\\\":500.0,\\\"max\\\":2260.0,\\\"rate\\\":0.0171,\\\"rate_yn\\\":0.0171,\\\"lh_rate\\\":0.0171,\\\"is_lh_rate\\\":0,\\\"type\\\":\\\"loan\\\",\\\"guarantee_rate\\\":0.006},{\\\"lh_rate_period\\\":4,\\\"period\\\":6,\\\"min\\\":1.0,\\\"max\\\":2000.0,\\\"rate\\\":0.0179,\\\"rate_yn\\\":0.0179,\\\"lh_rate\\\":0.0179,\\\"is_lh_rate\\\":0,\\\"type\\\":\\\"shop\\\",\\\"guarantee_rate\\\":0.006}]\",\n" +
                "        \"loanId\": ********,\n" +
                "        \"status\": 1,\n" +
                "        \"sourceSystem\": \"HAO_HUAN\",\n" +
                "        \"strategyType\": \"AMOUNT_Tfix_A\",\n" +
                "        \"accountStatus\": \"C\",\n" +
                "        \"shopAvailLine\": 748.89,\n" +
                "        \"shopActualLine\": 748.89,\n" +
                "        \"userLevel\": \"G\",\n" +
                "        \"creditLine\": 2260,\n" +
                "        \"strategyId\": 17116,\n" +
                "        \"loanKey\": \"hh_20240521101042_A8806170651511140371\",\n" +
                "        \"reasonCode\": {\n" +
                "            \"periods_details\": {\n" +
                "                \"reloan_periods\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"period_adjust_after\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"period_adjust_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"reloan_periods_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"period_adjust_before\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"add_periods\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"period_adjust_type\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"add_periods_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"period_ext\": {\n" +
                "                    \"tmp_period_end_time\": \"\",\n" +
                "                    \"fix_period\": 12\n" +
                "                }\n" +
                "            },\n" +
                "            \"repay_info\": {\n" +
                "                \"mob\": \"3.0\",\n" +
                "                \"payoff_install_inadvance\": \"0.0\",\n" +
                "                \"his_max_overdue_days\": \"0.0\",\n" +
                "                \"mon_unpaid_amt\": \"171.25\",\n" +
                "                \"payoff_periods_s\": \"3.0\",\n" +
                "                \"loan_cnt\": \"1.0\",\n" +
                "                \"repaid_principal\": \"{\\\"CASH_BORROW\\\": 488.89}\",\n" +
                "                \"last_payoff_days\": \"2.0\",\n" +
                "                \"mob_mon\": \"3.0\",\n" +
                "                \"principal\": \"{\\\"CASH_BORROW\\\": 2000.0}\",\n" +
                "                \"cur_overdue_cnt\": \"0.0\",\n" +
                "                \"survive_days\": \"92.0\",\n" +
                "                \"payoff_cnt\": \"0.0\",\n" +
                "                \"payoff_install_normal\": \"3.0\",\n" +
                "                \"loan_unpaid_cnt\": \"1.0\",\n" +
                "                \"last_loan_time\": \"2024-02-19\",\n" +
                "                \"cur_overdue_days\": \"-9999.0\",\n" +
                "                \"loan_time\": \"2024-02-19\",\n" +
                "                \"payoff_days\": \"-9999.0\",\n" +
                "                \"last_loantime_days\": \"92.0\",\n" +
                "                \"left_periods_max\": \"9.0\",\n" +
                "                \"last_pay_time\": \"1716053698.0\",\n" +
                "                \"last_payoff_time\": \"-1.0\",\n" +
                "                \"max_repay_amount\": \"200.87\",\n" +
                "                \"max_duetime_days\": \"274.0\",\n" +
                "                \"payoff_flag\": \"0.0\",\n" +
                "                \"left_periods_min\": \"9.0\",\n" +
                "                \"payoff_periods_min\": \"3.0\",\n" +
                "                \"last_pay_mob\": \"3.0\"\n" +
                "            },\n" +
                "            \"other_tag\": {\n" +
                "                \"cust_class2\": \"unpaid_active\",\n" +
                "                \"last_apply_days\": \"0.0\",\n" +
                "                \"cust_class1\": \"unpaid_active\",\n" +
                "                \"manage_level_v3_0_new\": 7,\n" +
                "                \"cust_class\": \"unpaid\",\n" +
                "                \"left_periods_max\": \"9.0\",\n" +
                "                \"last_payoff_days\": \"19864.0\",\n" +
                "                \"manage_level_3_0\": 19,\n" +
                "                \"small_flag\": \"0.0\"\n" +
                "            },\n" +
                "            \"mid_level_info\": {\n" +
                "                \"manage_level_v3_0_new\": 7,\n" +
                "                \"duotou_level_v1\": {\n" +
                "                    \"thirdparty_level_v1\": \"error\",\n" +
                "                    \"br_level_v1\": \"error\",\n" +
                "                    \"td_level_v1\": \"error\",\n" +
                "                    \"cus_v3\": \"error\"\n" +
                "                },\n" +
                "                \"manage_level_3_0\": 19,\n" +
                "                \"rule_score_v3\": \"error\"\n" +
                "            },\n" +
                "            \"certification_info\": {\n" +
                "\n" +
                "            },\n" +
                "            \"txxy_credit_report\": {\n" +
                "                \"finance_risk_evaluate_info_ability_grade\": \"None\",\n" +
                "                \"credit_report_query_id\": \"None\",\n" +
                "                \"credit_score_info_grade\": \"None\",\n" +
                "                \"risk_count\": \"-999.0\",\n" +
                "                \"credit_score_info_percent\": \"-999.0\",\n" +
                "                \"finance_risk_evaluate_info_ability_score\": \"-999.0\",\n" +
                "                \"origindata\": \"{}\",\n" +
                "                \"credit_score_info_score\": \"-999.0\",\n" +
                "                \"airtkt_info_frequency_desc\": \"None\",\n" +
                "                \"contactinfo_memberlevel\": \"None\",\n" +
                "                \"contactinfo_feelevel\": \"None\",\n" +
                "                \"house_info_status\": \"None\"\n" +
                "            },\n" +
                "            \"maintain_amt_result\": {\n" +
                "                \"loan\": {\n" +
                "                    \"lift_details\": {\n" +
                "                        \"lift_flag\": true,\n" +
                "                        \"lift_test\": \"A\"\n" +
                "                    },\n" +
                "                    \"lift_type\": {\n" +
                "                        \"lift_interval\": \"\",\n" +
                "                        \"lift_type\": \"fix\"\n" +
                "                    },\n" +
                "                    \"lift_strategy\": \"identify_line_increase_all_v1_experiment\",\n" +
                "                    \"lift_amount\": 20\n" +
                "                },\n" +
                "                \"shop\": {\n" +
                "                    \"lift_type\": {\n" +
                "\n" +
                "                    },\n" +
                "                    \"lift_amount\": 0\n" +
                "                }\n" +
                "            },\n" +
                "            \"certification_item\": \"AMOUNT_EDUCATION\",\n" +
                "            \"reject_rules\": {\n" +
                "\n" +
                "            },\n" +
                "            \"mid_sign_step\": \"HH_SIGN_A\",\n" +
                "            \"random_num_new\": {\n" +
                "                \"random12\": 0.942051790126,\n" +
                "                \"random8\": 0.26779456727,\n" +
                "                \"random10\": 0.99383806149,\n" +
                "                \"random9\": 0.934127180426,\n" +
                "                \"random11\": 0.364288121151\n" +
                "            },\n" +
                "            \"maintain_rate_result\": {\n" +
                "                \"loan\": {\n" +
                "\n" +
                "                },\n" +
                "                \"shop\": {\n" +
                "\n" +
                "                }\n" +
                "            },\n" +
                "            \"is_passed\": false,\n" +
                "            \"sign_reject_flag\": false,\n" +
                "            \"amt_rate_period_result\": {\n" +
                "                \"period_re\": {\n" +
                "\n" +
                "                },\n" +
                "                \"line_re\": {\n" +
                "                    \"lift_details\": {\n" +
                "                        \"loan\": {\n" +
                "                            \"lift_flag\": true,\n" +
                "                            \"lift_test\": \"A\"\n" +
                "                        }\n" +
                "                    },\n" +
                "                    \"lift_type\": {\n" +
                "                        \"loan\": {\n" +
                "                            \"lift_interval\": \"\",\n" +
                "                            \"lift_type\": \"fix\"\n" +
                "                        }\n" +
                "                    },\n" +
                "                    \"lift_strategy\": {\n" +
                "                        \"loan\": \"identify_line_increase_all_v1_experiment\"\n" +
                "                    },\n" +
                "                    \"lift_amount\": {\n" +
                "                        \"loan\": 20\n" +
                "                    }\n" +
                "                },\n" +
                "                \"rate_re\": {\n" +
                "\n" +
                "                }\n" +
                "            },\n" +
                "            \"event_code\": \"haoHuanAmountIdentify\",\n" +
                "            \"increased_details\": {\n" +
                "                \"membership_increased_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"operating_limit_increased_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"edu_internal_increased_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"newfl_increased_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"identify_house_increased_limit\": [\n" +
                "                    20\n" +
                "                ],\n" +
                "                \"edu_report_increased_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"identify_job_increased_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"tfix_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"tax_increased_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"reaudit_promo_increased_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"identify_edu_increased_limit\": [\n" +
                "                    20\n" +
                "                ],\n" +
                "                \"accumulation_increased_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"tfix_tmp_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"reaudit_increased_level\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"reaudit_promo_increased_level\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"identify_vehicle_increased_limit\": [\n" +
                "                    20\n" +
                "                ],\n" +
                "                \"loanpage_increased_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"newfl_increased_level\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"alipay_increased_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"reloan_increased_level\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"add_increased_level\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"providentfund_increased_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"enterprise_small_increased_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"add_decreased_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"identify_job_increased_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"tax_increased_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"reaudit_increased_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"identify_cc_increased_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"operating_limit_increased_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"reaudit_promo_increased_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"identify_marriage_increased_time\": [\n" +
                "                    \"2024-04-03\"\n" +
                "                ],\n" +
                "                \"tfix_tmp_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"vehicle_report_increased_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"add_increased_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"add_decreased_level\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"identify_house_increased_time\": [\n" +
                "                    \"2024-04-03\"\n" +
                "                ],\n" +
                "                \"lift_card_increased_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"lift_card_increased_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"reaudit_increased_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"vehicle_report_increased_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"identify_increased_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"enterprise_small_increased_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"providentfund_increased_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"identify_increased_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"reloan_decreased_level\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"newfl_increased_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"credit_report_increased_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"reloan_increased_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"sign_increased_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"sign_increased_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"credit_report_increased_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"zhima_credit_report_increased_time\": [\n" +
                "                    \"2024-03-10\"\n" +
                "                ],\n" +
                "                \"identify_edu_increased_time\": [\n" +
                "                    \"2024-05-21\"\n" +
                "                ],\n" +
                "                \"reloan_decreased_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"identify_vehicle_increased_time\": [\n" +
                "                    \"2024-04-03\"\n" +
                "                ],\n" +
                "                \"reloan_decreased_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"add_increased_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"edu_internal_increased_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"alipay_increased_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"identify_cc_increased_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"add_decreased_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"tfix_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"identify_marriage_increased_limit\": [\n" +
                "                    100\n" +
                "                ],\n" +
                "                \"zhima_credit_report_increased_limit\": [\n" +
                "                    100\n" +
                "                ],\n" +
                "                \"loanpage_increased_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"membership_increased_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"edu_report_increased_limit\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"reloan_increased_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"accumulation_increased_limit\": [\n" +
                "\n" +
                "                ]\n" +
                "            },\n" +
                "            \"tag_details\": {\n" +
                "                \"add_shop_flag\": true,\n" +
                "                \"tax_loan_increased_limit\": 0,\n" +
                "                \"add_loan_flag\": true,\n" +
                "                \"identify_house_increased_limit\": 0,\n" +
                "                \"credit_report_loan_increased_limit\": 0,\n" +
                "                \"identify_job_increased_limit\": 0,\n" +
                "                \"add_loan_increased_limit\": 20,\n" +
                "                \"add_shop_increased_limit\": 0,\n" +
                "                \"add_loan_decreased_rate\": 0,\n" +
                "                \"sign_shop_increased_limit\": 0,\n" +
                "                \"identify_edu_increased_limit\": 0,\n" +
                "                \"reloan_increased_limit\": 0,\n" +
                "                \"edu_report_loan_increased_limit\": 0,\n" +
                "                \"identify_vehicle_increased_limit\": 0,\n" +
                "                \"identify_loan_increased_limit\": 20,\n" +
                "                \"edu_internal_loan_increased_limit\": 0,\n" +
                "                \"sign_loan_increased_limit\": 0,\n" +
                "                \"enterprise_small_loan_increased_limit\": 0,\n" +
                "                \"operating_limit_loan_increased_limit\": 0,\n" +
                "                \"identify_cc_increased_limit\": 0,\n" +
                "                \"zhima_credit_report_loan_increased_limit\": 0,\n" +
                "                \"reloan_decreased_rate\": 0,\n" +
                "                \"membership_loan_increased_limit\": 0,\n" +
                "                \"identify_marriage_increased_limit\": 0,\n" +
                "                \"loanpage_increased_limit\": 0,\n" +
                "                \"vehicle_report_loan_increased_limit\": 0,\n" +
                "                \"lift_card_increased_limit\": 0,\n" +
                "                \"accumulation_increased_limit\": 0\n" +
                "            },\n" +
                "            \"maintain_period_result\": {\n" +
                "                \"loan\": {\n" +
                "\n" +
                "                },\n" +
                "                \"shop\": {\n" +
                "\n" +
                "                }\n" +
                "            },\n" +
                "            \"amount_period_rate_management\": {\n" +
                "                \"amount\": {\n" +
                "                    \"loan\": {\n" +
                "                        \"expire_date\": \"\",\n" +
                "                        \"limit\": 20,\n" +
                "                        \"type\": \"fix\"\n" +
                "                    },\n" +
                "                    \"shop\": {\n" +
                "                        \"expire_date\": \"\",\n" +
                "                        \"limit\": \"\",\n" +
                "                        \"type\": \"\"\n" +
                "                    }\n" +
                "                },\n" +
                "                \"period\": {\n" +
                "                    \"loan\": {\n" +
                "                        \"flag\": \"\",\n" +
                "                        \"expire_date\": \"\",\n" +
                "                        \"period_rate\": {\n" +
                "\n" +
                "                        },\n" +
                "                        \"type\": \"\"\n" +
                "                    },\n" +
                "                    \"shop\": {\n" +
                "                        \"flag\": \"\",\n" +
                "                        \"expire_date\": \"\",\n" +
                "                        \"period_rate\": {\n" +
                "\n" +
                "                        },\n" +
                "                        \"type\": \"\"\n" +
                "                    }\n" +
                "                },\n" +
                "                \"rate\": {\n" +
                "                    \"loan\": {\n" +
                "                        \"flag\": \"\",\n" +
                "                        \"expire_date\": \"\",\n" +
                "                        \"period_rate\": {\n" +
                "\n" +
                "                        },\n" +
                "                        \"type\": \"\"\n" +
                "                    },\n" +
                "                    \"shop\": {\n" +
                "                        \"flag\": \"\",\n" +
                "                        \"expire_date\": \"\",\n" +
                "                        \"period_rate\": {\n" +
                "\n" +
                "                        },\n" +
                "                        \"type\": \"\"\n" +
                "                    }\n" +
                "                }\n" +
                "            },\n" +
                "            \"personal_asset\": {\n" +
                "                \"income_level_new\": \"3-\",\n" +
                "                \"zhishu_income\": -1,\n" +
                "                \"income_level\": \"3-\",\n" +
                "                \"br_income\": {\n" +
                "                    \"br_cust_miss_flag\": 1,\n" +
                "                    \"flag_datacust1\": -1,\n" +
                "                    \"dc1_pc_rcnt_income\": -1\n" +
                "                }\n" +
                "            },\n" +
                "            \"sign_result\": {\n" +
                "                \"period_re\": {\n" +
                "\n" +
                "                },\n" +
                "                \"line_re\": {\n" +
                "                    \"lift_details\": {\n" +
                "                        \"loan\": {\n" +
                "                            \"lift_flag\": true,\n" +
                "                            \"lift_test\": \"A\"\n" +
                "                        }\n" +
                "                    },\n" +
                "                    \"lift_type\": {\n" +
                "                        \"loan\": {\n" +
                "                            \"lift_interval\": \"\",\n" +
                "                            \"lift_type\": \"fix\"\n" +
                "                        }\n" +
                "                    },\n" +
                "                    \"lift_strategy\": {\n" +
                "                        \"loan\": \"identify_line_increase_all_v1_experiment\"\n" +
                "                    },\n" +
                "                    \"lift_amount\": {\n" +
                "                        \"loan\": 20\n" +
                "                    }\n" +
                "                },\n" +
                "                \"rate_re\": {\n" +
                "\n" +
                "                }\n" +
                "            },\n" +
                "            \"events\": {\n" +
                "                \"0004\": {\n" +
                "                    \"tag\": \"loan_norm_line_004_lift\"\n" +
                "                },\n" +
                "                \"0025\": {\n" +
                "                    \"tag\": \"shop_add_line_001_geta\"\n" +
                "                },\n" +
                "                \"0011\": {\n" +
                "                    \"tag\": \"loan_add_line_001_geta\"\n" +
                "                }\n" +
                "            },\n" +
                "            \"rates_details\": {\n" +
                "                \"reloan_rates_time\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"add_rates\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"reloan_rates\": [\n" +
                "\n" +
                "                ],\n" +
                "                \"add_rates_time\": [\n" +
                "\n" +
                "                ]\n" +
                "            }\n" +
                "        },\n" +
                "        \"loanAvailLine\": 748.89,\n" +
                "        \"loanActualLine\": 748.89,\n" +
                "        \"userPoint\": 0,\n" +
                "        \"utilLine\": 1511.11,\n" +
                "        \"updateTime\": \"20240521101516\",\n" +
                "        \"segmentCode\": {\n" +
                "            \"survive_days\": 90,\n" +
                "            \"payoff_flag\": 0,\n" +
                "            \"last_diff_loan_days\": 90,\n" +
                "            \"payoff_cnt\": 0,\n" +
                "            \"loan_unpaid_cnt\": 1,\n" +
                "            \"loan_time\": \"2024-02-19\",\n" +
                "            \"last_payoff_periods\": 3\n" +
                "        },\n" +
                "        \"shopLine\": 2000,\n" +
                "        \"btUtilLine\": 0,\n" +
                "        \"isClosed\": false,\n" +
                "        \"shopUtilLine\": 0,\n" +
                "        \"tmpShopLineEndTime\": \"19700101080000\",\n" +
                "        \"createTime\": \"20240521101516\",\n" +
                "        \"loanRate\": 0.0171,\n" +
                "        \"btPeriod\": 6,\n" +
                "        \"loanPeriod\": 12\n" +
                "    },\n" +
                "    \"loanKey\": \"hh_cd1f3c6a-1ce9-4a12-b14e-7107c48afabe\",\n" +
                "    \"amountParams\": {\n" +
                "        \"icode\": \"0100010104\",\n" +
                "        \"gatewayCreateTime\": 1716288920336,\n" +
                "        \"sourceSystem\": \"HAO_HUAN\",\n" +
                "        \"transId\": \"38ddc73346a6ccd0776d5b4c2313c667\",\n" +
                "        \"certificationItem\": \"AMOUNT_EDUCATION\",\n" +
                "        \"occurTime\": \"20240521185520\",\n" +
                "        \"apiSource\": \"DXM\",\n" +
                "        \"isBatchEvent\": false,\n" +
                "        \"sessionId\": \"20240521185520_1701055781196279470\",\n" +
                "        \"userKey\": \"9d976351daa1d9b4223e68b297dcad70\",\n" +
                "        \"eventCode\": \"haoHuanAmountIdentify\",\n" +
                "        \"agencyRequestId\": \"cd1f3c6a-1ce9-4a12-b14e-7107c48afabe\",\n" +
                "        \"loanKey\": \"hh_cd1f3c6a-1ce9-4a12-b14e-7107c48afabe\",\n" +
                "        \"processSplitFlowType\": \"ONLINE\",\n" +
                "        \"processDefId\": \"haoHuanAmountIdentify_V8\",\n" +
                "        \"engineEventCreateTime\": 1716288925035,\n" +
                "        \"dataInput\": {\n" +
                "            \"icode\": \"0100010104\",\n" +
                "            \"systemId\": \"HAO_HUAN\",\n" +
                "            \"gatewayCreateTime\": 1716288920336,\n" +
                "            \"engineEventCreateTime\": 1716288925035,\n" +
                "            \"sourceSystem\": \"HAO_HUAN\",\n" +
                "            \"transId\": \"38ddc73346a6ccd0776d5b4c2313c667\",\n" +
                "            \"certificationItem\": \"AMOUNT_EDUCATION\",\n" +
                "            \"system_id\": \"HAO_HUAN\",\n" +
                "            \"occurTime\": \"20240521185520\",\n" +
                "            \"idCardNo\": \"342626199510122217\",\n" +
                "            \"targetSwitch\": \"RONGDAN\",\n" +
                "            \"mobile\": \"18315507550\",\n" +
                "            \"isBatchEvent\": false,\n" +
                "            \"sessionId\": \"20240521185520_1701055781196279470\",\n" +
                "            \"idno\": \"342626199510122217\",\n" +
                "            \"userKey\": \"9d976351daa1d9b4223e68b297dcad70\",\n" +
                "            \"eventCode\": \"haoHuanAmountIdentify\",\n" +
                "            \"processSplitFlowType\": \"ONLINE\",\n" +
                "            \"name\": \"王宏伟\",\n" +
                "            \"bankNo\": \"ZIXB4qUCi5yRIWH9ipoAOzUsfHuUY5ENe+DRVp/OCXA=\",\n" +
                "            \"agencyRequestId\": \"cd1f3c6a-1ce9-4a12-b14e-7107c48afabe\",\n" +
                "            \"loanKey\": \"hh_cd1f3c6a-1ce9-4a12-b14e-7107c48afabe\",\n" +
                "            \"processDefId\": \"haoHuanAmountIdentify_V8\",\n" +
                "            \"username\": \"王宏伟\"\n" +
                "        },\n" +
                "        \"step\": \"HH_SIGN_A\",\n" +
                "        \"isNewLoan\": false,\n" +
                "        \"amountType\": \"AMOUNT_DEFAULT\",\n" +
                "        \"realStep\": \"\",\n" +
                "        \"stepStack\": [\n" +
                "            \"HH_SIGN_A\"\n" +
                "        ],\n" +
                "        \"strategyType\": \"AMOUNT_SIGN_ALL\"\n" +
                "    },\n" +
                "    \"userKey\": \"9d976351daa1d9b4223e68b297dcad70\",\n" +
                "    \"strategyType\": \"AMOUNT_SIGN_ALL\"\n" +
                "}";
        JSONObject params = JSONObject.parseObject(param);
        VerifyUserLineManagement record = new VerifyUserLineManagement();
        record.setLoanKey("hh_cd1f3c6a-1ce9-4a12-b14e-7107c48afabe");
        apiUserLineManagementService.insertApiUserLines(params, "fa7a345469d0e6119a611d7997173bb2", record);
    }
}