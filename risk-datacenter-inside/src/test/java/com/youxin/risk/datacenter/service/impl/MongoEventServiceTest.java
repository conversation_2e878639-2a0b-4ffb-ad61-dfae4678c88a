package com.youxin.risk.datacenter.service.impl;

import com.youxin.risk.datacenter.service.MongoEventService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class MongoEventServiceTest {

    @Autowired
    private MongoEventService mongoEventService;

    @Test
    public void queryDataFromDataVo() {
        String data = mongoEventService.queryDataFromEventVo("20220418193326_6382579758494616590", "$.event");
        log.info("data:{}", data);
    }
}
