package com.youxin.risk.datacenter.service.impl;

import com.youxin.risk.datacenter.service.BaoGuoCardService;
import com.youxin.risk.verify.vo.SubmitAddressVo;
import com.youxin.risk.verify.vo.SubmitJobVo;
import junit.framework.TestCase;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @Desc 保过卡服务测试
 * @Auth linchongbin
 * @Date 2022/3/23 17:04
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class BaoGuoCardServiceImplTest extends TestCase {

    @Autowired
    private BaoGuoCardService baoGuoCardService;

    @Autowired
    private com.youxin.risk.verify.service.impl.SubmitAddressServiceImpl submitAddressService;

    @Autowired
    private com.youxin.risk.verify.service.impl.SubmitJobServiceImpl submitJobService;

    @Test
    public void testQueryByUserKey() {
        baoGuoCardService.queryByUserKey("80ca60280340dd01c0119d2add829230");
    }

    @Test
    public void testGetAddressInfoByUserKey() {
        SubmitAddressVo info = submitAddressService.getAddressInfoByUserKey("987a1a161e2b518a5753b0b735465703");
        System.out.println(info);
    }

    @Test
    public void testGetJobInfoByUserKey() {
        SubmitJobVo info = submitJobService.getJobInfoByUserKey("3837c38f62fa3daa1d1924ddd8548d26");
        System.out.println(info);
    }

}