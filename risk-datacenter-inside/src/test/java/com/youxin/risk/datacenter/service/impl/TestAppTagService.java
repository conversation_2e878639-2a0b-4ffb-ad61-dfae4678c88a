package com.youxin.risk.datacenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.commons.model.datacenter.DcSubmitPlist;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.datacenter.constants.SpiderDataSource;
import com.youxin.risk.datacenter.mapper.AppTagMapper;
import com.youxin.risk.datacenter.model.AppTag;
import com.youxin.risk.datacenter.model.DcIpData;
import com.youxin.risk.datacenter.pojo.AppTagVo;
import com.youxin.risk.datacenter.service.AppTagService;
import com.youxin.risk.verify.redis.RedisService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class TestAppTagService {

    @Resource()
    private AppTagService appTagService;
    @Resource()
    private DcIpDataService dcIpDataService;

    @Test
    public void testUpdate(){
        AppTagVo appTag = new AppTagVo();
        appTag.setAppName("Faceu激萌");
        List<String> appTags = new ArrayList<>();
        appTags.add("摄影图像");
        appTags.add("相机");
        appTag.setAppStoreTags(appTags);
        appTag.setWebSearchTag("命中");
        appTagService.updateByAppName(appTag);
    }

    @Test
    public void testSubmitJob() throws InterruptedException {
        List<DcSubmitPlist> dcSubmitPlists = new ArrayList<>();
        DcSubmitPlist plist = new DcSubmitPlist();
        plist.setAppName("今日头条");
        plist.setPackageName("com.toutiao");
        dcSubmitPlists.add(plist);
        appTagService.handleAppTagAsync(dcSubmitPlists);
        Thread.sleep(Integer.MAX_VALUE);
    }

    private static final Logger LOGGER = LoggerFactory.getLogger(TestAppTagService.class);

    @Value("${risk.spider.url}")
    private String url;

    @Resource
    private AppTagMapper appTagMapper;

    @Resource(name = "cacheRedisService")
    private RedisService cacheRedisService;

    private final static String APP_TAG_INC_KEY = "risk_datacenter_app_tag_inc_key";

    ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(1,1,0,
            TimeUnit.SECONDS,new ArrayBlockingQueue<>(2), new ThreadPoolExecutor.AbortPolicy());

    @Test
    public void testXxlJob() throws InterruptedException {
        cacheRedisService.set(APP_TAG_INC_KEY,"100");
        while (true) {
            try {
                Long id = cacheRedisService.incr(APP_TAG_INC_KEY);
                AppTag appTag = appTagMapper.getById(id);
                if (appTag == null){
                    continue;
                }
                submitCrawlerJobToDataFactory(appTag);
                Thread.sleep(1000);
            } catch (Exception e) {
                LOGGER.error("exec RefreshAppTagJob eror",e);
            }
        }
    }

    private void submitCrawlerJobToDataFactory(AppTag app) {
        String result = null;
        try {
            Map<Object, Object> params = new HashMap<>();
            List<String> tagSources = new ArrayList<>();
            tagSources.add(SpiderDataSource.BAIDU_WEB_SEARCH);
            tagSources.add(SpiderDataSource.LI_QU);
            params.put("type", "appTag");
            params.put("keyword", app.getAppName());
            params.put("dataSources", tagSources);
            params.put("callBackUrl", "/appTag/updateAppTag");
            result = SyncHTTPRemoteAPI.postJson(url + "/spider/produce", JSON.toJSONString(params), 10000);
        }catch (Exception e){
            LOGGER.info("submit crawlerJob error", e);
        }
        LOGGER.info("submit crawlerJob success,appName:{},result:{}", app.getAppName(),result);
    }

    @Test
    public void testDcIpData()  {
        String ip = "***************";
        DcIpData dcIpData = dcIpDataService.getIpArea(ip);
    }

    @Test
    public void testAppTagRealTime(){

        int index = 0;
        while (true){
            List<DcSubmitPlist> dcSubmitPlists = new ArrayList<>();
            index++;
            for (int i = 0; i < 10; i++) {
                DcSubmitPlist plist = new DcSubmitPlist();
                plist.setAppName("appName" + index);
                plist.setUserKey("asdfasdfasdfadfadsf");
                dcSubmitPlists.add(plist);
            }
            appTagService.handleAppTagAsync(dcSubmitPlists);
        }
    }

    @Test
    public void insertToWaitCrawlQueue(){
        AppTag appTag = new AppTag();
        appTag.setAppName("test-微财-2");
        appTag.setPackageName("com.weicai");
        appTag.setCreateTime(new Date());
        appTag.setUpdateTime(new Date());
        appTagMapper.insertToWaitCrawlQueue(appTag);
    }

    @Test
    public void getOneIdGreaterThanFromCrawlQueue(){
        AppTag oneIdGreaterThanFromCrawlQueue = appTagMapper.getOneIdGreaterThanFromCrawlQueue(1L);
        System.out.println(JSON.toJSONString(oneIdGreaterThanFromCrawlQueue));
    }



    @Test
    public void getFromWaitCrawlQueue(){
        AppTag fromWaitCrawlQueue = appTagMapper.getFromWaitCrawlQueue("test-微财");
        System.out.println(JSON.toJSONString(fromWaitCrawlQueue));
    }



    @Test
    public void getOneIdGreaterThan(){
        AppTag oneIdGreaterThan = appTagMapper.getOneIdGreaterThan(1L);
        System.out.println(JSON.toJSONString(oneIdGreaterThan));
    }


    @Test
    public void deleteFromWaitCrawlQueue(){
        appTagMapper.deleteFromWaitCrawlQueue("test-微财");
    }


    @Test
    public void insert(){
        AppTag appTag = new AppTag();
        appTag.setAppName("weicai");
        appTag.setPackageName("com.weicai");
        appTag.setCreateTime(new Date());
        appTagMapper.insert(appTag);
    }

}
