<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.1.xsd">

	<bean id="scheduledThreadPool" 
		class="com.youxin.risk.ra.schedule.ScheduledThreadPool">
		<property name="corePoolSize" value="50" />
	</bean>

	<bean id="kafkaRetryScheduledThreadPool"
		  class="com.youxin.risk.ra.schedule.ScheduledThreadPool">
		<property name="corePoolSize" value="50" />
	</bean>

	
	<bean id="antiFraudModelRetryThreadPool" 
		class="com.youxin.risk.ra.thread.ThreadPool">
		<property name="corePoolSize" value="200" />
	</bean>

	<!-- 检查所有未处理的kafka消息 -->
	<!--<bean id="kafkaMessageScheduleService"
		  class="com.youxin.risk.ra.schedule.service.KafkaMessageScheduleService"
		  init-method="startTask">
		<property name="initialDelay" value="30" />
		<property name="delay" value="60" />
	</bean>-->

</beans>