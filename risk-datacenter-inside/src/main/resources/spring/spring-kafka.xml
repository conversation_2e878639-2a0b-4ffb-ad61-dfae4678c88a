<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">


    <!-- 消息解析filter，将消息反序列化对对象，默认使用fastjson，可自己实现 -->
    <bean id="dpMsgPaserFilter" class="com.youxin.risk.commons.kafkav2.filter.impl.ParseFilter">
        <property name="serializedBeanClassName" value="com.youxin.risk.commons.vo.DataPlatformMessageVo"/>
    </bean>

    <!-- producer配置 -->
    <bean id="producerProperties" class="java.util.HashMap">
        <constructor-arg>
            <map>
                <entry key="bootstrap.servers" value="${kafka.dp.hosts}"/>
                <entry key="request.timeout.ms" value="3000"/>
                <entry key="buffer.memory" value="16777216"/>
                <entry key="max.request.size" value="15728640"/>
                <entry key="compression.type" value="lz4"/>
                <entry key="key.serializer" value="org.apache.kafka.common.serialization.StringSerializer"/>
                <entry key="value.serializer" value="org.apache.kafka.common.serialization.StringSerializer"/>
                <entry key="acks" value="all"/>
                <entry key="max.in.flight.requests.per.connection" value="5"/>
                <entry key="retries" value="2147483647"/>
                <entry key="batch.size" value="1048576"/>
                <entry key="linger.ms" value="500"/>
            </map>
        </constructor-arg>
    </bean>
    <bean id="producerFactory" class="org.springframework.kafka.core.DefaultKafkaProducerFactory">
        <constructor-arg>
            <ref bean="producerProperties"/>
        </constructor-arg>
    </bean>

    <bean id="verifyKafkaTemplate" class="org.springframework.kafka.core.KafkaTemplate">
        <constructor-arg ref="producerFactory"/>
        <constructor-arg name="autoFlush" value="true"/>
        <property name="defaultTopic" value="${kafka.verify.shadow.topic}"/>
    </bean>

    <bean id="mirrorProducerProperties" class="java.util.HashMap">
        <constructor-arg>
            <map>
                <entry key="bootstrap.servers" value="${kafka.dp.hosts}"/>
                <entry key="request.timeout.ms" value="3000"/>
                <entry key="buffer.memory" value="16777216"/>
                <entry key="max.request.size" value="15728640"/>
                <entry key="compression.type" value="lz4"/>
                <entry key="key.serializer" value="org.apache.kafka.common.serialization.StringSerializer"/>
                <entry key="value.serializer" value="org.apache.kafka.common.serialization.StringSerializer"/>
                <entry key="acks" value="all"/>
                <entry key="max.in.flight.requests.per.connection" value="5"/>
                <entry key="retries" value="2147483647"/>
                <entry key="batch.size" value="1048576"/>
                <entry key="linger.ms" value="500"/>
            </map>
        </constructor-arg>
    </bean>

    <bean id="mirrorProducerFactory" class="org.springframework.kafka.core.DefaultKafkaProducerFactory">
        <constructor-arg>
            <ref bean="mirrorProducerProperties"/>
        </constructor-arg>
    </bean>


    <bean id="abstractKafkaBuilder" abstract="true"
          class="com.youxin.risk.ra.kafka.builder.AbstractKafkaBuilder">
    </bean>


    <!--数据平台的消息Service，只作为消费者 -->
    <bean id="dataPlatformMessageService" class="com.youxin.risk.ra.kafka.service.impl.DataPlatformMessageServiceImpl"
          lazy-init="true" init-method="start">
        <property name="isConsumer" value="true" />
        <property name="isProducer" value="false" />
        <property name="consumerNew" ref="dpConsumer" />
    </bean>

    <bean id="dataPlatform" parent="abstractKafkaBuilder"
          class="com.youxin.risk.ra.kafka.builder.SimpleJsonSerializeBuilder">
        <property name="serializedBeanClass" value="com.youxin.risk.ra.kafka.vo.DataPlatformMessageVo" />
        <property name="topicName" value="${kafka.dp.result.topic}" />
        <property name="bootstrapServers" value="${kafka.dp.hosts}" />
        <property name="consumerGroupId" value="${kafka.dp.result.topic.group.id}" />
    </bean>

    <bean id="dpConsumer" factory-bean="dataPlatform"  factory-method="getConsumerNew" >
        <constructor-arg type="java.lang.String" value="main"/>
    </bean>

    <bean id="kafkaMirrorTemplate" class="org.springframework.kafka.core.KafkaTemplate">
        <constructor-arg ref="mirrorProducerFactory"/>
        <constructor-arg name="autoFlush" value="true"/>
        <property name="defaultTopic" value="defaultTopic"/>
    </bean>
</beans>
