<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.datacenter.mapper.DcSubmitAmountEducationMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.datacenter.model.DcSubmitAmountEducation">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="operation_log_id" property="operationLogId" jdbcType="INTEGER"/>
        <result column="user_key" property="userKey" jdbcType="VARCHAR"/>
        <result column="university" property="university" jdbcType="VARCHAR"/>
        <result column="highest_education_level" property="highestEducationLevel" jdbcType="VARCHAR"/>
        <result column="highest_degree" property="highestDegree" jdbcType="VARCHAR"/>
        <result column="graduate_year" property="graduateYear" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, operation_log_id,user_key, university, highest_education_level, highest_degree,
    graduate_year, create_time, update_time
  </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from dc_submit_amount_education
        where id = #{id,jdbcType=INTEGER}
    </select>


    <insert id="insert" parameterType="com.youxin.risk.datacenter.model.DcSubmitAmountEducation">
        <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
            SELECT LAST_INSERT_ID() AS id
        </selectKey>
        insert into dc_submit_amount_education (operation_log_id, user_key,
        university, highest_education_level, highest_degree,
        graduate_year, create_time, update_time
        )
        values (#{operationLogId}, #{userKey},
        #{university}, #{highestEducationLevel}, #{highestDegree},
        #{graduateYear}, #{createTime}, #{updateTime}
        )
    </insert>


    <select id="getByUserKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dc_submit_amount_education
        where user_key = #{userKey} order by id DESC limit 1
    </select>

    <select id="getLatestByUserKey" resultMap="BaseResultMap">
        select university,highest_education_level,highest_degree,graduate_year,create_time
        from dc_submit_amount_education
        where user_key = #{userKey} order by id DESC limit 1
    </select>
</mapper>