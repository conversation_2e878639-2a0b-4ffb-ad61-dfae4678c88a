<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.verify.sharding.mapper.VerifyUserLineManagementShardingMapper">

    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.verify.VerifyUserLineManagement">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="user_key" property="userKey"/>
        <result column="source_system" property="sourceSystem"/>
        <result column="credit_line" property="creditLine"/>
        <result column="avail_line" property="availLine"/>
        <result column="util_line" property="utilLine"/>
        <result column="loan_line" property="loanLine"/>
        <result column="loan_avail_line" property="loanAvailLine"/>
        <result column="loan_actual_line" property="loanActualLine"/>
        <result column="loan_util_line" property="loanUtilLine"/>
        <result column="loan_rate" property="loanRate"/>
        <result column="loan_period" property="loanPeriod"/>
        <result column="bt_line" property="btLine"/>
        <result column="bt_avail_line" property="btAvailLine"/>
        <result column="bt_actual_line" property="btActualLine"/>
        <result column="bt_util_line" property="btUtilLine"/>
        <result column="bt_rate" property="btRate"/>
        <result column="bt_period" property="btPeriod"/>
        <result column="shop_line" property="shopLine"/>
        <result column="shop_avail_line" property="shopAvailLine"/>
        <result column="shop_actual_line" property="shopActualLine"/>
        <result column="shop_util_line" property="shopUtilLine"/>
        <result column="shop_rate" property="shopRate"/>
        <result column="shop_period" property="shopPeriod"/>
        <result column="period_line_rate" property="periodLineRate"/>
        <result column="account_status" property="accountStatus"/>
        <result column="is_closed" property="isClosed"/>
        <result column="user_point" property="userPoint"/>
        <result column="user_level" property="userLevel"/>
        <result column="line_assign_time" property="lineAssignTime"/>
        <result column="strategy_id" property="strategyId"/>
        <result column="strategy_type" property="strategyType"/>
        <result column="is_active" property="isActive"/>
        <result column="last_id" property="lastId"/>
        <result column="loan_id" property="loanId"/>
        <result column="loan_key" property="loanKey"/>
        <result column="reason_code" property="reasonCode"/>
        <result column="segment_code" property="segmentCode"/>
        <result column="test_code" property="testCode"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="version" property="version"/>
        <result column="ext1" property="ext1"/>
        <result column="tmp_shop_line_end_time" property="tmpShopLineEndTime"/>
        <result column="tmp_loan_line_end_time" property="tmpLoanLineEndTime"/>
    </resultMap>


    <sql id="Base_Column_List">
        id,user_key, source_system, credit_line, avail_line, util_line, loan_line, loan_avail_line, loan_actual_line,
        loan_util_line, loan_rate, loan_period, bt_line, bt_avail_line, bt_actual_line, bt_util_line, bt_rate,
        bt_period, shop_line, shop_avail_line, shop_actual_line, shop_util_line, shop_rate, shop_period,
        period_line_rate, account_status, is_closed, user_point, user_level, line_assign_time, strategy_id,
        strategy_type, is_active, last_id, loan_id, loan_key, reason_code, segment_code, test_code, status,
        remark, create_time, update_time, version, ext1, tmp_shop_line_end_time, tmp_loan_line_end_time
    </sql>
    <delete id="deleteDataByIds">
        delete from ${shardingTableName} where id in
        <foreach collection="dataList" item="data" open="(" close=")" separator=",">
            #{data.id}
        </foreach>
        and is_active=0
    </delete>

    <select id="getByUserKey" resultMap="BaseResultMap" >
        select * from ${shardingTableName}
        where user_key = #{userKey} AND  source_system = #{sourceSystem}  AND is_active = 1 order  by id DESC limit 0,1
    </select>


    <update id="updateNoInvalid">
        update ${shardingTableName} set is_active = 0
        where user_key = #{userKey}
        and id = #{lineId}
        and is_active = 1;
    </update>
    <update id="updateActive">
        update ${shardingTableName} set is_active = #{isActive}
        where user_key = #{userKey}
        <![CDATA[ and id = #{id} ]]>
    </update>


    <insert id="saveUserLineManagemert" parameterType="com.youxin.risk.commons.model.verify.VerifyUserLineManagement">
        <!--<selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
            SELECT LAST_INSERT_ID()
        </selectKey>-->
        insert into ${shardingTableName}
        (
        id,user_key, source_system, credit_line, avail_line, util_line, loan_line, loan_avail_line, loan_actual_line,
        loan_util_line, loan_rate, loan_period, bt_line, bt_avail_line, bt_actual_line, bt_util_line, bt_rate,
        bt_period, shop_line, shop_avail_line, shop_actual_line, shop_util_line, shop_rate, shop_period,
        period_line_rate, account_status, is_closed, user_point, user_level, line_assign_time, strategy_id,
        strategy_type, is_active, last_id, loan_id, loan_key, reason_code, segment_code, test_code, status,
        remark, create_time, update_time, version, ext1, tmp_shop_line_end_time, tmp_loan_line_end_time
        )
        values
        (
        #{id},#{userKey}, #{sourceSystem}, #{creditLine}, #{availLine}, #{utilLine}, #{loanLine}, #{loanAvailLine}, #{loanActualLine},
        #{loanUtilLine}, #{loanRate}, #{loanPeriod}, #{btLine}, #{btAvailLine}, #{btActualLine}, #{btUtilLine}, #{btRate},
        #{btPeriod}, #{shopLine}, #{shopAvailLine}, #{shopActualLine},#{shopUtilLine}, #{shopRate}, #{shopPeriod},
        #{periodLineRate},#{accountStatus}, #{isClosed}, #{userPoint}, #{userLevel}, #{lineAssignTime},#{strategyId},
        #{strategyType},#{isActive},#{lastId},#{loanId},#{loanKey},#{reasonCode},#{segmentCode},#{testCode},#{status},
        #{remark}, #{createTime}, #{updateTime},#{version},#{ext1},#{tmpShopLineEndTime},#{tmpLoanLineEndTime}
        )
    </insert>


    <select id="getList" resultMap="BaseResultMap">
        select * from ${shardingTableName}
        where  user_key = #{userKey} order by id desc limit #{c};
    </select>

    <select id="getCheckVerifyUserLineManagement" resultMap="BaseResultMap">
        select * from ${shardingTableName}
        where  user_key = #{userKey} order by id ;
    </select>

    <select id="getAmountAssignByUserKey" resultMap="BaseResultMap">
        select * from ${shardingTableName}
        where user_key = #{userKey} AND  source_system = #{sourceSystem}  AND (strategy_type like 'AMOUNT_ASSIGN_%'
        or strategy_type in ('IRR36_VERIFY_AMOUNT', 'IRR24_VERIFY_AMOUNT', 'WHITE_LIST_VERIFY_AMOUNT'))
        order  by id DESC limit 0,1
    </select>

    <select id="queryAllTableNamesBySchema" resultType="java.lang.String">
        select table_name from information_schema.tables  where TABLE_schema=#{schemaName}
    </select>
    <select id="queryByIdAndLimit" resultMap="BaseResultMap">
        select id,user_key,create_time,is_active,strategy_type from ${shardingTableName}
        where `is_active`=0
          and `create_time` <![CDATA[<=]]> #{limitTime}
        limit #{limit}
    </select>
    <update id="updateExt">
        update ${shardingTableName} set ext1=#{lineManagement.ext1} where id =#{lineManagement.id}
    </update>

    <select id="getLoanPageAmountChanges" resultMap="BaseResultMap">
        select * from ${shardingTableName}
        where user_key = #{userKey} AND strategy_type = 'AMOUNT_SIGN_ALL'
        order by id DESC
    </select>

    <select id="getLineDataFromVerifyUserLineManagement" resultType="hashmap"><![CDATA[
        select loan_actual_line, shop_actual_line, loan_period, loan_line, credit_line from ${shardingTableName}
        where create_time <= #{time} and user_key = #{userKey} order by id desc limit 1]]>
    </select>

    <select id="getLineDataFromVerifyUserLineManagementBatch" resultType="java.util.Map">
        select loan_actual_line, shop_actual_line, loan_period, loan_line, credit_line,create_time as line_create_time from ${shardingTableName}
        where user_key = #{userKey} order by id desc
    </select>

    <select id="getStrategyType" resultMap="BaseResultMap">
        select * from ${shardingTableName}
        where user_key = #{userKey} AND strategy_type in ('IRR36_VERIFY_AMOUNT', 'AMOUNT_ASSIGN_IRR_ALL_FL_36', 'IRR24_VERIFY_AMOUNT', 'AMOUNT_ASSIGN_IRR_ALL_FL', 'WHITE_LIST_VERIFY_AMOUNT', 'AMOUNT_ASSIGN_ALL_WHITE')
        order by id desc
        limit 0,1
    </select>

    <select id="queryUserLineCount" resultType="java.lang.Integer">
        <![CDATA[
            select count(1)
            from ${shardingTableName}
            where is_active = 1 and create_time < #{time}
        ]]>
    </select>

    <select id="queryUserLineList" resultMap="BaseResultMap">
        <![CDATA[
            select *
            from ${shardingTableName}
            where is_active = 1 and create_time < #{time}
        ]]>
    </select>

    <select id="queryUserLinesWithId" resultMap="BaseResultMap">
        <![CDATA[
            select *
            from ${shardingTableName}
            where id >= #{startId} and id < #{endId} and is_active = 1 and create_time < #{time} order by id asc
        ]]>
    </select>

    <select id="getByUserKeyCreateTime" resultMap="BaseResultMap">
        <![CDATA[
            select * from ${shardingTableName}
            where user_key = #{userKey} AND source_system = #{sourceSystem} AND create_time < #{time} order by id DESC limit 0,1
        ]]>
    </select>

    <select id="getByLoanKey" resultMap="BaseResultMap">
        <![CDATA[
            select * from ${shardingTableName}
            where loan_key = #{loanKey} order by id DESC limit 0,1
        ]]>
    </select>

    <select id="queryByUserKey" resultMap="BaseResultMap">
        select *
        from ${shardingTableName}
        where user_key = #{userKey} and is_active = 1 order by id desc limit 1
    </select>

</mapper>