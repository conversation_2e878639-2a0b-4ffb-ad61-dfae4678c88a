<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.verify.mapper.VerifyOptionalAuthMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.verify.model.VerifyOptionalAuth">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="user_key" jdbcType="VARCHAR" property="userKey" />
        <result column="type" jdbcType="VARCHAR" property="type" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="auth_time" jdbcType="TIMESTAMP" property="authTime" />
        <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="version" jdbcType="INTEGER" property="version" />
    </resultMap>


    <sql id="table_name">
      verify_optional_authentication
    </sql>

    <select id="getByUserKey" resultMap="BaseResultMap">
        select * from <include refid="table_name"/>
        WHERE user_key=#{userKey}
    </select>



</mapper>