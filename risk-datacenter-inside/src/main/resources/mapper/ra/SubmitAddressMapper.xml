<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.ra.mapper.SubmitAddressMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.ra.model.SubmitAddress">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="apply_id" jdbcType="INTEGER" property="applyId" />
        <result column="source_system" jdbcType="VARCHAR" property="sourceSystem" />
        <result column="user_key" jdbcType="VARCHAR" property="userKey" />
        <result column="province" jdbcType="VARCHAR" property="province" />
        <result column="city" jdbcType="VARCHAR" property="city" />
        <result column="district" jdbcType="VARCHAR" property="district" />
        <result column="live_duration" jdbcType="VARCHAR" property="liveDuration" />
        <result column="live_address" jdbcType="VARCHAR" property="liveAddress" />
        <result column="marriage" jdbcType="VARCHAR" property="marriage" />
        <result column="child_num" jdbcType="VARCHAR" property="childNum" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="version" jdbcType="INTEGER" property="version" />
    </resultMap>

    <sql id="table_name">
      ra_address
    </sql>

    <insert id="insert" parameterType="com.youxin.risk.ra.model.SubmitAddress">
        insert into
        <include refid="table_name"/>
        (apply_id,source_system,user_key,province,city,district,live_duration,live_address,marriage,child_num,update_time,create_time,version)
        values
        (#{applyId},#{sourceSystem},#{userKey},#{province},#{city},#{district},#{liveDuration},#{liveAddress},#{marriage},#{childNum},now(),now(),#{version})
    </insert>

    <select id="findLastSubmitByUserKey" resultMap="BaseResultMap">
        select * from
        <include refid="table_name"/>
        WHERE user_key = #{userKey} AND source_system = #{sourceSystem} ORDER BY id DESC
        limit 1
    </select>

    <select id="findAddressByUserApply" resultMap="BaseResultMap">
        select * from
        <include refid="table_name"/>
        where user_key = #{userKey} and apply_id = #{applyId}
        limit 1
    </select>


</mapper>