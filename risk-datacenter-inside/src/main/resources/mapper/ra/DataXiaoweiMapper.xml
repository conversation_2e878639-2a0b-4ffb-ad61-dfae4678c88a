<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.ra.mapper.DataXiaoweiMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.ra.model.DataXiaowei">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="source_system" jdbcType="VARCHAR" property="sourceSystem"/>
        <result column="user_key" jdbcType="VARCHAR" property="userKey"/>
        <result column="task_id" jdbcType="INTEGER" property="taskId"/>
        <result column="loan_key" jdbcType="VARCHAR" property="loanKey"/>
        <result column="period" jdbcType="VARCHAR" property="period"/>
        <result column="data" jdbcType="VARCHAR" property="data"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
    </resultMap>
    <sql id="table_name">
      ra_data_xiaowei
    </sql>

    <insert id="insert" parameterType="com.youxin.risk.ra.model.DataXiaowei">
        insert into
        <include refid="table_name"/>
        (source_system, user_key,task_id, loan_key,period, data, create_time, update_time,version)
        values (#{sourceSystem}, #{userKey}, #{taskId}, #{loanKey}, #{period},#{data}, now(), now(),#{version})
    </insert>


</mapper>