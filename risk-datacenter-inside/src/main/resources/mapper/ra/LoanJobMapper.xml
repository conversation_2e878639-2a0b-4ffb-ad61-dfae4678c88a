<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.ra.mapper.LoanJobMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.ra.model.LoanJob">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="loan_key" jdbcType="VARCHAR" property="loanKey"/>
        <result column="loan_id" jdbcType="INTEGER" property="loanId"/>
        <result column="job_id" jdbcType="VARCHAR" property="jobID"/>
        <result column="job_type" jdbcType="VARCHAR" property="jobType"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
    </resultMap>


    <sql id="table_name">
      ra_loan_job
    </sql>


    <insert id="insert" parameterType="com.youxin.risk.ra.model.LoanJob">
        insert into
        <include refid="table_name"/>
        (loan_key,loan_id,job_id,job_type,update_time,create_time,version)
        values
        (#{loanKey},#{loanId},#{jobID},#{jobType},now(),now(),#{version})
    </insert>


    <select id="findByLoanKeyAndJobId" resultMap="BaseResultMap">
        select * from
        <include refid="table_name"/>
        where loan_key = #{loanKey} AND job_type = #{jobType} AND job_id = #{jobId} order by id desc
        limit 1
    </select>


    <select id="findByTypes" resultMap="BaseResultMap">
        select * from
        <include refid="table_name"/>
        where loan_key = #{loanKey} AND job_type IN
        <foreach collection="jobTypes" item="jobType" index="index" open="(" separator="," close=")">
            #{jobType}
        </foreach>
    </select>


</mapper>