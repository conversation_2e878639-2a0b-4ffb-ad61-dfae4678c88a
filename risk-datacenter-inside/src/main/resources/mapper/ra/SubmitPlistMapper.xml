<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.ra.mapper.SubmitPlistMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.ra.model.SubmitPlist">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="apply_id" jdbcType="INTEGER" property="applyId" />
        <result column="source_system" jdbcType="VARCHAR" property="sourceSystem" />
        <result column="user_key" jdbcType="VARCHAR" property="userKey" />
        <result column="package_name" jdbcType="VARCHAR" property="packageName" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
        <result column="launch_time" jdbcType="TIMESTAMP" property="launchTime" />
        <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="version" jdbcType="INTEGER" property="version" />
    </resultMap>


    <sql id="table_name">
      ra_plist
    </sql>


    <insert id="insert" parameterType="com.youxin.risk.ra.model.SubmitPlist">
        insert into
        <include refid="table_name"/>
        (apply_id,source_system,user_key,package_name,app_name,launch_time,last_update_time,update_time,create_time,version)
        values
        (#{applyId},#{sourceSystem},#{userKey},#{packageName},#{appName},#{launchTime},#{lastUpdateTime},now(),now(),#{version})
    </insert>


</mapper>