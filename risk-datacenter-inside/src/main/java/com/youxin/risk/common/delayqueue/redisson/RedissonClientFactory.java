package com.youxin.risk.common.delayqueue.redisson;

import com.youxin.risk.commons.utils.LoggerProxy;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;


public class RedissonClientFactory {

    private final static Logger logger = LoggerFactory.getLogger(RedissonClientFactory.class);

    private String nodes;
    private int poolSize;
    private int scanInterval;
    private int connectionTimeout;
    private int idleConnectionTimeout;
    private int minimumIdleSize;
    private int retryInterval;
    private String password;
    private RedissonClient redissonClient;

    public RedissonClientFactory(String nodes, Integer poolSize, Integer connectionTimeout,
                                 Integer scanInterval, Integer idleConnectionTimeout,
                                 Integer minimumIdleSize, Integer retryInterval, String password) {
        this.nodes = nodes;
        this.poolSize = poolSize;
        this.connectionTimeout = connectionTimeout;
        this.scanInterval = scanInterval;
        this.idleConnectionTimeout = idleConnectionTimeout;
        this.minimumIdleSize = minimumIdleSize;
        this.retryInterval = retryInterval;
        this.password = password;
    }

    public String[] nodes() {
        List<String> clusterNodes = new ArrayList<>();
        String[] n = nodes.split(",");
        for (String s : n) {
            clusterNodes.add("redis://" + s);
        }
        return clusterNodes.toArray(new String[n.length]);
    }


    public Config init() throws IOException {
        Config config = new Config();
        config.useClusterServers().addNodeAddress(nodes())
                .setMasterConnectionPoolSize(poolSize)
                .setMasterConnectionMinimumIdleSize(minimumIdleSize)
                .setSlaveConnectionPoolSize(poolSize)
                .setSlaveConnectionMinimumIdleSize(minimumIdleSize)
                .setScanInterval(scanInterval)
                .setConnectTimeout(connectionTimeout)
                .setIdleConnectionTimeout(idleConnectionTimeout)
                .setRetryInterval(retryInterval);

        if (StringUtils.isNotBlank(this.password)) {
            config.useClusterServers().setPassword(this.password);
        }
        LoggerProxy.info("init", logger, "redisson config:{}", config.toJSON());
        return config;
    }

    public RedissonClient create() throws IOException {
        redissonClient = Redisson.create(init());
        return redissonClient;
    }

}
