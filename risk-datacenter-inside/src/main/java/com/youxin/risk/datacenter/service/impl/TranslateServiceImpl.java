package com.youxin.risk.datacenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.utils.HttpResult;
import com.youxin.risk.commons.utils.HttpUtils;
import com.youxin.risk.datacenter.pojo.JsonResultVo;
import com.youxin.risk.datacenter.service.TranslateService;
import com.youxin.risk.ra.mapper.DataCityLowestSalaryMapper;
import com.youxin.risk.ra.model.DataCityLowestSalary;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TranslateServiceImpl implements TranslateService {
    private Logger logger = LoggerFactory.getLogger(getClass());
    //高德开放接口的key
    private static String GAODE_KEY = "c26d860c41e26e254553e3f9ccc2a8a8";
    private static String DEFAULT_CITY = "默认城市";
    private static final int DAILY_QUERY_OVER_LIMIT=1003;
    private static final int ACCESS_TOO_FREQUENT=10021;

    private static Map<String, BigDecimal> citySalaryMap ;

    @Resource
    private DataCityLowestSalaryMapper dataCityLowestSalaryMapper;

    @PostConstruct
    public void initCityMap(){
        List<DataCityLowestSalary> cityCodes = dataCityLowestSalaryMapper.selectAll();
        citySalaryMap = cityCodes.stream().collect(Collectors.toMap(DataCityLowestSalary::getCity, DataCityLowestSalary::getSalary));
        // 存放默认城市的最低薪资
        Optional<BigDecimal> lowestSalary = cityCodes.stream().map(DataCityLowestSalary::getSalary).sorted().limit(1L).findFirst();
        citySalaryMap.put(DEFAULT_CITY, lowestSalary.orElse(BigDecimal.ZERO));
    }

    /**
     * 根据gps获取城市所在城市
     * @param param
     * @return
     */
    @Override
    public JsonResultVo transGps(JSONObject param){
        Map<String,Object> resultMap=new HashMap<>();
        String longitude = param.getString("longitude");
        String latitude = param.getString("latitude");
        try {
            String url = "http://restapi.amap.com/v3/geocode/regeo?key=" + GAODE_KEY + "&location=" + longitude + "," + latitude+"&batch=false";
            logger.info("request gaode api to get address start");
            long start = System.currentTimeMillis();
            HttpResult httpResult = HttpUtils.get(url, null);
            if(Objects.isNull(httpResult)){
                logger.error("query gaode api error.");
                return JsonResultVo.error();
            }
            String s = httpResult.getMessage();
            logger.info("request gaode api to get address end,cost:{},result:{}",  System.currentTimeMillis() - start, s);
            JSONObject resultJson = JSONObject.parseObject(s);
            JSONObject resultMessage = resultJson.getJSONObject("regeocode");
            String address = dealSpecialValue(resultMessage.getString("formatted_address"));
            JSONObject locationMessage = resultMessage.getJSONObject("addressComponent");
            String province = dealSpecialValue(locationMessage.getString("province"));
            String city = dealSpecialValue(locationMessage.getString("city"));

            resultMap.put("province", province);
            if (province.contains("市")) {
                resultMap.put("city", province);
            } else {
                resultMap.put("city", city);
            }
            resultMap.put("address", address);
            int infocode = resultJson.getIntValue("infocode");
            if(DAILY_QUERY_OVER_LIMIT == infocode
                    || ACCESS_TOO_FREQUENT == infocode){
                JsonResultVo jsonResultVo=JsonResultVo.error(infocode,"请求高德接口超限！");
                logger.error("request gaode api to get address error,error code ",infocode);
                return jsonResultVo;
            }
            JsonResultVo jsonResultVo=JsonResultVo.success();
            jsonResultVo.setData(resultMap);
            return jsonResultVo;
        } catch (Exception e) {
            logger.error("request gaode api to get address error",  e);
        }
        return JsonResultVo.error();
    }


    @Override
    public JsonResultVo queryCityLowestSalary(JSONObject param) {
        JsonResultVo jsonResultVo=JsonResultVo.success();
        Map<String, Object> resultMap=new HashMap<>(2);
        // gps对应城市
        String gpsCity = param.getString("gpsCity");
        if (StringUtils.isBlank(gpsCity)) {
            gpsCity = DEFAULT_CITY;
        }

        String finalGpsCity = gpsCity;
        String cityKey = citySalaryMap.keySet().stream()
                .filter(city -> city.equals(finalGpsCity) || finalGpsCity.startsWith(city))
                .findFirst().orElse(DEFAULT_CITY);

        resultMap.put("gpsCitySalary", citySalaryMap.get(cityKey));

        //进件时填写城市
        String sbmCity = param.getString("sbmCity");
        if (StringUtils.isBlank(sbmCity)) {
            sbmCity = DEFAULT_CITY;
        }
        String finalSbmCity = sbmCity;
        String sbmCityKey = citySalaryMap.keySet().stream()
                .filter(city -> city.equals(finalSbmCity) || finalSbmCity.startsWith(city))
                .findFirst().orElse(DEFAULT_CITY);

        resultMap.put("sbmCitySalary", citySalaryMap.get(sbmCityKey));

        jsonResultVo.setData(resultMap);
        return jsonResultVo;
    }



    /**
     * 处理一下高德返回[]的情况，转为空串
     * @param value
     * @return
     */
    private String dealSpecialValue(String value){
        if(StringUtils.equals(value,"[]")){
            return StringUtils.EMPTY;
        }
        return value;
    }
}
