package com.youxin.risk.datacenter.mapper;

import com.youxin.risk.datacenter.model.DcSubmitAmountMarriage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DcSubmitAmountMarriageMapper {


    int insert(DcSubmitAmountMarriage record);

    DcSubmitAmountMarriage selectByPrimaryKey(Integer id);

    DcSubmitAmountMarriage getByUserKey(@Param("userKey")String userKey);
    List<DcSubmitAmountMarriage> getLatestByUserKey(@Param("userKey")String userKey);
}