package com.youxin.risk.datacenter.pojo;

import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * @Desc 黑名单信息
 * @Auth linchongbin
 * @Date 2021/12/12 10:44
 */
public class BlackUserInfo implements RowMapper<BlackUserInfo>, Serializable {
    private static final long serialVersionUID = -7865619838377812107L;
    private String userKey;
    private String source;
    private String rule;
    private String ruleValue;
    private String indexType;
    private String value;
    private String detail;
    private String busiDate;

    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getRule() {
        return rule;
    }

    public void setRule(String rule) {
        this.rule = rule;
    }

    public String getRuleValue() {
        return ruleValue;
    }

    public void setRuleValue(String ruleValue) {
        this.ruleValue = ruleValue;
    }

    public String getIndexType() {
        return indexType;
    }

    public void setIndexType(String indexType) {
        this.indexType = indexType;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getBusiDate() {
        return busiDate;
    }

    public void setBusiDate(String busiDate) {
        this.busiDate = busiDate;
    }

    @Override
    public BlackUserInfo mapRow(ResultSet rs, int i) throws SQLException {
        BlackUserInfo entity = new BlackUserInfo();
        entity.setUserKey(rs.getString("user_key"));
        entity.setSource(rs.getString("source"));
        entity.setRule(rs.getString("rule"));
        entity.setRuleValue(rs.getString("rule_value"));
        entity.setIndexType(rs.getString("index_type"));
        entity.setValue(rs.getString("value"));
        entity.setDetail(rs.getString("detail"));
        entity.setBusiDate(rs.getString("dt"));
        return entity;
    }
}
