package com.youxin.risk.datacenter.service;

import com.youxin.risk.commons.model.datacenter.DcSubmitRegister;
import com.youxin.risk.ra.vo.RaSubmitRegisterVo;

import java.util.List;

public interface SubmitRegisterService extends BaseDcDbService<DcSubmitRegister>{

    DcSubmitRegister getByOperationId(Long operationId);

    DcSubmitRegister getByUserKey(String userKey, String apiLoanSource);

    List<DcSubmitRegister> getListByUserKey(String userKey, String apiLoanSource);

    RaSubmitRegisterVo getSubmitRegisterVoByUserKey(String userKey, String apiLoanSource);
}
