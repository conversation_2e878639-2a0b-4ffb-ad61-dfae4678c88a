package com.youxin.risk.datacenter.service;

import com.youxin.risk.commons.model.datacenter.DcOperationLog;
import com.youxin.risk.commons.model.datacenter.common.OperationType;
import com.youxin.risk.commons.model.datacenter.common.VerifyCommonData;

import java.util.List;

public interface OperationLogDbService extends BaseDcDbService<DcOperationLog>{
    DcOperationLog getOpLogVoFromInput(VerifyCommonData commonVo,
                                       OperationType opType);

    DcOperationLog getByUserKeyAndSourceSystem(String userKey, String sourceSystem);

    DcOperationLog selectBy(String userKey, String sourceSystem, String operationType);

    List<DcOperationLog> getListByUserKeyAndSourceSystemAndType(String userKey, String sourceSystem, String operationType);

    List<DcOperationLog> distinctLogByDeviceId(String userKey, String sourceSystem);
}
