package com.youxin.risk.datacenter.controller;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.datacenter.dto.LoanRequestDTO;
import com.youxin.risk.datacenter.pojo.JsonResultVo;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.HashMap;
import java.util.Map;

/**
 * 入参表达式Mock
 */
@RestController
@RequestMapping("/inputParamsExpression")
public class InputParamsExpressionController {

    @RequestMapping(value = "/inputParam", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultVo inputParam(@RequestBody JSONObject jsonObject) throws ParseException{
        JsonResultVo result = JsonResultVo.success();
        String userKey = jsonObject.getString("userKey");
        Map<String, Object> data = new HashMap<>();
        if (userKey.equals("zhangsan")) {
            data.put("value1",1);
            data.put("value2",2);
            data.put("value3",3);
            data.put("value4",4);
            data.put("value5",5);
            data.put("value6",6);
            data.put("value7",7);
            data.put("value8",8);
            data.put("value9",9);
        }else if (userKey.equals("lisi")){
            data.put("value1",11);
            data.put("value2",22);
            data.put("value3",33);
            data.put("value4",44);
            data.put("value5",55);
            data.put("value6",66);
            data.put("value7",77);
            data.put("value8",88);
            data.put("value9",99);
        }
        result.setData(data);
        return result;
    }
}
