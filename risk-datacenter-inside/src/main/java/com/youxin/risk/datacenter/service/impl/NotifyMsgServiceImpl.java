package com.youxin.risk.datacenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xxl.job.core.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Desc 通知服务
 * @Auth linchongbin
 * @Date 2022/11/24 16:39
 */
@Service
public class NotifyMsgServiceImpl {

    private static Logger logger = LoggerFactory.getLogger(NotifyMsgServiceImpl.class);

    private static RetryTemplate retryTemplate = new RetryTemplate();

    @Value("${variable.api.url}")
    private String variableApiUrl;

    @Value("${datastatistics.url}")
    private String datastatisticsUrl;

    @Resource
    private RestTemplate restTemplate;

    /**
     * 发送通知信息
     */
    public void sendAddContactMsg(String date, boolean slave) {
        try {
            retryTemplate.execute(context -> {
                logger.info("sendAddContactMsg, date: {}", date);
                restTemplate.postForEntity(String.format("%s%s", variableApiUrl, "/business/api/updateSyncTime"), buildWechatWarnBody("needContactUser", date, slave), String.class);
                return true;
            }, context -> {
                // 多次重试失败，插入error_record
                logger.info("sendAddContactMsg, date: {}, slave: {}", date, slave);
                return false;
            });
        } catch (Exception ex) {
            logger.error("sendAddContactMsg error, date: {}, slave: {}", date, slave, ex);
        }
    }

    /**
     * 发送通知到质量监控平台
     */
    public void sendMsgToQuality(String date, String dataType, Long count) {
        try {
            List<Map<String, Object>> params = Lists.newArrayList();
            Map<String, Object> param = new HashMap<>();
            param.put("timeType", "day");
            param.put("count", count);
            param.put("dataType", dataType);
            param.put("startTime", DateUtil.parseDate(date).getTime());
            param.put("endTime", DateUtil.addDays(DateUtil.parseDate(date), 1).getTime());
            params.add(param);
            HttpHeaders headers = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            headers.add("Accept", MediaType.APPLICATION_JSON.toString());
            headers.add("X-Token", "T03P000000392829A000001001OPN6ZP");
            HttpEntity<String> formEntity = new HttpEntity(JSONObject.toJSONString(params), headers);
            retryTemplate.execute(context -> {
                logger.info("sendMsgToQuality, date: {}, dataType: {}", date, dataType);
                restTemplate.postForEntity(String.format("%s%s", datastatisticsUrl, "/dataStatistics/add"), formEntity, String.class);
                return true;
            }, context -> {
                // 多次重试失败，插入error_record
                logger.info("sendMsgToQuality, date: {}, dateType: {}", date, dataType);
                return false;
            });
        } catch (Exception ex) {
            logger.error("sendMsgToQuality error, date: {}, dateType: {}", date, dataType, ex);
        }
    }

    public JSONObject buildWechatWarnBody(String dataType, String date, boolean slave) {
        JSONObject requestBody = new JSONObject();
        requestBody.put("dt", date);
        requestBody.put("cluster", slave ? "slave" : "master");
        requestBody.put("dataType", dataType);
        return requestBody;
    }
}
