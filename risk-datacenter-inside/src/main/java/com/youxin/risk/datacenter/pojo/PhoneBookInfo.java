package com.youxin.risk.datacenter.pojo;

import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * hive通讯录
 * <AUTHOR>
 * @since 2021/12/27 14:58
 */
public class PhoneBookInfo implements RowMapper<PhoneBookInfo>, Serializable {
    private static final long serialVersionUID = -7865619838377812107L;
    private String userKey;
    private String loanKey;
    private String jobId;
    private String name;
    private String mobile;
    private String remark;
    private String dt;

    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey;
    }

    public String getLoanKey() {
        return loanKey;
    }

    public void setLoanKey(String loanKey) {
        this.loanKey = loanKey;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getDt() {
        return dt;
    }

    public void setDt(String dt) {
        this.dt = dt;
    }

    @Override
    public PhoneBookInfo mapRow(ResultSet rs, int i) throws SQLException {
        PhoneBookInfo entity = new PhoneBookInfo();
        entity.setUserKey(rs.getString("user_key"));
        entity.setLoanKey(rs.getString("loan_key"));
        entity.setJobId(rs.getString("job_id"));
        entity.setName(rs.getString("name"));
        entity.setMobile(rs.getString("mobile"));
        entity.setRemark(rs.getString("remark"));
        entity.setDt(rs.getString("dt"));
        return entity;
    }
}