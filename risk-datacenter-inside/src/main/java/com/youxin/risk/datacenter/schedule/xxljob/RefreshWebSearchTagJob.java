package com.youxin.risk.datacenter.schedule.xxljob;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.youxin.risk.commons.xxl.job.XxlJobBase;
import com.youxin.risk.datacenter.mapper.AppTagMapper;
import com.youxin.risk.datacenter.model.AppTag;
import com.youxin.risk.datacenter.service.impl.AppTagServiceImpl;
import com.youxin.risk.ra.utils.StringUtils;
import com.youxin.risk.verify.redis.RedisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Random;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 *
 * 刷新dc_app_tag中的数据
 * <AUTHOR>
 */
@Component
public class RefreshWebSearchTagJob implements XxlJobBase {

    private static final Logger LOGGER = LoggerFactory.getLogger(RefreshWebSearchTagJob.class);

    @Resource
    private AppTagMapper appTagMapper;

    @Resource
    private AppTagServiceImpl appTagService;

    @Resource(name = "cacheRedisService")
    private RedisService cacheRedisService;

    private final static String WEB_APP_TAG_KEY = "risk_datacenter_app_WebSearch_tag__key";

    ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(1,2,0,
            TimeUnit.SECONDS,new ArrayBlockingQueue<>(1000), new ThreadPoolExecutor.DiscardPolicy());

    @Override
    @XxlJob(value = "refreshWebSearchTagJob")
    public ReturnT<String> execJobHandler(String param) {
        LOGGER.info("refreshAppTagJob start");
        String isNull=StringUtils.isBlank(param)?"1":"2";
        List<AppTag> allWebTag = appTagMapper.getAllWebTag(isNull);
        allWebTag.forEach(appTag->{
            int finalPauseSeconds = 10;
            int randomPauseSeconds = new Random().nextInt(finalPauseSeconds);
            try {
                Thread.sleep(randomPauseSeconds);
                threadPoolExecutor.execute(() -> {
                    LOGGER.info("refreshAppTagJob,id:{},appName:{}",appTag.getId(),appTag.getAppName());
                    appTagService.submitCrawlerJobToSpider(appTag,"/appTag/updateAppTag");
                });
            } catch (Exception e) {
                LOGGER.error("exec refreshAppTagJob eror",e);
            }
        });
        LOGGER.info("refreshAppTagJob end!");
        return ReturnT.SUCCESS;
    }
}
