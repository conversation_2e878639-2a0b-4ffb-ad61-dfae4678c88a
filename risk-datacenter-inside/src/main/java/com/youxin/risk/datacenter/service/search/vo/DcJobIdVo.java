package com.youxin.risk.datacenter.service.search.vo;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-04-24
 */
public class DcJobIdVo {

    // 是否为陈旧的数据（存在超过一天）
    private Boolean staleData;

    private String jobId;

    private Date createTime;

    public DcJobIdVo() {
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Boolean getStaleData() {
        return staleData;
    }

    public void setStaleData(Boolean staleData) {
        this.staleData = staleData;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("staleData", staleData)
                .append("jobId", jobId)
                .append("createTime", createTime)
                .toString();
    }
}
