package com.youxin.risk.datacenter.model;

import com.youxin.risk.commons.model.BaseModel;

/**
 * dc 提额需要用到婚姻状况
 * 
 * <AUTHOR>
 * 
 * @date 2020-09-02
 */
public class DcSubmitAmountMarriage extends BaseModel {

    /**
     * operation_log表id
     */
    private Long operationLogId;

    /**
     * 用户key
     */
    private String userKey;

    /**
     * 婚姻状况
     */
    private String maritalStatus;



    public Long getOperationLogId() {
        return operationLogId;
    }

    public void setOperationLogId(Long operationLogId) {
        this.operationLogId = operationLogId;
    }

    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey == null ? null : userKey.trim();
    }

    public String getMaritalStatus() {
        return maritalStatus;
    }

    public void setMaritalStatus(String maritalStatus) {
        this.maritalStatus = maritalStatus;
    }
}