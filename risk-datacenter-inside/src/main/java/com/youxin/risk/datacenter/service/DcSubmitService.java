package com.youxin.risk.datacenter.service;


import com.youxin.risk.commons.model.datacenter.DcSubmitXw;
import com.youxin.risk.commons.model.datacenter.vo.SubmitAddressVo;
import com.youxin.risk.commons.model.datacenter.vo.SubmitBankCardVo;
import com.youxin.risk.commons.model.datacenter.vo.SubmitContactVo;
import com.youxin.risk.commons.model.datacenter.vo.SubmitCreditcardVo;
import com.youxin.risk.commons.model.datacenter.vo.SubmitIdcardVo;
import com.youxin.risk.commons.model.datacenter.vo.SubmitJobVo;
import com.youxin.risk.commons.model.datacenter.vo.SubmitLoansInfoVo;
import com.youxin.risk.commons.model.datacenter.vo.SubmitLocateListVo;
import com.youxin.risk.commons.model.datacenter.vo.SubmitPhoneBookVo;
import com.youxin.risk.commons.model.datacenter.vo.SubmitPlistVo;
import com.youxin.risk.commons.model.datacenter.vo.SubmitRegisterVo;
import com.youxin.risk.commons.model.datacenter.vo.SubmitSocialContactVo;
import com.youxin.risk.commons.model.datacenter.vo.VerifySubmitVo;
import com.youxin.risk.commons.remote.model.datacenter.DcRequestService;

import java.util.List;

public interface DcSubmitService {

    List<Long> submitPlist(SubmitPlistVo plistVo) throws InstantiationException,
            IllegalAccessException, SecurityException;

    long submitAddress(SubmitAddressVo submitAddressVo) throws Exception;

    long submitVerifySubmit(VerifySubmitVo verifySubmitVo) throws Exception;

    void hanlderBatch(List<DcRequestService> services) throws Exception;

    long submitBankCard(SubmitBankCardVo submitBankCardVo) throws Exception;

    List<Long> submitContact(SubmitContactVo contactVo) throws Exception;

    Long submitCreditcard(SubmitCreditcardVo submitCreditcardVo) throws Exception;

    Long submitIdcard(SubmitIdcardVo submitIdcardVo) throws Exception;

    Long submitJob(SubmitJobVo submitJobVo) throws Exception;

    Long submitLoansInfo(SubmitLoansInfoVo submitLoansInfoVo) throws Exception;

    void submitLocateList(SubmitLocateListVo submitLocateListVo) throws Exception;

    List<Long> submitPhoneBook(SubmitPhoneBookVo submitPhoneBookVo) throws Exception;

    Long submitRegister(SubmitRegisterVo submitRegisterVo) throws Exception;

    Long submitSocialContact(SubmitSocialContactVo submitSocialContactVo) throws Exception;

    Long submitXwBank(DcSubmitXw dcSubmitXw) throws Exception;



    void insertAddressToOldDataBase(VerifySubmitVo verifySubmitVo);

    void insertAddressToDcDataBase(VerifySubmitVo verifySubmitVo);
}
