package com.youxin.risk.datacenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.youxin.risk.commons.dao.datacenter.DcRequestTaskMapper;
import com.youxin.risk.commons.model.datacenter.DcRequestTask;
import com.youxin.risk.commons.model.datacenter.common.OperationType;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.datacenter.service.AbstractSubmitAmountService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class SubmitAmountTianxiaxinyongServiceImpl extends AbstractSubmitAmountService {


    private static final Logger logger = LoggerFactory.getLogger(SubmitAmountTianxiaxinyongServiceImpl.class);

    @Autowired
    DcRequestTaskMapper dcRequestTaskMapper;

    @Value("${data.url.write.base}")
    private String babelWriteUrl;

    @Value("${data.url.record.job.tianxiaxinyong}")
    private String tianxiaxinyongUrl;


    /**
     * 获取需要校验的数据参数
     */
    @Override
    protected List<String> getCheckNullKeyList() {
        return new ArrayList<>();
    }

    @Override
    protected List<String> checkNullCustomizeParameters(JSONObject param) {

        List<String> result = new ArrayList<>();
        JSONObject job = param.getJSONObject("job");
        //job 相关信息
        String jobId = job.getString("jobID");
        String systemID = job.getString("systemID");
        // babel需要的数据类型
        String type = param.getString("type");
        if (StringUtils.isEmpty(jobId)) {
            result.add("job-jobId");
        }
        if (StringUtils.isEmpty(systemID)) {
            result.add("job-systemID");
        }
        if (StringUtils.isEmpty(type)) {
            result.add("type");
        }

        return result;
    }

    @Override
    protected void setSubmitType(JSONObject param) {

        param.put("operationType", OperationType.AMOUNT_TIANXIAXINYONG.name());
    }

    /**
     * 提额操作的锁类型，
     *
     * @return
     */
    @Override
    protected String getLockType() {
        return OperationType.AMOUNT_TIANXIAXINYONG.name();
    }

    @Override
    protected void insertAmountParam(JSONObject param) {

        //插入task 表
        JSONObject job = param.getJSONObject("job");
        DcRequestTask dcRequestTask = new DcRequestTask();
        dcRequestTask.setOperationLogId(param.getLong("operationLogId"));
        dcRequestTask.setJobId(job.getString("jobID"));
        dcRequestTask.setSourceSystem(param.getString("sourceSystem"));
        dcRequestTask.setUserKey(param.getString("userKey"));
        dcRequestTask.setCreateTime(new Date());
        dcRequestTask.setUpdateTime(new Date());
        dcRequestTaskMapper.insert(dcRequestTask);


        LoggerProxy.info("before submitTianxiaxinyongToDP", logger, "request={}", param.toJSONString());
        //透传天下信用报告信息到 数据平台
        fixJsonObject(param);
        String responseStr = SyncHTTPRemoteAPI.postJson(babelWriteUrl + tianxiaxinyongUrl, param.toJSONString(), 60000);
        LoggerProxy.info("after submitTianxiaxinyongToDP", logger, " result={}", responseStr);
    }

    // fix submitAmountError
    private void fixJsonObject(JSONObject original) {
        try{
            String originalJson = original.toJSONString();
            Object summaryInfo = JSONPath.read(originalJson, "$.data.summaryInfo.data.list");
            if (summaryInfo != null) {
                JSONPath.set(original, "$.data.summaryInfo.data", summaryInfo);
            }
        }catch (Exception e) {
            logger.error("handle submitTianxiaxinyongToDP JsonObject error", e);
        }
        //目前线上有新旧两版格式的股权模块（shareholderInfo），这里改为用String传递，防止babel解析异常
        try{
            String originalJson = original.toJSONString();
            Object shareholderInfo = JSONPath.read(originalJson, "$.data.shareholderInfo");
            if(shareholderInfo != null){
                JSONPath.set(original, "$.data.shareholderInfo", JSON.toJSONString(shareholderInfo));
            }
        }catch (Exception e){
            logger.error("handle submitTianxiaxinyongToDP JsonObject error", e);
        }
    }

}
