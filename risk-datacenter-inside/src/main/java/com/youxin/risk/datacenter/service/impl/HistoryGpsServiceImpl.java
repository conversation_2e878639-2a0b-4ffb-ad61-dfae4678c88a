package com.youxin.risk.datacenter.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.dao.datacenter.DcVerifySubmitMapper;
import com.youxin.risk.commons.dao.verify.VerifySubmitMapper;
import com.youxin.risk.commons.model.datacenter.DcVerifySubmit;
import com.youxin.risk.commons.model.datacenter.vo.VerifySubmitVo;
import com.youxin.risk.commons.model.verify.VerifySubmit;
import com.youxin.risk.commons.tools.redis.RetryableJedis;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.datacenter.service.DcSubmitService;
import com.youxin.risk.metrics.helpers.ThreadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Service
public class HistoryGpsServiceImpl {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private DcSubmitService submitService;

    @Resource
    private VerifySubmitMapper verifySubmitMapper;

    @Resource
    private DcVerifySubmitMapper dcVerifySubmitMapper;


    @Resource
    private RetryableJedis retryableJedis;

    static String oldTaskIdKey = "oldTaskId";
    static String dcTaskIdKey = "dcTaskId";
    static String taskFlagKey = "taskFlag";

    //老库任务的线程池
    private final ExecutorService oldTaskThreadPool = new ThreadPoolExecutor(10, 20, 30, TimeUnit.SECONDS,
            new LinkedBlockingQueue<Runnable>(10000), ThreadUtils.defaultThreadFactory("oldTask" + "-pool"), new ThreadPoolExecutor.DiscardPolicy());


    //dc库的线程池
    private final ExecutorService dcTaskThreadPool = new ThreadPoolExecutor(10, 20, 30, TimeUnit.SECONDS,
            new LinkedBlockingQueue<Runnable>(10000), ThreadUtils.defaultThreadFactory("dcTask" + "-pool"), new ThreadPoolExecutor.DiscardPolicy());


    public void executeOldTask(JSONObject param) {
        //执行 老库任务
        oldTaskThreadPool.execute(() -> {
            int loopTime = param.getIntValue("loopTime");
            long taskId = Long.valueOf(retryableJedis.get(oldTaskIdKey));
            String taskFlag;
            int size;
            VerifySubmit verifySubmit = null;
            Date taskEndDate = null;
            try {
                taskEndDate = new SimpleDateFormat("yyyy-MM-dd").parse("2020-06-18");
            } catch (ParseException e) {
                e.printStackTrace();
            }

            rootFlag:
            for (int i = 0; i < loopTime; i++) {
                taskFlag = retryableJedis.get(taskFlagKey);
                if (!"1".equals(taskFlag)) {
                    break;
                }
                //下一次循环开始从新的taskId开始查询
                param.put("id", taskId);
                try {
                    //睡1秒，防止循环太快超过百度访问量限制
                    Thread.sleep(1000);
//                    List<VerifySubmit> verifySubmitList = verifySubmitMapper.selectVerifySubmitAfterId(param);

                    List<VerifySubmit> verifySubmitList = verifySubmitMapper.selectVerifySubmitBeforeId(param);
                    size = verifySubmitList.size();

                    if(size==0){
                        break;
                    }

                    for (int j = 0; j < size; j++) {
                        try {
                            verifySubmit = verifySubmitList.get(j);
                            //只执行到 2020-06-18
                            if (verifySubmit.getCreateTime().after(taskEndDate)) {
                                break rootFlag;
                            }

                            VerifySubmitVo verifySubmitVo = new VerifySubmitVo();
                            BeanUtils.copyProperties(verifySubmit, verifySubmitVo);
                            oldTaskThreadPool.execute(() -> submitService.insertAddressToOldDataBase(verifySubmitVo));
                            //如果成功更新任务的起始Id
                            taskId = verifySubmit.getId();
                        } catch (Exception e) {
                            LoggerProxy.error("oldTaskError", logger, "params{},verifySubmit={}", param, JSON.toJSONString(verifySubmit));
                        }
                    }
                } catch (Exception e) {
                } finally {
                    //任务的起始Id更新到redis中
                    retryableJedis.set(oldTaskIdKey, String.valueOf(taskId));
                }
            }
        });
    }

    public void executeDcTask(JSONObject param) {
        //执行 老库任务
        dcTaskThreadPool.execute(() -> {
            int loopTime = param.getIntValue("loopTime");
            long taskId = Long.valueOf(retryableJedis.get(dcTaskIdKey));
            String taskFlag;
            int size;
            DcVerifySubmit dcVerifySubmit = null;
            Date taskEndDate = null;
            try {
                taskEndDate = new SimpleDateFormat("yyyy-MM-dd").parse("2020-06-18");
            } catch (ParseException e) {
                e.printStackTrace();
            }

            rootFlag:
            for (int i = 0; i < loopTime; i++) {
                taskFlag = retryableJedis.get(taskFlagKey);
                if (!"1".equals(taskFlag)) {
                    break;
                }
                //下一次循环开始从新的taskId开始查询
                param.put("id", taskId);
                try {
                    //睡1秒，防止循环太快超过百度访问量限制
                    Thread.sleep(1000);
//                    List<DcVerifySubmit> verifySubmitList = dcVerifySubmitMapper.selectVerifySubmitAfterId(param);
                    List<DcVerifySubmit> verifySubmitList = dcVerifySubmitMapper.selectVerifySubmitBeforeId(param);
                    size = verifySubmitList.size();

                    if(size==0){
                        break;
                    }

                    for (int j = 0; j < size; j++) {
                        try {
                            dcVerifySubmit = verifySubmitList.get(j);
                            //只执行到 2020-06-18
                            if (dcVerifySubmit.getCreateTime().after(taskEndDate)) {
                                break rootFlag;
                            }

                            VerifySubmitVo verifySubmitVo = new VerifySubmitVo();
                            BeanUtils.copyProperties(dcVerifySubmit, verifySubmitVo);
                            oldTaskThreadPool.execute(() -> submitService.insertAddressToDcDataBase(verifySubmitVo));
                            //如果成功更新任务的起始Id
                            taskId = dcVerifySubmit.getId();
                        } catch (Exception e) {
                            LoggerProxy.error("dcTaskError", logger, "params{},dcVerifySubmit={}", param, JSON.toJSONString(dcVerifySubmit));
                        }
                    }
                } catch (Exception e) {
                } finally {
                    //任务的起始Id更新到redis中
                    retryableJedis.set(dcTaskIdKey, String.valueOf(taskId));
                }
            }
        });
    }


    public String getTaskId() {
        return "oldTaskId : " + Long.valueOf(retryableJedis.get(oldTaskIdKey)) + " dcTaskId : " + Long.valueOf(retryableJedis.get(dcTaskIdKey));
    }

    public String setTaskId(long oldTaskId, long dcTaskId) {
        retryableJedis.set(oldTaskIdKey, String.valueOf(oldTaskId));
        retryableJedis.set(dcTaskIdKey, String.valueOf(dcTaskId));
        return "setTaskId sucess";
    }


    public String getCurrentTaskFlag() {
        return retryableJedis.get(taskFlagKey);
    }

    public String setCurrentTaskFlag(String taskFlag) {
        retryableJedis.set(taskFlagKey, taskFlag);
        return "setTaskFlag sucess";
    }
}
