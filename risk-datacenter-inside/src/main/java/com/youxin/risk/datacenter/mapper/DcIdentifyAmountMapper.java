package com.youxin.risk.datacenter.mapper;

import com.youxin.risk.datacenter.model.DcIdentifyAmount;
import org.apache.ibatis.annotations.Param;

public interface DcIdentifyAmountMapper {

    int insert(DcIdentifyAmount record);

    DcIdentifyAmount selectByPrimaryKey(Integer id);

    /**
     * 查询十五天内是否有该userKey 天下信用的提额记录
     * @param userKey userKey
     * @param certificationItem 天下信用
     * @param dateTime 时间
     * @return id
     */
    Integer queryTxxyRecordInFifteenDays(@Param("userKey") String userKey,@Param("certificationItem")String certificationItem, @Param("dateTime")String dateTime);


    /**
     * 查询十五天内是否有该userKey的记录
     * @param userKey userKey
     * @param certificationItem 非天下信用
     * @param dateTime 时间
     * @return id
     */
    Integer queryOtherRecordInFifteenDays(@Param("userKey") String userKey,@Param("certificationItem")String certificationItem, @Param("dateTime")String dateTime);
}