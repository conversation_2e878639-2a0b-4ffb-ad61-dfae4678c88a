package com.youxin.risk.datacenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import com.google.common.collect.Lists;
import com.youxin.risk.commons.vo.EventVo;
import com.youxin.risk.datacenter.dao.MongoEventDao1;
import com.youxin.risk.datacenter.service.MongoEventService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.Objects;

@Slf4j
@Service
public class MongoEventServiceImpl implements MongoEventService {

    @Autowired
    private MongoEventDao1 mongoEventDao1;

    @Override
    public String queryDataFromEventVo(String loanKey, String path) {
        EventVo eventVo = getEventVo(loanKey);
        return JSON.toJSONString(JSONPath.eval(eventVo, path));
    }

    /**
     * 单独提出方法
     * @param loanKey
     * @return
     */
    public EventVo getEventVo(String loanKey) {
        return (EventVo) Lists.newArrayList(mongoEventDao1)
                    .parallelStream()
                    .map(mongoEventDao -> mongoEventDao.queryEventVo(loanKey))
                    .filter(Objects::nonNull)
                    .max(Comparator.comparing(EventVo::getUpdateTime)).orElse(new EventVo());
    }
}
