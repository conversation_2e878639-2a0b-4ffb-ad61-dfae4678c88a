package com.youxin.risk.datacenter.model;

import com.youxin.risk.commons.model.BaseModel;

/**
 * dc 提额需要用到车辆信息
 * 
 * <AUTHOR>
 * 
 * @date 2020-09-02
 */
public class DcSubmitAmountVehicle extends BaseModel {

    /**
     * operation_log表id
     */
    private Long operationLogId;

    /**
     * 用户key
     */
    private String userKey;
    /**
     * 车辆品牌
     */
    private String brand;
    /**
     * 车辆归属
     */
    private String owner;
    /**
     *
     */
    private String number;
    /**
     * 购车金额
     */
    private String amount;
    /**
     * 每月车贷
     */
    private String loanMonth;

    public Long getOperationLogId() {
        return operationLogId;
    }

    public void setOperationLogId(Long operationLogId) {
        this.operationLogId = operationLogId;
    }

    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey == null ? null : userKey.trim();
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getLoanMonth() {
        return loanMonth;
    }

    public void setLoanMonth(String loanMonth) {
        this.loanMonth = loanMonth;
    }
}