package com.youxin.risk.datacenter.service.impl;

import com.youxin.risk.commons.dao.datacenter.DcSubmitJobMapper;
import com.youxin.risk.commons.dao.datacenter.RiskApiSubmitOtherMapper;
import com.youxin.risk.commons.model.datacenter.DcSubmitJob;
import com.youxin.risk.commons.model.datacenter.api.RiskApiSubmitOther;
import com.youxin.risk.commons.utils.ObjectTransferUtils;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.datacenter.service.SubmitJobService;
import com.youxin.risk.ra.mapper.SubmitJobMapper;
import com.youxin.risk.ra.model.SubmitJob;
import com.youxin.risk.ra.vo.RaSubmitJobVo;
import org.apache.commons.lang3.NotImplementedException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class SubmitJobServiceImpl extends AbstractBatchQueryService<DcSubmitJob> implements SubmitJobService {

    private static final Logger logger = LoggerFactory.getLogger(SubmitAddressServiceImpl.class);

    @Autowired
    private DcSubmitJobMapper dcSubmitJobMapper;

    @Autowired
    private RiskApiSubmitOtherMapper riskApiSubmitOtherMapper;

    @Autowired
    private SubmitJobMapper raSubmitJobMapper;

    @Override
    public DcSubmitJob getItem(DcSubmitJob item) {
        throw new NotImplementedException("SubmitJobServiceImpl.getItem");
    }

    @Override
    public DcSubmitJob getItemByPrimaryKey(long id) {
        return dcSubmitJobMapper.selectByPrimaryKey(id);
    }

    @Override
    public Long insertItem(DcSubmitJob item) {
        dcSubmitJobMapper.insert(item);
        return item.getId();
    }

    @Override
    public DcSubmitJob getByOperationId(Long operationId) {
        return dcSubmitJobMapper.getByOperationId(operationId);
    }

    @Override
    public DcSubmitJob getByUserKey(String userKey, String apiLoanSource) {
        if (StringUtils.isNotBlank(apiLoanSource)) {
            RaSubmitJobVo vo = getVoFromApi(userKey, apiLoanSource);
            if (Objects.nonNull(vo)) {
                try {
                    return ObjectTransferUtils.transferObject(vo, DcSubmitJob.class);
                } catch (Exception e) {
                    logger.error("job api transfer error", e);
                }
            }
            return null;
        }
        return this.dcSubmitJobMapper.getByUserKey(userKey);
    }

    @Override
    public RaSubmitJobVo getRaSubmitJobVoByUserKeyAndSourceSystem(String sourceSystem, String userKey, String apiLoanSource) {
        if (StringUtils.isNotBlank(apiLoanSource)) {
            return getVoFromApi(userKey, apiLoanSource);
        }

        RaSubmitJobVo raSubmitJobVo = null;
        DcSubmitJob dcSubmitJob = this.dcSubmitJobMapper.getByUserKey(userKey);
        if (dcSubmitJob != null) {
            try {
                raSubmitJobVo = ObjectTransferUtils.transferObject(dcSubmitJob, RaSubmitJobVo.class);
                raSubmitJobVo.setSourceSystem(sourceSystem);
//                raSubmitJobVo.setApplyId(Integer.valueOf(String.valueOf(dcSubmitJob.getOperationLogId())));
            } catch (Exception e) {
                logger.error("transfer dc model to vo error,userKey:{}", userKey, e);
            }
        }
        if (raSubmitJobVo == null){
            logger.info("get job from dc is empty,get from ra again userKey:{}", userKey);
            SubmitJob job = raSubmitJobMapper.findLastSubmitByUserKey(userKey, sourceSystem);
            try {
                raSubmitJobVo = ObjectTransferUtils.transferObject(job, RaSubmitJobVo.class);
            } catch (Exception e) {
                logger.error("transfer ra model to vo error,userKey:{}", userKey);
            }
        }
        return raSubmitJobVo;
    }

    private RaSubmitJobVo getVoFromApi(String userKey, String apiLoanSource) {
        RaSubmitJobVo raSubmitJobVo = null;
        RiskApiSubmitOther apiInfo = riskApiSubmitOtherMapper.getByUserKey(userKey, apiLoanSource);
        if (Objects.nonNull(apiInfo)) {
            raSubmitJobVo = new RaSubmitJobVo();
            // API 模式无岗位信息
            raSubmitJobVo.setCompanyPosition(null);
            raSubmitJobVo.setCreateTime(apiInfo.getCreateTime());
            raSubmitJobVo.setSourceSystem(apiInfo.getSourceSystem());
            raSubmitJobVo.setIndustry(apiInfo.getIndustry());
            raSubmitJobVo.setSalary(apiInfo.getMonthlyIncome());
            raSubmitJobVo.setUserKey(apiInfo.getUserKey());
            return raSubmitJobVo;
        }
        logger.info("job from api empty userKey: {}, api: {}", userKey, apiLoanSource);
        return null;
    }
}
