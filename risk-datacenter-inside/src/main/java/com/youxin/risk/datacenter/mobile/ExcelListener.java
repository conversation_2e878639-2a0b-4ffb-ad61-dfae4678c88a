package com.youxin.risk.datacenter.mobile;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.youxin.risk.datacenter.mapper.DcMobileAreaMapper;
import com.youxin.risk.datacenter.model.DcMobileAreaModel;
import com.youxin.risk.ra.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
public class ExcelListener extends AnalysisEventListener<DcMobileAreaModel> {
    private DcMobileAreaMapper dcMobileAreaMapper;
    private final List<DcMobileAreaModel> rows = new ArrayList<>();
    private int count = 0;
    private ExcelResultDto resultDto;

    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 500;

    public ExcelListener(DcMobileAreaMapper dcMobileAreaMapper, ExcelResultDto resultDto) {
        this.dcMobileAreaMapper = dcMobileAreaMapper;
        this.resultDto = resultDto;
    }

    @Override
    public void invoke(DcMobileAreaModel object, AnalysisContext context) {
        if (StringUtils.isNotBlank(object.getPrefix())) {
            object.setPrefix(object.getPrefix().trim());
        }
        if (StringUtils.isNotBlank(object.getParagraph())) {
            object.setParagraph(object.getParagraph().trim());
        }
        if (StringUtils.isNotBlank(object.getProvince())) {
            object.setProvince(object.getProvince().trim());
        }
        if (StringUtils.isNotBlank(object.getCityCounty())) {
            object.setCityCounty(object.getCityCounty().trim());
        }
        if (StringUtils.isNotBlank(object.getProvinceSimple())) {
            object.setProvinceSimple(object.getProvinceSimple().trim());
        }
        if (StringUtils.isNotBlank(object.getCityCountySimple())) {
            object.setCityCountySimple(object.getCityCountySimple().trim());
        }
        if (StringUtils.isNotBlank(object.getZipCode())) {
            object.setZipCode(object.getZipCode().trim());
        }
        if (StringUtils.isNotBlank(object.getZoneDescription())) {
            object.setZoneDescription(object.getZoneDescription().trim());
        }
        if (StringUtils.isNotBlank(object.getProvinceId())) {
            object.setProvinceId(object.getProvinceId().trim());
        }
        if (StringUtils.isNotBlank(object.getCityCountyId())) {
            object.setCityCountyId(object.getCityCountyId().trim());
        }
        if (StringUtils.isNotBlank(object.getLng())) {
            object.setLng(object.getLng().trim());
        }
        if (StringUtils.isNotBlank(object.getLat())) {
            object.setLat(object.getLat().trim());
        }
        if (StringUtils.isNotBlank(object.getAscIsp())) {
            object.setAscIsp(object.getAscIsp().trim());
        }

        if (StringUtils.isNotBlank(object.getIsp())) {
            object.setIsp(object.getIsp().trim());
        }


        // 跳过空
        if (StringUtils.isBlank(object.getIsp())) {
            return;
        }

        object.setSource("INTERNAL");
        if (rows.size() > BATCH_COUNT) {
            this.saveData(rows);
            count += rows.size();
            log.info(String.format("has save data size %s", count));

            rows.clear();
        }
        rows.add(object);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        this.saveData(rows);
        count += rows.size();
        resultDto.setTotal(count);
        log.info(String.format("final save data size %s", rows.size()));
        log.info(String.format("final has save data size %s", count));
    }


    public void saveData(List<DcMobileAreaModel> rows) {
        dcMobileAreaMapper.batchInsertOrUpdate(rows);
    }

}
