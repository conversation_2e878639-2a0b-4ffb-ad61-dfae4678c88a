package com.youxin.risk.datacenter.cache;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.model.WhiteListDto;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.datacenter.service.DcWhiteListService;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.youxin.risk.commons.apollo.ApolloNamespaceEnum.DC_INSIDE_SPACE;

/**
 * @description: 黑白名单的缓存
 * @author: juxiang
 * @create: 2022-04-07 15:58
 **/
@Component
public class WhiteListCache {
    private final static Logger logger = LoggerFactory.getLogger(WhiteListCache.class);
    private Date lastRefreshTime;
    @Autowired
    DcWhiteListService dcWhiteListService;
    private static Map<String, Map<String,String>> mobileCache=new ConcurrentHashMap<>();
    private static Map<String, List<CustomServiceBlackList>> blackListCache = new ConcurrentHashMap<>();
    private static Map<String, JSONArray> whiteListCache = new ConcurrentHashMap<>();
    private static Map<String, String> eblackListCache = new ConcurrentHashMap<>();
    private static Map<String,Set<String>> mobileListCache=new ConcurrentHashMap<>();
    // 通过手机号判断是否命中黑名单
    private static Map<String, Set<String>> mobileBlackListCache = new ConcurrentHashMap<>();
    private static final String KEY="haoHuanVerifyWhiteListKey";
    @PostConstruct
    public void init(){
        lastRefreshTime=new Date();
        this.initAppointMobileCache();
        this.initAllMobileWhiteListCache();
        this.initAllMobileBlackListCache();
        LoggerProxy.info("WhiteListCacheInit",logger,"init success!");
    }

    private void initAllMobileWhiteListCache() {
        List<WhiteListDto> whiteList = dcWhiteListService.queryAllWhiteList();
        whiteList.forEach(whiteListDto -> {
            this.analysisData(whiteListDto);
        });
    }

    private void initAllMobileBlackListCache() {
        List<WhiteListDto> blackList = dcWhiteListService.queryAllBlackList();
        blackList.forEach(this::analysisBlackListData);
    }

    public void refreshCache(){
        // 刷新黑名单缓存
        this.refreshBlackListCache();
        // 刷新白名单缓存
        this.refreshWhiteListCache();
        // 刷新特定key的mobile的白名单
        this.refreshPointMobileWhiteListCache();
        // 刷新所有的mobile的白名单
        this.refreshAllMobileWhiteListCache();
        // 刷新所有的mobile的黑名单
        this.refreshAllMobileBlackListCache();
        this.lastRefreshTime=new Date();
        LoggerProxy.info("refreshCache",logger,"refresh cache finish!");

    }

    private void refreshAllMobileWhiteListCache() {
        try {
            List<WhiteListDto> whiteList = dcWhiteListService.queryAllWhiteList();
            for(WhiteListDto whiteListDto:whiteList){
                if(!this.needRefresh(whiteListDto.getCrateTime())){
                    LoggerProxy.info("refreshAllMobileWhiteListCache",logger,"refresh finish:{}",JSONObject.toJSONString(whiteListDto));
                    break;
                }
                this.analysisData(whiteListDto);
            }
        }catch (Exception e){
            LoggerProxy.error("refreshAllMobileWhiteListCache",logger,"refresh error:",e);
        }
    }

    private void refreshAllMobileBlackListCache() {
        try {
            List<WhiteListDto> blacklist = dcWhiteListService.queryAllBlackList();
            for(WhiteListDto blackListDto:blacklist){
                if(!this.needRefresh(blackListDto.getCrateTime())){
                    LoggerProxy.info("refreshAllMobileWhiteListCache",logger,"refresh finish:{}",JSONObject.toJSONString(blackListDto));
                    break;
                }
                this.analysisData(blackListDto);
            }
        }catch (Exception e){
            LoggerProxy.error("refreshAllMobileWhiteListCache",logger,"refresh error:",e);
        }
    }


    private void initAppointMobileCache() {
        try {
            String key=ApolloClientAdapter.getStringConfig(DC_INSIDE_SPACE,KEY,StringUtils.EMPTY);
            if(StringUtils.isEmpty(key)){
                return;
            }
            WhiteListDto whiteListDto = dcWhiteListService.queryWhiteByKey(key);
            if(Objects.isNull(whiteListDto)){
                return;
            }
            this.dealData(whiteListDto);
        }catch (Exception e){
            LoggerProxy.error("mobileCache",logger,"init cache error:",e);
        }
        LoggerProxy.info("mobileCache",logger,"init cache success,data:{}",JSONObject.toJSONString(mobileCache));
    }





    private void refreshPointMobileWhiteListCache() {
        try {
            String key= ApolloClientAdapter.getStringConfig(DC_INSIDE_SPACE,KEY,StringUtils.EMPTY);
            if(StringUtils.isEmpty(key)){
                return;
            }
            LoggerProxy.info("WhiteListCache",logger,"refresh cache ,data:{}",JSONObject.toJSONString(mobileCache));
            WhiteListDto whiteListDto = dcWhiteListService.queryWhiteByKey(key);
            if(Objects.isNull(whiteListDto)){
                return;
            }
            if(!needRefresh(whiteListDto.getCrateTime())){
                LoggerProxy.info("no need refreshCache",logger,"refresh cache finished,id:{},time:{}",
                        whiteListDto.getId(),whiteListDto.getCrateTime());
                return;
            }
            this.dealData(whiteListDto);
        }catch (Exception e){
            LoggerProxy.error("WhiteListCache",logger,"refresh cache error:",e);
        }
        LoggerProxy.info("WhiteListCache",logger,"refresh cache success!");
    }

    /**
     * 客服黑名单
     * @param eventCode
     * @param key
     * @return
     */
    public List<CustomServiceBlackList> getHitCustomServiceBlackList(String eventCode, String key) {
        return Optional.ofNullable(blackListCache.get(buildBlackOrWhiteListCacheKey(eventCode, key))).orElse(Collections.emptyList());
    }

    /**
     * 白名单专用
     * @param userKey String
     * @param eventCode String
     * @param key String
     * @return JSONObject
     */
    public JSONObject getWhiteListByKey(String userKey,
                                        String mobile,
                                        String eventCode,
                                        String key) throws Exception {
        JSONArray whiteLists = whiteListCache.get(buildBlackOrWhiteListCacheKey(eventCode, key));
        if (whiteLists != null) {
            Set<String> keywordSet = new HashSet<>(3);
            keywordSet.add("default");
            keywordSet.add("mobile");
            keywordSet.add("phone");
            for (Object o : whiteLists) {
                JSONObject whiteList = (JSONObject) o;
                if (whiteList.containsValue(userKey)) {
                    whiteList.put("isHit", true);
                    return whiteList;
                }
                // todo 判断主键是否mobile,当前根据default、mobile、phone同时13数字
                if (StringUtils.isNotBlank(mobile) && hitMobile(whiteList, mobile, keywordSet)) {
                    whiteList.put("isHit", true);
                    return whiteList;
                }
            }
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("isHit", false);
        return jsonObject;
    }

    /**
     * 判断是否命中手机号
     */
    private boolean hitMobile(JSONObject jsonObject, String mobile, Set<String> keywordSet) {
        if (CollectionUtils.isNotEmpty(keywordSet)) {
            for (String keyword : keywordSet) {
                if (StringUtils.isNotBlank(keyword) && jsonObject.containsKey(keyword)) {
                    // key不为空并且数据库中包含该字段
                    return StringUtils.equals(mobile, jsonObject.getString(keyword));
                }
            }
            keywordSet.clear();
        }
        // 没有对应的关键字，遍历jsonObject，判断13位长度+数字型字符串
        Set<String> keySet = jsonObject.keySet();
        for (String keyword : keySet) {
            String dataValue = jsonObject.getString(keyword);
            if (dataValue.length() == 13
                    && StringUtils.isNumeric(dataValue)
                    && StringUtils.startsWith(dataValue, "1")) {
                keywordSet.add(keyword);
                return StringUtils.equals(mobile, dataValue);
            }
        }
        return false;
    }

    /**
     * ehr对接接口,离职清理白名单
     */
    public boolean clearByEhr(String userKey, String mobile, String idNo) {
        // 查询全部白名单列表
        List<WhiteListDto> whiteListDtoList = dcWhiteListService.queryAllWhiteList();
        boolean needRefresh = false;
        for (WhiteListDto whiteListDto : whiteListDtoList) {
            JSONArray whiteLists = JSONArray.parseArray(whiteListDto.getContent());
            if (whiteLists != null) {
                Iterator<Object> iterator = whiteLists.iterator();
                Set<String> keywordSet = new HashSet<>(3);
                keywordSet.add("default");
                keywordSet.add("mobile");
                keywordSet.add("phone");
                boolean needUpdate = false;
                while (iterator.hasNext()) {
                    Object obj = iterator.next();
                    if (obj instanceof JSONObject) {
                        JSONObject jsonObject = (JSONObject) obj;
                        if (StringUtils.isNotBlank(userKey) && jsonObject.containsValue(userKey)) {
                            iterator.remove();
                            needUpdate = true;
                            needRefresh = true;
                            LoggerProxy.warn("clearByEhr",logger,"remove userKey: {}", userKey);
                        } else {
                            // todo 判断主键是否mobile,当前根据default、mobile、phone同时13数字
                            if (StringUtils.isNotBlank(mobile) && hitMobile(jsonObject, mobile, keywordSet)) {
                                iterator.remove();
                                needUpdate = true;
                                needRefresh = true;
                                LoggerProxy.warn("clearByEhr",logger,"remove mobile: {}", mobile);
                            }
                        }
                    }
                }
                if (needUpdate) {
                    LoggerProxy.warn("clearByEhr",logger,"update WhiteListDto: {}", whiteListDto.getKey());
                    dcWhiteListService.updateByEhr(whiteListDto.getKey(), whiteLists.toJSONString());
                }
            }
        }
        if (needRefresh) {
            this.refreshWhiteListCache();
        }
        return true;
    }

    /**
     *
     * @param matchKey 需要匹配的key值
     * @param eventCode 事件编码
     * @param key 白名单唯一标识
     * @return
     */
    public JSONObject getWhiteInfoByKey(String matchKey, String eventCode, String key){
        JSONArray whiteList = whiteListCache.get(buildBlackOrWhiteListCacheKey(eventCode, key));
        if (whiteList != null) {
            for (Object o : whiteList) {
                JSONObject whiteInfo = (JSONObject) o;
                if (whiteInfo.containsValue(matchKey)) {
                    return whiteInfo;
                }
            }
        }
        JSONObject jsonObject = new JSONObject();
        return jsonObject;
    }

    private void refreshBlackListCache() {
        List<WhiteListDto> blackList = dcWhiteListService.queryAllBlackList();
        LoggerProxy.info("BlackListCache",logger,"refresh cache ,data:{}",JSONObject.toJSONString(mobileCache));
        blackListCache = new ConcurrentHashMap<>();
        for (WhiteListDto dto : blackList) {
            // 这里特殊处理  后续还需要进一步优化
            try {
                List<CustomServiceBlackList> customServiceBlackLists = JSON.parseArray(dto.getContent(), CustomServiceBlackList.class);
                if (customServiceBlackLists.get(0).isEmpty()) {
                    for (String eventCode : dto.getEventCodes()) {
                        eblackListCache.put(buildBlackOrWhiteListCacheKey(eventCode, dto.getKey()), dto.getContent());
                    }
                    continue;
                }
                for (String eventCode : dto.getEventCodes()) {
                    blackListCache.put(buildBlackOrWhiteListCacheKey(eventCode, dto.getKey()), customServiceBlackLists);
                }
            } catch (Exception e) {
                LoggerProxy.warn("parseBlackListError", logger, "", e);
            }
        }
    }

    private void refreshWhiteListCache() {
        List<WhiteListDto> whiteList = dcWhiteListService.queryAllWhiteList();
        whiteListCache = new ConcurrentHashMap<>();
        for (WhiteListDto dto : whiteList) {
            try {
                for (String eventCode : dto.getEventCodes()) {
                    whiteListCache.put(buildBlackOrWhiteListCacheKey(eventCode, dto.getKey()), JSONArray.parseArray(dto.getContent()));
                }
            } catch (Exception e) {
                LoggerProxy.error("refreshWhiteListCache", logger, "parseWhiteListCacheError", e);
            }
        }
        LoggerProxy.info("refreshWhiteListCache", logger, "refreshWhiteListCache end whiteListCache size = " + whiteListCache.size());
    }

    private void dealData(WhiteListDto whiteListDto){
        try {
            JSONArray jsonArray = JSONObject.parseArray(Optional.ofNullable(whiteListDto.getContent()).orElse("[]"));
            if(CollectionUtils.isEmpty(jsonArray)){
                return;
            }
            Map map=new HashMap();
            for(int i=0;i<jsonArray.size();i++){
                JSONObject table = jsonArray.getJSONObject(i);
                String mobile = table.getString("mobile");
                if(!StringUtils.isEmpty(mobile)){
                    map.put(mobile,"1");
                }
            }
            Set<String> eventCodes = whiteListDto.getEventCodes();
            if(!Objects.isNull(eventCodes)){
                eventCodes.forEach(eventCode->{
                    Map mobileMap= new ConcurrentHashMap();
                    mobileMap.putAll(map);
                    mobileCache.put(eventCode,mobileMap);
                });
            }
        }catch (Exception e){
            LoggerProxy.error("WhiteListCache",logger,"deal data error:",e);
        }
    }


    private void analysisData(WhiteListDto whiteListDto) {
        try {
            JSONArray jsonArray = JSONObject.parseArray(Optional.ofNullable(whiteListDto.getContent()).orElse("[]"));
            if(CollectionUtils.isEmpty(jsonArray)){
                return;
            }
            Set set=new HashSet();
            for(int i=0;i<jsonArray.size();i++){
                JSONObject table = jsonArray.getJSONObject(i);
                String mobile = table.getString("mobile");
                if(!StringUtils.isEmpty(mobile)){
                    set.add(mobile);
                }
            }
            if(set.isEmpty()){
                return;
            }
            Set<String> eventCodes = whiteListDto.getEventCodes();
            if(!Objects.isNull(eventCodes)){
                eventCodes.forEach(eventCode->{
                    Set mobileSet=new HashSet();
                    mobileSet.addAll(set);
                    mobileListCache.put(buildBlackOrWhiteListCacheKey(eventCode,whiteListDto.getKey()),mobileSet);
                });
            }
        }catch (Exception e){
            LoggerProxy.error("analysisData",logger,"deal data error:",e);
        }
    }

    private void analysisBlackListData(WhiteListDto blackListDto) {
        try {
            JSONArray jsonArray = JSONObject.parseArray(Optional.ofNullable(blackListDto.getContent()).orElse("[]"));
            if(CollectionUtils.isEmpty(jsonArray)){
                return;
            }
            // 将数仓同步过来的黑名单过滤掉 量大 不包含手机号
            if ("custom_service_black_list".equals(blackListDto.getKey())) {
                return;
            }
            Set set=new HashSet();
            for(int i=0;i<jsonArray.size();i++){
                JSONObject table = jsonArray.getJSONObject(i);
                String mobile = table.getString("mobile");
                if(!StringUtils.isEmpty(mobile)){
                    set.add(mobile);
                }
            }
            if(set.isEmpty()){
                return;
            }
            Set<String> eventCodes = blackListDto.getEventCodes();
            if(!Objects.isNull(eventCodes)){
                eventCodes.forEach(eventCode->{
                    Set mobileSet=new HashSet();
                    mobileSet.addAll(set);
                    mobileBlackListCache.put(buildBlackOrWhiteListCacheKey(eventCode,blackListDto.getKey()),mobileSet);
                });
            }
        }catch (Exception e){
            LoggerProxy.error("analysisData",logger,"deal data error:",e);
        }
    }

    public boolean needRefresh(Date createTime){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(this.lastRefreshTime);
        calendar.add(Calendar.MINUTE, -1);
        Date beforeFiveMinutes = calendar.getTime();
        return (!createTime.before(beforeFiveMinutes));
    }


    public boolean containsMobileNew(String eventCode,String whiteListKey,String mobile, String isBlackList){
        String mapKey = buildBlackOrWhiteListCacheKey(eventCode, whiteListKey);
        if (Boolean.parseBoolean(isBlackList)) {
            return Optional.ofNullable(mobileBlackListCache.get(mapKey)).orElse(Collections.emptySet()).contains(mobile);
        }
        return Optional.ofNullable(mobileListCache.get(mapKey)).orElse(new HashSet<>()).contains(mobile);
    }


    @Data
    @SuppressWarnings("all")
    public static class CustomServiceBlackList {
        private String dt;
        private String tousu_reason;
        private String crt_dt;
        private String cus_id;

        @JSONField(serialize = false)
        public boolean isEmpty() {
            return tousu_reason == null && crt_dt == null && cus_id ==null;
        }

        public boolean matchUserkey(String userKey) {
            return StringUtils.equals(userKey, cus_id);
        }
    }


    public String buildBlackOrWhiteListCacheKey(String eventCode, String key) {
        return eventCode + "-" + key;
    }

}
