package com.youxin.risk.datacenter.mapper;

import com.youxin.risk.commons.model.datacenter.DcSubmitContactInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface DcSubmitReCertificationContactInfoMapper {

    void insertBatch(List<DcSubmitContactInfo> list);

    List<DcSubmitContactInfo> getByUserKey(@Param("userKey") String userKey);

    // 修数用的方法，勿使用
    List<Map<String, Object>> queryById(Map<String, Object> map);

    // 修数用的方法，勿使用
    int updateById(Map<String, Object> map);
}