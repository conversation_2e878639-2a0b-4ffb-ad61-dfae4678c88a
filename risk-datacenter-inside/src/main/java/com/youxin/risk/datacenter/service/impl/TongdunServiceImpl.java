package com.youxin.risk.datacenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.weicai.caesar.CaesarUtil;
import com.youxin.risk.commons.model.datacenter.*;
import com.youxin.risk.commons.model.datacenter.vo.DcSubmitContactInfoVo;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.NacosClientAdapter;
import com.youxin.risk.datacenter.service.MongoEventService;
import com.youxin.risk.datacenter.service.TongdunService;
import com.youxin.risk.datacenter.service.search.QueryServiceDispatcher;
import com.youxin.risk.ra.enums.DataPlatformConstants;
import com.youxin.risk.ra.enums.RiskProduct;
import com.youxin.risk.ra.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.youxin.risk.commons.utils.StringUtils.replaceBlank;

/**
 * <AUTHOR>
 * @date 2023年10月16日 下午8:10
 */
@Slf4j
@Service
public class TongdunServiceImpl implements TongdunService {
    @Autowired
    private MongoEventService mongoEventService;
    @Autowired
    private QueryServiceDispatcher queryServiceDispatcher;
    @Resource
    private DcReportRequestServiceImpl dcReportRequestService;

    private static final String ID_CARD = "ID_CARD";
    private static final String BANK_CARD = "BANK_CARD";
    private static final String REGISTER = "REGISTER";
    private static final String JOB = "JOB";
    private static final String ADDRESS = "ADDRESS";
    private static final String CONTACT = "CONTACT";

    private String crawlerV2Url = DataPlatformConstants.crawlerV2Url;


    private void buildDataInputParams(String loanKey, Map<String, Object> params) {
        String path = "$.event.params.dataInput";
        String response = mongoEventService.queryDataFromEventVo(loanKey, path);
        if (StringUtils.isBlank(response) || "null".equals(response)) {
            return;
        }

        JSONObject jsonObject = JSONObject.parseObject(response);
        if (jsonObject == null) {
            return;
        }
        params.put("keyId", jsonObject.getString("reportKey") + "_tongdunService");
        params.put("systemId", jsonObject.getString("sourceSystem"));
        params.put("apply_id", loanKey);
        String platform = jsonObject.getString("platform");
        if (StringUtils.isBlank(platform) || !StringUtils.containsAny(platform, "iPhone", "Android", "ios", "android","harmony", "applet")) {
            platform = dcReportRequestService.getLastNotNullPlatform((String) params.get("userKey"));
        }
        Boolean castFlag = NacosClientAdapter.getBooleanConfig("applet.cast.to.android.flag", true);
        if("applet".equals(platform) && castFlag){
            platform = "android";
        }
        params.put("platform", platform);
        RiskProduct product = RiskProduct.findProduct(jsonObject.getString("sourceSystem"));
        params.put("caller", product.name());
    }

    private void buildSubmitRegisterVoParams(String userKey, Map<String, Object> params) {
        Map<String, Object> query = new HashMap(2);
        query.put("userKey", userKey);
        query.put("serviceCode", REGISTER);

        DcSubmitRegister dcSubmitRegister = (DcSubmitRegister) queryServiceDispatcher.dispatch(REGISTER, query);
        if (dcSubmitRegister == null) {
            return;
        }

        /** 用户传入的tongdunfingerPrint */
        params.put("black_box", dcSubmitRegister.getTongdunFingerprint());
        params.put("account_mobile", dcSubmitRegister.getMobile());
    }
    private void buildSubmitIdcardVoParams(String userKey, Map<String, Object> params) {
        Map<String, Object> query = new HashMap(2);
        query.put("userKey", userKey);
        query.put("serviceCode", ID_CARD);

        DcSubmitIdcard dcSubmitIdcard = (DcSubmitIdcard) queryServiceDispatcher.dispatch(ID_CARD, query);
        if (dcSubmitIdcard == null) {
            return;
        }
        /** 身份证号 */
        params.put("id_number", dcSubmitIdcard.getIdcardNumber());
        params.put("account_name", dcSubmitIdcard.getIdcardName());
    }

    private void buildSubmitJobVoParams(String userKey, Map<String, Object> params) {
        Map<String, Object> query = new HashMap(2);
        query.put("userKey", userKey);
        query.put("serviceCode", JOB);

        DcSubmitJob dcSubmitJob = (DcSubmitJob) queryServiceDispatcher.dispatch(JOB, query);
        if (dcSubmitJob == null) {
            return;
        }

        params.put("organization", dcSubmitJob.getCompanyName());
    }

    private void buildAddressParams(String userKey, Map<String, Object> params) {
        Map<String, Object> query = new HashMap(2);
        query.put("userKey", userKey);
        query.put("serviceCode", ADDRESS);

        DcSubmitAddress dcSubmitAddress = (DcSubmitAddress) queryServiceDispatcher.dispatch(ADDRESS, query);
        if (dcSubmitAddress == null) {
            return;
        }
        StringBuilder addressSb = new StringBuilder();
        addressSb.append(dcSubmitAddress.getProvince());
        addressSb.append(dcSubmitAddress.getCity());
        addressSb.append(dcSubmitAddress.getDistrict());
        params.put("account_address", addressSb.toString());

    }

    private void buildSubmitBankCardVoParams(String userKey, Map<String, Object> params) {
        Map<String, Object> query = new HashMap(2);
        query.put("userKey", userKey);
        query.put("serviceCode", BANK_CARD);

        DcSubmitBankCard dcSubmitBankCard = (DcSubmitBankCard) queryServiceDispatcher.dispatch(BANK_CARD, query);
        if (dcSubmitBankCard == null) {
            return;
        }
        /** 银行卡号 */
        String cardNumber = dcSubmitBankCard.getBankCardNo();
        params.put("card_number", cardNumber);
        try{
            if(CaesarUtil.isEncrypted(cardNumber)){
                cardNumber = CaesarUtil.decode(cardNumber);
                params.put("card_number",cardNumber);
            }
        }catch (Exception e){
            LoggerProxy.error("decodeBankNo",log,"同盾银行卡号解密失败,bankNo={}",cardNumber, e);
            throw e;
        }
        params.put("ext_contact4_mobile", dcSubmitBankCard.getReservedMobile());

    }

    private void buildContactParams(String userKey, Map<String, Object> params) {
        Map<String, Object> query = new HashMap(2);
        query.put("userKey", userKey);
        query.put("serviceCode", CONTACT);

        List<DcSubmitContactInfoVo> emergencyContactVos = (List<DcSubmitContactInfoVo>) queryServiceDispatcher.dispatch(CONTACT, query);
        if (CollectionUtils.isEmpty(emergencyContactVos)) {
            return;
        }
        // 5个联系人
        List<String> alreadyMobile = new ArrayList<>();
        for (DcSubmitContactInfoVo submitContactDetailsVo : emergencyContactVos) {
            String pre = submitContactDetailsVo.getMobile();
            String post = replaceBlank(pre);
            submitContactDetailsVo.setMobile(post);
        }
        if (emergencyContactVos.size() > 0) {
            params.put("contact1_mobile", emergencyContactVos.get(0).getMobile());
            if (null != emergencyContactVos.get(0).getContactName()) {
                params.put("contact1_name", emergencyContactVos.get(0).getContactName().replace("&", ""));
            }
            String contact1Relation = emergencyContactVos.get(0).getRelation();
            if (isContactRelationLegal(contact1Relation)) {
                params.put("contact1_relation", contact1Relation.split("_")[2]);
            }
            alreadyMobile.add(emergencyContactVos.get(0).getMobile());
            if (emergencyContactVos.size() > 1) {
                params.put("contact2_mobile", emergencyContactVos.get(1).getMobile());
                if (null != emergencyContactVos.get(1).getContactName()) {
                    params.put("contact2_name", emergencyContactVos.get(1).getContactName().replace("&", ""));
                }
                String contact2Relation = emergencyContactVos.get(1).getRelation();
                if (isContactRelationLegal(contact2Relation)) {
                    params.put("contact2_relation", contact2Relation.split("_")[2]);
                }
                alreadyMobile.add(emergencyContactVos.get(1).getMobile());
            }
        }
    }

    /**
     * 判断紧急联系人关系的是否合法
     * @param contactRelation
     * @return
     */
    public boolean isContactRelationLegal(String contactRelation){
        return org.apache.commons.lang3.StringUtils.isNotBlank(contactRelation) && contactRelation.split("_").length > 2;
    }

    private void buildMobileParams(String loanKey, Map<String, Object> params) {
        /*Map<String, Integer> mobileTimeMap = new HashMap<>();
        List<MobileCallRecordVo> callList = dataVo.getMobileCallList();
        if (callList != null) {
            for (MobileCallRecordVo mcr : callList) {
                String toMobile = mcr.getToMobile();
                int duration = 0;
                if (mcr.getDuration() != null) {
                    duration = mcr.getDuration().intValue();
                }
                if (mobileTimeMap.get(toMobile) != null) {
                    int tmpTime = mobileTimeMap.get(toMobile) + duration;
                    mobileTimeMap.put(toMobile, tmpTime);
                } else {
                    mobileTimeMap.put(toMobile, duration);
                }
            }
            Set<Map.Entry<String, Integer>> mobileTimeSet = mobileTimeMap.entrySet();
            List<Map.Entry<String, Integer>> mobileTimeEntryList = new ArrayList<>(mobileTimeSet);
            Collections.sort(mobileTimeEntryList,
                    new Comparator<Map.Entry<String, Integer>>() {
                        @Override
                        public int compare(Map.Entry<String, Integer> o1,
                                           Map.Entry<String, Integer> o2) {
                            if (o1.getValue() < o2.getValue()) {
                                return 1;
                            } else if (o1.getValue() > o2.getValue()) {
                                return -1;
                            } else {
                                return 0;
                            }
                        }
                    });

            for (Map.Entry<String, Integer> entry : mobileTimeEntryList) {
                if (alreadyMobile.contains(entry.getKey())) {
                    continue;
                }
                if (alreadyMobile.size() > 3) {
                    break;
                } else if (alreadyMobile.size() == 3) {
                    params.put("contact4_mobile", entry.getKey());
                } else if (alreadyMobile.size() == 2) {
                    params.put("contact3_mobile", entry.getKey());
                } else if (alreadyMobile.size() == 1) {
                    params.put("contact2_mobile", entry.getKey());
                } else if (alreadyMobile.size() == 0) {
                    params.put("contact1_mobile", entry.getKey());
                }
            }
        }*/
    }

    @Override
    public Map<String, Object> buildParams(JSONObject params) {
        String userKey = params.get("userKey").toString();
        String loanKey = params.get("loanKey").toString();
        log.info("tongDunService buildParams start, userKey:{}, loanKey:{}", userKey, loanKey);
        params.put("nextMethod", "tongdunApiV2");
        params.put("queryReason", 0);
        params.put("strategy", "2");
        params.put("invokeType", 1);
        params.put("isGuard", false);
        params.put("useCache", false);

        buildDataInputParams(loanKey, params);
        buildSubmitRegisterVoParams(userKey, params);
        buildAddressParams(userKey, params);
        buildContactParams(userKey, params);
        buildSubmitBankCardVoParams(userKey, params);
        buildSubmitIdcardVoParams(userKey, params);
        buildSubmitJobVoParams(userKey, params);

        return params;
    }
}
