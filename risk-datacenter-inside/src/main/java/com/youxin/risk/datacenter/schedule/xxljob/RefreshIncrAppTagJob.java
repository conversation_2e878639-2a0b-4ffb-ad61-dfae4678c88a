package com.youxin.risk.datacenter.schedule.xxljob;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.youxin.risk.commons.xxl.job.XxlJobBase;
import com.youxin.risk.datacenter.mapper.AppTagMapper;
import com.youxin.risk.datacenter.model.AppTag;
import com.youxin.risk.datacenter.service.impl.AppTagServiceImpl;
import com.youxin.risk.verify.redis.RedisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Random;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 刷新增量的appTag
 * 定时任务从dc_app_crawl_queue 中查到数据并调用爬虫去爬取。
 * <AUTHOR>
 */
@Component
public class RefreshIncrAppTagJob implements XxlJobBase {

    private static final Logger logger = LoggerFactory.getLogger(RefreshIncrAppTagJob.class);

    @Resource
    private AppTagMapper appTagMapper;

    @Resource
    private AppTagServiceImpl appTagService;

    @Resource(name = "cacheRedisService")
    private RedisService cacheRedisService;

    ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(2,5,0,
            TimeUnit.SECONDS,new ArrayBlockingQueue<>(1000), new ThreadPoolExecutor.DiscardPolicy());

    private final static String APP_TAG_INCR_KEY = "risk_datacenter_app_tag_incr_key";

    @Override
    @XxlJob(value = "refreshIncrAppTagJob")
    public ReturnT<String> execJobHandler(String param) {
        int pauseSeconds = 0;
        if(param != null){
            pauseSeconds = Integer.parseInt(param);
        }
        logger.info("refreshIncrAppTagJob start");
        int finalPauseSeconds = pauseSeconds;
        threadPoolExecutor.execute(() -> {
            int randomPauseSeconds = new Random().nextInt(finalPauseSeconds);
            try {
                //固定时间爬百度，5s一次，坚持不了多少时间就不行了，尝试以不固定频率爬取。
                Thread.sleep(randomPauseSeconds);
                String idstr = cacheRedisService.get(APP_TAG_INCR_KEY);
                long id = 0;
                if (idstr != null){
                    id = Long.parseLong(idstr);
                }
                AppTag appTag = appTagMapper.getOneIdGreaterThanFromCrawlQueue(id);
                if (appTag == null) {
                    return;
                }
                long lastId = appTag.getId();
                logger.info("refresh app tag,id:{},appName:{}", lastId, appTag.getAppName());
                appTagService.submitCrawlerJobToSpider(appTag,"/appTag/addAppTag");
                //爬取成功回调时插入数据，防止多个渠道都没有爬到导致标签库中有空记录。
//                appTag.setId(null);
//                appTag.setCreateTime(new Date());
//                appTag.setUpdateTime(new Date());
//                appTagService.insert(appTag);
                cacheRedisService.set(APP_TAG_INCR_KEY,String.valueOf(lastId));
                //此逻辑可以改成爬取多次未成功再将其删除
                appTagMapper.deleteFromWaitCrawlQueue(appTag.getAppName());
            } catch (Exception e) {
                logger.error("exec refreshFullAppTagJob eror", e);
            }
        });
        return ReturnT.SUCCESS;
    }
}
