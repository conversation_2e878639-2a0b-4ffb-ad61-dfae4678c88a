package com.youxin.risk.verify.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.verify.VerifyResult;
import com.youxin.risk.commons.utils.JsonUtils;
import com.youxin.risk.datacenter.pojo.JsonResultVo;
import com.youxin.risk.verify.model.VerifyTransaction;
import com.youxin.risk.verify.service.VerifyResultService;
import com.youxin.risk.verify.service.VerifyUserSystemService;
import com.youxin.risk.verify.service.impl.VerifyTransactionDataService;
import com.youxin.risk.verify.vo.VerifyUserLoanVo;
import com.youxin.risk.verify.vo.VerifyUserSystemVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/search")
public class SearchController {

    private static final Logger LOG = LoggerFactory.getLogger(SearchController.class);


    @Autowired
    private VerifyResultService verifyResultService;

    @Autowired
    private VerifyTransactionDataService verifyTransactionDataService;

    @Autowired
    private VerifyUserSystemService userSystemService;



    @RequestMapping(value = "/verifyInfo", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultVo findUserVerifyInfo(@RequestBody VerifyUserLoanVo vo) {

        try {
            LOG.info("findUserVerifyInfo request param={}", JsonUtils.toJson(vo));

            VerifyResult verifyResult = this.verifyResultService.findResultByLoanId(vo.getLoanId());

            VerifyTransaction trans = this.verifyTransactionDataService.findByUserKey(vo.getUserKey());



            JsonResultVo resultVo = JsonResultVo.success();
            resultVo.addData("verifyResult", verifyResult);
            resultVo.addData("verifyTrans", trans);
            if(verifyResult != null && verifyResult.getUserLevelId() != null) {
                resultVo.addData("userLevel", this.userSystemService.findById(verifyResult.getUserLevelId()));
            }

            return resultVo;
        } catch (Exception e) {
            LOG.error("search verifyInfo error:", e);
            return JsonResultVo.error();
        }

    }


    @RequestMapping(value = "/userLevel", method = RequestMethod.POST)
    @ResponseBody
    public JsonResultVo findUserLevel(@RequestBody VerifyUserLoanVo vo) {

        try {
            LOG.info("findUserLevel request param={}", JsonUtils.toJson(vo));
            return JsonResultVo.success().addData("userLevel",this.userSystemService.getUserLevelByUserKey(vo.getUserKey()) );
        } catch (Exception e) {
            LOG.error("findUserLevel error,request={}",JsonUtils.toJson(vo),e);
            return JsonResultVo.error();
        }

    }

}

