package com.youxin.risk.verify.service.impl;

import com.youxin.risk.verify.mapper.VerifySubmitLoansInfoMapper;
import com.youxin.risk.verify.model.VerifySubmitLoansInfo;
import com.youxin.risk.verify.service.VerifySubmitLoansInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class VerifySubmitLoansInfoServiceImpl implements VerifySubmitLoansInfoService {

	@Autowired
	private VerifySubmitLoansInfoMapper verifySubmitLoansInfoMapper;

	@Override
	public void saveSubmitLoansInfo(VerifySubmitLoansInfo loansInfo) {
		verifySubmitLoansInfoMapper.insert(loansInfo);
	}

}
