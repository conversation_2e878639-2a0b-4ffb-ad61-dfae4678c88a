package com.youxin.risk.verify.service.impl;

import com.youxin.risk.commons.dao.verify.UserLineMidverifyResultMapper;
import com.youxin.risk.commons.model.verify.UserLineMidverifyResult;
import com.youxin.risk.verify.service.UserLineMidverifyResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UserLineMidverifyResultServiceImpl implements UserLineMidverifyResultService {

    @Autowired
    private UserLineMidverifyResultMapper userLineMidverifyResultMapper;

	@Override
	public void saveOrUpdateRec(UserLineMidverifyResult userLine) {
		if (userLine.getId() == null) {
			this.userLineMidverifyResultMapper.persist(userLine);
		} else {
			this.userLineMidverifyResultMapper.update(userLine);
		}
	}

	@Override
	public UserLineMidverifyResult queryByUserKey(String sourceSystem, String userKey) {
		return this.userLineMidverifyResultMapper.queryByUserKey(sourceSystem, userKey);
	}


}
