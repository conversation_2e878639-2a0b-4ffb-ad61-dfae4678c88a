package com.youxin.risk.verify.service;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.datacenter.pojo.UserLineManagementEffective;
import com.youxin.risk.verify.dto.AmountDto;

import java.util.List;

/**
 * 额度接口
 * <AUTHOR>
 */
public interface AmountUpdateService {


    /**
     * 更新额度信息
     *
     * @param amountDto 额度数据传输对象
     * @return 是否更新成功
     */
    boolean updateUserAmount(AmountDto amountDto);


    /**
     * 有效额度不为空，更新有效额度
     *
     * @param amountDto                   业务方传参：需要更新的额度信息
     * @param userLineManagementEffective 新表有效额度
     */
    void updateEffectiveFromAmountDto(AmountDto amountDto, UserLineManagementEffective userLineManagementEffective);

    /**
     * 有效额度不为空，将当前有效额度插入到历史额度表
     * @param userLineManagementEffective 有效额度
     * @return 历史额度的id
     */
    Long insertHistoryFromEffective(UserLineManagementEffective userLineManagementEffective);

    boolean fixAmount(String userKey, int c);

    boolean updateAmountExt(String userKey);

    List<JSONObject> fixAmountExt(List<String> usrKey);
}