package com.youxin.risk.verify.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.verify.VerifyUserLineManagement;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.vo.BooleanResult;
import com.youxin.risk.verify.service.AmountResultProcesser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class AmountResultProcesserProxy implements AmountResultProcesser {

    private Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private Map<String, AmountResultProcesser> amountResultProcessers;

    @Value("${heika.amount.callback}")
    private String heiKaAmountCallback;
    @Value("${rongdan.amount.callback}")
    private String rongDanAmountCallback;


    @Override
    public BooleanResult process(JSONObject param) {
        String amountType = param.getString("amountType");
        AmountResultProcesser amountResultProcesser = amountResultProcessers.get(amountType);
        if (null == amountResultProcesser) {
            logger.error("match amount processor failed, param={}", param.toJSONString());
            return null;
        }
        return amountResultProcesser.process(param);
    }

    @Override
    public BooleanResult processAndNotify(JSONObject param) {
        String amountType = param.getString("amountType");
        AmountResultProcesser amountResultProcesser = amountResultProcessers.get(amountType);
        if (null == amountResultProcesser) {
            logger.error("match amount processor failed, param={}", param.toJSONString());
            return null;
        }
        try {
            return amountResultProcesser.process(param);
        } finally {
            // 判断新老客
            try {
                Integer isOldLoanUser = param.getInteger("isOldLoanUser");
                String callBackUrl = isOldLoanUser == 0? heiKaAmountCallback : rongDanAmountCallback;

                Map<String, String> header = new HashMap<>();
                header.put("Content-Type", "application/x-www-form-urlencoded");
                Map<String, String> body = new HashMap<>();
                JSONObject amountParams = param.getJSONObject("amountParams");
                body.put("requestId", amountParams.getString("loanKey"));
                body.put("partnerUserId", amountParams.getString("userKey"));
                JSONObject verifyResult = param.getJSONObject("verifyResult");
                VerifyUserLineManagement userLineManagement = JSON.parseObject(verifyResult.toJSONString(), VerifyUserLineManagement.class);
                body.put("totalCredit", userLineManagement.getCreditLine().toString());
                body.put("availableCredit", userLineManagement.getAvailLine().toString());
                String result = SyncHTTPRemoteAPI.post(callBackUrl + "soter/risk/callback/runBatchCallback", body, header, 1000);
                logger.info("notify heika/rongdan success, param={}, result={}", param.toJSONString(), result);
            } catch (Exception e) {
                logger.error("notify heika/rongdan failed, param={}", param.toJSONString(), e);
            }
        }
    }
}
