/**
 * Copyright(c) 2011-2017 by YouCredit Inc.
 * All Rights Reserved
 */
package com.youxin.risk.verify.service.impl;

import com.youxin.risk.commons.dao.verify.UserLevelConfigMapper;
import com.youxin.risk.commons.dao.verify.VerifyUserSystemMapper;
import com.youxin.risk.commons.model.verify.UserLevelConfig;
import com.youxin.risk.commons.model.verify.VerifyUserSystem;
import com.youxin.risk.commons.utils.ObjectTransferUtils;
import com.youxin.risk.verify.enums.LoanType;
import com.youxin.risk.verify.service.VerifyUserSystemService;

import com.youxin.risk.verify.vo.UserLevelConfigVo;
import com.youxin.risk.verify.vo.VerifyUserSystemVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * 用户体系服务类
 *
 * <AUTHOR>
 * @version 创建时间：2017年8月30日-下午4:20:42
 */
@Service
public class VerifyUserSystemServiceImpl implements VerifyUserSystemService {

	private static final Logger LOG = LoggerFactory.getLogger(VerifyUserSystemServiceImpl.class);

	@Autowired
	private VerifyUserSystemMapper verifyUserSystemMapper;
	@Autowired
	private UserLevelConfigMapper userLevelConfigMapper;


	@Override
	public VerifyUserSystemVo getUserLevelByUserKey(String userKey){
		VerifyUserSystem user = this.verifyUserSystemMapper.getLastRecordByUserKey(userKey);
		VerifyUserSystemVo resultVo = null;
		if(user == null){
			LOG.warn("没有找到用户等级 userKey={}",userKey);
			return resultVo;
		}
		resultVo = new VerifyUserSystemVo();
		resultVo.setUserLevelConfigList(this.getAllUserLevelConfig());
		resultVo.setUserLevel(user.getUserLevel());
		resultVo.setScore(user.getScore());
		resultVo.setIsPass(user.getIsPass());
		return resultVo;
	}



	public List<UserLevelConfigVo> getAllUserLevelConfig(){

		List<UserLevelConfig> configs = this.userLevelConfigMapper.getConfigs();
		List<UserLevelConfigVo> resultList = new ArrayList<>();
		for(UserLevelConfig config : configs){
			try {
				resultList.add(ObjectTransferUtils.transferObject(config, UserLevelConfigVo.class));
			} catch (Exception e) {
				LOG.error("Bean 转换出错");
			}
		}

		return resultList;
	}

	@Override
	public VerifyUserSystem addUserLevelByVerify(String userKey,String userLevel,Double score,String reason,boolean isReloan){

		VerifyUserSystem user = new VerifyUserSystem();
		user.setUserKey(userKey);
		user.setUserLevel(userLevel);
		user.setScore(score);
		user.setReasonCode(reason);
		user.setCreateTime();
		user.setUpdateTime();
		user.setVersion();
		if(isReloan){
			user.setLoanType(LoanType.RELOAN.name());
		}else{
			user.setLoanType(LoanType.LOAN.name());
		}

		this.verifyUserSystemMapper.persist(user);

		return user;
	}

	@Override
	public VerifyUserSystemVo getUserLevel(String userKey){
		VerifyUserSystem user = this.verifyUserSystemMapper.getLastRecordByUserKey(userKey);
		VerifyUserSystemVo resultVo = new VerifyUserSystemVo();
		resultVo.setUserLevelConfigList(this.getAllUserLevelConfig());
		if(user == null){
			LOG.warn("没有找到用户等级 userKey={}",userKey);
			return resultVo;
		}
		resultVo.setUserLevel(user.getUserLevel());
		resultVo.setScore(user.getScore());
		resultVo.setIsPass(user.getIsPass());
		return resultVo;
	}

	@Override
	public VerifyUserSystem findById(Integer id){
		return this.verifyUserSystemMapper.get(id);
	}

}
