package com.youxin.risk.verify.service;


import com.youxin.risk.commons.model.verify.VerifyConfig;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

public interface VerifyConfigLoaderService {
    /**
     * 获取全部配置
     * 
     * @return
     */
    List<VerifyConfig> getAllConfigInfo();

    /**
     * 修改配置
     * 
     * @param key
     * @param value
     * @param operator
     */
    void update(String key, String value, Integer operator);

    /**
     * 获取配置
     * 
     * @param key
     * @return
     */
    String getValue(String key);

    /**
     * 获取Integer型配置
     * 
     * @param key
     * @return
     */
    Integer getValueAsInteger(String key);

    /**
     * 获取Long型配置
     * 
     * @param key
     * @return
     */
    Long getValueAsLong(String key);

    /**
     * 获取Double型配置
     * 
     * @param key
     * @return
     */
    Double getValueAsDouble(String key);

    /**
     * 获取Boolean型配置
     * 
     * @param key
     * @return
     */
    Boolean getValueAsBoolean(String key);

    /**
     * 获取Date型配置
     * 
     * @param key
     * @param format
     * @return
     * @throws ParseException
     */
    Date getValueAsDate(String key, String format) throws ParseException;

    /**
     * 获取配置
     * 
     * @param key
     * @return
     */
    VerifyConfig getConfigInfo(String key);
}
