package com.youxin.risk.verify.enums;

import org.apache.commons.lang3.RandomUtils;

import java.util.HashMap;
import java.util.Map;

public enum VerifyUELevel {
    A {
        @Override
        public Map<String, Object> getConfigMap(VerifyStrategyType strategyType) {
            Map<String, Object> configMap = new HashMap<String, Object>();
            switch (strategyType) {
                case VERIFY_NEW:
                case VERIFY:
                case RONG_360:
                    configMap.put(VerifyUEDetails.NOTIFY_TIME_LOWER.name(), 5);
                    configMap.put(VerifyUEDetails.NOTIFY_TIME_UPPER.name(), 10);
                    break;
                case VERIFY_RELOAN:
                    configMap.put(VerifyUEDetails.NOTIFY_TIME_LOWER.name(), 0);
                    configMap.put(VerifyUEDetails.NOTIFY_TIME_UPPER.name(), 5);
                default:
                    break;
            }
            return configMap;
        }
    },
    B {
        @Override
        public Map<String, Object> getConfigMap(VerifyStrategyType strategyType) {
            Map<String, Object> configMap = new HashMap<String, Object>();
            switch (strategyType) {
                case VERIFY_NEW:
                case RONG_360:
                case VERIFY:
                    configMap.put(VerifyUEDetails.NOTIFY_TIME_LOWER.name(), 10);
                    configMap.put(VerifyUEDetails.NOTIFY_TIME_UPPER.name(), 20);
                    break;
                case VERIFY_RELOAN:
                    configMap.put(VerifyUEDetails.NOTIFY_TIME_LOWER.name(), 5);
                    configMap.put(VerifyUEDetails.NOTIFY_TIME_UPPER.name(), 10);
                default:
                    break;
            }
            return configMap;
        }
    },
    C {
        @Override
        public Map<String, Object> getConfigMap(VerifyStrategyType strategyType) {
            Map<String, Object> configMap = new HashMap<String, Object>();
            switch (strategyType) {
                case VERIFY_NEW:
                case RONG_360:
                case VERIFY:
                    configMap.put(VerifyUEDetails.NOTIFY_TIME_LOWER.name(), 20);
                    configMap.put(VerifyUEDetails.NOTIFY_TIME_UPPER.name(), 30);
                    break;
                case VERIFY_RELOAN:
                    configMap.put(VerifyUEDetails.NOTIFY_TIME_LOWER.name(), 10);
                    configMap.put(VerifyUEDetails.NOTIFY_TIME_UPPER.name(), 15);
                default:
                    break;
            }
            return configMap;
        }
    },
    D {
        @Override
        public Map<String, Object> getConfigMap(VerifyStrategyType strategyType) {
            Map<String, Object> configMap = new HashMap<String, Object>();
            switch (strategyType) {
                case VERIFY_NEW:
                case RONG_360:
                case VERIFY:
                    configMap.put(VerifyUEDetails.NOTIFY_TIME_LOWER.name(), 30);
                    configMap.put(VerifyUEDetails.NOTIFY_TIME_UPPER.name(), 45);
                    break;
                case VERIFY_RELOAN:
                    configMap.put(VerifyUEDetails.NOTIFY_TIME_LOWER.name(), 15);
                    configMap.put(VerifyUEDetails.NOTIFY_TIME_UPPER.name(), 20);
                default:
                    break;
            }
            return configMap;
        }
    },
    E {
        @Override
        public Map<String, Object> getConfigMap(VerifyStrategyType strategyType) {
            Map<String, Object> configMap = new HashMap<String, Object>();
            switch (strategyType) {
                case VERIFY_NEW:
                case RONG_360:
                case VERIFY:
                    configMap.put(VerifyUEDetails.NOTIFY_TIME_LOWER.name(), 40);
                    configMap.put(VerifyUEDetails.NOTIFY_TIME_UPPER.name(), 60);
                    break;
                case VERIFY_RELOAN:
                    configMap.put(VerifyUEDetails.NOTIFY_TIME_LOWER.name(), 20);
                    configMap.put(VerifyUEDetails.NOTIFY_TIME_UPPER.name(), 30);
                default:
                    break;
            }
            return configMap;
        }
    },
    F {
		@Override
		public Map<String, Object> getConfigMap(VerifyStrategyType strategyType) {
            Map<String, Object> configMap = new HashMap<String, Object>();
            switch (strategyType) {
                case VERIFY_NEW:
                case RONG_360:
                case VERIFY:
                    configMap.put(VerifyUEDetails.NOTIFY_TIME_LOWER.name(), 40);
                    configMap.put(VerifyUEDetails.NOTIFY_TIME_UPPER.name(), 60);
                    break;
                case VERIFY_RELOAN:
                    configMap.put(VerifyUEDetails.NOTIFY_TIME_LOWER.name(), 20);
                    configMap.put(VerifyUEDetails.NOTIFY_TIME_UPPER.name(), 30);
                default:
                    break;
            }
            return configMap;
		}
    	
    },
    NONE {

        @Override
        public Map<String, Object> getConfigMap(VerifyStrategyType strategyType) {
            Map<String, Object> configMap = new HashMap<String, Object>();
            configMap.put(VerifyUEDetails.NOTIFY_TIME_LOWER.name(), 10);
            configMap.put(VerifyUEDetails.NOTIFY_TIME_UPPER.name(), 20);
            return configMap;
        }

    };
    public abstract Map<String, Object> getConfigMap(
            VerifyStrategyType strategyType);

    public Integer getHoldMinute(VerifyStrategyType strategyType) {
        Map<String, Object> configMap = getConfigMap(strategyType);
        Integer lo = (Integer) configMap.get(VerifyUEDetails.NOTIFY_TIME_LOWER
            .name());
        Integer up = (Integer) configMap.get(VerifyUEDetails.NOTIFY_TIME_UPPER
            .name());
        if (up == null || lo == null) {
            return null;
        }
        return RandomUtils.nextInt(lo, up);
    }
}
