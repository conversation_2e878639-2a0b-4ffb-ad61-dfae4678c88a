package com.youxin.risk.verify.vo;

import com.youxin.risk.commons.model.datacenter.common.OperationType;
import org.apache.commons.lang3.builder.ToStringBuilder;

/**
 * <AUTHOR>
 * @date 2019-04-22
 */
public class DcSubmitApply {

    private String appVersion;

    private String channelCode;

    private String mobileModel;

    private String deviceId;

    private String platform;

    private String osVersion;

    private String sourceSystem;

    private String userKey;

    private String ip;

    private String applyType;

    private String channel;

    private Integer jailBroken;

    private String device;

    private Integer isCopyPackage;

    private String jobId;

    public DcSubmitApply() {
    }

    public DcSubmitApply(VerifyCommonVo commonVo, OperationType type, String jobId) {
        this.appVersion = commonVo.getAppVersion();
        this.channelCode = commonVo.getChannelCode();
        this.mobileModel = commonVo.getMobileModel();
        this.deviceId = commonVo.getDeviceId();
        this.platform = commonVo.getPlatform();
        this.osVersion = commonVo.getOsVersion();
        this.sourceSystem = commonVo.getSourceSystem();
        this.userKey = commonVo.getUserKey();
        this.ip = commonVo.getIp();
        this.channel = commonVo.getChannel();
        this.jailBroken = commonVo.getJailBroken();
        this.device = commonVo.getDevice();
        this.isCopyPackage = commonVo.getIsCopyPackage();
        this.jobId = jobId;
        this.applyType = type.name();
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getMobileModel() {
        return mobileModel;
    }

    public void setMobileModel(String mobileModel) {
        this.mobileModel = mobileModel;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    public String getSourceSystem() {
        return sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem;
    }

    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getApplyType() {
        return applyType;
    }

    public void setApplyType(String applyType) {
        this.applyType = applyType;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public Integer getJailBroken() {
        return jailBroken;
    }

    public void setJailBroken(Integer jailBroken) {
        this.jailBroken = jailBroken;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public Integer getIsCopyPackage() {
        return isCopyPackage;
    }

    public void setIsCopyPackage(Integer isCopyPackage) {
        this.isCopyPackage = isCopyPackage;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("appVersion", appVersion)
                .append("channelCode", channelCode)
                .append("mobileModel", mobileModel)
                .append("deviceId", deviceId)
                .append("platform", platform)
                .append("osVersion", osVersion)
                .append("sourceSystem", sourceSystem)
                .append("userKey", userKey)
                .append("ip", ip)
                .append("applyType", applyType)
                .append("channel", channel)
                .append("jailBroken", jailBroken)
                .append("device", device)
                .append("isCopyPackage", isCopyPackage)
                .append("jobId", jobId)
                .toString();
    }
}
