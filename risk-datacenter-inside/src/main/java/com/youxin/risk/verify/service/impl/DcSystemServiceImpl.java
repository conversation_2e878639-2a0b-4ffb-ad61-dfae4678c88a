package com.youxin.risk.verify.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.constants.AppName;
import com.youxin.risk.commons.constants.DcConstant;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.exception.RiskRuntimeException;
import com.youxin.risk.commons.model.datacenter.common.SubmitDataType;
import com.youxin.risk.commons.remote.model.RpcRequest;
import com.youxin.risk.commons.remote.model.RpcResponse;
import com.youxin.risk.commons.remote.model.datacenter.DcRequest;
import com.youxin.risk.commons.remote.model.datacenter.DcRequestService;
import com.youxin.risk.commons.remote.model.datacenter.DcResponse;
import com.youxin.risk.commons.tools.repeat.RepeatResult;
import com.youxin.risk.commons.tools.repeat.RepeatService;
import com.youxin.risk.commons.utils.JacksonUtil;
import com.youxin.risk.commons.utils.LogUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.datacenter.constants.DcRequestType;
import com.youxin.risk.datacenter.service.DcSubmitService;
import com.youxin.risk.metrics.MetricsAPI;
import com.youxin.risk.metrics.enums.MetricsOpType;
import com.youxin.risk.ra.utils.ClassLoaderUtil;
import com.youxin.risk.verify.service.DcSystemService;
import com.youxin.risk.verify.vo.DcReqDataVo;
import com.youxin.risk.verify.vo.DcReqServiceVo;
import com.youxin.risk.verify.vo.DcReqVo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Dc系统服务类
 *
 * <AUTHOR>
@Service
public class DcSystemServiceImpl implements DcSystemService {

    private static Logger LOGGER = LoggerFactory.getLogger(DcSystemServiceImpl.class);

    private static Properties property = ClassLoaderUtil.loadProperties(
            "/verify/risk_dc_system_api.properties",
            DcSystemServiceImpl.class);

    public static final String HOST = property.getProperty("url.base");

    public static final String URL = property.getProperty("risk.submit.dcHandler");

    public static final String RET_CODE_SUCCESS = "07S0000";

    private static Map<String, String> header = new HashMap<>();

    static {
        header.put("Content-Type", "application/json;charset=utf-8");

    }

    @Autowired
    DcSubmitService dcSubmitService;

    @Resource
    private RepeatService repeatService;


    @Override
    public DcReqVo buildDcReq(Map<String, Object> params, SubmitDataType submitDataType, String userKey) {

        DcReqServiceVo serviceVo = new DcReqServiceVo();
        serviceVo.setParams(params);
        serviceVo.setServiceCode(submitDataType.name());

        List<DcReqServiceVo> services = new ArrayList<>();
        services.add(serviceVo);

        DcReqDataVo dataVo = new DcReqDataVo();
        dataVo.setServices(services);
        dataVo.setUserKey(userKey);

        DcReqVo reqVo = new DcReqVo();
        reqVo.setData(dataVo);
        reqVo.setRequestId(UUID.randomUUID().toString());
        return reqVo;
    }

    @Override
    public boolean submitDcData(Object postData) {
        String requestBody = "";
        int retryCount = 0;
        boolean retFlag = false;
        while (retryCount < 3) {
            try {
                requestBody = JSON.toJSONString(postData);
                RpcResponse<DcResponse> dcResponseVo = submitData(requestBody);
                if (!(dcResponseVo != null && DcSystemServiceImpl.RET_CODE_SUCCESS.equals(dcResponseVo.getRetCode()))) {
                    throw new Exception("dc http result illagel ");
                }
                LOGGER.info("submitDcData success, post data={}", requestBody);
                retFlag = true;
                break;
            } catch (Exception e) {
                if (retryCount == 2) {
                    LOGGER.error("submitDcData error, post data={}", requestBody, e);
                } else {
                    LOGGER.warn("submitDcData error, post data={}", requestBody, e);
                }
            }
            retryCount++;
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                LOGGER.error("submitDcData error", e);
            }
        }
        return retFlag;
    }


    /*
     * 将 verify系统 的接口迁移到 dc ，本来是  verify 对 dc 进行http调用，现在变成本地方法调用
     *
     *
     * */
    public RpcResponse<DcResponse> submitData(String message) {

        String requestType = "submit";
        LogUtil.buildAndBindLog();
        RpcRequest<DcRequest> request;
        Stopwatch stopwatch = Stopwatch.createStarted();
        long costTime = 0L;
        RpcResponse<DcResponse> response = null;
        String responseStr = "";
        LoggerProxy.info(" handlerReceivedMessage", LOGGER, "received orginal message, message={}", message);
        try {
            // 生成request对象，包含参数校验，如果参数校验失败，打印请求日志，然后抛出异常
            request = generatorRequest(message, requestType);
        } catch (Exception e) {
            LoggerProxy.error("generatorRequestError", LOGGER, "", e);
            response = LOGGER(e);
            return response;
        } finally {
            LogUtil.unbindLogId();
        }
        // 校验幂等
        RepeatResult repeatResult = repeatService.checkRepeat(AppName.risk_datacenter.value, request.getRequestId(), DcConstant.DC_REQUEST_PROCESSING_EXPIRE_TIME);
        if (repeatResult.getType().name().equals(RepeatResult.RepeatEnum.PROCESSING.name())) {
            response = generatorProcessingResponse();
            responseStr = JacksonUtil.toJson(response);
            LoggerProxy.info("finishedMessage", LOGGER, "finished message,message={},response={}", message, responseStr);
            return response;
        } else if (repeatResult.getType().name().equals(RepeatResult.RepeatEnum.REPEAT.name())) {
            responseStr = repeatResult.getValue();
            LoggerProxy.info("finishedMessage", LOGGER, "message repeat, message={},response={}", message, responseStr);
            return response;
        }
        //真正开始处理请求
        try {
            dcSubmitService.hanlderBatch(request.getData().getServices());
            response = new RpcResponse<>();
            response.setRetCode(RetCodeEnum.SUCCESS.getValue());
            response.setRetMsg(RetCodeEnum.SUCCESS.getRetMsg());
            responseStr = JacksonUtil.toJson(response);

            // 保存幂等信息
            repeatService.setRepeat(AppName.risk_datacenter.value, request.getRequestId(), responseStr, DcConstant.DC_REQUEST_REPEAT_EXPIRE_TIME);
        } catch (Exception e) {
            // 删除幂等
            repeatService.deleteRepeat(AppName.risk_datacenter.value, request.getRequestId());
            response = LOGGER(e);
            responseStr = JacksonUtil.toJson(response);
            LoggerProxy.error("handlerError", LOGGER, "received orginal message, message=" + message + ", responseStr=" + responseStr, e);
        } finally {
            costTime = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
            LoggerProxy.info("finishedMessage", LOGGER, "cost={},message={},response={}", costTime, message, responseStr);
            // 埋点
            Map<String, String> tags = Maps.newHashMap();
            tags.put("retCode", response.getRetCode());
            tags.put("requestType", requestType);
            MetricsAPI.point(DcConstant.DC_CALL_COST_TIME_POINT, tags, costTime, true, MetricsOpType.timer);
        }
        return response;
    }


    private RpcRequest<DcRequest> generatorRequest(String message, String requestType) {
        if (!DcRequestType.submit.name().equals(requestType) && !DcRequestType.query.name().equals(requestType) &&
                !DcRequestType.queryUserBasicInfo.name().equals(requestType)) {
            throw new RiskRuntimeException(RetCodeEnum.ILLEGAL_ARGUMENT, "requestType is not supported");
        }
        if (StringUtils.isBlank(message)) {
            throw new RiskRuntimeException(RetCodeEnum.ILLEGAL_ARGUMENT, "message is blank");
        }
        try {
            RpcRequest<DcRequest> request = JacksonUtil.toObject(message, RpcRequest.class, DcRequest.class);
            if (StringUtils.isBlank(request.getAppName())) {
                throw new RiskRuntimeException(RetCodeEnum.ILLEGAL_ARGUMENT, "appName is blank");
            }
            if (StringUtils.isBlank(request.getRequestId())) {
                throw new RiskRuntimeException(RetCodeEnum.ILLEGAL_ARGUMENT, "requestId is blank");
            }
            if (request.getData() == null) {
                throw new RiskRuntimeException(RetCodeEnum.ILLEGAL_ARGUMENT, "data is null");
            }
            if (CollectionUtils.isEmpty(request.getData().getServices())) {
                throw new RiskRuntimeException(RetCodeEnum.ILLEGAL_ARGUMENT, "request service list is empty");
            }
            Set<String> serviceCodeSet = new HashSet<>();
            for (DcRequestService service : request.getData().getServices()) {
                if (serviceCodeSet.contains(service.getServiceCode())) {
                    throw new RiskRuntimeException(RetCodeEnum.ILLEGAL_ARGUMENT, "service code is duplicated");
                }
                serviceCodeSet.add(service.getServiceCode());
            }
            return request;
        } catch (RiskRuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new RiskRuntimeException(RetCodeEnum.ILLEGAL_ARGUMENT, "parse message to request failed");
        }
    }

    private RpcResponse<DcResponse> generatorProcessingResponse() {
        RpcResponse<DcResponse> response = new RpcResponse<>();
        response.setRetCode(RetCodeEnum.PROCESSING.getValue());
        response.setRetMsg(RetCodeEnum.PROCESSING.getRetMsg());
        return response;
    }

    private RpcResponse<DcResponse> LOGGER(Exception e) {
        RpcResponse<DcResponse> response = new RpcResponse<>();
        if (e instanceof RiskRuntimeException) {
            LoggerProxy.error("riskDcError", LOGGER, "handle message error", e);
            RiskRuntimeException riskRuntimeException = (RiskRuntimeException) e;
            response.setRetCode(riskRuntimeException.getExceptionCode().getValue());
            response.setRetMsg(riskRuntimeException.getMessage());
        } else {
            LoggerProxy.error("unKnownError", LOGGER, "handle message error", e);
            response.setRetCode(RetCodeEnum.FAILED.getValue());
            response.setRetMsg(RetCodeEnum.FAILED.getRetMsg());
        }
        return response;
    }


}
