package com.youxin.risk.verify.schedule.xxljob;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.youxin.apollo.client.NacosClient;
import com.youxin.risk.commons.constants.ApolloNamespace;
import com.youxin.risk.commons.dao.haofenqi.HaoHuanUserMapper;
import com.youxin.risk.commons.dao.verify.VerifyUserLineManagementMapper;
import com.youxin.risk.commons.model.haofenqi.HaoHuanUser;
import com.youxin.risk.commons.model.verify.VerifyUserLineManagement;
import com.youxin.risk.commons.tools.redis.RetryableJedis;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.commons.xxl.job.XxlJobBase;
import com.youxin.risk.verify.sharding.mapper.VerifyUserLineManagementShardingMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2021/01/05
 * @Version 1.0
 */
@Component
public class CheckVerifyUserLineManagementJob implements XxlJobBase {

    private static final Logger logger = LoggerFactory.getLogger(CheckVerifyUserLineManagementJob.class);

    @Resource
    private VerifyUserLineManagementMapper verifyUserLineManagementMapper;


    @Resource
    private VerifyUserLineManagementShardingMapper verifyUserLineManagementShardingMapper;

    @Resource
    private HaoHuanUserMapper haoHuanUserMapper;


    @Resource
    private RetryableJedis retryableJedis;

    //同步到的idkey
    static String userLineManagementCheckIdKey = "userLineManagementIdKey";

    //自定义任务线程池
    private ExecutorService executorService = new ThreadPoolExecutor(10, 30,
            5, TimeUnit.MINUTES, new ArrayBlockingQueue<>(10000), new ExecutorThreadFactory("checkUserLineShardingPool-"), new ThreadPoolExecutor.CallerRunsPolicy());


    @XxlJob(value = "checkVerifyUserLineManagementJob")
    @Override
    public ReturnT<String> execJobHandler(String param) {


        LoggerProxy.info("verifyUserLineManagementCheckStart", logger, "startTime={}", new Date());

        //启动15个线程，每个线程同步1000W的数据
        executorService.execute(new SyncTask(1, 0L));


        LoggerProxy.info("verifyUserLineManagementCheckEnd", logger, "endTime={}", new Date());
        return ReturnT.SUCCESS;
    }


    class SyncTask implements Runnable {


        private Integer taskId;
        private Long minId;

        SyncTask(Integer taskId, Long startId) {
            this.taskId = taskId;
            this.minId = startId;
        }


        @Override
        public void run() {

            LoggerProxy.info("verifyUserLineManagementCheckStart", logger, "startTime={}", new Date());
            long startTime = System.currentTimeMillis();

            Long startId = minId;


            String redisKey = userLineManagementCheckIdKey + "_" + taskId;
            //从redis中取出上次查询到的时间点
            try {
                String taskStr = retryableJedis.get(redisKey);
                if (StringUtils.isNotEmpty(taskStr)) {
                    startId = Long.valueOf(taskStr);
                }
            } catch (Exception ignored) {

            }


            int limit = 100;

            try {
                String limitStr = NacosClient.getByNameSpace(ApolloNamespace.commonSpace, "user.line.management.sharding.check.limit", "100");
                limit = Integer.parseInt(limitStr);
            } catch (NumberFormatException ignored) {
            }

            int successCount = 0;

            try {

                //每次查询100条记录进行校验
                List<HaoHuanUser> userKeyList = haoHuanUserMapper.queryValidUser(startId, limit);


                String userKey = "";

                for (HaoHuanUser user : userKeyList) {
                    try {
                        userKey = user.getSystemUniqueId();
                        String shardingName = VerifyUserLineManagement.getShardingTableName(userKey);


                        List<VerifyUserLineManagement> originalList = verifyUserLineManagementMapper.getCheckVerifyUserLineManagement(userKey);
                        List<VerifyUserLineManagement> shardingList = verifyUserLineManagementShardingMapper.getCheckVerifyUserLineManagement(shardingName, userKey);

                        // 条数不一致，打印出来
                        if (originalList.size() != shardingList.size()) {
                            logger.warn("userLineManagement size not equal userKey={},originalSize={},shardingSize={}", userKey, originalList.size(), shardingList.size());
                            continue;
                        }

                        VerifyUserLineManagement originalRecord;
                        VerifyUserLineManagement shardingRecord;
                        for (int i = 0; i < originalList.size(); i++) {
                            // 原表的记录
                            originalRecord = originalList.get(i);
                            // sharding表的记录
                            shardingRecord = shardingList.get(i);
                            // 如果不相等，打印出来
                            if (!originalRecord.equals(shardingRecord)) {
                                logger.warn("userLineManagement record not equal userKey={},originalId={},shardingId={}", userKey, originalRecord.getId(), shardingRecord.getId());
                                break;
                            }
                        }
                        successCount++;

                    } catch (Exception e) {
                        LoggerProxy.error("checkUserLineManagementShardingError", logger, "error : ", e);
                    } finally {
                        startId = user.getId();
                    }
                }


            } catch (Exception e) {
                LoggerProxy.error("verifyUserLineManagementCheckError", logger, "error : ", e);
            } finally {
                //任务的起始Id更新到redis中
                retryableJedis.set(redisKey, String.valueOf(startId));
            }

            LoggerProxy.info("verifyUserLineManagementCheckSuccess", logger, "taskId={},cost={},successCount={}", taskId, System.currentTimeMillis() - startTime, successCount);
        }
    }


    static class ExecutorThreadFactory implements ThreadFactory {
        private static final AtomicInteger poolNumber = new AtomicInteger(1);
        private final ThreadGroup group;
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;

        ExecutorThreadFactory(String poolName) {
            SecurityManager s = System.getSecurityManager();
            group = (s != null) ? s.getThreadGroup() :
                    Thread.currentThread().getThreadGroup();
            namePrefix = poolName +
                    poolNumber.getAndIncrement() +
                    "-thread-";
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(group, r,
                    namePrefix + threadNumber.getAndIncrement(),
                    0);
            if (t.isDaemon()) {
                t.setDaemon(false);
            }

            if (t.getPriority() != Thread.NORM_PRIORITY) {
                t.setPriority(Thread.NORM_PRIORITY);
            }
            return t;
        }
    }


}
