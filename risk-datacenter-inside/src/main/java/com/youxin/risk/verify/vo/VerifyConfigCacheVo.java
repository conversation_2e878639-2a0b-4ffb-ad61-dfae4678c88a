package com.youxin.risk.verify.vo;


import com.youxin.risk.commons.model.verify.VerifyConfig;
import com.youxin.risk.commons.utils.DateUtil;

import java.util.Date;

public class VerifyConfigCacheVo {

    private VerifyConfig cacheConfig;

    private Date cacheDate;

    private Integer cacheMinute;

    public VerifyConfigCacheVo(Integer cacheMinute, VerifyConfig cacheConfig) {
        this.cacheConfig = cacheConfig;
        this.cacheMinute = cacheMinute;
        this.cacheDate = new Date();
    }

    public VerifyConfig getCacheConfig() {
        return cacheConfig;
    }

    public void setCacheConfig(VerifyConfig cacheConfig) {
        this.cacheConfig = cacheConfig;
    }

    public boolean isExpire() {
        Date now = new Date();
        if (DateUtil.differenceMinutes(now, cacheDate) >= cacheMinute) {
            return true;
        }
        return false;
    }
}
