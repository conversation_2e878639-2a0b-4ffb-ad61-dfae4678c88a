package com.youxin.risk.verify.schedule;

import com.alibaba.fastjson.JSONObject;
import com.weicai.caesar.CaesarUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.youxin.risk.commons.dao.datacenter.DcSubmitIdcardMapper;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.MaskUtils;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.commons.xxl.job.XxlJobBase;
import com.youxin.risk.datacenter.mapper.DcSubmitReCertificationIdcardMapper;
import com.youxin.risk.ra.mapper.SubmitIdcardMapper;
import com.youxin.risk.verify.mapper.VerifySubmitIdcardMapper;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class MaskIdCardJob implements XxlJobBase {

    private static final Logger LOG = LoggerFactory.getLogger(MaskIdCardJob.class);

    @Autowired
    private DcSubmitReCertificationIdcardMapper dcSubmitReCertificationIdcardMapper;

    @Autowired
    private DcSubmitIdcardMapper dcSubmitIdcardMapper;

    @Autowired
    private VerifySubmitIdcardMapper verifySubmitIdcardMapper;

    @Autowired
    private SubmitIdcardMapper submitIdcardMapper;

    @XxlJob(value = "maskIdCardJob")
    @Override
    public ReturnT<String> execJobHandler(String param) {
        JSONObject paramObj = JSONObject.parseObject(param);
        Long minId = paramObj.getLong("minId");
        long maxId = paramObj.getLong("maxId");
        int pageSize = paramObj.getIntValue("pageSize");
        String typeStr = paramObj.getString("type");
        LOG.info("maskIdCardJob execute start, type: {}", typeStr);
        int index = minId.intValue();
        if ("dc".equals(typeStr)) {
            index = dealDc(index, maxId, pageSize);
        } else if ("dcRe".equals(typeStr)) {
            index = dealDcRe(index, maxId, pageSize);
        } else if ("verify".equals(typeStr)) {
            index = dealVerify(index, maxId, pageSize);
        } else {
            index = dealRa(index, maxId, pageSize);
        }
        LOG.info("maskIdCardJob execute end, index: {}, type: {}", index, typeStr);
        return ReturnT.SUCCESS;
    }

    private int dealDc(int index, long maxId, int pageSize) {
        Map<String, Object> paramMap = new HashMap<>();
        while (index <= maxId) {
            paramMap.put("start", index);
            paramMap.put("end", index + pageSize);
            List<Map<String, Object>> registers = dcSubmitIdcardMapper.queryById(paramMap);
            if (CollectionUtils.isNotEmpty(registers)) {
                for (Map<String, Object> register : registers) {
                    String idcardNumber = (String) register.get("idcard_number");
                    String idcardName = (String) register.get("idcard_name");
                    try {
                        if (StringUtils.isBlank(idcardNumber) && StringUtils.isBlank(idcardName)) {
                            continue;
                        }
                        if (!CaesarUtil.isEncrypted(idcardNumber) || !CaesarUtil.isEncrypted(idcardName)) {
                            idcardNumber = MaskUtils.maskValue(idcardNumber);
                            idcardName = MaskUtils.maskValue(idcardName);
                            register.put("idcard_number", idcardNumber);
                            register.put("idcard_name", idcardName);
                            register.put("id", register.get("id"));
                            dcSubmitIdcardMapper.updateById(register);
                            LoggerProxy.info("maskIdCardJob", LOG, "updateById, id: {}, type: dc", register.get("id"));
                            Thread.sleep(1);
                        }
                    } catch (Exception e) {
                        LoggerProxy.error("maskIdCardJob", LOG, "处理失败, id: {}, type: dc", register.get("id"), e);
                    }
                }
            }
            index = index + pageSize;
        }
        return index;
    }

    private int dealDcRe(int index, long maxId, int pageSize) {
        Map<String, Object> paramMap = new HashMap<>();
        while (index <= maxId) {
            paramMap.put("start", index);
            paramMap.put("end", index + pageSize);
            List<Map<String, Object>> registers = dcSubmitReCertificationIdcardMapper.queryById(paramMap);
            if (CollectionUtils.isNotEmpty(registers)) {
                for (Map<String, Object> register : registers) {
                    String idcardNumber = (String) register.get("idcard_number");
                    String idcardName = (String) register.get("idcard_name");
                    try {
                        if (StringUtils.isBlank(idcardNumber) && StringUtils.isBlank(idcardName)) {
                            continue;
                        }
                        if (!CaesarUtil.isEncrypted(idcardNumber) || !CaesarUtil.isEncrypted(idcardName)) {
                            idcardNumber = MaskUtils.maskValue(idcardNumber);
                            idcardName = MaskUtils.maskValue(idcardName);
                            register.put("idcard_number", idcardNumber);
                            register.put("idcard_name", idcardName);
                            register.put("id", register.get("id"));
                            dcSubmitReCertificationIdcardMapper.updateById(register);
                            LoggerProxy.info("maskIdCardJob", LOG, "updateById, id: {}, type: dcRe", register.get("id"));
                            Thread.sleep(1);
                        }
                    } catch (Exception e) {
                        LoggerProxy.error("maskIdCardJob", LOG, "处理失败, id: {}, type: dcRe", register.get("id"), e);
                    }
                }
            }
            index = index + pageSize;
        }
        return index;
    }

    private int dealVerify(int index, long maxId, int pageSize) {
        Map<String, Object> paramMap = new HashMap<>();
        while (index <= maxId) {
            paramMap.put("start", index);
            paramMap.put("end", index + pageSize);
            List<Map<String, Object>> registers = verifySubmitIdcardMapper.queryById(paramMap);
            if (CollectionUtils.isNotEmpty(registers)) {
                for (Map<String, Object> register : registers) {
                    String idcardNumber = (String) register.get("idcard_number");
                    String idcardName = (String) register.get("idcard_name");
                    try {
                        if (StringUtils.isBlank(idcardNumber) && StringUtils.isBlank(idcardName)) {
                            continue;
                        }
                        if (!CaesarUtil.isEncrypted(idcardNumber) || !CaesarUtil.isEncrypted(idcardName)) {
                            idcardNumber = MaskUtils.maskValue(idcardNumber);
                            idcardName = MaskUtils.maskValue(idcardName);
                            register.put("idcard_number", idcardNumber);
                            register.put("idcard_name", idcardName);
                            register.put("id", register.get("id"));
                            verifySubmitIdcardMapper.updateById(register);
                            LoggerProxy.info("maskIdCardJob", LOG, "updateById, id: {}, type: verify", register.get("id"));
                            Thread.sleep(1);
                        }
                    } catch (Exception e) {
                        LoggerProxy.error("maskIdCardJob", LOG, "处理失败, id: {}, type: verify", register.get("id"), e);
                    }
                }
            }
            index = index + pageSize;
        }
        return index;
    }

    private int dealRa(int index, long maxId, int pageSize) {
        Map<String, Object> paramMap = new HashMap<>();
        while (index <= maxId) {
            paramMap.put("start", index);
            paramMap.put("end", index + pageSize);
            List<Map<String, Object>> registers = submitIdcardMapper.queryById(paramMap);
            if (CollectionUtils.isNotEmpty(registers)) {
                for (Map<String, Object> register : registers) {
                    String idcardNumber = (String) register.get("idcard_number");
                    String idcardName = (String) register.get("idcard_name");
                    try {
                        if (StringUtils.isBlank(idcardNumber) && StringUtils.isBlank(idcardName)) {
                            continue;
                        }
                        if (!CaesarUtil.isEncrypted(idcardNumber) || !CaesarUtil.isEncrypted(idcardName)) {
                            idcardNumber = MaskUtils.maskValue(idcardNumber);
                            idcardName = MaskUtils.maskValue(idcardName);
                            register.put("idcard_number", idcardNumber);
                            register.put("idcard_name", idcardName);
                            register.put("id", register.get("id"));
                            submitIdcardMapper.updateById(register);
                            LoggerProxy.info("maskIdCardJob", LOG, "updateById, id: {}, type: ra", register.get("id"));
                            Thread.sleep(1);
                        }
                    } catch (Exception e) {
                        LoggerProxy.error("maskIdCardJob", LOG, "处理失败, id: {}, type: ra", register.get("id"), e);
                    }
                }
            }
            index = index + pageSize;
        }
        return index;
    }

}
