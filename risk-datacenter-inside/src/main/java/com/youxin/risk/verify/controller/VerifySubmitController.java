package com.youxin.risk.verify.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.dao.datacenter.DcSubmitRegisterMapper;
import com.youxin.risk.commons.model.datacenter.DcSubmitRegister;
import com.youxin.risk.commons.model.datacenter.common.OperationType;
import com.youxin.risk.commons.utils.JsonUtils;
import com.youxin.risk.datacenter.pojo.JsonResultVo;
import com.youxin.risk.datacenter.service.SubmitRegisterService;
import com.youxin.risk.ra.utils.StringUtils;
import com.youxin.risk.verify.constants.VerifyChannelConstants;
import com.youxin.risk.verify.model.VerifySubmitXwBank;
import com.youxin.risk.verify.service.VerifySubmitService;
import com.youxin.risk.verify.utils.ApiCheck;
import com.youxin.risk.verify.utils.ValidCodeUtil;
import com.youxin.risk.verify.vo.*;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Controller
@RequestMapping(value = "/submit")
public class VerifySubmitController extends BaseController {

    private static final Logger LOG = LoggerFactory.getLogger(VerifySubmitController.class);

    @Autowired
    @Qualifier("verifySubmitServImpl")
    private VerifySubmitService verifySubmitService;
    @Autowired
    private SubmitRegisterService submitRegisterService;

    @RequestMapping(value = "/register")
    @ResponseBody
    public JsonResultVo submitRegister(@RequestBody JSONObject param) {

        try {
            LOG.info("submit register : [{}]", param);
            VerifySubmitRegisterVo registerVo = JSON.toJavaObject(param, VerifySubmitRegisterVo.class);
            VerifyChannelConstants channel = this.checkChannelCode(registerVo);
            if (channel == VerifyChannelConstants.PAY_DAY_LOAN) {
                List<String> nullParamList = ApiCheck.checkNullParameters(registerVo);
                if (nullParamList.size() != 0) {
                    LOG.info("Submit Register has null param [{}]", nullParamList);
                    return JsonResultVo.error().addData("missingParameters", nullParamList);
                }
                List<String> enumParamList = ApiCheck.checkEnumParameters(registerVo);
                if (enumParamList.size() != 0) {
                    LOG.info("register has wrong enum param [{}]", enumParamList);
                    return JsonResultVo.error().addData("enumErrorParameters", enumParamList);
                }
            }

            verifySubmitService.submitRegister(registerVo);

            return JsonResultVo.success();
        } catch (Exception e) {
            LOG.error("submitRegister error", e);
            return JsonResultVo.error();
        }

    }


    @RequestMapping(value = "/update/register")
    @ResponseBody
    public JsonResultVo updateRegister(@RequestBody JSONObject param) {

        try {
            LOG.info("submit update register : [{}]", param);
            VerifySubmitRegisterVo registerVo = JSON.toJavaObject(param, VerifySubmitRegisterVo.class);

            String userKey = registerVo.getUserKey();
            DcSubmitRegister sr = submitRegisterService.getByUserKey(userKey, null);
            if(sr == null){
                return JsonResultVo.error(JsonResultVo.ERROR, "can't find old info by userKey");
            }

            registerVo.setOldMobile(sr.getMobile());

            if(StringUtils.isEmpty(registerVo.getTongdunFingerprint())){
                registerVo.setTongdunFingerprint(sr.getTongdunFingerprint());
            }
            if(StringUtils.isEmpty(registerVo.getIdfa())){
                registerVo.setIdfa(sr.getIdfa());
            }
            if(StringUtils.isEmpty(registerVo.getImei())){
                registerVo.setImei(sr.getImei());
            }
            //operation_log表中IP为非空字段，如果是cms修改手机号这个ip一定没有，需要给一个默认值
            if(StringUtils.isEmpty(registerVo.getIp())){
                registerVo.setIp("0.0.0.0");
            }

            VerifyChannelConstants channel = this.checkChannelCode(registerVo);
            if (channel == VerifyChannelConstants.PAY_DAY_LOAN) {
                List<String> nullParamList = ApiCheck.checkNullParameters(registerVo);
                if (nullParamList.size() != 0) {
                    LOG.info("update Register has null param [{}]", nullParamList);
                    return JsonResultVo.error().addData("missingParameters", nullParamList);
                }
                List<String> enumParamList = ApiCheck.checkEnumParameters(registerVo);
                if (enumParamList.size() != 0) {
                    LOG.info("update register has wrong enum param [{}]", enumParamList);
                    return JsonResultVo.error().addData("enumErrorParameters", enumParamList);
                }
            }

            verifySubmitService.submitRegister(registerVo);

            return JsonResultVo.success();
        } catch (Exception e) {
            LOG.error("submitUpdateRegister error", e);
            return JsonResultVo.error();
        }

    }



    @RequestMapping(value = "/idcard")
    @ResponseBody
    public JsonResultVo submitIdcard(@RequestBody JSONObject param) {

        try {
            LOG.info("submit idcard : [{}]", param);
            VerifySubmitIdcardVo idcardVo = JSON.toJavaObject(param, VerifySubmitIdcardVo.class);
            VerifyChannelConstants channel = this.checkChannelCode(idcardVo);
            if (channel == VerifyChannelConstants.PAY_DAY_LOAN) {
                List<String> nullParamList = ApiCheck.checkNullParameters(idcardVo);
                if (nullParamList.size() != 0) {
                    LOG.info("id card has null param [{}]", nullParamList);
                    return JsonResultVo.error().addData("missingParameters", nullParamList);
                }
                List<String> enumParamList = ApiCheck.checkEnumParameters(idcardVo);
                if (enumParamList.size() != 0) {
                    LOG.info("idcard has wrong enum param [{}]", enumParamList);
                    return JsonResultVo.error().addData("enumErrorParameters", enumParamList);
                }
                if (idcardVo.getPidAuth() && (idcardVo.getFaceScore() == null || idcardVo.getFaceThreshold() == null)) {
                    if (idcardVo.getFaceScore() == null) {
                        nullParamList.add("faceScore");
                    }
                    if (idcardVo.getFaceThreshold() == null) {
                        nullParamList.add("saceThreshold");
                    }
                    LOG.info("null param", enumParamList);
                    return JsonResultVo.error().addData("missingParameters", enumParamList);
                }
            }

            //check idcard_number and idcard_name
            if (StringUtils.isBlank(idcardVo.getIdcardNumber())) {
                return JsonResultVo.error().addData("missingParameters", "idcardNumber");
            }
            if (!ValidCodeUtil.validateIdCard(idcardVo.getIdcardNumber())) {
                LOG.warn("idcardNumber is invalid,userkey={},idno={}", idcardVo.getUserKey(), idcardVo.getIdcardNumber());
                return JsonResultVo.error().addData("idcardNumberInvalid", "idcardNumber");
            }

            if (StringUtils.isBlank(idcardVo.getIdcardName())) {
                return JsonResultVo.error().addData("missingParameters", "idcardName");
            }

            verifySubmitService.submitIdcard(idcardVo);

            return JsonResultVo.success().addData("logId", 1);
        } catch (Exception e) {
            LOG.error("idcard error", e);
            return JsonResultVo.error();
        }
    }

    @RequestMapping(value = "/address")
    @ResponseBody
    public JsonResultVo submitAddress(@RequestBody JSONObject param) {

        try {
            LOG.info("submit address : [{}]", param);
            VerifySubmitAddressVo addressVo = JSON.toJavaObject(param, VerifySubmitAddressVo.class);

            verifySubmitService.submitAddress(addressVo);

            return JsonResultVo.success();
        } catch (Exception e) {
            LOG.error("address error", e);
            return JsonResultVo.error();
        }
    }

    @RequestMapping(value = "/job")
    @ResponseBody
    public JsonResultVo submitJob(@RequestBody JSONObject param) {

        try {
            LOG.info("submit job : [{}]", param);
            VerifySubmitJobVo jobVo = JSON.toJavaObject(param, VerifySubmitJobVo.class);
            VerifyChannelConstants channel = this.checkChannelCode(jobVo);
            if (channel == VerifyChannelConstants.PAY_DAY_LOAN) {
                List<String> nullParamList = ApiCheck.checkNullParameters(jobVo);
                if (nullParamList.size() != 0) {
                    LOG.info("job has null param [{}]", nullParamList);
                    return JsonResultVo.error().addData("missingParameters", nullParamList);
                }
                List<String> enumParamList = ApiCheck.checkEnumParameters(jobVo);
                if (enumParamList.size() != 0) {
                    LOG.info("job has wrong enum param [{}]", enumParamList);
                    return JsonResultVo.error().addData("enumErrorParameters", enumParamList);
                }
            }
            verifySubmitService.submitJob(jobVo);

            return JsonResultVo.success();
        } catch (Exception e) {
            LOG.error("job error", e);
            return JsonResultVo.error();
        }
    }

    @RequestMapping(value = "/contact")
    @ResponseBody
    public JsonResultVo submitContact(@RequestBody JSONObject param) {

        try {
            LOG.info("submit contact : [{}]", param);
            VerifySubmitContactVo contactVo = JSON.toJavaObject(param, VerifySubmitContactVo.class);
            VerifyChannelConstants channel = this.checkChannelCode(contactVo);
            if (channel == VerifyChannelConstants.PAY_DAY_LOAN) {
                List<String> nullParamList = ApiCheck.checkNullParameters(contactVo);
                if (nullParamList.size() != 0) {
                    LOG.info("contact has null param [{}]", nullParamList);
                    return JsonResultVo.error().addData("missingParameters", nullParamList);
                }
                List<String> enumParamList = ApiCheck.checkEnumParameters(contactVo);
                if (enumParamList.size() != 0) {
                    LOG.info("contact has wrong enum param [{}]", enumParamList);
                    return JsonResultVo.error().addData("enumErrorParameters", enumParamList);
                }
            }
            verifySubmitService.submitContact(contactVo);

            return JsonResultVo.success();
        } catch (Exception e) {
            LOG.info("submit contact : [{}]", param);
            LOG.error("contact error", e);
            return JsonResultVo.error();
        }
    }


    @RequestMapping(value = "/phoneBook")
    @ResponseBody
    public JsonResultVo submitPhoneBook(@RequestBody JSONObject param) {

        try {
            LOG.info("submit phoneBook : [{}]", param);
            VerifySubmitPhoneBookVo phoneBookVo = JSON.toJavaObject(param, VerifySubmitPhoneBookVo.class);
            if (StringUtils.isBlank(phoneBookVo.getPhoneBookJobId())) {
                LOG.error("phonebookJobId is blank,param={}", param);
                return JsonResultVo.error().addData("phoneBookJobId", "phoneBookJobId is blank");
            }
            VerifyChannelConstants channel = this.checkChannelCode(phoneBookVo);
            if (channel == VerifyChannelConstants.PAY_DAY_LOAN) {
                List<String> nullParamList = ApiCheck.checkNullParameters(phoneBookVo);
                if (nullParamList.size() != 0) {
                    LOG.info("phone book has null param [{}]", nullParamList);
                    return JsonResultVo.error().addData("missingParameters", nullParamList);
                }
                List<String> enumParamList = ApiCheck.checkEnumParameters(phoneBookVo);
                if (enumParamList.size() != 0) {
                    LOG.info("phone book has wrong enum param [{}]", enumParamList);
                    return JsonResultVo.error().addData("enumErrorParameters", enumParamList);
                }
            }
            verifySubmitService.submitPhoneBook(phoneBookVo);

            return JsonResultVo.success();
        } catch (Exception e) {
            LOG.info("submit phone book : [{}]", param);
            LOG.error("phone book error", e);
            return JsonResultVo.error();
        }
    }

    @RequestMapping(value = "/bankCard")
    @ResponseBody
    public JsonResultVo submitBankCard(@RequestBody JSONObject param) {

        try {
            LOG.info("submit bankcard : [{}]", param);
            VerifySubmitBankCardVo bankCardVo = JSON.toJavaObject(param, VerifySubmitBankCardVo.class);
            VerifyChannelConstants channel = this.checkChannelCode(bankCardVo);
            if (channel == VerifyChannelConstants.PAY_DAY_LOAN) {
                List<String> nullParamList = ApiCheck.checkNullParameters(bankCardVo);
                if (nullParamList.size() != 0) {
                    LOG.info("bank card has null param [{}]", nullParamList);
                    return JsonResultVo.error().addData("missingParameters", nullParamList);
                }
                List<String> enumParamList = ApiCheck.checkEnumParameters(bankCardVo);
                if (enumParamList.size() != 0) {
                    LOG.info("bank card has wrong enum param [{}]", enumParamList);
                    return JsonResultVo.error().addData("enumErrorParameters", enumParamList);
                }
            }
            verifySubmitService.submitBankCard(bankCardVo);

            return JsonResultVo.success();
        } catch (Exception e) {
            LOG.error("bank card error", e);
            return JsonResultVo.error();
        }
    }


    @RequestMapping(value = "/plist")
    @ResponseBody
    public JsonResultVo submitPlist(@RequestBody JSONObject param) {

        try {
            String logId = UUID.randomUUID().toString();
            LOG.info("logId={},submit plist : [{}]", logId, param);

            //若userKey为空，直接返回
            if (StringUtils.isBlank(param.getString("userKey"))) {
                return JsonResultVo.error().addData("missingParameters", "userKey");
            }

            VerifySubmitPlistVo plistVo = JSON.toJavaObject(param, VerifySubmitPlistVo.class);

            // 过滤emoji表情
            filterPlistEmoji(plistVo);
            VerifyChannelConstants channel = this.checkChannelCode(plistVo);
            if (channel == VerifyChannelConstants.PAY_DAY_LOAN) {
                List<String> nullParamList = ApiCheck.checkNullParameters(plistVo);
                if (nullParamList.size() != 0) {
                    LOG.info("plist has null param [{}]", nullParamList);
                    return JsonResultVo.error().addData("missingParameters", nullParamList);
                }
                List<String> enumParamList = ApiCheck.checkEnumParameters(plistVo);
                if (enumParamList.size() != 0) {
                    LOG.info("plist has wrong enum param [{}]", enumParamList);
                    return JsonResultVo.error().addData("enumErrorParameters", enumParamList);
                }
            }
            verifySubmitService.submitPlist(plistVo);

            return JsonResultVo.success();
        } catch (Exception e) {
            LOG.error("plist error", e);
            return JsonResultVo.error();
        }
    }

    private boolean filterPlistEmoji(VerifySubmitPlistVo plistVo) {

        if (CollectionUtils.isEmpty(plistVo.getPlist())) {
            return false;
        }

        boolean containsEmoji = false;
        for (VerifySubmitPlistDetailsVo item : plistVo.getPlist()) {
            String originalAppName = item.getAppName();
            if (StringUtils.isBlank(originalAppName)) {
                continue;
            }
            item.setAppName(StringUtils.filterEmoji(item.getAppName()));

            if (!Objects.equals(item.getAppName(), originalAppName)) {
                LOG.info("filter emoji for originalAppName: {}, appName: {}", originalAppName, item.getAppName());
                containsEmoji = true;
            }
        }
        return containsEmoji;
    }


    @RequestMapping("/loansInfo")
    @ResponseBody
    public JsonResultVo submitLoansInfo(@RequestBody JSONObject json) {
        try {
            LOG.info("submit loansInfo : [{}]", json);
            VerifySubmitLoansInfoVo vo = JSON.toJavaObject(json, VerifySubmitLoansInfoVo.class);

            verifySubmitService.saveLoansInfo(vo);

            return JsonResultVo.success();
        } catch (Exception e) {
            LOG.error("loansInfo error ", e);
            return JsonResultVo.error();
        }
    }

    @RequestMapping(value = "/sms")
    @ResponseBody
    public JsonResultVo submitSMS(@RequestBody VerifyCommonVo commonVo) {

        LOG.info("短信请求 {}", JsonUtils.toJson(commonVo));
        try {
            verifySubmitService.submitVerifyCommon(commonVo, OperationType.SMS);

            return JsonResultVo.success();
        } catch (Exception e) {
            LOG.info("submit sms error : [{}]", JsonUtils.toJson(commonVo), e);
            return JsonResultVo.error();
        }
    }

    @RequestMapping(value = "/callRecord")
    @ResponseBody
    public JsonResultVo submitCallRecord(@RequestBody VerifyCommonVo commonVo) {

        LOG.info("通话记录请求 {}", JsonUtils.toJson(commonVo));
        try {

            verifySubmitService.submitVerifyCommon(commonVo, OperationType.CALL_RECORD);

            return JsonResultVo.success();
        } catch (Exception e) {
            LOG.info("submit callRecord error : [{}]", JsonUtils.toJson(commonVo), e);
            return JsonResultVo.error();
        }
    }


    @RequestMapping(value = "/xwbank")
    @ResponseBody
    public JsonResultVo submitXwBank(@RequestBody VerifySubmitXwBank xwBank) {

        try {
            LOG.info("submit xwBank : {}", JsonUtils.toJson(xwBank));
            xwBank.setVersion(0);

            verifySubmitService.submitXwBank(xwBank);

            return JsonResultVo.success();
        } catch (Exception e) {
            LOG.error("submit socialContact error", e);
            return JsonResultVo.error();
        }
    }

    @RequestMapping(value = "/calendar")
    @ResponseBody
    public JsonResultVo submitCalendar(@RequestBody VerifyCommonVo commonVo) {

        LOG.info("日历请求 {}", JsonUtils.toJson(commonVo));
        try {

            verifySubmitService.submitVerifyCommon(commonVo, OperationType.CALENDAR);

            return JsonResultVo.success();
        } catch (Exception e) {
            LOG.info("submit calendar error : [{}]", JsonUtils.toJson(commonVo), e);
            return JsonResultVo.error();
        }
    }



    @Autowired
    DcSubmitRegisterMapper dcSubmitRegisterMapper;

    /**
     * 初始化old_mobile字段
     *      回溯数据用，用后需下线
     * @param param
     * @return
     */
    @RequestMapping(value = "/init/updateMobile")
    @ResponseBody
    public JsonResultVo initOldMobile(@RequestBody JSONObject param) {

        try {

            List<String> logIdList = JsonUtils.jsonToList(param.getString("logIdList"), String.class);
            for(String logId : logIdList){
                LOG.info("更新中---  logId={}", logId);
                dcSubmitRegisterMapper.initOldMobile(logId);
            }
            return JsonResultVo.success();
        } catch (Exception e) {
            LOG.error("submitUpdateRegister error", e);
            return JsonResultVo.error();
        }

    }

}

