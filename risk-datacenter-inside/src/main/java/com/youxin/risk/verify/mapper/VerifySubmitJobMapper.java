package com.youxin.risk.verify.mapper;

import com.youxin.risk.verify.model.VerifySubmitJob;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface VerifySubmitJobMapper {

     int insert(VerifySubmitJob verifySubmitJob);

     @Deprecated
     VerifySubmitJob findLastSubmitInfoByUserKey(String userKey);

     VerifySubmitJob findLastSubmitInfoByUserKeyNoId(String userKey);

     // 修数用的方法，勿使用
     List<Map<String, Object>> queryById(Map<String, Object> map);

     // 修数用的方法，勿使用
     int updateById(Map<String, Object> map);
}
