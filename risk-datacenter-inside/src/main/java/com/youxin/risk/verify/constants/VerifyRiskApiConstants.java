package com.youxin.risk.verify.constants;

import com.youxin.risk.verify.utils.ClassLoaderUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

public class VerifyRiskApiConstants {
    private static Properties property = ClassLoaderUtil.loadProperties(
        "config/api/risk_api.properties", VerifyRiskApiConstants.class);

    public static Map<String, String> header = new HashMap<>();
    static {
        header.put("Content-Type", "application/json;charset=utf-8");
    }
    // submit
    public static String riskUrl = property.getProperty("risk.url");
    public static String submitRegisterUrl = property
        .getProperty("risk.submit.register");
    public static String submitIdcardUrl = property
        .getProperty("risk.submit.idcard");
    public static String submitAddressUrl = property
        .getProperty("risk.submit.address");
    public static String submitJobUrl = property.getProperty("risk.submit.job");
    public static String submitContactUrl = property
        .getProperty("risk.submit.contact");
    public static String submitMobileOperatorUrl = property
        .getProperty("risk.submit.mobileoperator");
    public static String submitBankCardUrl = property
        .getProperty("risk.submit.bankcard");
    public static String submitCreditcardUrl = property
            .getProperty("risk.submit.creditcard");
    public static String submitTaobaoUrl = property
        .getProperty("risk.submit.taobao");
    public static String submitEmailUrl = property
        .getProperty("risk.submit.email");
    public static String submitLocateUrl = property
        .getProperty("risk.submit.locate");
    public static String submitPhoneBookUrl = property
        .getProperty("risk.submit.phonebook");
    public static String postRequestUrl = property
        .getProperty("risk.verify.request");
    public static String reportUrl = property.getProperty("risk.verify.report");

    public static String reportThirdPartyUrl = property.getProperty("risk.verify.thirdparty.report");

    public static String reqByStepUrl = property.getProperty("risk.verify.requeststep");

    public static String reqSupplemnetaryUrl = property.getProperty("risk.verify.supplementary");

    public static String submitPlistUrl = property
        .getProperty("risk.submit.plist");

    public static String thirdPartyUrl = property
        .getProperty("risk.thridparty.data");

    public static String submitCallRecordUrl = property
            .getProperty("risk.submit.callrecord");

    public static String submitSmsUrl = property
            .getProperty("risk.submit.sms");

    public static String submitAlipayUrl = property.getProperty("risk.submit.alipay");

    public static String submitUnionpayUrl = property.getProperty("risk.submit.unionpay");

    public static String submitLoansInfoUrl = property.getProperty("risk.submit.loansinfo");

    public static String submitCreditCardBillUrl = property.getProperty("risk.submit.creditCardBill");

    public static final String submitXWBankURL = "/submit/xwBank";

    public static final String submitCalendarURL = "/submit/calendar";

    public static final String submitDfxkZhimaURL = "/submit/dfxkZhima";

    public static final String queryJobIdURL = "/thirdPartyFetch/jobid";

    public static final String queryThirdMiddURL = "/thirdPartyFetch/getMiddleThirdData";

    public static final String querySingleThirdURL = "/thirdPartyFetch/getSingleLineThirdData";

    public static final String querySyncMiddURL = "/thirdPartyFetch/getMiddleSyncData";

    public static final String submitLendAuditURL = "/submit/lendRequest";

    public static final String submitGraphMsgURL = "/submit/graphRequest";

    public static final String preThirdMiddURL = "/thirdPartyFetch/preMiddleThirdData";

    public static String submitDcDataUrl = property.getProperty("risk.submit.dcHandler");
}
