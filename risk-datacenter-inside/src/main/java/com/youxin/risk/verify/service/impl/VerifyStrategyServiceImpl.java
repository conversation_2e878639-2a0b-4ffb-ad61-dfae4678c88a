package com.youxin.risk.verify.service.impl;

import com.youxin.risk.verify.mapper.VerifyStrategyCodeMapper;
import com.youxin.risk.verify.mapper.VerifyStrategyMapper;
import com.youxin.risk.verify.model.VerifyStrategy;
import com.youxin.risk.verify.model.VerifyStrategyCode;
import com.youxin.risk.verify.service.VerifyStrategyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;
import java.util.Date;

@Service
public class VerifyStrategyServiceImpl implements VerifyStrategyService {

    private static final Logger LOG = LoggerFactory.getLogger(VerifyStrategyServiceImpl.class);

    private static final String DEFAULT_VERSION = "1.0-SNAPSHOT";

    @Autowired
    private VerifyStrategyMapper verifyStrategyMapper;

    @Autowired
    private VerifyStrategyCodeMapper verifyStrategyCodeMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveStrategy(String code, String remark, String type, String userName, String modelId, Boolean riskApproval, Boolean businessApproval, Boolean managerApproval, int id) {
        // 加密
        String encryptCode = code;
        VerifyStrategyCode strategyCode = new VerifyStrategyCode();
        strategyCode.setId(id);
        strategyCode.setStrategyCode(encryptCode);
        strategyCode.setVersion(0);
        this.verifyStrategyCodeMapper.insert(strategyCode);
        // 存储
        VerifyStrategy strategy = new VerifyStrategy();
        strategy.setId(id);
        strategy.setOperator(userName);
        strategy.setType(type);
        strategy.setRemark(remark);
        strategy.setStrategyVersion(DEFAULT_VERSION);
        strategy.setStrategyCodeId(strategyCode.getId());
        strategy.setModelId(modelId);
        strategy.setRiskApproval(riskApproval);
        strategy.setBusinessApproval(businessApproval);
        strategy.setManagerApproval(managerApproval);
        strategy.setSequenceNo(this.getSequenceNo(type));
        strategy.setVersion(0);
        this.verifyStrategyMapper.insert(strategy);
        LOG.info("strategyId:{}, strategyCodeId:{}", strategy.getId(), strategyCode.getId());
    }

    @Override
    public int getSequenceNo(String type) {
        Date today = getToday();
        VerifyStrategy strategy = this.verifyStrategyMapper.getTodayLatestStrategy(type, today);
        if (strategy == null || strategy.getSequenceNo() == null) {
            return 1;
        }
        return 1 + strategy.getSequenceNo();
    }

    public static Date getToday() {
        Date date = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

}
