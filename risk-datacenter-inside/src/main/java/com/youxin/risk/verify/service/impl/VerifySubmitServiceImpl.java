package com.youxin.risk.verify.service.impl;

import com.alibaba.fastjson.JSON;
import com.weicai.caesar.CaesarUtil;
import com.youxin.apollo.client.NacosClient;
import com.youxin.risk.commons.constants.ApolloNamespace;
import com.youxin.risk.commons.dao.verify.VerifySubmitMapper;
import com.youxin.risk.commons.model.datacenter.common.OperationType;
import com.youxin.risk.commons.model.datacenter.common.SubmitDataType;
import com.youxin.risk.commons.model.verify.VerifySubmit;
import com.youxin.risk.commons.utils.JsonUtils;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.ObjectTransferUtils;
import com.youxin.risk.commons.vo.datavo.SubmitCommonVo;
import com.youxin.risk.ra.enums.ApplyType;
import com.youxin.risk.ra.enums.DataRequestTaskType;
import com.youxin.risk.ra.model.RaXwBankRequest;
import com.youxin.risk.ra.service.RaSubmitService;
import com.youxin.risk.ra.service.RaXwBankService;
import com.youxin.risk.ra.vo.*;
import com.youxin.risk.verify.delayqueue.SubmitBankCardDelayHandler;
import com.youxin.risk.verify.delayqueue.SubmitDelayService;
import com.youxin.risk.verify.delayqueue.SubmitIdCardDelayHandler;
import com.youxin.risk.verify.delayqueue.SubmitRegisterDelayHandler;
import com.youxin.risk.verify.model.*;
import com.youxin.risk.verify.service.*;
import com.youxin.risk.verify.vo.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

@Service("verifySubmitServImpl")
public class VerifySubmitServiceImpl implements VerifySubmitService {

    private static final Logger LOG = LoggerFactory.getLogger(VerifySubmitServiceImpl.class);

    @Autowired
    private OperationLogService operationLogService;

    @Autowired
    private VerifySubmitRegisterService verifySubmitRegisterService;

    @Autowired
    private RaSubmitService raSubmitService;

    @Autowired
    private VerifySubmitAddressService verifySubmitAddressService;
    @Autowired
    private VerifySubmitJobService verifySubmitJobService;
    @Autowired
    private VerifySubmitIdcardService verifySubmitIdcardService;
    @Autowired
    private VerifySubmitContactInfoService verifySubmitContactInfoService;

    @Autowired
    private VerifySubmitBankCardService verifySubmitBankCardService;


    @Autowired
    private VerifySubmitLoansInfoService verifySubmitLoansInfoService;

    @Autowired
    private VerifySubmitXwBankService verifySubmitXwBankService;

    @Autowired
    private RaXwBankService raXwBankService;

    @Autowired
    private VerifyHttpRequestService requestService;

    @Autowired
    private DcSystemService dcSystemService;

    @Autowired
    private VerifySubmitMapper verifySubmitMapper;

    @Autowired
    private SubmitDelayService submitDelayService;

    @Override
    public void submitRegister(VerifySubmitRegisterVo registerVo) throws InstantiationException, IllegalAccessException, SecurityException {
        String mobile = registerVo.getMobile();
        try {
            if (CaesarUtil.isEncrypted(mobile)) {
                LoggerProxy.info("RegisterMobileEncrypt", LOG, "mobile={}", mobile);
                mobile = CaesarUtil.decode(mobile);
                LoggerProxy.info("RegisterMobileDecrypt", LOG, "mobile={}", mobile);
                registerVo.setMobile(mobile);
            }
        } catch (Exception e) {
            LoggerProxy.error("decodeRegisterMobile", LOG, "注册信息mobile解密失败,mobile={}", mobile, e);
            throw e;
        }
        String oldMobile = registerVo.getOldMobile();
        try {
            if (StringUtils.isNotEmpty(oldMobile) && CaesarUtil.isEncrypted(oldMobile)) {
                LoggerProxy.info("RegisterOldMobileEncrypt", LOG, "oldMobile={}", oldMobile);
                oldMobile = CaesarUtil.decode(oldMobile);
                LoggerProxy.info("RegisterOldMobileDecrypt", LOG, "oldMobile={}", oldMobile);
                registerVo.setOldMobile(oldMobile);
            }
        } catch (Exception e) {
            LoggerProxy.error("decodeOldRegisterMobile", LOG, "注册信息原mobile解密失败,mobile={}", mobile, e);
            throw e;
        }
        try {
            //保存到 dc 数据库
            this.submitInformationToDc(registerVo, SubmitDataType.SUBMIT_REGISTER, registerVo.getUserKey());

            // 是否存verify,ra库，默认打开
            int saveReplica = Integer.parseInt(NacosClient.getByNameSpace(ApolloNamespace.DATACENTER_NAMESPACE, "save_replica_register", "1"));
            if (saveReplica == 1) {
                OperationLogVo opLogVo = this.operationLogService.getOpLogVoFromInput(registerVo, OperationType.REGISTER);
                Integer opLogId = this.operationLogService.saveOperationLog(opLogVo);
                VerifySubmitRegister register = ObjectTransferUtils.transferObject(registerVo, VerifySubmitRegister.class);
                register.setOperationLogId(opLogId);
                register.setVersion(0);
                this.verifySubmitRegisterService.saveSubmitRegister(register);
                //保存到 ra 数据库
                RaSubmitRegisterVo raSubmitRegisterVo = new RaSubmitRegisterVo();
                BeanUtils.copyProperties(registerVo, raSubmitRegisterVo);
                this.raSubmitService.submitRegister(raSubmitRegisterVo);
            }
        } catch (Exception e) {
            LoggerProxy.error("submitRegisterError", LOG, "parar={}", JSON.toJSONString(registerVo), e);
            String delayStr = NacosClient.getByNameSpace(ApolloNamespace.commonSpace, "submit.delay.time", "30");
            Long delayTime = Long.parseLong(delayStr);
            submitDelayService.schedule(registerVo, delayTime, SubmitRegisterDelayHandler.class);
        }
    }

    // 提交操作的步骤
    //1、 构建oplog，并写入oplog
    //2、 将oplog的Log id灌注到内容中，写库
    //3、 向风险引擎发布通知。
    @Override
    public void submitAddress(VerifySubmitAddressVo addressVo) throws InstantiationException, IllegalAccessException, SecurityException {

        OperationLogVo opLogVo = this.operationLogService.getOpLogVoFromInput(addressVo, OperationType.ADDRESS);
        Integer opLogId = this.operationLogService.saveOperationLog(opLogVo);
        addressVo.setOperationLogId(opLogId);
        VerifySubmitAddress address = ObjectTransferUtils.transferObject(addressVo, VerifySubmitAddress.class);
        address.setOperationLogId(opLogId);
        address.setVersion(0);

        //保存到 dc 数据库
        this.submitInformationToDc(addressVo, SubmitDataType.SUBMIT_ADDRESS, addressVo.getUserKey());

        this.verifySubmitAddressService.saveSubmitAddress(address);
        //  发布通知。

        //保存到 ra 数据库
        RaSubmitAddressVo raSubmitAddressVo = new RaSubmitAddressVo();
        BeanUtils.copyProperties(addressVo, raSubmitAddressVo);
        this.raSubmitService.submitAddress(raSubmitAddressVo);

    }

    @Override
    public void submitJob(VerifySubmitJobVo jobVo) throws InstantiationException, IllegalAccessException, SecurityException {

        OperationLogVo opLogVo = this.operationLogService.getOpLogVoFromInput(jobVo, OperationType.JOB);
        Integer opLogId = this.operationLogService.saveOperationLog(opLogVo);
        jobVo.setOperationLogId(opLogId);
        VerifySubmitJob job = ObjectTransferUtils.transferObject(jobVo, VerifySubmitJob.class);
        job.setOperationLogId(opLogId);
        job.setVersion(0);

        //保存到 dc 数据库
        this.submitInformationToDc(jobVo, SubmitDataType.SUBMIT_JOB, jobVo.getUserKey());

        this.verifySubmitJobService.saveSubmitJob(job);
        //  向下提交工作信息

        //保存到 ra 数据库
        RaSubmitJobVo raSubmitJobVo = new RaSubmitJobVo();
        BeanUtils.copyProperties(jobVo, raSubmitJobVo);
        this.raSubmitService.submitJob(raSubmitJobVo);

    }

    @Override
    public Integer submitIdcard(VerifySubmitIdcardVo idcardVo) throws InstantiationException, IllegalAccessException, SecurityException {

        Integer opLogId = null;
        try {
            //保存到 dc 数据库
            this.submitInformationToDc(idcardVo, SubmitDataType.SUBMIT_ID_CARD, idcardVo.getUserKey());
            int saveReplica = Integer.parseInt(NacosClient.getByNameSpace(ApolloNamespace.DATACENTER_NAMESPACE, "save_replica_idcard", "1"));
            // 是否存verify,ra库，默认打开
            if (saveReplica == 1) {
                OperationLogVo opLogVo = this.operationLogService.getOpLogVoFromInput(idcardVo, OperationType.ID_CARD);
                opLogId = this.operationLogService.saveOperationLog(opLogVo);
                idcardVo.setOperationLogId(opLogId);
                VerifySubmitIdcard idcard = ObjectTransferUtils.transferObject(idcardVo, VerifySubmitIdcard.class);
                idcard.setOperationLogId(opLogId);
                this.verifySubmitIdcardService.saveOrUpdateSubmitIdcard(idcard);
                //  向下提交身份证信息

                //保存到 ra 数据库
                RaSubmitIdcardVo raSubmitIdcardVo = new RaSubmitIdcardVo();
                BeanUtils.copyProperties(idcardVo, raSubmitIdcardVo);
                this.raSubmitService.submitIdcard(raSubmitIdcardVo);
            }
        } catch (Exception e) {
            LoggerProxy.error("submitIdCardError", LOG, "parar={}", JSON.toJSONString(idcardVo), e);
            String delayStr = NacosClient.getByNameSpace(ApolloNamespace.commonSpace, "submit.delay.time", "30");
            Long delayTime = Long.parseLong(delayStr);
            //过滤掉无用户信息的延迟重试
            if (StringUtils.isNotEmpty(idcardVo.getUserKey())) {
                submitDelayService.schedule(idcardVo, delayTime, SubmitIdCardDelayHandler.class);
            }

        }


        return opLogId;
    }

    @Override
    public void submitContact(VerifySubmitContactVo contactVo) throws InstantiationException, IllegalAccessException, SecurityException {
        List<VerifySubmitContactDetailsVo> contactList = contactVo.getContactList();
        if (CollectionUtils.isNotEmpty(contactList)) {
            contactList.forEach(contact -> {
                String contactMobile = contact.getMobile();
                try {
                    if (CaesarUtil.isEncrypted(contactMobile)) {
                        LoggerProxy.info("contactMobileEncrypt", LOG, "contactMobile={}", contactMobile);
                        contactMobile = CaesarUtil.decode(contactMobile);
                        LoggerProxy.info("contactMobileDecrypt", LOG, "contactMobile={}", contactMobile);
                        contact.setMobile(contactMobile);
                    }
                } catch (Exception e) {
                    LoggerProxy.error("decodecontactMobile", LOG, "紧急联系人手机号解密失败,contactMobile={}", contactMobile, e);
                    throw e;
                }
            });
        }

        //保存到 dc 数据库
        this.submitInformationToDc(contactVo, SubmitDataType.SUBMIT_CONTACT_INFO, contactVo.getUserKey());

        OperationLogVo opLogVo = this.operationLogService.getOpLogVoFromInput(contactVo, OperationType.CONTACT);
        Integer opLogId = this.operationLogService.saveOperationLog(opLogVo);
        for (VerifySubmitContactDetailsVo detailsVo : contactVo.getContactList()) {
            VerifySubmitContactInfo contactInfo = ObjectTransferUtils.transferObject(detailsVo, VerifySubmitContactInfo.class);
            contactInfo.setUserKey(contactVo.getUserKey());
            contactInfo.setOperationLogId(opLogId);
            if (StringUtils.isNotEmpty(contactInfo.getContactName()) && contactInfo.getContactName().length() > 64) {
                LOG.warn("contactNameTooLong,contactVo={}", JSON.toJSONString(contactVo));
                contactInfo.setContactName(contactInfo.getContactName().substring(0, 64));
            }
            contactInfo.setVersion(0);
            this.verifySubmitContactInfoService.saveSubmitConctactInfo(contactInfo);
        }
        //  向下提交联系人信息

        //保存到 ra 数据库
        RaSubmitContactVo raSubmitContactVo = JSON.parseObject(JSON.toJSONString(contactVo), RaSubmitContactVo.class);
        this.raSubmitService.submitContact(raSubmitContactVo);

    }

    @Override
    public void submitPhoneBook(VerifySubmitPhoneBookVo phoneBookVo) throws InstantiationException, IllegalAccessException, SecurityException {
        //保存到 dc 数据库
        this.submitInformationToDc(phoneBookVo, SubmitDataType.SUBMIT_PHONE_BOOK, phoneBookVo.getUserKey());

        OperationLogVo opLogVo = this.operationLogService.getOpLogVoFromInput(phoneBookVo, OperationType.PHONE_BOOK);
        this.operationLogService.saveOperationLog(opLogVo);
        //  提交通讯录

        //保存到 ra 数据库
        RaSubmitPhoneBookVo raSubmitPhoneBookVo = JSON.parseObject(JSON.toJSONString(phoneBookVo), RaSubmitPhoneBookVo.class);
        this.raSubmitService.submitPhoneBook(raSubmitPhoneBookVo);
    }


    @Override
    public void submitBankCard(VerifySubmitBankCardVo bankCardVo) throws InstantiationException, IllegalAccessException, SecurityException {

        String reservedMobile = bankCardVo.getReservedMobile();
        try {
            if (CaesarUtil.isEncrypted(reservedMobile)) {
                LoggerProxy.info("bankMobileEncrypt", LOG, "reservedMobile={}", reservedMobile);
                reservedMobile = CaesarUtil.decode(reservedMobile);
                LoggerProxy.info("bankMobileDecrypt", LOG, "reservedMobile={}", reservedMobile);
                bankCardVo.setReservedMobile(reservedMobile);
            }
        } catch (Exception e) {
            LoggerProxy.error("decodeBankMobile", LOG, "银行卡手机号解密失败,reservedMobile={}", reservedMobile, e);
            throw e;
        }

        try {
            OperationLogVo opLogVo = this.operationLogService.getOpLogVoFromInput(bankCardVo, OperationType.BANK_CARD);
            Integer opLogId = this.operationLogService.saveOperationLog(opLogVo);
            VerifySubmitBankCard bankCard = ObjectTransferUtils.transferObject(bankCardVo, VerifySubmitBankCard.class);
            bankCard.setOperationLogId(opLogId);
            bankCard.setVersion(0);

            //保存到 dc 数据库
            this.submitInformationToDc(bankCardVo, SubmitDataType.SUBMIT_BANK_CARD, bankCardVo.getUserKey());

            this.verifySubmitBankCardService.saveSubmitBankCard(bankCard);
            // 提交银行卡信息

            //保存到 ra 数据库
            /*RaSubmitBankCardVo raSubmitBankCardVo = new RaSubmitBankCardVo();
            BeanUtils.copyProperties(bankCardVo, raSubmitBankCardVo);
            this.raSubmitService.submitBankCard(raSubmitBankCardVo);*/

        } catch (Exception e) {
            LoggerProxy.error("submitBankCardError", LOG, "parar={}", JSON.toJSONString(bankCardVo), e);
            String delayStr = NacosClient.getByNameSpace(ApolloNamespace.commonSpace, "submit.delay.time", "30");
            Long delayTime = Long.parseLong(delayStr);
            submitDelayService.schedule(bankCardVo, delayTime, SubmitBankCardDelayHandler.class);
        }
    }


    @Override
    public void submitVerifyCommon(VerifyCommonVo commonVo, OperationType type) throws InstantiationException, IllegalAccessException, SecurityException {

        LOG.info("submitVerifyCommon begin,type={},userKey={},jobId={}", type, commonVo.getUserKey(), commonVo.getJobId());
        // 是否正在提交到submitApply表
        boolean isSubmitApply = true;
        DcSubmitApply dcSubmitApply = new DcSubmitApply(commonVo, type, commonVo.getJobId());
        OperationLogVo opLogVo = this.operationLogService.getOpLogVoFromInput(commonVo, type);
        this.operationLogService.saveOperationLog(opLogVo);
        switch (type) {
            case SMS:
                this.submitInformationToDc(dcSubmitApply, SubmitDataType.SUBMIT_APPLY, commonVo.getUserKey());

                //保存到 ra 数据库
                SubmitCommonVo smsSubmitCommonVo = new SubmitCommonVo();
                BeanUtils.copyProperties(commonVo, smsSubmitCommonVo);
                this.raSubmitService.submitCommonInfoWithJobId(smsSubmitCommonVo, ApplyType.SMS, DataRequestTaskType.SMS_REPORT);
                break;
            case CALL_RECORD:
                this.submitInformationToDc(dcSubmitApply, SubmitDataType.SUBMIT_APPLY, commonVo.getUserKey());

                //保存到 ra 数据库
                SubmitCommonVo callRecordSubmitCommonVo = new SubmitCommonVo();
                BeanUtils.copyProperties(commonVo, callRecordSubmitCommonVo);
                this.raSubmitService.submitCommonInfoWithJobId(callRecordSubmitCommonVo, ApplyType.CALL_RECORD, DataRequestTaskType.PHONE_CALLRECORD);
                break;
            case CALENDAR:
                this.submitInformationToDc(dcSubmitApply, SubmitDataType.SUBMIT_APPLY, commonVo.getUserKey());

                //保存到 ra 数据库
                SubmitCommonVo calendarSubmitCommonVo = new SubmitCommonVo();
                BeanUtils.copyProperties(commonVo, calendarSubmitCommonVo);
                this.raSubmitService.submitCommonInfoWithJobId(calendarSubmitCommonVo, ApplyType.CALENDAR_INFO, DataRequestTaskType.CALENDAR_INFO);
                break;
            default:
                isSubmitApply = false;
                LOG.error("unreachable case : [{}]", type);
                break;
        }
//        if (isSubmitApply) {
//            //保存到 dc 数据库
//            this.submitInformationToDc(dcSubmitApply, SubmitDataType.SUBMIT_APPLY, commonVo.getUserKey());
//        }

    }


    @Override
    public void submitPlist(VerifySubmitPlistVo plistVo) throws InstantiationException, IllegalAccessException, SecurityException {
        //保存到 dc 数据库
        this.submitInformationToDc(plistVo, SubmitDataType.SUBMIT_PLIST, plistVo.getUserKey());

        OperationLogVo opLogVo = this.operationLogService.getOpLogVoFromInput(plistVo, OperationType.PLIST);
        this.operationLogService.saveOperationLog(opLogVo);

    }


    @Override
    public void saveLoansInfo(VerifySubmitLoansInfoVo loansInfoVo) throws InstantiationException, IllegalAccessException, SecurityException {

        OperationLogVo opLogVo = this.operationLogService.getOpLogVoFromInput(loansInfoVo, OperationType.LOANSINFO);
        Integer opLogId = this.operationLogService.saveOperationLog(opLogVo);

        loansInfoVo.setOperationLogId(opLogId);
        VerifySubmitLoansInfo loansInfo = ObjectTransferUtils.transferObject(loansInfoVo, VerifySubmitLoansInfo.class);
        try {
            if (loansInfoVo.getOtherLoan() != null) {
                loansInfo.setOtherLoanList(JSON.toJSONString(loansInfoVo.getOtherLoan()));
            }
        } catch (Exception e) {
            LOG.error("用户[{}]的loansInfo json转换出错", loansInfoVo.getUserKey());
        }
        loansInfo.setOperationLogId(opLogId);
        loansInfo.setVersion(0);

        //保存到 dc 数据库
        this.submitInformationToDc(loansInfoVo, SubmitDataType.SUBMIT_LOANS_INFO, loansInfoVo.getUserKey());

        this.verifySubmitLoansInfoService.saveSubmitLoansInfo(loansInfo);
        //向下提交信息

        //保存到 ra 数据库
        RaSubmitLoansInfoVo raSubmitLoansInfoVo = new RaSubmitLoansInfoVo();
        BeanUtils.copyProperties(loansInfoVo, raSubmitLoansInfoVo);
        this.raSubmitService.submitLoansInfo(raSubmitLoansInfoVo);

    }


    @Override
    public void submitXwBank(VerifySubmitXwBank xwBank) throws InstantiationException, IllegalAccessException, SecurityException {

        String mobile = xwBank.getMobile();
        try {
            if (CaesarUtil.isEncrypted(mobile)) {
                LoggerProxy.info("xwMobileEncrypt", LOG, "mobile={}", mobile);
                mobile = CaesarUtil.decode(mobile);
                LoggerProxy.info("xwMobileDecrypt", LOG, "mobile={}", mobile);
                xwBank.setMobile(mobile);
            }
        } catch (Exception e) {
            LoggerProxy.error("decodexwMobile", LOG, "xwMobile解密失败,mobile={}", mobile, e);
            throw e;
        }

        String bankMobile = xwBank.getBankMobile();
        try {
            if (CaesarUtil.isEncrypted(bankMobile)) {
                LoggerProxy.info("xwBankMobileEncrypt", LOG, "mobile={}", bankMobile);
                bankMobile = CaesarUtil.decode(bankMobile);
                LoggerProxy.info("xwBankMobileDecrypt", LOG, "mobile={}", bankMobile);
                xwBank.setBankMobile(bankMobile);
            }
        } catch (Exception e) {
            LoggerProxy.error("decodexwBankMobile", LOG, "xwBankMobile解密失败,mobile={}", bankMobile, e);
            throw e;
        }

        //保存到 dc 数据库
        this.submitInformationToDc(xwBank, SubmitDataType.SUBMIT_XW, xwBank.getUserKey());

        this.verifySubmitXwBankService.saveSubmitInfo(xwBank);

        //post to RA
        //保存到 ra 数据库
        RaXwBankRequest raXwBankRequest = new RaXwBankRequest();
        BeanUtils.copyProperties(xwBank, raXwBankRequest);
        this.raXwBankService.saveReq(raXwBankRequest);

    }

    /**
     * 提交业务端数据到datacenter
     *
     * @param obj
     * @param submitDataType
     */
    @Override
    public void submitInformationToDc(Object obj, SubmitDataType submitDataType, String userKey) {
        HashMap<String, Object> paras = new HashMap<>();
        paras.put("submitData", obj);
        //构建dc请求
        DcReqVo dcReqVo = dcSystemService.buildDcReq(paras, submitDataType, userKey);
        VerifySubmitServiceImpl.this.submitDcData(dcReqVo);
    }

    private void submitDcData(Object obj) {
        boolean b = dcSystemService.submitDcData(obj);
        if (!b) {//提交数据到dc失败
            this.requestService.addNewAppRequest(DcSystemServiceImpl.HOST, DcSystemServiceImpl.URL, obj);
            String data = JsonUtils.toJson(obj);
            LOG.error("submitDcData error,postData={}", data);
            throw new RuntimeException("submitDcData error,postData: " + data);
        } else {
            LOG.info("submitDcData success,data={}", JsonUtils.toJson(obj));
        }
    }

    @Override
    public VerifySubmit findSubmitByLoanKey(String loanKey) {
        return this.verifySubmitMapper.findVerifySubmitByLoanKey(loanKey);
    }

    @Override
    public List<VerifySubmit> findSubmitByUserKey(String userKey) {
        return this.verifySubmitMapper.findVerifySubmitByUserKey(userKey);
    }

}
