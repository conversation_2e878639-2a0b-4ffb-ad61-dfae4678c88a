package com.youxin.risk.ra.service.impl;

import com.youxin.risk.ra.mapper.SubmitPlistMapper;
import com.youxin.risk.ra.model.SubmitPlist;
import com.youxin.risk.ra.service.SubmitPlistService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("raSubmitPlistService")
public class SubmitPlistServiceImpl implements SubmitPlistService {

	@Autowired
	private SubmitPlistMapper submitPlistMapper;

	@Override
	public void saveSubmitPlist(SubmitPlist submitPlist) {
		submitPlistMapper.insert(submitPlist);
	}



}
