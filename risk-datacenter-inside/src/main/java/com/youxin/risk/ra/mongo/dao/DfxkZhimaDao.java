package com.youxin.risk.ra.mongo.dao;

import com.youxin.risk.ra.mongo.vo.DfxkZhimaDataVo;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * 
 * @ClassName: DfxkZhimaDao 
 * @Description: 
 * Copyright: Copyright (c) 2018
 * Company:人人贷商务顾问(北京)有限公司 
 * <AUTHOR> 
 * @date 2018年5月28日 下午4:36:36
 */
@Repository
public class DfxkZhimaDao extends MongoBaseDaoImpl<DfxkZhimaDataVo> {

	public DfxkZhimaDataVo getRecordByTaskId(Integer taskId){

		Query query = new Query();
		query.addCriteria(Criteria.where("taskId").is(taskId));

		return this.findOne(query);
	}

	public List<String> getRecordByTaskIdList(List<Integer> taskIdList){
		List<String> result = new ArrayList<>();
		Query query = new Query();
		query.addCriteria(Criteria.where("taskId").in(taskIdList));
		List<DfxkZhimaDataVo> zhimaList = this.findList(query);
		for(DfxkZhimaDataVo record: zhimaList){
			result.add(record.getData());
		}
		return result;
	}

}
