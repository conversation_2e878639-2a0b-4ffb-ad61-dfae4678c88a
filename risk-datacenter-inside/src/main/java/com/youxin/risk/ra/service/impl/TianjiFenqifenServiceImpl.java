package com.youxin.risk.ra.service.impl;

import com.youxin.risk.ra.enums.ThirdPartyDataSource;
import com.youxin.risk.ra.model.DataRequestTask;
import com.youxin.risk.ra.model.ReportRequest;
import com.youxin.risk.ra.mongo.dao.TianjiFenqifenDao;
import com.youxin.risk.ra.mongo.vo.TianjiFenqifenDataVo;
import com.youxin.risk.ra.service.TianjiFenqifenService;
import com.youxin.risk.ra.vo.TianjiFenqifenRecordVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Date;

/**
 * 融360天机分期分
 * 
 * <AUTHOR>
 * @version 2018年5月10日 下午7:48:26
 */
@Service("tianjiFenqifenService")
public class TianjiFenqifenServiceImpl implements TianjiFenqifenService {
    private static final Logger LOG = LoggerFactory.getLogger(TianjiFenqifenServiceImpl.class);

    @Autowired
    private TianjiFenqifenDao tianjiFenqifenDao;
    @Override
    public void saveData(DataRequestTask dataTask, TianjiFenqifenRecordVo recordVo) {
        // 保存到mongodb
        TianjiFenqifenDataVo tianjiVo = new TianjiFenqifenDataVo();
        tianjiVo.setSourceSystem(dataTask.getSourceSystem());
        tianjiVo.setTaskId(dataTask.getId());
        tianjiVo.setUserKey(dataTask.getUserKey());
        tianjiVo.setLoanKey(dataTask.getLoanKey());
        tianjiVo.setName(recordVo.getData().getName());
        tianjiVo.setIdNumber(recordVo.getData().getIdNumber());
        tianjiVo.setPhone(recordVo.getData().getPhone());
        tianjiVo.setData(recordVo.getData().getData());
        tianjiVo.setCreateTime(new Date());
        this.tianjiFenqifenDao.insert(tianjiVo);
    }

    @Override
    public void submitData(ReportRequest reportRequest, ThirdPartyDataSource dataSource) {

    }

    @Override
    public String getData(ReportRequest reportRequest, ThirdPartyDataSource dataSource) {
        return null;
    }
}
