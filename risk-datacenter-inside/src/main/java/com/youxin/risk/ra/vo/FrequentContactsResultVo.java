package com.youxin.risk.ra.vo;

import com.youxin.risk.ra.enums.DataStatus;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

public class FrequentContactsResultVo {
	private DataStatus status;

	private List<FrequentContactsVo> contactList;

	private String strategyName;

	public String getStrategyName() {
		return strategyName;
	}

	public void setStrategyName(String strategyName) {
		this.strategyName = strategyName;
	}

	public DataStatus getStatus() {
		return status;
	}

	public void setStatus(DataStatus status) {
		this.status = status;
	}

	public List<FrequentContactsVo> getContactList() {
		return contactList;
	}

	public void setContactList(List<FrequentContactsVo> contactList) {
		this.contactList = contactList;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this,
				ToStringStyle.SHORT_PREFIX_STYLE);
	}
}
