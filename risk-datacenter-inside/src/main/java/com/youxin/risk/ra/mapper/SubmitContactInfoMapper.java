package com.youxin.risk.ra.mapper;

import com.youxin.risk.ra.model.SubmitContactInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface SubmitContactInfoMapper extends BaseMapper<SubmitContactInfo> {

    @Deprecated
    List<SubmitContactInfo> findByApplyId(@Param("applyId") Integer applyId);

    // 修数用的方法，勿使用
    List<Map<String, Object>> queryById(Map<String, Object> map);

    // 修数用的方法，勿使用
    int updateById(Map<String, Object> map);
}
