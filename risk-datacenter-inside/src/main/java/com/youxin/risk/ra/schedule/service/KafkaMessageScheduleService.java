package com.youxin.risk.ra.schedule.service;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import com.youxin.risk.commons.xxl.job.XxlJobBase;
import com.youxin.risk.ra.kafka.enums.DpKafkaMessageType;
import com.youxin.risk.ra.kafka.enums.KafkaMessageStatus;
import com.youxin.risk.ra.kafka.service.KafkaMessageLogServive;
import com.youxin.risk.ra.kafka.vo.DataPlatformMessageVo;
import com.youxin.risk.ra.model.DataRequestTask;
import com.youxin.risk.ra.model.KafkaMessageLog;
import com.youxin.risk.ra.service.DataRequestTaskService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class KafkaMessageScheduleService extends BaseScheduleService implements XxlJobBase {

    @Autowired
    private KafkaMessageLogServive kafkaMessageLogServive;

    @Autowired
    private DataRequestTaskService dataRequestTaskService;



    @Override
    protected Logger getLogger() {
        return LoggerFactory.getLogger(KafkaMessageScheduleService.class);
    }

    @Override
    protected void runTask() {
        List<KafkaMessageLog> list = null;
        try {
            list = this.kafkaMessageLogServive.getUndealKafkaMessage();
        } catch (Exception e) {
            this.logger.error("查询未处理的kafka消息列表发生异常", e);
        }
        if (list == null || list.isEmpty()) {
            this.logger.info("没有未处理的kafka消息");
            return;
        }
        for (KafkaMessageLog kafkaMessageLog : list) {
            this.process(kafkaMessageLog);
        }
    }

    private void process(KafkaMessageLog kafkaMessageLog) {

        if (kafkaMessageLog == null || StringUtils.isEmpty(kafkaMessageLog.getKey())) {
            return;
        }
//        String redisKey = ScheduleConstants.KAFKA_MESSAGE_KEY_PRE + kafkaMessageLog.getKey();
//        if (!this.redisService.acquireLock(redisKey, ScheduleConstants.KAFKA_MESSAGE_LOCK_TIME)) {
//            this.logger.warn("请求redis锁失败, key :" + redisKey);
//            return;
//        }
        try {
            if (kafkaMessageLog.getStatus() != KafkaMessageStatus.UNDEAL) {
                return;
            }
            if (System.currentTimeMillis() - kafkaMessageLog.getCreateTime().getTime() < 5000) {
                this.logger.info("消息[{}]的创建时间不足5秒，等下次再处理", kafkaMessageLog.getMessage());
                return;
            }
            //logger.info("重新处理消息:[{}]", kafkaMessageLog.getMessage());

            if (kafkaMessageLog.isExpired()) {
                this.logger.info("消息:{}已超时，不再处理", kafkaMessageLog.getMessage());
                this.kafkaMessageLogServive.setExpired(kafkaMessageLog.getId());
                return;
            }

            String jobId = kafkaMessageLog.getKey();
            DataPlatformMessageVo msgVo = new DataPlatformMessageVo();
            msgVo.deserialize(kafkaMessageLog.getMessage());

            DataRequestTask dataRequestTask = null;
            if (StringUtils.isEmpty(msgVo.getRecordType())) {
                dataRequestTask = this.dataRequestTaskService.getDataRequestTaskByJobId(jobId);
            } else {
                dataRequestTask = this.dataRequestTaskService.getDataRequestTaskByJobIdAndType(jobId, msgVo.getRecordType());
            }
            if (dataRequestTask == null) {
                //logger.error("jobID[{}]对应的task不存在", jobId);
                return;
            }
            try {
                this.logger.info("重新处理任务{}", dataRequestTask.getId());
                msgVo.deserialize(kafkaMessageLog.getMessage());
                if (DpKafkaMessageType.SUCCESSFUL.name().equalsIgnoreCase(msgVo.getType())) {
                    this.kafkaMessageLogServive.setDeal(kafkaMessageLog.getId());

                    if (dataRequestTask.getTaskStatus().isFinalStatus()) {
                        this.logger.warn("jobID[{}]对应的task已经处理过，或者正在处理中，这里不做处理", jobId);
                        return;
                    }
                    switch (dataRequestTask.getTaskType()) {
                        case CALL_HISTORY:
                        case PHONE_BOOK:
                            // 数据已准备好，调用获取数据接口
                            // 处理积压的kafka消息
                            this.dataRequestTaskService.processDataRequestTaskWithJobId(dataRequestTask.getId());
                            break;
                        case RONG_RECORD:
                        case RONG_REPORT:
                            this.dataRequestTaskService.processDataRequestTaskWithJobId(dataRequestTask.getId());
                            break;
                        case DATAKEY_ALIPAY_BILL:
                        case DATAKEY_ALIPAY_JIAOFEI:
                        case DATAKEY_TAOBAO_BILL:
                        case DATAKEY_TAOBAO_REPORT:
                        case DATAKEY_ZHIMA_SCORE:
                            this.dataRequestTaskService.fetch(dataRequestTask.getId());
                            break;
                        default:
                            logger.info("handleKafKaMessage,userKey={},loanKey={},jobid={},taskType={}",
                                    dataRequestTask.getUserKey(), dataRequestTask.getLoanKey(),
                                    dataRequestTask.getJobID(), dataRequestTask.getTaskType());
                            this.dataRequestTaskService.processDataRequestTaskWithJobId(dataRequestTask.getId());
                            this.dataRequestTaskService.fetch(dataRequestTask.getId());
                            break;

                    }

                    this.dataRequestTaskService.checkReportDataTask(dataRequestTask.getSourceSystem(), dataRequestTask.getUserKey());
                }
            } catch (Exception e) {
                this.logger.error("重新处理kafka信息出错", e);
            }
        } catch (Exception e) {
            this.logger.error("重新处理kafka信息出错", e);
        } finally {
//            this.redisService.releaseLock(redisKey);
        }
    }

    @XxlJob(value = "fixedRateHandler")
    @Override
    public ReturnT<String> execJobHandler(String param) {
        try {
            this.runTask();
        }catch (Exception e){
            XxlJobLogger.log("执行失败，失败信息为："+e);
            return  ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
