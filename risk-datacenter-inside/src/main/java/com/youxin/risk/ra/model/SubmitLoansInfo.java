package com.youxin.risk.ra.model;

import com.youxin.risk.ra.timeinterface.CreateTimeSetter;
import com.youxin.risk.ra.timeinterface.UpdateTimeSetter;

import java.io.Serializable;
import java.util.Date;


public class SubmitLoansInfo implements CreateTimeSetter, UpdateTimeSetter, Serializable {

	private static final long serialVersionUID = -6787124566859189294L;

    private Integer id;

    private Integer applyId;

    private String userKey;

    private String sourceSystem;

	private String loanUsage;

	private Integer otherLoanNumber;

	private String repaymentSource;

	private Integer hasHouseLoan;

	private Integer hasCarLoan;

	private String otherLoanList;

    private Date updateTime;

    private Date createTime;

    private Integer version;

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}


	public String getUserKey() {
		return this.userKey;
	}

	public void setUserKey(String userKey) {
		this.userKey = userKey;
	}

	public String getSourceSystem() {
		return this.sourceSystem;
	}

	public void setSourceSystem(String sourceSystem) {
		this.sourceSystem = sourceSystem;
	}

	public String getLoanUsage() {
		return this.loanUsage;
	}

	public void setLoanUsage(String loanUsage) {
		this.loanUsage = loanUsage;
	}

	public Integer getOtherLoanNumber() {
		return this.otherLoanNumber;
	}

	public void setOtherLoanNumber(Integer otherLoanNumber) {
		this.otherLoanNumber = otherLoanNumber;
	}

	public String getRepaymentSource() {
		return this.repaymentSource;
	}

	public void setRepaymentSource(String repaymentSource) {
		this.repaymentSource = repaymentSource;
	}

	public Integer getHasHouseLoan() {
		return this.hasHouseLoan;
	}

	public void setHasHouseLoan(Integer hasHouseLoan) {
		this.hasHouseLoan = hasHouseLoan;
	}

	public Integer getHasCarLoan() {
		return this.hasCarLoan;
	}

	public void setHasCarLoan(Integer hasCarLoan) {
		this.hasCarLoan = hasCarLoan;
	}

	public String getOtherLoanList() {
		return this.otherLoanList;
	}

	public void setOtherLoanList(String otherLoanList) {
		this.otherLoanList = otherLoanList;
	}

	public Date getUpdateTime() {
		return this.updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Integer getVersion() {
		return this.version;
	}

	public void setVersion(Integer version) {
		this.version = version;
	}

	@Override
	public void setUpdateTime() {
		this.updateTime = new Date();
	}

	@Override
	public void setCreateTime() {
		this.createTime = new Date();
	}

	public Integer getApplyId() {
		return this.applyId;
	}

	public void setApplyId(Integer applyId) {
		this.applyId = applyId;
	}

}
