package com.youxin.risk.ra.mapper;

import com.youxin.risk.ra.model.DataCityLowestSalary;
import com.youxin.risk.ra.model.RaXwBankRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DataCityLowestSalaryMapper extends BaseMapper<DataCityLowestSalary> {

    /**
     * 加载所有
     * @return
     */
    List<DataCityLowestSalary> selectAll();
}
