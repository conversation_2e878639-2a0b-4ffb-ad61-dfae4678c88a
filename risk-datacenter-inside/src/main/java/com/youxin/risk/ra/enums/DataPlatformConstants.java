package com.youxin.risk.ra.enums;

import com.youxin.risk.ra.utils.ClassLoaderUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

public class DataPlatformConstants {

	private static Properties property = ClassLoaderUtil.loadProperties(
			"/ra/data_platform_api.properties", DataPlatformConstants.class);

	public static Map<String, String> header = new HashMap<>();
	static {
		header.put("Content-Type", "application/json;charset=utf-8");
	}

	public static String dpbaseUrl = property.getProperty("data.url.base");

	public static String mobileBaseUrl = property.getProperty("data.mobile.base");

	public static String loanBaseUrl = property.getProperty("data.loan.base");


	public static String getCallHistoryUrl = property
			.getProperty("data.url.record.job.callhistory");


	public static String getPhoneBookUrl = property
			.getProperty("data.url.record.job.phonebook");


	public static int callHistoryExpiry = Integer.valueOf(property.getProperty(
			"data.expiry.callhistory", "30"));

	public static int phoneBookExpiry = Integer.valueOf(property.getProperty(
			"data.expiry.phonebook", "-1"));

	public static int tbExpiry = Integer.valueOf(property.getProperty(
			"data.expiry.tb", "30"));
	// mobile area search
	public static String mobileArea = property.getProperty("data.mobile.url");


	public static String recordCallRecordUrl = property
			.getProperty("data.url.record.call.record");


	// rong360
	public static String rongRecordUrl = property
			.getProperty("data.url.record.job.rongrecord");
	public static String rongReportUrl = property
			.getProperty("data.url.record.job.rongreport");
	// loan
	public static String loanHistoryUrl = property
			.getProperty("data.url.loan.loanhistory");
	public static String blacklistUrl = property
			.getProperty("data.url.loan.blacklist");

	public static String loanRepayUrl = property
			.getProperty("data.url.loan.loanrepay");

	// spider
	public static String spiderBaseUrl = property
			.getProperty("data.url.spider.base");

	public static String spiderBlacklistUrl = property
			.getProperty("data.url.spider.blacklist");

	public static String spiderTianjiXgfUrl = property
			.getProperty("data.url.spider.tianji.xigua");

	public static String zhimaCreditUrl = property
			.getProperty("data.url.record.zhima.credit");

	public static String smsIdcardUrl = property.getProperty("data.url.record.sms.idcard");

	public static String smsUrl = property.getProperty("data.url.record.sms");

	public static String crawlersBaseUrl = property.getProperty("data.url.crawlers.base");

	public static String crawlUrl = property.getProperty("data.url.crawlers.crawl");

	public static String creditxFraudUrl = property.getProperty("data.url.record.creditx.fraud");

	public static String creditxScoreUrl = property.getProperty("data.url.record.creditx.score");

	public static String shumeiUrl = property.getProperty("data.url.record.shumei");

	public static String taobaoUrl = property.getProperty("data.url.record.taobao");

	public static String phoneCallRecordUrl = property.getProperty("data.url.record.callrecord");

	public static String alipayUrl = property.getProperty("data.url.record.alipay");

	public static String unionpayUrl = property.getProperty("data.url.record.unionpay");

	public static String creditCardBillUrl = property.getProperty("data.url.record.creditCardBill");

	public static String crawlerV2Url = property.getProperty("data.url.crawlers.v2");


	public static final String RECORD_XIAOWEI_URL = "/v1/record/PCAC_RISK_INFO";

	public static final String RECORD_TENCENTCLOUD_URL = "/v1/record/QCLOUD_ANTIFRAUD";

	public static final String RECORD_HUJIN_URL = "/v1/record/NIFA_QUERY";

	public static final String RECORD_DHB_URL = "/v1/record/DHB_CUISHOU";

	public static final String RECORD_XW_URL = "/v1/record/XWBANK_APPROVAL";

	public static final String RECORD_CALENDAR = "/v1/record/CALENDAR_INFO";

    public static final String TIANJI_BASIC_SCORE_URL = "/v1/record/TIANJI_BASIC_SCORE";

    public static final String TIANJI_SCORE_URL = "/v2/crawl/tianjiscore";

    public static final String REPORT_NFCS_URL = "/v1/record/NFCS_REPORT";

    public static final String RECORD_DFXK_ZHIMA_URL = "/v1/record/DFXK_ZHIMA_RECORD";

    public static final String RECORD_GZT_EDU_URL = "/v1/record/GZT_EDUCATION";

    public static final String RECORD_GZT_EDU_REQ_URL = "/v2/crawl/gzteducation";

    public static final String RECORD_XINYAN_URL = "/v2/crawl/xinyan";

    public static final String RECORD_XINYAN_RADAR_URL = "/v1/record/XINYAN_RADAR_RECORD";

    public static final String RECORD_XINYAN_BLACK_URL = "/v1/record/XINYAN_BLACK_RECORD";

    public static final String RECORD_XINYAN_WHITE_URL = "/v1/record/XINYAN_WHITE_RECORD";

    public static final String RECORD_DATAKEY_TAOBAO_BILL = "/v1/record/DATAKEY_TAOBAO_BILL";

	public static final String RECORD_DATAKEY_TAOBAO_REPORT = "/v1/record/DATAKEY_TAOBAO_REPORT";

	public static final String RECORD_DATAKEY_ALIPAY_BILL = "/v1/record/DATAKEY_ALIPAY_BILL";

	public static final String RECORD_DATAKEY_ALIPAY_JIAOFEI = "/v1/record/DATAKEY_ALIPAY_JIAOFEI";

	public static final String RECORD_DATAKEY_ZHIMA_SCORE = "/v1/record/DATAKEY_ZHIMA_SCORE";

	public static final String NIFASERVICE_URL = "/nifaHybrid";
	public static final String TONGDUNSER_URL = "/pudao";

	public static int getExpriry(DataRequestTaskType type) {
		switch (type) {
		case CALL_HISTORY:
			return callHistoryExpiry;
		case PHONE_BOOK:
			return phoneBookExpiry;
		case ALITAOBAO:
			return tbExpiry;
		default:
			return -1;
		}
	}
}
