package com.youxin.risk.ra.mongo.vo;

import org.apache.commons.lang3.builder.EqualsBuilder;

import java.util.Date;
import java.util.Objects;

public class MobileCallRecordVo {

    /** 任务id */
    private Integer taskId;
    /** 来源系统 */
    private String sourceSystem;
    /** 用户ID */
    private String userKey;
    /** 开始时间 */
    private Date startTime;
    /** 地区 */
    private String area;
    /** 呼叫类型 1 主叫 2 被叫 */
    private Integer mode;
    /** 对方号码 */
    private String toMobile;
    /** 自己号码 */
    private String mobile;
    /** 通话时长（int 单位：秒） */
    private Integer duration;
    /** 通话类型 本地通话、国内漫游、国际漫游 */
    private String type;
    /** 费用小计（单位：分） */
    private Integer totalFee;
    /** 手机运营商代码 */
    private String carrierCode;
    /** 普通话费 */
    private Integer normalFee;
    /** 长途话费 */
    private Integer roamingFee;

    private Date createTime;

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getTaskId() {
        return this.taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public String getSourceSystem() {
        return this.sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem;
    }

    public String getUserKey() {
        return this.userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey;
    }

    public Date getStartTime() {
        return this.startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public String getArea() {
        return this.area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public Integer getMode() {
        return this.mode;
    }

    public void setMode(Integer mode) {
        this.mode = mode;
    }

    public String getToMobile() {
        return this.toMobile;
    }

    public void setToMobile(String toMobile) {
        this.toMobile = toMobile;
    }

    public String getMobile() {
        return this.mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getDuration() {
        return this.duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getTotalFee() {
        return this.totalFee;
    }

    public void setTotalFee(Integer totalFee) {
        this.totalFee = totalFee;
    }

    public String getCarrierCode() {
        return this.carrierCode;
    }

    public void setCarrierCode(String carrierCode) {
        this.carrierCode = carrierCode;
    }

    public Integer getNormalFee() {
        return this.normalFee;
    }

    public void setNormalFee(Integer normalFee) {
        this.normalFee = normalFee;
    }

    public Integer getRoamingFee() {
        return this.roamingFee;
    }

    public void setRoamingFee(Integer roamingFee) {
        this.roamingFee = roamingFee;
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.startTime, this.mode, this.toMobile);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj instanceof MobileCallRecordVo) {
            MobileCallRecordVo record = (MobileCallRecordVo) obj;
            return new EqualsBuilder().append(this.startTime, record.startTime).append(this.mode, record.mode)
                    .append(this.toMobile, record.toMobile).isEquals();
        }
        return false;
    }
}
