package com.youxin.risk.ra.mongo.dao;

import com.youxin.risk.ra.mongo.vo.GraphBlackDataVo;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.Order;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

@Repository
public class GraphBlackDao extends MongoBaseDaoImpl<GraphBlackDataVo>{

	public GraphBlackDataVo getByUserKey(String userKey) {
		Query query = new Query();
		query.addCriteria(Criteria.where("userKey").is(userKey));
		query.with(new Sort(new Order(Direction.DESC, "_id")));

		return this.findOne(query);
	}

    public GraphBlackDataVo getByLoanKey(String loanKey) {
        Query query = new Query();
        query.addCriteria(Criteria.where("loanKey").is(loanKey));
        query.with(new Sort(new Order(Direction.DESC, "createTime")));
        
        return this.findOne(query);
    }
    
}
