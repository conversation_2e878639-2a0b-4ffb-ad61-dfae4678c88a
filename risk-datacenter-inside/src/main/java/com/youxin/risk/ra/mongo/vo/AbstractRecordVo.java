package com.youxin.risk.ra.mongo.vo;

import org.springframework.data.mongodb.core.index.Indexed;

public class AbstractRecordVo {
	private String type;
	private Long timestamp;
	private String idNumber;
	private String userKey;
	private DpJob job;

	@Indexed
	private Integer taskId;

	public String getType() {
		return this.type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Long getTimestamp() {
		return this.timestamp;
	}

	public void setTimestamp(Long timestamp) {
		this.timestamp = timestamp;
	}

	public String getIdNumber() {
		return this.idNumber;
	}

	public void setIdNumber(String idNumber) {
		this.idNumber = idNumber;
	}

	public String getUserKey() {
		return this.userKey;
	}

	public void setUserKey(String userKey) {
		this.userKey = userKey;
	}

	public DpJob getJob() {
		return this.job;
	}

	public void setJob(DpJob job) {
		this.job = job;
	}

	public Integer getTaskId() {
		return this.taskId;
	}

	public void setTaskId(Integer taskId) {
		this.taskId = taskId;
	}


}
