/**
 * Copyright(c) 2011-2017 by YouCredit Inc.
 * All Rights Reserved
 */
package com.youxin.risk.ra.mongo.dao;

import com.youxin.risk.ra.mongo.vo.RongRecordVo;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version 创建时间：2017年9月29日-下午2:10:48
 */
@Repository
public class RongRecordDao extends MongoBaseDaoImpl<RongRecordVo>{

	public RongRecordVo getRecordByTaskId(String sourceSystem,String userKey,Integer taskId){

		Query query = new Query();
		query.addCriteria(Criteria.where("taskId").is(taskId))
			 .addCriteria(Criteria.where("userKey").is(userKey))
			 .addCriteria(Criteria.where("sourceSystem").is(sourceSystem));

		return this.findOne(query);
	}

	public RongRecordVo getRecordByTaskId(Integer taskId){

		Query query = new Query();
		query.addCriteria(Criteria.where("taskId").is(taskId));

		return this.findOne(query);
	}


	public RongRecordVo getLastRecord(String sourceSystem,String userKey){

		Query query = new Query();
		query.addCriteria(Criteria.where("userKey").is(userKey))
			 .addCriteria(Criteria.where("sourceSystem").is(sourceSystem))
			 .with(new Sort(Sort.Direction.DESC,"timestamp"))
			 .limit(1);

		return this.findOne(query);
	}
}
