package com.youxin.risk.ra.model;

import com.youxin.risk.ra.timeinterface.CreateTimeSetter;
import com.youxin.risk.ra.timeinterface.UpdateTimeSetter;

import java.io.Serializable;
import java.util.Date;

public class SubmitIdcard implements CreateTimeSetter, UpdateTimeSetter, Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -373076704432937173L;

	private Integer id;

	private Integer applyId;

	private String sourceSystem;

	private String userKey;

	private String idcardNumber;

	private String idcardName;

	private String idcardAddress;

	private String pidPositiveUrl;

	private String pidNegativeUrl;

	private String faceUrl;

	private String curveFaceUrl;

	private Double faceScore;

	private Boolean pidAuth;

	private String faceThreshold;

	private String faceGenuineness;

	private String idcardNation;

	private String idcardIssue;

	private String idcardLegalityFront;

	private String idcardLegalityBack;

	private String idcardValid;

	private Date updateTime;

	private Date createTime;

	private Integer version;

	@Override
	public void setUpdateTime() {
		this.updateTime = new Date();
	}

	@Override
	public void setCreateTime() {
		this.createTime = new Date();
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getIdcardAddress() {
		return idcardAddress;
	}

	public void setIdcardAddress(String idcardAddress) {
		this.idcardAddress = idcardAddress;
	}

	public String getFaceGenuineness() {
		return faceGenuineness;
	}

	public void setFaceGenuineness(String faceGenuineness) {
		this.faceGenuineness = faceGenuineness;
	}

	public Integer getApplyId() {
		return applyId;
	}

	public void setApplyId(Integer applyId) {
		this.applyId = applyId;
	}

	public String getSourceSystem() {
		return sourceSystem;
	}

	public void setSourceSystem(String sourceSystem) {
		this.sourceSystem = sourceSystem;
	}

	public String getUserKey() {
		return userKey;
	}

	public void setUserKey(String userKey) {
		this.userKey = userKey;
	}

	public String getIdcardNumber() {
		return idcardNumber;
	}

	public void setIdcardNumber(String idcardNumber) {
		this.idcardNumber = idcardNumber;
	}

	public String getIdcardName() {
		return idcardName;
	}

	public void setIdcardName(String idcardName) {
		this.idcardName = idcardName;
	}

	public String getPidPositiveUrl() {
		return pidPositiveUrl;
	}

	public void setPidPositiveUrl(String pidPositiveUrl) {
		this.pidPositiveUrl = pidPositiveUrl;
	}

	public String getPidNegativeUrl() {
		return pidNegativeUrl;
	}

	public void setPidNegativeUrl(String pidNegativeUrl) {
		this.pidNegativeUrl = pidNegativeUrl;
	}

	public String getFaceUrl() {
		return faceUrl;
	}

	public void setFaceUrl(String faceUrl) {
		this.faceUrl = faceUrl;
	}

	public String getCurveFaceUrl() {
		return curveFaceUrl;
	}

	public void setCurveFaceUrl(String curveFaceUrl) {
		this.curveFaceUrl = curveFaceUrl;
	}

	public Double getFaceScore() {
		return faceScore;
	}

	public void setFaceScore(Double faceScore) {
		this.faceScore = faceScore;
	}

	public Boolean getPidAuth() {
		return pidAuth;
	}

	public void setPidAuth(Boolean pidAuth) {
		this.pidAuth = pidAuth;
	}

	public String getFaceThreshold() {
		return faceThreshold;
	}

	public void setFaceThreshold(String faceThreshold) {
		this.faceThreshold = faceThreshold;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Integer getVersion() {
		return version;
	}

	public void setVersion(Integer version) {
		this.version = version;
	}

	public String getIdcardNation() {
		return idcardNation;
	}

	public void setIdcardNation(String idcardNation) {
		this.idcardNation = idcardNation;
	}

	public String getIdcardIssue() {
		return idcardIssue;
	}

	public void setIdcardIssue(String idcardIssue) {
		this.idcardIssue = idcardIssue;
	}

	public String getIdcardValid() {
		return idcardValid;
	}

	public void setIdcardValid(String idcardValid) {
		this.idcardValid = idcardValid;
	}

	public String getIdcardLegalityFront() {
		return idcardLegalityFront;
	}

	public void setIdcardLegalityFront(String idcardLegalityFront) {
		this.idcardLegalityFront = idcardLegalityFront;
	}

	public String getIdcardLegalityBack() {
		return idcardLegalityBack;
	}

	public void setIdcardLegalityBack(String idcardLegalityBack) {
		this.idcardLegalityBack = idcardLegalityBack;
	}

}
