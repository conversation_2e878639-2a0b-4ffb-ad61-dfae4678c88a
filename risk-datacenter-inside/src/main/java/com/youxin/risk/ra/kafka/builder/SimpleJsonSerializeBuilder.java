package com.youxin.risk.ra.kafka.builder;

import com.youxin.risk.ra.kafka.IMessage;
import com.youxin.risk.ra.kafka.commom.BeanSerializer;
import com.youxin.risk.ra.kafka.commom.JsonBeanSerializer;

public class SimpleJsonSerializeBuilder<T extends IMessage<T>> extends
		AbstractKafkaBuilder<T> {

	private Class<T> serializedBeanClass;

	public Class<T> getSerializedBeanClass() {
		return serializedBeanClass;
	}

	public void setSerializedBeanClass(Class<T> serializedBeanClass) {
		this.serializedBeanClass = serializedBeanClass;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public BeanSerializer createBeanSerializer() {
		return new JsonBeanSerializer(serializedBeanClass);
	}

}
