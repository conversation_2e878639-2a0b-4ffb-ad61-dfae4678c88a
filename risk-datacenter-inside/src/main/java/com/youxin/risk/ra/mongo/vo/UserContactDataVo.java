package com.youxin.risk.ra.mongo.vo;

import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * 
 * @ClassName: ContactDataVo 
 * @Description: 
 * Copyright: Copyright (c) 2018
 * Company:人人贷商务顾问(北京)有限公司 
 * <AUTHOR> 
 * @date 2018年6月6日 下午3:00:50
 */
@Document(collection = "UserContact")
public class UserContactDataVo {
	
	@Indexed
    private Integer applyId;

	private String isRepart;
	
	@Indexed
    private Date createTime;

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Integer getApplyId() {
		return applyId;
	}

	public void setApplyId(Integer applyId) {
		this.applyId = applyId;
	}

	public String getIsRepart() {
		return isRepart;
	}

	public void setIsRepart(String isRepart) {
		this.isRepart = isRepart;
	}

}
