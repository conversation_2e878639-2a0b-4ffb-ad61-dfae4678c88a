package com.youxin.risk.ra.model;

import com.youxin.risk.ra.timeinterface.CreateTimeSetter;
import com.youxin.risk.ra.timeinterface.UpdateTimeSetter;

import java.io.Serializable;
import java.util.Date;

public class SubmitContactInfo implements CreateTimeSetter, UpdateTimeSetter, Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2929438983080975510L;

	private Integer id;

	private String sourceSystem;

	private String userKey;

	private String relation;

	private Integer applyId;

	private String contactName;

	private String mobile;

	private Date updateTime;

	private Date createTime;

	private Integer version;

	@Override
	public void setUpdateTime() {
		this.updateTime = new Date();
	}

	@Override
	public void setCreateTime() {
		this.createTime = new Date();
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getSourceSystem() {
		return sourceSystem;
	}

	public void setSourceSystem(String sourceSystem) {
		this.sourceSystem = sourceSystem;
	}

	public String getUserKey() {
		return userKey;
	}

	public void setUserKey(String userKey) {
		this.userKey = userKey;
	}

	public String getRelation() {
		return relation;
	}

	public void setRelation(String relation) {
		this.relation = relation;
	}

	public Integer getApplyId() {
		return applyId;
	}

	public void setApplyId(Integer applyId) {
		this.applyId = applyId;
	}

	public String getContactName() {
		return contactName;
	}

	public void setContactName(String contactName) {
		this.contactName = contactName;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Integer getVersion() {
		return version;
	}

	public void setVersion(Integer version) {
		this.version = version;
	}

	public boolean checkValidity() {
		return !((contactName != null && contactName.length() > 255)
				|| (mobile != null && mobile.length() > 255) || (relation != null && relation
				.length() > 255));
	}
}
