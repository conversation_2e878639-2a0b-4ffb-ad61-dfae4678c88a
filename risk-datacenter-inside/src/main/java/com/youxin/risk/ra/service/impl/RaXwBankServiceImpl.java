/**
 * Copyright(c) 2011-2018 by YouCredit Inc.
 * All Rights Reserved
 */
package com.youxin.risk.ra.service.impl;

import com.youxin.risk.ra.enums.ThirdPartyDataSource;
import com.youxin.risk.ra.mapper.XwBankRequestMapper;
import com.youxin.risk.ra.model.ReportRequest;
import com.youxin.risk.ra.model.RaXwBankRequest;
import com.youxin.risk.ra.service.RaXwBankService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 新网银行数据服务类
 *
 * <AUTHOR>
 * @version 创建时间：2018年3月13日-下午12:38:48
 */
@Service("xwBankService")
public class RaXwBankServiceImpl implements RaXwBankService {

    @Autowired
    private XwBankRequestMapper xwBankRequestMapper;

    @Override
    public void saveReq(RaXwBankRequest raXwBankRequest) {
        this.xwBankRequestMapper.insert(raXwBankRequest);
    }

	@Override
	public void submitData(ReportRequest reportRequest, ThirdPartyDataSource dataSource) {

	}

	@Override
	public String getData(ReportRequest reportRequest, ThirdPartyDataSource dataSource) {
		return null;
	}
}
