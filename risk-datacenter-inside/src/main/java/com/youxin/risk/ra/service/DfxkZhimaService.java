package com.youxin.risk.ra.service;

import com.youxin.risk.ra.model.DataRequestTask;
import com.youxin.risk.ra.mongo.dao.DfxkZhimaDao;
import com.youxin.risk.ra.mongo.vo.DfxkZhimaDataVo;
import com.youxin.risk.ra.service.impl.AbstractBaseDataService;
import com.youxin.risk.ra.vo.DataFetchVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 
 * @ClassName: DfxkZhimaService 
 * @Description: 东方星空芝麻分
 * Copyright: Copyright (c) 2018
 * Company:人人贷商务顾问(北京)有限公司 
 * <AUTHOR> 
 * @date 2018年5月28日 下午4:35:05
 */
@Service
public class DfxkZhimaService extends AbstractBaseDataService<DfxkZhimaDataVo> {


	@Autowired
	private DfxkZhimaDao dfxkZhimaDao;




	public void saveData(DfxkZhimaDataVo recordVo, DataRequestTask reqTask) {
		recordVo.setTaskId(reqTask.getId());
		recordVo.setCreateTime(new Date());
		recordVo.setUserKey(reqTask.getUserKey());
		recordVo.setSourceSystem(reqTask.getSourceSystem());
		this.dfxkZhimaDao.insert(recordVo);
	}


	@Override
	public DfxkZhimaDataVo getDataFromDB(DataRequestTask dataTask) {
		return null;
	}

	@Override
	public DfxkZhimaDataVo getDataFromPlatform(DataFetchVo vo) {
		return null;
	}

	@Override
	public void saveData(DfxkZhimaDataVo recordVo, Integer dataTaskId) {

	}
}
