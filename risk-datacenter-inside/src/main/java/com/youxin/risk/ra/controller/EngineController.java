package com.youxin.risk.ra.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.cache.CacheApi;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.JsonUtils;
import com.youxin.risk.datacenter.pojo.JsonResultVo;
import com.youxin.risk.datacenter.schedule.xxljob.UserDataToRedisJob;
import com.youxin.risk.datacenter.service.SubmitAddressService;
import com.youxin.risk.datacenter.service.SubmitBankCardService;
import com.youxin.risk.datacenter.service.SubmitJobService;
import com.youxin.risk.datacenter.service.impl.DcReportRequestServiceImpl;
import com.youxin.risk.ra.enums.ApplyType;
import com.youxin.risk.ra.enums.DataPlatformConstants;
import com.youxin.risk.ra.enums.DataRequestTaskStatus;
import com.youxin.risk.ra.enums.DataRequestTaskType;
import com.youxin.risk.ra.model.DataRequestTask;
import com.youxin.risk.ra.model.ReportRequest;
import com.youxin.risk.ra.model.SubmitApply;
import com.youxin.risk.ra.mongo.dao.UserContactDao;
import com.youxin.risk.ra.mongo.vo.MobileCallHistoryVo;
import com.youxin.risk.ra.service.*;
import com.youxin.risk.ra.vo.*;
import com.youxin.risk.verify.redis.RedisService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

import static com.youxin.risk.commons.apollo.ApolloNamespaceEnum.DC_INSIDE_SPACE;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/engine")
public class EngineController {
    private static final Logger logger = LoggerFactory.getLogger(EngineController.class);

    @Resource
    private ReportRequestService reportRequestService;

    @Resource
    private DcReportRequestServiceImpl dcReportRequestService;

    @Resource
    private SubmitApplyService submitApplyService;

    @Qualifier("submitAddressServiceImpl")
    @Resource
    private SubmitAddressService submitAddressService;

    @Qualifier("submitBankCardServiceImpl")
    @Resource
    private SubmitBankCardService submitBankCardService;

    @Qualifier("raSubmitContactInfoService")
    @Resource
    private SubmitContactInfoService submitContactInfoService;


    @Qualifier("submitContactInfoServiceImpl")
    @Resource
    private com.youxin.risk.datacenter.service.SubmitContactInfoService dcSubmitContactInfoService;

    @Resource
    private UserContactDao userContactDao;

    @Qualifier("raSubmitIdcardService")
    @Resource
    private SubmitIdcardService submitIdcardService;

    @Qualifier("submitIdcardServiceImpl")
    @Resource
    private com.youxin.risk.datacenter.service.SubmitIdcardService dcSubmitIdcardService;

    @Qualifier("submitJobServiceImpl")
    @Resource
    private SubmitJobService submitJobService;

    @Qualifier("raSubmitLocateService")
    @Resource
    private SubmitLocateService submitLocateService;

    @Qualifier("raSubmitRegisterService")
    @Resource
    private SubmitRegisterService submitRegisterService;

    @Qualifier("submitRegisterServiceImpl")
    @Resource
    private com.youxin.risk.datacenter.service.SubmitRegisterService dcSubmitRegisterService;

    @Qualifier("raSubmitLoansInfoService")
    @Resource
    private SubmitLoansInfoService loansInfoService;

    @Qualifier("submitLoansInfoServiceImpl")
    @Resource
    private com.youxin.risk.datacenter.service.SubmitLoansInfoService dcLoansInfoService;

    @Autowired
    private DataRequestTaskService dataRequestTaskService;

    @Autowired
    private DataService dataService;

    @Autowired
    private DataCollectService dataCollectService;

    @Autowired
    @Qualifier("datacenterCacheRedisService")
    private RedisService datacenterRedisService;


    @RequestMapping("/dataVoByUser")
    @ResponseBody
    private JsonResultVo getDataVoByUser(@RequestBody ReportRequestVo requestVo) {

        logger.info("received user dataVo message, message={}", JsonUtils.toJson(requestVo));

        try {
           /* ReportRequest reportRequest = this.reportRequestService.findReportRequestByUserKey(requestVo.getSourceSystem(), requestVo.getUserKey());
            if (reportRequest == null) {
                logger.info("can not find reportRequest in getDataVoByUser,userKey={},reportKey={}", requestVo.getUserKey(), requestVo.getReportKey());
                return JsonResultVo.error(404, "no data");
            }*/
            DataVo dataVo = dealDataVo(requestVo);
            if (null == dataVo) {
                return JsonResultVo.error(404, "no data");
            }
            return JsonResultVo.success().addData("dataVo", dataVo);
        } catch (Exception e) {
            logger.error("getDataVoByUser error,userKey={},loanKey={}", requestVo.getUserKey(), requestVo.getLoanKey(), e);
        }
        return JsonResultVo.error();

    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.GET,value = "/getLastNotNullPlatform")
    public JsonResultVo getLastNotNullPlatform(@RequestParam String userKey){
        logger.info("getLastNotNullPlatform,userKey:{}",userKey);
        try{
            String platform = dcReportRequestService.getLastNotNullPlatform(userKey);
            logger.info("getLastNotNullPlatform,userKey:{},platform:{}",userKey,platform);
            return JsonResultVo.success().addData("platform",platform);
        }catch (Exception e){
            logger.error("getLastNotNullPlatform error,userKey:{}",userKey);
        }
        return JsonResultVo.error();
    }

    /**
     * 获取submitApply列表
     *
     * @param userKey      userKey
     * @param sourceSystem sourceSystem
     * @return submitApply列表
     */
    @Deprecated
    public List<SubmitApply> getSubmitApplyList(String userKey, String sourceSystem) {
        String redisKey = UserDataToRedisJob.USER_BASIC_DATA_REDIS_KEY_PREFIX + userKey;
        List<SubmitApply> applyList = new ArrayList<>();
        //线上运行稳定后，删除开关及异常处理
        String cache = CacheApi.getDictSysConfig("cacheUserBasicDataToRedis", "false");
        if ("true".equals(cache)) {
            try {
                if (datacenterRedisService.exists(redisKey)) {
                    Map<String, String> submitApplyMap = datacenterRedisService.hgetAll(redisKey);
                    for (String value : submitApplyMap.values()) {
                        if (StringUtils.isBlank(value)) {
                            continue;
                        }
                        applyList.add(JSON.parseObject(value, SubmitApply.class));
                    }
                    logger.info("getUserSubmitApply from redis success,userKey={},", userKey);
                } else {
                    applyList = this.submitApplyService.getLastApply(sourceSystem, userKey);
                    logger.info("getUserSubmitApply from redis mysql,userKey={},", userKey);
                }
            } catch (Exception e) {
                logger.error("getUserSubmitApply from redis error,userKey={},", userKey, e);
                applyList = this.submitApplyService.getLastApply(sourceSystem, userKey);
            }
        } else {
            applyList = this.submitApplyService.getLastApply(sourceSystem, userKey);
        }
        return applyList;
    }

    private DataVo dealDataVo(ReportRequestVo requestVo) {

        DataVo dataVo = new DataVo();
        String sourceSystem = requestVo.getSourceSystem();
        String userKey = requestVo.getUserKey();
        String apiLoanSource = requestVo.getApiLoanSource();

        long startTime = System.currentTimeMillis();
        //获取可选认证信息

        //拼接地址信息
        RaSubmitAddressVo dcSubmitAddress = submitAddressService.getRaSubmitAddressVoByUserKey(userKey, sourceSystem, apiLoanSource);
        dataVo.setSubmitAddressVo(dcSubmitAddress);

        //银行卡
        RaSubmitBankCardVo raSubmitBankCardVo = submitBankCardService.getSubmitBankCardByUserKey(sourceSystem, userKey, apiLoanSource);
        dataVo.setSubmitBankCardVo(raSubmitBankCardVo);

        // 身份证
        RaSubmitIdcardVo raSubmitIdcardVo = dcSubmitIdcardService.getSubmitIdcardVoByUserKey(userKey, apiLoanSource);
        if (StringUtils.isBlank(apiLoanSource) && Objects.nonNull(raSubmitIdcardVo) && StringUtils.isBlank(raSubmitIdcardVo.getIdcardNumber())) {
            raSubmitIdcardVo = submitIdcardService.getIdcardVoByUserKey(sourceSystem, userKey);
        }
        dataVo.setSubmitIdcardVo(raSubmitIdcardVo);

        // 联系人
        List<RaSubmitContactDetailsVo> contactList = dcSubmitContactInfoService.getSubmitContactListByUserKey(userKey, apiLoanSource);
        dataVo.setContactList(contactList);
        dataVo.setIsRepart("0");

        //工作信息
        RaSubmitJobVo raSubmitJobVo = submitJobService.getRaSubmitJobVoByUserKeyAndSourceSystem(sourceSystem, userKey, apiLoanSource);
        dataVo.setSubmitJobVo(raSubmitJobVo);

        // 注册信息
        RaSubmitRegisterVo raSubmitRegisterVo = dcSubmitRegisterService.getSubmitRegisterVoByUserKey(userKey, apiLoanSource);
        if (StringUtils.isBlank(apiLoanSource) && raSubmitRegisterVo == null) {
            raSubmitRegisterVo = submitRegisterService.getLastSubmitRegisterVoByUserKey(sourceSystem, userKey);
        }
        dataVo.setSubmitRegisterVo(raSubmitRegisterVo);

        // 借款信息
        RaSubmitLoansInfoVo submitLoansInfoVo = this.dcLoansInfoService.getLoansInfoVoByUserKey(userKey, apiLoanSource);
        if (StringUtils.isBlank(apiLoanSource) && submitLoansInfoVo == null) {
            submitLoansInfoVo = this.loansInfoService.getLoansInfoVoByUserKey(userKey);
        }
        dataVo.setSubmitLoansInfoVo(submitLoansInfoVo);

        ReportRequestVo reportRequestVo = this.dcReportRequestService.getReportRequestVo(userKey, apiLoanSource);

        if (StringUtils.isBlank(apiLoanSource) && reportRequestVo == null) {
            reportRequestVo = this.reportRequestService.getReportRequestVo(sourceSystem, userKey);
        }
        dataVo.setReportRequestVo(reportRequestVo);

        logger.info("get dataVoItem,reportKey={},cost={}", userKey, System.currentTimeMillis() - startTime);
        return dataVo;
    }


    private DataVo dealDataVo2(ReportRequest reportRequest) {
        DataVo dataVo = null;
        String sourceSystem = reportRequest.getSourceSystem();
        String userKey = reportRequest.getUserKey();
        List<SubmitApply> applyList = getSubmitApplyList(userKey, sourceSystem);
        DataRequestTask requestTask;
        //获取可选认证信息
        if (!CollectionUtils.isEmpty(applyList)) {
            dataVo = new DataVo();
            dealApplyOperationList(applyList, dataVo);
            for (SubmitApply submitApply : applyList) {
                long startTime = System.currentTimeMillis();
                Integer applyId = submitApply.getId();
                ApplyType applyType = ApplyType.valueOf(submitApply.getApplyType());
                switch (applyType) {
                    case ADDRESS:
                        RaSubmitAddressVo dcSubmitAddress = submitAddressService.getRaSubmitAddressVoByUserKey(userKey, sourceSystem, null);
                        dataVo.setSubmitAddressVo(dcSubmitAddress);
                        break;
                    case BANK_CARD:
                        RaSubmitBankCardVo raSubmitBankCardVo = submitBankCardService.getSubmitBankCardByUserKey(sourceSystem, userKey, null);
                        dataVo.setSubmitBankCardVo(raSubmitBankCardVo);
                        break;
                    case CONTACT:
                        List<RaSubmitContactDetailsVo> contactList = submitContactInfoService.getSubmitContactListByUserApply(sourceSystem, userKey);
                        dataVo.setContactList(contactList);
                        dataVo.setIsRepart("0");
                        break;
                    case ID_CARD:
                        RaSubmitIdcardVo raSubmitIdcardVo =
                                submitIdcardService.getSubmitIdcardVoByUserApply(sourceSystem, userKey, applyId);
                        if (raSubmitIdcardVo.getIdcardNumber() == null) {
                            raSubmitIdcardVo =
                                    submitIdcardService.getIdcardVoByUserKey(sourceSystem, userKey);
                        }
                        dataVo.setSubmitIdcardVo(raSubmitIdcardVo);
                        break;
                    case JOB:
                        RaSubmitJobVo raSubmitJobVo = submitJobService.getRaSubmitJobVoByUserKeyAndSourceSystem(sourceSystem, userKey, null);
                        dataVo.setSubmitJobVo(raSubmitJobVo);
                        break;
                    case LOCATE:
                        List<SubmitLocateVo> locateList =
                                submitLocateService.getSubmitLocateList(sourceSystem, userKey);
                        dataVo.setLocateList(locateList);
                        break;
                    case REGISTER:
                        RaSubmitRegisterVo raSubmitRegisterVo = submitRegisterService.getSubmitRegisterVoByUserApply(sourceSystem, userKey, applyId);
                        if (raSubmitRegisterVo == null) {
                            raSubmitRegisterVo = submitRegisterService.getLastSubmitRegisterVoByUserKey(sourceSystem, userKey);
                        }
                        dataVo.setSubmitRegisterVo(raSubmitRegisterVo);
                        break;
                    case LOANSINFO:
                        dataVo.setSubmitLoansInfoVo(
                                this.loansInfoService.getLoansInfoVoByUserApply(sourceSystem, userKey, applyId));
                        break;
                    case MOBILE_OPERATOR:
                        try {
                            requestTask = this.dataRequestTaskService.getDataRequestTaskByApplyIdAndType(applyId,
                                    DataRequestTaskType.CALL_HISTORY);
                            if (requestTask != null) {
                                // 直接从数据平台获取详单数据
                                DataFetchVo vo = new DataFetchVo();
                                vo.setJobid(requestTask.getJobID());
                                vo.setSystemid(requestTask.getSourceSystem());
                                MobileCallHistoryVo mobileCallHistoryVo = this.dataService.getCallHistoryData(vo);
                                if (mobileCallHistoryVo == null || mobileCallHistoryVo.getUserInfo() == null) {
                                    mobileCallHistoryVo = this.dataCollectService.getCallHistoryByHisJobid(sourceSystem, userKey, requestTask);
                                }
                                this.dataCollectService.processCallHistory(mobileCallHistoryVo, dataVo, requestTask);
                            }
                        } catch (Exception e) {
                            logger.info("get MOBILE_OPERATOR error, userKey: {}", userKey, e);
                        }

                        break;
                    case SUMBMIT:
                        dataVo.setReportRequestVo(
                                this.reportRequestService.getReportRequestVo(reportRequest.getId()));
                        break;
                    default:
                        break;
                }
                logger.info("get dataVoItem,reportKey={},cost={}", reportRequest.getReportKey(), System.currentTimeMillis() - startTime);
            }

            //以下三个类型的必要信息进行一次补偿
            //注册信息
            if (dataVo.getSubmitRegisterVo() == null) {
                RaSubmitRegisterVo raSubmitRegisterVo = submitRegisterService.getSubmitRegisterVoByUserApply(sourceSystem, userKey, 1000);
                if (raSubmitRegisterVo == null) {
                    raSubmitRegisterVo = submitRegisterService.getLastSubmitRegisterVoByUserKey(sourceSystem, userKey);
                }
                dataVo.setSubmitRegisterVo(raSubmitRegisterVo);
            }
            // 身份证
            if (dataVo.getSubmitIdcardVo() == null) {
                RaSubmitIdcardVo raSubmitIdcardVo = submitIdcardService.getSubmitIdcardVoByUserApply(sourceSystem, userKey, 1000);
                if (raSubmitIdcardVo.getIdcardNumber() == null) {
                    raSubmitIdcardVo = submitIdcardService.getIdcardVoByUserKey(sourceSystem, userKey);
                }
                dataVo.setSubmitIdcardVo(raSubmitIdcardVo);
            }
            //银行卡
            if (dataVo.getSubmitBankCardVo() == null) {
                RaSubmitBankCardVo raSubmitBankCardVo = submitBankCardService.getSubmitBankCardByUserKey(sourceSystem, userKey, null);
                dataVo.setSubmitBankCardVo(raSubmitBankCardVo);
            }
        }
        return dataVo;
    }

    private void dealApplyOperationList(List<SubmitApply> applyList, DataVo dataVo) {
        try {
            List<SubmitApplyVo> submitApplyVoList = JSONArray.parseArray(JSON.toJSONString(applyList), SubmitApplyVo.class);
            dataVo.setApplyOperationList(submitApplyVoList);
        } catch (Exception e) {
            logger.warn("dealDataVo for applyOperationList error");
        }
    }


    @RequestMapping("/dataTask")
    @ResponseBody
    public JsonResultVo getUserDataTask(@RequestBody DataRequestTaskVo requestVo) {

        logger.info("reveive dataTask request,param={}", JsonUtils.toJson(requestVo));
        long startTime = System.currentTimeMillis();
        try {
            Boolean getDataTaskFromDp = ApolloClientAdapter.getBooleanConfig(DC_INSIDE_SPACE, "getDataTaskFromDp", false);
            if (getDataTaskFromDp) {
                // 根据userKey从dp获取最新的一笔
                String result = SyncHTTPRemoteAPI.get(String.format("%s/v1/record/user/%s?systemid=%s&userkey=%s", DataPlatformConstants.dpbaseUrl, requestVo.getTaskType().name(), requestVo.getSourceSystem(), requestVo.getUserKey()), 30000);
                // 异常处理
                final JSONObject resultObj = JSONObject.parseObject(result);
                if (resultObj == null || resultObj.getInteger("status") == 500) {
                    logger.error("getDataTask from dp error,userKey={}, result: {}", requestVo.getUserKey(), result);
                    return JsonResultVo.error();
                }
                // 解析jobid
                JSONArray records = resultObj.getJSONArray("records");
                if (CollectionUtils.isEmpty(records)) {
                    return JsonResultVo.success();
                }
                String jobId = (String) JSONPath.eval(records.getJSONObject(0), "$.job.jobID");
                Long timestamp = (Long) JSONPath.eval(records.getJSONObject(0), "$.timestamp");
                DataRequestTask task = new DataRequestTask();
                task.setJobID(jobId);
                task.setCreateTime(new Date(timestamp));
                logger.info("getDataTaskDp result,userKey={},result={},cost={}", requestVo.getUserKey(), JsonUtils.toJson(task), System.currentTimeMillis() - startTime);
                return JsonResultVo.success().addData("dataTask", task);
            } else {
                DataRequestTask task = this.dataRequestTaskService
                        .findDataRequestTaskByUserAndTypeTaskStatus(requestVo.getSourceSystem(), requestVo.getUserKey(),
                                requestVo.getTaskType(), DataRequestTaskStatus.FETCHED);
                logger.info("getDataTaskVerify result,userKey={},result={},cost={}", requestVo.getUserKey(), JsonUtils.toJson(task), System.currentTimeMillis() - startTime);

                if (null == task) {
                    return JsonResultVo.success();
                }

                return JsonResultVo.success().addData("dataTask", task);
            }
        } catch (Exception e) {
            logger.error("getDataTask error,userKey={}", requestVo.getUserKey(), e);
        }

        return JsonResultVo.error();
    }
}
