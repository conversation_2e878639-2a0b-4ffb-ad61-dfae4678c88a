package com.youxin.risk.ra.utils;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Locale;

/**
 * 日期工具类
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

    /**
     * 一秒的长度
     */
    public static final int ONE_SECOND = 1000;

    /**
     * 一分钟的长度，修改此值将影响后台线程（自动投标、标状态检查、自动还款）的运行间隔
     */
    public static final int ONE_MINUTE = ONE_SECOND * 60;
    /**
     * 一个小时的长度，修改此值将影响邮件验证过期的判断
     */
    public static final int ONE_HOUR = ONE_MINUTE * 60;
    /**
     * 一天的长度，修改此值将影响逾期天数的计算
     */
    public static final int ONE_DAY = ONE_HOUR * 24;

    public static final int ONE_DAY_SECONDS = 24 * 60 * 60;
    // long ONE_DAY = 1L * 60 * 1000;

    private static final Logger LOG = LoggerFactory.getLogger(DateUtils.class);

    private static final String[] CN_MM = {"", "\u4e00\u6708", "\u4e8c\u6708", "\u4e09\u6708", "\u56db\u6708",
            "\u4e94\u6708", "\u516d\u6708", "\u4e03\u6708", "\u516b\u6708", "\u4e5d\u6708", "\u5341\u6708",
            "\u5341\u4e00\u6708", "\u5341\u4e8c\u6708"};

    /**
     * 长日期格式化模式 精确到小时
     */
    private static final DateTimeFormatter LONG_HOURTIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH");


    /**
     * 长日期格式化模式 精确到小时
     */
    private static final DateTimeFormatter LONG_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static final String PATTERN_LONG = "yyyy-MM-dd HH:mm:ss";

    /**
     * 给指定的时间串 time 加 mm 分钟 返回 fmt 的串
     *
     * @param time
     * @param mm
     * @return
     */
    public static String getAddMinutes(String time, String mm, String fmt) {

        String sTime = "";
        SimpleDateFormat formatter = new SimpleDateFormat(fmt);
        SimpleDateFormat sdf = new SimpleDateFormat("", Locale.SIMPLIFIED_CHINESE);
        Date d1 = null;
        try {
            int iAdd = Integer.parseInt(mm);
            d1 = formatter.parse(time);
            long lRst = d1.getTime() + iAdd * 60 * 1000;
            sdf.applyPattern(fmt);
            sTime = sdf.format(new Date(lRst));

        } catch (ParseException e) {
            e.printStackTrace();
        }
        return sTime;
    }

    /**
     * 获取指定时间 加xx秒后的时间
     *
     * @param day
     * @param second
     * @return
     */
    public static Date getAddTime(Date day, int second) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(day);
        cal.add(Calendar.SECOND, second);

        return cal.getTime();
    }

    /**
     * 得到某天所在月份的最后一天的date
     *
     * @param date
     * @return
     */
    public static Date getLastDate(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, 1);
        cal.set(Calendar.DAY_OF_MONTH, 0);
        return cal.getTime();
    }

    /**
     * 得到某天所在月份的最后第一天的date
     *
     * @param date
     * @return
     */
    public static Date getFirstDate(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        return cal.getTime();
    }

    public static String getFirstDayOfNextMonth(String dateStr) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        try {
            Date date = sdf.parse(dateStr);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(5, 1);
            calendar.add(2, 1);
            return sdf.format(calendar.getTime());
        } catch (ParseException var4) {
            var4.printStackTrace();
            return null;
        }
    }

    /**
     * 得到月份的中文名
     *
     * @param myDate
     * @return
     */
    public static String formatDateCN(Date myDate) {
        String strDate = CN_MM[DateUtils.getMonth(myDate)];
        return strDate;
    }

    /**
     * 得到日期对应的月份数
     *
     * @param date
     * @return
     */
    public static int getMonth(Date date) {
        Calendar cld = Calendar.getInstance();
        cld.setTime(date);
        return cld.get(Calendar.MONTH) + 1;
    }

    /**
     * 得到日期对应的日
     *
     * @param date
     * @return
     */
    public static int getDay(Date date) {
        Calendar cld = Calendar.getInstance();
        cld.setTime(date);
        return cld.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 获得下一个特定日（月份第几天）的日期
     *
     * @param date
     * @param dayNum
     * @return
     */
    public static Date getNextDateAtDay(Date date, int dayNum) {
        Calendar cld = Calendar.getInstance();
        cld.setTime(date);
        cld.set(Calendar.DAY_OF_MONTH, dayNum);
        cld.set(Calendar.HOUR_OF_DAY, 0);
        cld.set(Calendar.MINUTE, 0);
        cld.set(Calendar.SECOND, 0);
        cld.set(Calendar.MILLISECOND, 0);
        Date newDate = cld.getTime();
        if (newDate.before(date)) {
            cld.add(Calendar.DAY_OF_MONTH, 1);
        }
        return cld.getTime();
    }

    /**
     * 得到本月对应的第一天
     *
     * @return String
     */
    public static String getMonthFristDay() {
        Calendar cld = Calendar.getInstance();
        cld.set(Calendar.DAY_OF_MONTH, 1);
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
        String firstDay = dft.format(cld.getTime());
        return firstDay;
    }

    /**
     * 得到本月对应的最后一天
     *
     * @return String
     */
    public static String getMonthEndDay() {
        Calendar cld = Calendar.getInstance();
        cld.add(Calendar.MONTH, 1);
        cld.set(Calendar.DAY_OF_MONTH, 0);
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
        String endDay = dft.format(cld.getTime());
        return endDay;
    }

    /**
     * 得到指定月的第一天
     *
     * @return String
     */
    public static String getFristDayInMonth(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(year, month, 1);
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
        String firstDay = dft.format(calendar.getTime());
        return firstDay;
    }

    /**
     * 得到指定月的最后一天
     *
     * @return String
     */
    public static String getEndDayInMonth(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(year, month + 1, 0);
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
        String endDay = dft.format(calendar.getTime());
        return endDay;
    }

    /**
     * 获取当前月份的日期
     *
     * @return
     */
    public static String getCurMonthDate() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
        return simpleDateFormat.format(new Date());
    }

    /**
     * 获取指定时间的月份
     *
     * @return
     */
    public static String getMonthDate(Date time) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
        return simpleDateFormat.format(time);
    }

    /**
     * 获取格式化今天的日期字符串
     *
     * @return
     */
    public static String getCurDayDate() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return simpleDateFormat.format(new Date());
    }

    /**
     * 获得指定格式的日期字符串
     *
     * @return
     */
    public static String getCurrentDate(String format) {
        String dateString = "";
        try {
            SimpleDateFormat formatter = new SimpleDateFormat(format);
            Date currentTime_1 = new Date();
            dateString = formatter.format(currentTime_1);
        } catch (Exception e) {
        }
        return dateString;
    }

    /**
     * 获取格式化今天的时间字符串 yyyy-MM-dd HH:mm:ss
     *
     * @return
     */
    public static String getCurrentTime() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return simpleDateFormat.format(new Date());
    }

    public static String getTimeStr(Date d) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        return simpleDateFormat.format(d);
    }

    /**
     * 得到今天的长整型时间since January 1, 1970, 00:00:00 GMT represented by this Date
     * object.
     *
     * @return
     */
    public static long getTodayTime() {
        Calendar cld = Calendar.getInstance();
        // cld.setTime(new Date());
        int year = cld.get(Calendar.YEAR);
        int month = cld.get(Calendar.MONTH);
        int day = cld.get(Calendar.DAY_OF_MONTH);
        Calendar todaycld = Calendar.getInstance();
        todaycld.set(year, month, day, 0, 0, 0);
        return todaycld.getTime().getTime();
    }

    /**
     * 判断是否今天
     *
     * @param atime
     * @return
     */
    public static boolean isTodayTime(long atime) {
        Calendar cld = Calendar.getInstance();
        // cld.setTime(new Date());
        int year = cld.get(Calendar.YEAR);
        int month = cld.get(Calendar.MONTH);
        int day = cld.get(Calendar.DAY_OF_MONTH);
        Calendar todaycld = Calendar.getInstance();
        todaycld.set(year, month, day, 0, 0, 0);
        if (atime + 1000l >= todaycld.getTime().getTime()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 判断是否昨天
     *
     * @param atime
     * @return
     */
    public static boolean isLastdayTime(long atime) {
        Calendar cld = Calendar.getInstance();
        // cld.setTime(new Date());
        cld.add(Calendar.DAY_OF_MONTH, -1);
        int year = cld.get(Calendar.YEAR);
        int month = cld.get(Calendar.MONTH);
        int day = cld.get(Calendar.DAY_OF_MONTH);
        Calendar lastdaycld = Calendar.getInstance();
        lastdaycld.set(year, month, day, 0, 0, 0);
        if (atime >= lastdaycld.getTime().getTime()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 判断是否昨天
     *
     * @param atime
     * @return
     */
    public static boolean isLastday(long atime) {
        Calendar today = Calendar.getInstance();
        today.set(today.get(Calendar.YEAR), today.get(Calendar.MONDAY), today.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        Calendar cld = Calendar.getInstance();
        cld.add(Calendar.DAY_OF_MONTH, -1);
        int year = cld.get(Calendar.YEAR);
        int month = cld.get(Calendar.MONTH);
        int day = cld.get(Calendar.DAY_OF_MONTH);
        Calendar lastdaycld = Calendar.getInstance();
        lastdaycld.set(year, month, day, 0, 0, 0);
        if (atime >= lastdaycld.getTimeInMillis() && atime < today.getTimeInMillis()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 判断是否在今天之前
     *
     * @param atime
     * @return
     */
    public static boolean isBeforeToday(long atime) {
        Calendar cld = Calendar.getInstance();
        int year = cld.get(Calendar.YEAR);
        int month = cld.get(Calendar.MONTH);
        int day = cld.get(Calendar.DAY_OF_MONTH);
        Calendar todaycld = Calendar.getInstance();
        todaycld.set(year, month, day, 0, 0, 0);
        todaycld.set(Calendar.MILLISECOND, 0);
        if (atime <= todaycld.getTimeInMillis()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 转换字符串为日期
     *
     * @param s
     * @return
     * @throws Exception
     */
    public static Date getFormatDateOnDay(String s) throws Exception {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return simpleDateFormat.parse(s);
    }

    /**
     * 转换字符串为日期,精确到时分秒
     *
     * @param s
     * @return
     * @throws Exception
     */
    public static Date getFormatDateOnDay(String s, String format) throws Exception {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        return simpleDateFormat.parse(s);
    }

    /*
     * 转换为中文日期
     */
    public static String getFormatZHDay(String s) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        try {
            date = simpleDateFormat.parse(s);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        SimpleDateFormat sf = new SimpleDateFormat();
        sf.applyPattern("yyyy\u5E74MM\u6708dd\u65E5");
        return sf.format(date);
    }

    /**
     * 转换字符串为日期和时间
     *
     * @param s
     * @return
     * @throws Exception
     */
    public static Date getFormatDateOnDayAndTime(String s) {
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return simpleDateFormat.parse(s);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换时间字符串为日期和时间
     *
     * @param s
     * @return
     */
    public static Date getFormatTime(String s) {
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH:mm:ss");
            return simpleDateFormat.parse(s);
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    /**
     * 获取前几天的日期字符串
     *
     * @param num
     * @return
     * @throws Exception
     */
    public static String getPriorDayDateStr(int num) {
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
        Calendar date = Calendar.getInstance();
        date.set(Calendar.DATE, date.get(Calendar.DATE) - num);
        String beforeDate = dft.format(date.getTime());
        return beforeDate;
    }

    public static String getPriorDayDateStr(String cDate, int num) {
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
        Calendar date = Calendar.getInstance();
        try {
            date.setTime(DateUtils.getFormatDateOnDay(cDate));
        } catch (Exception e) {
            e.printStackTrace();
        }
        date.set(Calendar.DATE, date.get(Calendar.DATE) - num);
        String beforeDate = dft.format(date.getTime());
        return beforeDate;
    }

    /**
     * 获取前几天的日期
     *
     * @param num
     * @return
     */
    public static Date getPriorDayDate(int num) {
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
        Calendar date = Calendar.getInstance();
        date.set(Calendar.DATE, date.get(Calendar.DATE) - num);
        String beforeDate = dft.format(date.getTime()) + " 00:00:00";
        Date fdate = null;
        try {
            fdate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(beforeDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return fdate;
    }

    public static Date getPriorDayLastTime(int num) {
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
        Calendar date = Calendar.getInstance();
        date.set(Calendar.DATE, date.get(Calendar.DATE) - num);
        String beforeDate = dft.format(date.getTime()) + " 23:59:59";
        Date fdate = null;
        try {
            fdate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(beforeDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return fdate;
    }

    /**
     * 获取后几天的日期
     *
     * @param num
     * @return
     */
    public static Date getNextSecondDate(int num) {
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        Calendar date = Calendar.getInstance();
        date.set(Calendar.SECOND, date.get(Calendar.SECOND) + num);
        Date ndate = null;
        try {
            ndate = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").parse(dft.format(date.getTime()));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return ndate;
    }

    /**
     * 获取后几天的日期
     *
     * @param num
     * @return
     */
    public static Date getNextDayDate(int num) {
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
        Calendar date = Calendar.getInstance();
        date.set(Calendar.DATE, date.get(Calendar.DATE) + num);
        String nextDate = dft.format(date.getTime()) + " 00:00:00";
        Date ndate = null;
        try {
            ndate = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").parse(nextDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return ndate;
    }

    public static String getNextDay(String startDate, Integer diffDay, Integer diffMonth) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = sdf.parse(startDate, new ParsePosition(0));
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(2, diffMonth);
        calendar.add(5, diffDay);
        Date date1 = calendar.getTime();
        String out = sdf.format(date1);
        return out;
    }

    /**
     * 获取后几天的日期
     *
     * @param num
     * @return
     */
    public static Date getNextDayDate(Date cDate, int num) {
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
        Calendar date = Calendar.getInstance();
        try {
            date.setTime(cDate);
        } catch (Exception e) {
            e.printStackTrace();
        }
        date.set(Calendar.DATE, date.get(Calendar.DATE) + num);
        String nextDate = dft.format(date.getTime()) + " 00:00:00";
        Date ndate = null;
        try {
            ndate = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").parse(nextDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return ndate;
    }

    /**
     * 获取今年第一天的日期
     *
     * @return
     * @throws Exception
     */
    public static Date getCurYearFristDate() throws Exception {
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        Calendar date = Calendar.getInstance();
        String beforeDate = date.get(Calendar.YEAR) + "-01-01 00:00:00";
        return dft.parse(beforeDate);
    }

    /**
     * 时间转换
     *
     * @param year
     * @param month
     * @param date
     * @param hour
     * @param minute
     * @param second
     * @return
     */
    public static long Time2Long(int year, int month, int date, int hour, int minute, int second) {
        Calendar cld = Calendar.getInstance();
        month = month - 1;
        cld.set(year, month, date, hour, minute, second);
        return cld.getTime().getTime();
    }

    /**
     * 格式化日期
     *
     * @param date
     * @return
     * @throws Exception
     */
    public static String getFormatDate(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return simpleDateFormat.format(date);
    }

    public static String getFormatDateOnTime(Date date) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return simpleDateFormat.format(date);
    }

    public static String getFormatDateOnMinite(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        return simpleDateFormat.format(date);
    }

    /**
     * 格式化日期
     *
     * @param date
     * @param format
     * @return
     */
    public static String getFormatDate(Date date, String format) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        return simpleDateFormat.format(date);
    }

    /**
     * 获取两个日期相差的天数，参数精确到天 如2015-10-10 23:59:59 与 2015-10-11 00:00:00相差为1天
     * 2015-10-11 23:59:59 与 2015-10-10 00:00:00相差为-1天
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static int getDistDays(Date startDate, Date endDate) {
        Calendar end = Calendar.getInstance();
        end.setTime(endDate);
        end.set(Calendar.HOUR_OF_DAY, 0);
        end.set(Calendar.MINUTE, 0);
        end.set(Calendar.SECOND, 0);
        end.set(Calendar.MILLISECOND, 0);
        Calendar start = Calendar.getInstance();
        start.setTime(startDate);
        start.set(Calendar.HOUR_OF_DAY, 0);
        start.set(Calendar.MINUTE, 0);
        start.set(Calendar.SECOND, 0);
        start.set(Calendar.MILLISECOND, 0);
        long days = (end.getTimeInMillis() - start.getTimeInMillis()) / (1000 * 60 * 60 * 24);
        return (int) days;
    }

    /**
     * 获取第二天00:00:00
     *
     * @param date
     * @return
     */
    public static Date getNextDateBegin(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DAY_OF_MONTH, 1);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    /**
     * 获取两个日期相差的月份 2015-10-10与 2015-11-09 相差为0月 2015-10-10与 2015-11-10 相差为1月
     *
     * @param date1
     * @param date2
     * @return
     */
    public static int getDistMonths(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            throw new RuntimeException("日期不允许为空");
        }

        int result;
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();
        cal1.setTime(date1.before(date2) ? date1 : date2);
        cal2.setTime(date1.before(date2) ? date2 : date1);
        if (cal2.get(Calendar.DAY_OF_MONTH) - cal1.get(Calendar.DAY_OF_MONTH) >= 0) {
            result = 12 * (cal2.get(Calendar.YEAR) - cal1.get(Calendar.YEAR)) + cal2.get(Calendar.MONTH)
                    - cal1.get(Calendar.MONTH);
        } else {
            result = 12 * (cal2.get(Calendar.YEAR) - cal1.get(Calendar.YEAR)) + cal2.get(Calendar.MONTH)
                    - cal1.get(Calendar.MONTH) - 1;
        }
        return result;
    }

    /**
     * 返回两个日期相差的天数
     *
     * @param startDate
     * @param endDate
     * @return
     * @throws ParseException
     */
    public static int getDistDates(Date startDate, Date endDate) {
        long totalDate = 0;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        long timestart = calendar.getTimeInMillis();
        calendar.setTime(endDate);
        long timeend = calendar.getTimeInMillis();
        totalDate = Math.abs(timeend - timestart) / (1000 * 60 * 60 * 24);
        return (int) totalDate;
    }

    public static int getDistDates(String startDate, String endDate) throws ParseException {
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
        Date sDate = dft.parse(startDate);
        Date eDate = dft.parse(endDate);

        long num = (eDate.getTime() - sDate.getTime()) / (1000 * 60 * 60 * 24);
        return (int) num;
    }

    public static int getDistHours(Date startTime, Date endTime) {
        long num = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60);
        return (int) num;
    }

    public static int getDistMinutes(Date startTime, Date endTime) {
        long num = (endTime.getTime() - startTime.getTime()) / 60000;
        return (int) num;
    }

    public static Date getPriorDayStartTime(int num, Date startTime) {
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
        Calendar date = Calendar.getInstance();
        if (startTime != null) {
            date.setTime(startTime);
        }
        date.add(Calendar.DATE, -num);
        String beforeDate = dft.format(date.getTime()) + " 00:00:00";
        Date fdate = null;
        try {
            fdate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(beforeDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return fdate;
    }

    public static int getQuarter(Date d) {
        int m = DateUtils.getMonth(d);
        if (0 < m && m < 4) {
            return 1;
        } else if (3 < m && m < 7) {
            return 2;
        } else if (6 < m && m < 10) {
            return 3;
        } else {
            return 4;
        }
    }

    public static String getDayOfWeekCn() {

        return DateUtils.getDayOfWeekCn(null);
    }

    public static String getDayOfWeekCn(Date date) {

        return DateUtils.getDayOfWeekCn(date, null);
    }

    public static String getDayOfWeekCn(Date date, String style) {
        Calendar calendar = Calendar.getInstance();
        if (date != null) {
            calendar.setTime(date);
        }
        LOG.debug("============= " + style + "  =========== " + date);
        if (StringUtils.isBlank(style)) {
            style = "星期";
        }
        String week = "";
        switch (calendar.get(Calendar.DAY_OF_WEEK)) {
            case 1: {
                week = style + "日";
            }
            break;
            case 2: {
                week = style + "一";
            }
            break;
            case 3: {
                week = style + "二";
            }
            break;
            case 4: {
                week = style + "三";
            }
            break;
            case 5: {
                week = style + "四";
            }
            break;
            case 6: {
                week = style + "五";
            }
            break;
            case 7: {
                week = style + "六";
            }
            break;
            default:
                break;
        }
        return week;
    }

    public static Date getPriorDayEndTime(int num, Date startTime) {
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
        Calendar date = Calendar.getInstance();
        if (startTime != null) {
            date.setTime(startTime);
        }
        date.add(Calendar.DATE, -num);
        String beforeDate = dft.format(date.getTime()) + " 23:59:59";
        Date fdate = null;
        try {
            fdate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(beforeDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return fdate;
    }

    /**
     * 获得当前日期与本周一相差的天数
     *
     * @return
     */
    public static int getMondayPlus() {
        Calendar cd = Calendar.getInstance();
        // 获得今天是一周的第几天，星期日是第一天，星期二是第二天......
        int dayOfWeek = cd.get(Calendar.DAY_OF_WEEK);
        if (dayOfWeek == 1) {
            return -6;
        } else {
            return 2 - dayOfWeek;
        }
    }

    /**
     * 获得本周星期日的日期
     *
     * @return
     */
    public static Date getCurrentWeekday() {
        int mondayPlus = DateUtils.getMondayPlus();
        GregorianCalendar currentDate = new GregorianCalendar();
        currentDate.add(Calendar.DATE, mondayPlus + 6);
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = dateFormat.parse(dft.format(currentDate.getTime()) + " 23:59:59");
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    /**
     * 获得本周星期一的日期
     *
     * @return
     */
    public static Date getCurrentMonday() {
        int mondayPlus = DateUtils.getMondayPlus();
        GregorianCalendar currentDate = new GregorianCalendar();
        currentDate.add(Calendar.DATE, mondayPlus);
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = dateFormat.parse(dft.format(currentDate.getTime()) + " 00:00:00");
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    /**
     * 获取当前时间到本周日凌晨所剩秒数
     *
     * @return
     */
    public static int getTimeMillisFromNowToWeekend() {
        long currentTimeMillis = System.currentTimeMillis();
        // 1000*60*60*8为本时区与UTC时差(8小时)
        long todayLeftTimeMillis = 1000 * 60 * 60 * 24
                - (currentTimeMillis % (1000 * 60 * 60 * 24) + 1000 * 60 * 60 * 8);
        // 明天到本周日的天数
        int days = DateUtils.getMondayPlus() + 6;
        return (int) (60 * 60 * 24 * days + todayLeftTimeMillis / 1000);
    }

    /**
     * 获取当前时间到今天凌晨所剩毫秒数
     *
     * @return
     */
    public static int getTimeMillisLeftToday() {
        long currentTimeMillis = System.currentTimeMillis();
        // 1000*60*60*8为本时区与UTC时差(8小时)
        long todayLeftTimeMillis = 1000 * 60 * 60 * 24
                - (currentTimeMillis % (1000 * 60 * 60 * 24) + 1000 * 60 * 60 * 8);
        return (int) todayLeftTimeMillis;
    }

    public static Date getNextDayTime(Date cDate, int num) {
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar date = Calendar.getInstance();
        try {
            date.setTime(cDate);
        } catch (Exception e) {
            e.printStackTrace();
        }
        date.set(Calendar.DATE, date.get(Calendar.DATE) + num);
        String nextDate = dft.format(date.getTime());
        Date ndate = null;
        try {
            ndate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(nextDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return ndate;
    }

    public static Date getDateWithDay(Date cDate, int num) {
        Calendar date = Calendar.getInstance();
        try {
            date.setTime(cDate);
        } catch (Exception e) {
            e.printStackTrace();
        }
        date.set(Calendar.DATE, date.get(Calendar.DATE) + num);
        return date.getTime();
    }

    public static Date getDateWithDay(String cDate, int num) {
        Date ndate = null;
        Calendar date = Calendar.getInstance();
        try {
            ndate = new SimpleDateFormat("yyyy-MM-dd").parse(cDate);
            date.setTime(ndate);
            date.set(Calendar.DATE, date.get(Calendar.DATE) + num);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date.getTime();
    }

    /**
     * 根据秒数计算出时长
     *
     * @return
     */
    public static String getTimeBySeconds(int seconds) {
        StringBuffer time = new StringBuffer();
        int day = seconds / (24 * 60 * 60);
        int hour = seconds % (24 * 60 * 60) / (60 * 60);
        int minute = seconds % (24 * 60 * 60) % (60 * 60) / 60;
        int second = seconds % (24 * 60 * 60) % (60 * 60) % 60;
        if (day == 0 && hour == 0 && minute == 0) {
            time.append(second + "秒");
        } else if (day == 0 && hour == 0) {
            time.append(minute + "分钟" + second + "秒");
        } else if (day == 0) {
            time.append(hour + "小时" + minute + "分钟" + second + "秒");
        } else {
            time.append(day + "天" + hour + "小时" + minute + "分钟" + second + "秒");
        }
        return time.toString();
    }

    /**
     * 获取当前月剩余天数
     *
     * @return
     */
    public static int getCurMonthDaysLeft() {
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        int day = c.getActualMaximum(Calendar.DAY_OF_MONTH);
        int passDay = c.get(Calendar.DAY_OF_MONTH);
        return day - passDay;
    }

    /**
     * 获取当前月天数
     *
     * @return
     */
    public static int getCurMonthDays() {
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        return c.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 日期(yyyy-MM-dd)
     *
     * @param date
     * @return
     */
    public static Date parseSimpleDate(Date date) {
        String pattern = "yyyy-MM-dd";
        try {
            return org.apache.commons.lang3.time.DateUtils.parseDate(org.apache.commons.lang3.time.DateFormatUtils.format(date, pattern), pattern);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 相差天数
     *
     * @param date1
     * @param date2
     * @return
     */
    public static Integer differenceDays(Date date1, Date date2) {
        date1 = DateUtils.parseSimpleDate(date1);
        date2 = DateUtils.parseSimpleDate(date2);
        return (int) ((date1.getTime() - date2.getTime()) / ONE_DAY);
    }

    /**
     * 获取字段
     *
     * @param date
     * @param field
     * @return
     */
    public static Integer getField(Date date, int field) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(field);
    }

    /**
     * 获取日期
     *
     * @param date
     * @return
     */
    public static Integer getDate(Date date) {
        return DateUtils.getField(date, Calendar.DATE);
    }

    /**
     * 转换日期，转换失败时返回null
     *
     * @param str
     * @param parsePatterns
     * @return
     */
    public static Date parse(String str, String parsePatterns) {
        try {
            return org.apache.commons.lang3.time.DateUtils.parseDate(str, parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取上月的时间
     *
     * @return
     */
    public static Date getPriorMonthDate() {
        Calendar c = Calendar.getInstance();
        c.add(Calendar.MONTH, -1);
        return c.getTime();
    }

    public static Date getPriorMonthDate(Date date1) {
        Calendar c = Calendar.getInstance();
        c.setTime(date1);
        c.add(Calendar.MONTH, -1);
        return c.getTime();
    }

    /**
     * 获取上个季度值
     *
     * @param date
     * @return
     */
    public static Date getPriorSeasonDate(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MONTH, -3);
        return c.getTime();
    }

    /**
     * 得到季度值
     *
     * @param date
     * @return
     */
    public static int getSeason(Date date) {
        int month = DateUtils.getMonth(date);
        if (month < 4) {
            return 1;
        }
        if (month >= 4 && month < 7) {
            return 2;
        }
        if (month >= 7 && month < 10) {
            return 3;
        } else {
            return 4;
        }
    }

    /**
     * 获取去年今天的日期
     *
     * @param date
     * @return
     */
    public static Date getPriorYearDate(Date date) {
        Calendar c = Calendar.getInstance();
        c.add(Calendar.YEAR, -1);
        return c.getTime();
    }

    /**
     * 得到下个月的日期
     *
     * @param date
     * @return
     */
    public static Date getNextMonthDate(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MONTH, 1);
        return c.getTime();
    }

    /**
     * 得到某个月的天数
     *
     * @param date
     * @return
     */
    public static int getMonthDaysByDate(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    public static boolean isAtSameDay(Date date1, Date date2) {
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        c1.setTime(date1);
        c2.setTime(date2);
        return c1.get(Calendar.DAY_OF_MONTH) == c2.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取较大的日期
     *
     * @param date1
     * @param date2
     * @return
     */
    public static Date max(Date date1, Date date2) {
        if (date1 == null) {
            return date2;
        }
        if (date2 == null) {
            return date1;
        }
        return date1.after(date2) ? date1 : date2;
    }

    public static boolean isMonday(Date date) {
        Calendar c1 = Calendar.getInstance();
        c1.setTime(date);
        if (c1.get(Calendar.DAY_OF_WEEK) == Calendar.MONDAY) {
            return true;
        }
        return false;
    }

    public static boolean isFirstDateOfMonth(Date date) {
        Calendar c1 = Calendar.getInstance();
        c1.setTime(date);
        if (c1.get(Calendar.DAY_OF_MONTH) == 1) {
            return true;
        }
        return false;
    }

    public static boolean isFirstDateOfYear(Date date) {
        Calendar c1 = Calendar.getInstance();
        c1.setTime(date);
        if (c1.get(Calendar.DAY_OF_YEAR) == 1) {
            return true;
        }
        return false;
    }

    public static Date getDateOfLastYear(Date dateEnd) {
        Calendar c1 = Calendar.getInstance();
        c1.setTime(dateEnd);
        c1.add(Calendar.YEAR, -1);
        return c1.getTime();
    }

    /**
     * 得到年初的日期
     *
     * @param lastYearDate
     * @return
     */
    public static Date getFirstDateOfYear(Date lastYearDate) {
        Calendar c1 = Calendar.getInstance();
        c1.setTime(lastYearDate);
        c1.set(c1.get(Calendar.YEAR), Calendar.JANUARY, 1);
        return c1.getTime();
    }

    /**
     * 得到年末的日期
     *
     * @param lastYearDate
     * @return
     */
    public static Date getLastDateOfYear(Date lastYearDate) {
        Calendar c1 = Calendar.getInstance();
        c1.setTime(lastYearDate);
        c1.set(c1.get(Calendar.YEAR), Calendar.DECEMBER, 31);
        return c1.getTime();
    }

    public static boolean isLastMonthDate(Date date) {
        Date date1 = DateUtils.getLastDate(date);
        if (date1.compareTo(date) == 0) {
            return true;
        }
        return false;
    }

    public static Date setDateBegin(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static Date setDateBegin(Date date, int day) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        day = Math.min(day, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        c.set(Calendar.DAY_OF_MONTH, day);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static Date setDateEnd(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        c.set(Calendar.MILLISECOND, 999);
        return c.getTime();
    }

    /**
     * 获取前一天的最后一毫秒
     *
     * @param date
     * @return
     */
    public static Date getLastDateEnd(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DAY_OF_MONTH, -1);
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        c.set(Calendar.MILLISECOND, 999);
        return c.getTime();
    }

    public static Date getDateByYearMonthDate(int year, int month, int date) {
        Calendar c = Calendar.getInstance();
        c.set(year, month - 1, date);
        return c.getTime();
    }

    /**
     * 比较两个时间的日期，1：大于，0：等于，-1：小于
     *
     * @param date1
     * @param date2
     * @return
     */
    public static Integer compareDate(Date date1, Date date2) {
        Calendar c1 = Calendar.getInstance();
        c1.setTime(date1);
        c1.set(Calendar.HOUR_OF_DAY, 0);
        c1.set(Calendar.MINUTE, 0);
        c1.set(Calendar.SECOND, 0);
        c1.set(Calendar.MILLISECOND, 0);

        Calendar c2 = Calendar.getInstance();
        c2.setTime(date2);
        c2.set(Calendar.HOUR_OF_DAY, 0);
        c2.set(Calendar.MINUTE, 0);
        c2.set(Calendar.SECOND, 0);
        c2.set(Calendar.MILLISECOND, 0);
        return c1.compareTo(c2);
    }

    public static Date getNextHourBegin(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.HOUR, 1);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static Date getNextDayBegin(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DATE, 1);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static Date getNextWeekBegin(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.WEEK_OF_YEAR, 1);
        c.set(Calendar.DAY_OF_WEEK, 1);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static Date getNextMonthBegin(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MONTH, 1);
        c.set(Calendar.DAY_OF_MONTH, 1);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static Date getNextYearBegin(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.YEAR, 1);
        c.set(Calendar.MONTH, 0);
        c.set(Calendar.DAY_OF_MONTH, 1);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static Date getCurHourBegin(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.HOUR, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static Date getCurDayBegin(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DATE, 0);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static Date getCurWeekBegin(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.WEEK_OF_YEAR, 0);
        c.set(Calendar.DAY_OF_WEEK, 1);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static Date getCurMonthBegin(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MONTH, 0);
        c.set(Calendar.DAY_OF_MONTH, 1);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static Date getCurYearBegin(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.YEAR, 0);
        c.set(Calendar.MONTH, 0);
        c.set(Calendar.DAY_OF_MONTH, 1);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static Date getLastHourBegin(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.HOUR, -1);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static Date getLastDayBegin(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DATE, -1);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static Date getLastWeekBegin(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.WEEK_OF_YEAR, -1);
        c.set(Calendar.DAY_OF_WEEK, 1);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static Date getLastMonthBegin(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MONTH, -1);
        c.set(Calendar.DAY_OF_MONTH, 1);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static Date getLastYearBegin(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.YEAR, -1);
        c.set(Calendar.MONTH, 0);
        c.set(Calendar.DAY_OF_MONTH, 1);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static boolean isSameDateInMonth(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();
        cal1.setTime(date1.before(date2) ? date1 : date2);
        cal2.setTime(date1.before(date2) ? date2 : date1);
        int diff = 12 * (cal2.get(Calendar.YEAR) - cal1.get(Calendar.YEAR)) + cal2.get(Calendar.MONTH)
                - cal1.get(Calendar.MONTH);
        cal1.add(Calendar.MONTH, diff);
        return org.apache.commons.lang3.time.DateUtils.isSameDay(cal1, cal2);
    }

    public static boolean isSameDateInYear(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();
        cal1.setTime(date1.before(date2) ? date1 : date2);
        cal2.setTime(date1.before(date2) ? date2 : date1);
        int diff = cal2.get(Calendar.YEAR) - cal1.get(Calendar.YEAR);
        cal1.add(Calendar.YEAR, diff);
        return org.apache.commons.lang3.time.DateUtils.isSameDay(cal1, cal2);
    }

    public static Integer compare(Date date1, Date date2) {
        Calendar c1 = Calendar.getInstance();
        c1.setTime(date1);

        Calendar c2 = Calendar.getInstance();
        c2.setTime(date2);
        return c1.compareTo(c2);
    }

    /**
     * 获取当天00:00:00
     *
     * @param date
     * @return
     */
    public static Date getDateBegin(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static boolean isWeekend(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        return dayOfWeek == 1 || dayOfWeek == 7;
    }

    /**
     * 根据当期日期返回长日期格式字符串 精确到小时
     *
     * @param localDateTime
     * @return
     */
    public static String getCurrentLongHourTimeStr(LocalDateTime localDateTime) {
        return LONG_HOURTIME_FORMATTER.format(localDateTime);
    }


    /**
     * 根据字符串返回 localDateTime
     *
     * @param strDate
     * @return
     */
    public static LocalDateTime getLocalDateTimeByStr(String strDate) {
        return LocalDateTime.parse(strDate, LONG_TIME_FORMATTER);
    }


    public static String format(Date date, String pattern) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().format(DateTimeFormatter.ofPattern(pattern));
    }

    public static Date getDateZeroMinAndSec(Date date) {
        Calendar calender = Calendar.getInstance();
        calender.setTime(date);
        calender.set(Calendar.MINUTE, 0);
        calender.set(Calendar.SECOND, 0);
        return calender.getTime();
    }

    public static Date getDateWithHourAndZeroSec(Date date, int hour) {
        Calendar calender = Calendar.getInstance();
        calender.setTime(date);
        calender.add(Calendar.HOUR, hour);
        calender.set(Calendar.MINUTE, 0);
        calender.set(Calendar.SECOND, 0);
        return calender.getTime();
    }

    public static String timeStampToDateStr(long timestamp) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(new Date(timestamp));
    }

    public static void main(String[] args) {
    	/*Date date = getFormatDateOnDayAndTime("2017-02-12 12:30:00");
    	System.out.println(getFormatDateOnTime(getNextHourBegin(date)));
    	System.out.println(getFormatDateOnTime(getNextDayBegin(date)));
    	System.out.println(getFormatDateOnTime(getNextWeekBegin(date)));
    	System.out.println(getFormatDateOnTime(getNextMonthBegin(date)));
    	System.out.println(getFormatDateOnTime(getNextYearBegin(date)));
    	System.out.println("------------");
    	System.out.println(getFormatDateOnTime(getCurHourBegin(date)));
    	System.out.println(getFormatDateOnTime(getCurDayBegin(date)));
    	System.out.println(getFormatDateOnTime(getCurWeekBegin(date)));
    	System.out.println(getFormatDateOnTime(getCurMonthBegin(date)));
    	System.out.println(getFormatDateOnTime(getCurYearBegin(date)));*/

        Date d1 = DateUtils.getFormatDateOnDayAndTime("2016-02-29 12:30:00");
        Date d2 = DateUtils.getFormatDateOnDayAndTime("2017-02-28 12:30:00");
        System.out.println(DateUtils.isSameDateInMonth(d1, d2));
        System.out.println(DateUtils.isSameDateInYear(d1, d2));
        System.out.println(timeStampToDateStr(1672209864000L));
    }
}