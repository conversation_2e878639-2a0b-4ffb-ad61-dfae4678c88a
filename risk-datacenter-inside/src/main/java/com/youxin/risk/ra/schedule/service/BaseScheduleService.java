package com.youxin.risk.ra.schedule.service;

import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public abstract class BaseScheduleService {
	
	protected Logger logger = getLogger();
	
	@Autowired
	private ScheduledThreadPoolExecutor scheduledThreadPool;
	
	protected long initialDelay;

	protected long delay;

	public long getInitialDelay() {
		return initialDelay;
	}

	public void setInitialDelay(long initialDelay) {
		this.initialDelay = initialDelay;
	}

	public long getDelay() {
		return delay;
	}

	public void setDelay(long delay) {
		this.delay = delay;
	}
	
	protected abstract Logger getLogger();
	
	protected abstract void runTask();
	
	public void startTask() {
		logger.info("begin to run task [{}], initialDelay = {}s, delay = {}", 
				logger.getName(), initialDelay, delay);
		try {
			scheduledThreadPool.scheduleWithFixedDelay(new Runnable() {
	             @Override
	             public void run() {
	            	 runTask();
	             }
	         }, initialDelay, delay, TimeUnit.SECONDS);	
		} catch (Exception e) {
			logger.error("task [{}] fail, reason is {}", logger.getName(), e.getMessage());
		}
		
	}

}
