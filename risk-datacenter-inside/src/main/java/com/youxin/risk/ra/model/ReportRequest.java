package com.youxin.risk.ra.model;

import com.youxin.risk.commons.constants.AnalysisSystemStatus;
import com.youxin.risk.ra.timeinterface.CreateTimeSetter;
import com.youxin.risk.ra.timeinterface.UpdateTimeSetter;

import java.io.Serializable;
import java.util.Date;

public class ReportRequest implements CreateTimeSetter, UpdateTimeSetter,
		Serializable {

	private static final long serialVersionUID = 673908283346620148L;

	public ReportRequest(){}
	public ReportRequest(Integer loanId, Double latitude, Double longitude){
		this.loanId = loanId;
		this.latitude = latitude;
		this.longitude = longitude;
	}

	private Integer id;

	private Integer applyId;

	protected String sourceSystem;

	private String userKey;

	private Integer loanId;

	private String loanKey;

	private Integer loanDuration;

	private Double principalAmount;

	private Double limitAmount;

	private Double latitude;

	private Double longitude;

	/** 风险分析处理状态 */
	private AnalysisSystemStatus status;

	/** 报告key */
	private String reportKey;

	private Date updateTime;

	private Date createTime;

	private Integer version;

    private String wifiSSID;

    private String wifiLevel;

    private String wifiMac;

    private Integer batteryLevel;

    private String batteryPlugType;

    private String deviceName;

    private Integer lowBatteryMode;

    private String shard;

    private Integer periodNo;

    private String step;

    private String fundChannel;

    private String authVersion;


	@Override
	public void setUpdateTime() {
		this.updateTime = new Date();
	}

	@Override
	public void setCreateTime() {
		this.createTime = new Date();
	}

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getApplyId() {
		return this.applyId;
	}

	public void setApplyId(Integer applyId) {
		this.applyId = applyId;
	}

	public Integer getLoanId() {
		return this.loanId;
	}

	public void setLoanId(Integer loanId) {
		this.loanId = loanId;
	}

	public Integer getLoanDuration() {
		return this.loanDuration;
	}

	public void setLoanDuration(Integer loanDuration) {
		this.loanDuration = loanDuration;
	}

	public Double getPrincipalAmount() {
		return this.principalAmount;
	}

	public void setPrincipalAmount(Double principalAmount) {
		this.principalAmount = principalAmount;
	}

	public Double getLimitAmount() {
		return this.limitAmount;
	}

	public void setLimitAmount(Double limitAmount) {
		this.limitAmount = limitAmount;
	}

	public Double getLatitude() {
		return this.latitude;
	}

	public void setLatitude(Double latitude) {
		this.latitude = latitude;
	}

	public Double getLongitude() {
		return this.longitude;
	}

	public void setLongitude(Double longitude) {
		this.longitude = longitude;
	}

	public AnalysisSystemStatus getStatus() {
		return this.status;
	}

	public void setStatus(AnalysisSystemStatus status) {
		this.status = status;
	}

	public String getReportKey() {
		return this.reportKey;
	}

	public void setReportKey(String reportKey) {
		this.reportKey = reportKey;
	}

	public Date getUpdateTime() {
		return this.updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Date getCreateTime() {
		return this.createTime;
	}

	public String getShard() {
		return this.shard;
	}
	public void setShard(String shard) {
		this.shard = shard;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Integer getVersion() {
		return this.version;
	}

	public void setVersion(Integer version) {
		this.version = version;
	}

	public String getUserKey() {
		return this.userKey;
	}

	public void setUserKey(String userKey) {
		this.userKey = userKey;
	}

	public String getSourceSystem() {
		return this.sourceSystem;
	}

	public void setSourceSystem(String sourceSystem) {
		this.sourceSystem = sourceSystem;
	}
	public String getWifiSSID() {
		return this.wifiSSID;
	}
	public void setWifiSSID(String wifiSSID) {
		this.wifiSSID = wifiSSID;
	}
	public String getWifiLevel() {
		return this.wifiLevel;
	}
	public void setWifiLevel(String wifiLevel) {
		this.wifiLevel = wifiLevel;
	}
	public String getWifiMac() {
		return this.wifiMac;
	}
	public void setWifiMac(String wifiMac) {
		this.wifiMac = wifiMac;
	}
	public Integer getBatteryLevel() {
		return this.batteryLevel;
	}
	public void setBatteryLevel(Integer batteryLevel) {
		this.batteryLevel = batteryLevel;
	}
	public String getBatteryPlugType() {
		return this.batteryPlugType;
	}
	public void setBatteryPlugType(String batteryPlugType) {
		this.batteryPlugType = batteryPlugType;
	}
	public String getDeviceName() {
		return this.deviceName;
	}
	public void setDeviceName(String deviceName) {
		this.deviceName = deviceName;
	}
	public Integer getLowBatteryMode() {
		return this.lowBatteryMode;
	}
	public void setLowBatteryMode(Integer lowBatteryMode) {
		this.lowBatteryMode = lowBatteryMode;
	}
	public Integer getPeriodNo() {
		return this.periodNo;
	}
	public void setPeriodNo(Integer periodNo) {
		this.periodNo = periodNo;
	}
	public String getStep() {
		return this.step;
	}
	public void setStep(String step) {
		this.step = step;
	}
	public String getLoanKey() {
		return this.loanKey;
	}
	public void setLoanKey(String loanKey) {
		this.loanKey = loanKey;
	}
    public String getFundChannel() {
        return fundChannel;
    }
    public void setFundChannel(String fundChannel) {
        this.fundChannel = fundChannel;
    }
    public String getAuthVersion() {
        return authVersion;
    }
    public void setAuthVersion(String authVersion) {
        this.authVersion = authVersion;
    }

}
