package com.youxin.risk.alert.configuration;

import com.youxin.risk.alert.config.InfluxDBConfig;
import com.youxin.risk.alert.config.InfluxDBInitConfig;
import com.youxin.risk.alert.influxdb.InfluxDbAPIV2;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @description: InfluxDb实例化类
 * @author: juxiang
 * @create: 2022-03-14 14:39
 **/
@Configuration
public class InfluxDBConfiguration {
    @Bean(name = "riskMonitorInfluxDb")
    public InfluxDbAPIV2 riskMonitorInfluxDb(@Qualifier("influxDBConfig") InfluxDBConfig influxDBConfig){
        InfluxDBInitConfig influxDBInitConfig=new InfluxDBInitConfig();
        influxDBInitConfig.setDatabase(influxDBConfig.getRiskMonitorDatabase());
        influxDBInitConfig.setPassword(influxDBConfig.getRiskMonitorPassword());
        influxDBInitConfig.setUrl(influxDBConfig.getRiskMonitorUrl());
        influxDBInitConfig.setUserName(influxDBConfig.getRiskMonitorUserName());
        influxDBInitConfig.setRetentionPolicy("one_month");
        return new InfluxDbAPIV2(influxDBInitConfig);
    }

}
