package com.youxin.risk.alert.web.controller;

import com.youxin.risk.commons.model.AlertCollectCondition;
import com.youxin.risk.commons.model.AlertCollectConf;
import com.youxin.risk.commons.model.AlertPolicy;
import com.youxin.risk.commons.utils.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;

@RestController
public class AlertSqlBuildController {

    private Logger logger = LoggerFactory.getLogger(AlertSqlBuildController.class);

    public static void main(String[] args) {

        String collectCode = "risk_gateway_qps";

        buildAlertCollectConfSql(collectCode);
        buildAlertPolicySql(collectCode);
    }

    private static void buildAlertPolicySql(String collectCode){
        AlertPolicy policy = new AlertPolicy();
        policy.setPolicyName("risk_gateway_qps");
        policy.setPolicyDesc("新风控网关qps过小");
        policy.setAlertLevel("NOTICE");
        policy.setApplicationName("risk");
        policy.setAlerts("email");
        policy.setOutOfChars("");
        policy.setGroupNames("");
        policy.setUserNames("liujuyang");
        policy.setIsCollect(1);
        policy.setLeftParam(collectCode);
        policy.setOperator("");
        policy.setRightParamType("final");
        policy.setLogic("<=");
        policy.setParam(new BigDecimal("10000.00"));

        System.out.println(buildPolicySql(policy));

    }
    private static void buildAlertCollectConfSql(String collectCode){
        // SELECT count(value) FROM risk_gateway_http_qps WHERE time > now() - %s and eventCode!='NONE' GROUP BY time(%s), eventCode fill(0)
        AlertCollectCondition alertCollectCondition = new AlertCollectCondition();
        alertCollectCondition.setLogic("!=");
        alertCollectCondition.setLeftParamType("");
        alertCollectCondition.setLeftParam("eventCode");
        alertCollectCondition.setCollectCode(collectCode);
        alertCollectCondition.setRightParamType("");
        alertCollectCondition.setRightParam("NONE");
        System.out.println(buildCollectConfCondiSql(alertCollectCondition));

        AlertCollectConf alertCollectConf = new AlertCollectConf();
        alertCollectConf.setCollectCode(collectCode);
        alertCollectConf.setCollectDesc("新风控网关请求qps");
        alertCollectConf.setDatasourceName("antifraud_risk_gw");
        alertCollectConf.setTableName("risk_gateway_http_qps");
        alertCollectConf.setFunction("count");
        alertCollectConf.setFunctionField("value");
        alertCollectConf.setCollectMinutes(20);
        alertCollectConf.setTags("eventCode");

        ArrayList<AlertCollectCondition> conditions = new ArrayList<>();
        conditions.add(alertCollectCondition);
        alertCollectConf.setConditions(conditions);

        System.err.println(alertCollectConf.getQueryCommand());
        System.err.println(alertCollectConf.getYesQueryCommand());

        System.out.println(buildCollectConfSql(alertCollectConf));
    }

    private static String buildPolicySql(AlertPolicy model) {
        String sql = "insert into alert_admin_policy ";
        sql += "(policy_name, policy_desc, alert_level, application_name, out_of_chars,group_names, " +
                "user_names, alerts, block_time, start_time_day, end_time_day, create_time, " +
                "update_time,minutes,times,left_param,operator,right_param_type,logic,param,is_collect)";
        sql += "values(";
        sql += field(model.getPolicyName());
        sql += field(model.getPolicyDesc());
        sql += field(model.getAlertLevel());
        sql += field(model.getApplicationName());
        sql += field(model.getOutOfChars());
        sql += field(model.getGroupNames());
        sql += field(model.getUserNames());
        sql += field(model.getAlerts());
        sql += field("2018-12-12 00:00:00");
        sql += field("2018-12-12 00:00:00");
        sql += field("2018-12-12 23:59:59");
        sql += "now(),now(),";
        sql += model.getMinutes() + ",";
        sql += model.getTimes() + ",";
        sql += field(model.getLeftParam());
        sql += field(model.getOperator());
        sql += field(model.getRightParamType());
        sql += field(model.getLogic());
        sql += model.getParam().toString() + ",";
        sql += model.getIsCollect() + "";
        sql += ");";
        return sql;

    }

    private static String buildCollectConfCondiSql(AlertCollectCondition model) {
        String sql = "insert into alert_admin_collect_condition ";
        sql += "(collect_code, left_param_type, left_param, logic, right_param, right_param_type, create_time, update_time)";
        sql += "values(";
        sql += field(model.getCollectCode());
        sql += field(model.getLeftParamType());
        sql += field(model.getLeftParam());
        sql += field(model.getLogic());
        sql += field(model.getRightParam());
        sql += field(model.getRightParamType());
        sql += "now(),now()";
        sql += ");";
        return sql;
    }

    private static String buildCollectConfSql(AlertCollectConf alertCollectConf) {
        String collectCode = alertCollectConf.getCollectCode();
        String collectDesc = alertCollectConf.getCollectDesc();
        String datasourceName = alertCollectConf.getDatasourceName();
        String tableName = alertCollectConf.getTableName();
        String function = alertCollectConf.getFunction();
        String functionField = alertCollectConf.getFunctionField();
        long collectMinutes = alertCollectConf.getCollectMinutes();
        String tags = alertCollectConf.getTags();
        String sql = "insert into alert_admin_collect_conf ";
        sql += "(collect_code, collect_desc, datasource_name, table_name, function, function_field, collect_minutes, tags, create_time, update_time)";
        sql += "values(";
        sql += field(collectCode);
        sql += field(collectDesc);
        sql += field(datasourceName);
        sql += field(tableName);
        sql += field(function);
        sql += field(functionField);
        sql += collectMinutes + ",";
        sql += field(tags);
        sql += "now(),now()";
        sql += ");";
        return sql;
    }

    private static String field(String v) {
        return "'" + v + "',";
    }
}