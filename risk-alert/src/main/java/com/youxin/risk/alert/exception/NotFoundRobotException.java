package com.youxin.risk.alert.exception;

import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;

/**
 * 没找到机器人配置异常
 *
 * <AUTHOR>
 */
public class NotFoundRobotException extends RuntimeException {

    public final ApolloNamespaceEnum namespace;
    public final String configKey;

    public NotFoundRobotException(ApolloNamespaceEnum namespace, String configKey, String alertType) {
        super("在["+namespace.namespace+"]的["+configKey+"配置项下没有找到alertType="+alertType+"的机器人信息");
        this.namespace = namespace;
        this.configKey = configKey;
    }
}
