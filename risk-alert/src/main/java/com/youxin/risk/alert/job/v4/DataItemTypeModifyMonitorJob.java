package com.youxin.risk.alert.job.v4;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.youxin.risk.alert.collecter.Collecter;
import com.youxin.risk.alert.collecter.CollecterFactory;
import com.youxin.risk.alert.collecter.impl.InfluxDBCollecter;
import com.youxin.risk.alert.constants.AlertSourceEnum;
import com.youxin.risk.alert.model.DataItemDiffTypeEntity;
import com.youxin.risk.alert.sender.impl.RobotAlertSender;
import com.youxin.risk.alert.vo.AlertEvent;
import com.youxin.risk.alert.vo.DataItemTypeMonitorParam;
import com.youxin.risk.alert.vo.QueryResultModel;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.commons.model.AlertPolicy;
import com.youxin.risk.commons.tools.redis.RetryableJedis;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.xxl.job.XxlJobBase;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022/8/29 19:59
 * @desc
 */
@Component
public class DataItemTypeModifyMonitorJob implements XxlJobBase {
    private Logger logger = LoggerFactory.getLogger(DataItemTypeModifyMonitorJob.class);

    @Resource(name = "RobotAlertSender")
    private RobotAlertSender sender;
    @Autowired
    private RetryableJedis retryableJedis;

    private final static String PTAH_SPLIT = "-";


    @Override
    @XxlJob(value = "dataItemTypeModifyMonitorJob")
    public ReturnT<String> execJobHandler(String param) {
        logger.info("dataItemTypeModifyMonitorJob start");
        process(param);
        logger.info("dataItemTypeModifyMonitorJob end");
        return ReturnT.SUCCESS;
    }

    private void process(String param) {
        try {
            DataItemTypeMonitorParam monitorParam = JSON.parseObject(param, DataItemTypeMonitorParam.class);

            // 查询语句
            String currentQueryCommand = getCommand(monitorParam.getWindow());
            String beforeQueryCommand = getYesCommand(monitorParam.getWindow(), monitorParam.getDays());
            // 查询数据且结构化
            Collecter collecter = CollecterFactory.getCollecter(CollecterFactory.DatasourceType.influxdb);
            Map<String, Object> currentQueryResult = collecter.parseResult(collecter.collect(currentQueryCommand));
            Map<String, Object> beforeQueryResult = collecter.parseResult(collecter.collect(beforeQueryCommand));
            LoggerProxy.info("dataItemTypeModifyMonitor", logger, "currentQueryResult size={}, " +
                            "beforeQueryResult size={}", currentQueryResult.size(), beforeQueryResult.size());
            //获取新增的数据项类型
            String diffDataType = getDiffDataType(beforeQueryResult, currentQueryResult, monitorParam);

            //获取缺失的数据项
            String missDataItem = getMissDataItem(beforeQueryResult, currentQueryResult, monitorParam);
            if (StringUtils.isNotBlank(diffDataType)) {
                alert(monitorParam.getTitles().get(0), diffDataType, monitorParam.getRobotId());
            }
            if (StringUtils.isNotBlank(missDataItem)) {
                alert(monitorParam.getTitles().get(1), missDataItem, monitorParam.getRobotId());
            }
        } catch (Exception e) {
            LoggerProxy.error("process",logger,"数据项类型监控报警异常,msg=", e);
        }

    }

    /**
     * 现在与之前相比 同一个service 缺失的数据项
     *
     * @param yesDataItemTypeResult   之前数据项集合
     * @param todayDataItemTypeResult 现在的数据项集合
     * @param monitorParam
     * @return
     */
    private String getMissDataItem(Map<String, Object> yesDataItemTypeResult,
                                   Map<String, Object> todayDataItemTypeResult, DataItemTypeMonitorParam monitorParam) {
        Map<String, Double> yesPathAndCountMap = buildPathAndCountMap(yesDataItemTypeResult);
        Map<String, Double> todayPathAndCountMap = buildPathAndCountMap(todayDataItemTypeResult);
        yesPathAndCountMap.keySet().removeAll(todayPathAndCountMap.keySet());
//        LoggerProxy.info("missDataItem", logger, "filter pre data={}", JSON.toJSONString(yesPathAndCountMap));
        //根据事件编码过滤
        filterByEventCode(yesPathAndCountMap, todayPathAndCountMap);
        //apollo配置指定的过滤项
        filterByApolloConfig(yesPathAndCountMap);
        //xxljob参数配置默认过滤条件
        filterByXxlJobParam(yesPathAndCountMap, monitorParam);
        //数据源下线，或者当前数据源请求量少
        filterByServiceCode(yesPathAndCountMap, todayPathAndCountMap,monitorParam);
        LoggerProxy.info("missDataItem", logger, "filter after data={}", JSON.toJSONString(yesPathAndCountMap));

        for (String path : yesPathAndCountMap.keySet()) {
            retryableJedis.incrBy(path, 1);
            retryableJedis.expire(path, monitorParam.getRedisKeyExpireSeconds());
        }
        Map<String, DataItemDiffTypeEntity> diffTypeEntityMap =
                yesPathAndCountMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,
                e -> new DataItemDiffTypeEntity(0, e.getValue())));
        return transferToAlertMsg(diffTypeEntityMap, monitorParam.getDays());
    }

    /**
     * 根据preKey(eventCode+serviceCode)过滤
     * 当前某事件某数据源请求量较少时，如十分钟小于10条（或者数据源下线，请求量为0），则不报警
     * @param yesPathAndCountMap
     * @param todayPathAndCountMap
     * @param monitorParam
     */
    private void filterByServiceCode(Map<String, Double> yesPathAndCountMap, Map<String, Double> todayPathAndCountMap
            , DataItemTypeMonitorParam monitorParam) {

        Map<String, Double> todayServiceCodeAndMaxCountMap =
                todayPathAndCountMap.entrySet().stream()
                        .collect(Collectors.toMap(e -> getEventCodeAndServiceCodeByAllPath(e.getKey()), Map.Entry::getValue, Double::max));

        yesPathAndCountMap.keySet().removeIf(key -> {
            String eventCodeAndServiceCode = getEventCodeAndServiceCodeByAllPath(key);
            Double todayServiceRequestCount = todayServiceCodeAndMaxCountMap.get(eventCodeAndServiceCode);
            if (todayServiceRequestCount == null || todayServiceRequestCount < monitorParam.getCountThreshold()) {
                LoggerProxy.info("missDataItem", logger, "filterByServiceCode, key={}, yesCount={}, " +
                                "todayServiceRequestCount={}", key, yesPathAndCountMap.get(key), todayServiceRequestCount);
                return true;
            }
            return false;
        });
    }

    String getEventCodeAndServiceCodeByAllPath(String allPath) {
        if (!allPath.contains(PTAH_SPLIT)) {
            return allPath;
        }
        return allPath.substring(0, allPath.lastIndexOf(PTAH_SPLIT));
    }


    /**
     * 根据事件编码过滤，若当前无该事件数据，则该事件下的数据项缺失无需报警
     * 主要是针对跑批的场景，每天跑批的时间不一样，
     * @param yesPathAndCountMap
     * @param todayPathAndCountMap
     */
    private void filterByEventCode(Map<String, Double> yesPathAndCountMap, Map<String, Double> todayPathAndCountMap) {
        Set<String> todayEventCodeSet =
                todayPathAndCountMap.keySet().stream().map(e -> e.split(PTAH_SPLIT)[0]).collect(Collectors.toSet());
        yesPathAndCountMap.keySet().removeIf(key -> {
            String yesEventCode = key.split(PTAH_SPLIT)[0];
            return !todayEventCodeSet.contains(yesEventCode);
        });
    }

    /**
     * apollo配置指定的过滤项,支持配置全路径以及serviceCode
     * @param yesPathAndCountMap
     */
    private void filterByApolloConfig(Map<String, ?> yesPathAndCountMap) {
        List<String> ignoreItems = ApolloClientAdapter.getListConfig(ApolloNamespaceEnum.ALERT_SPACE, "data.monitor" +
                ".ignore.items", String.class);
        yesPathAndCountMap.keySet().removeIf(allPath -> {
            String serviceCode = allPath.split(PTAH_SPLIT)[1];
            if (ignoreItems.contains(allPath) || ignoreItems.contains(serviceCode)) {
                return true;
            }
            return false;
        });
    }

    private void filterByXxlJobParam(Map<String, Double> yesPathAndCountMap, DataItemTypeMonitorParam monitorParam) {
        yesPathAndCountMap.entrySet().removeIf(e -> e.getValue() < monitorParam.getCountThreshold() );
    }

    /**
     * 现在与之前相比 相同serviceCode 相同path, 新的type
     *
     * @param leftQueryResult
     * @param rightQueryResult
     * @param param
     * @return
     */
    private String getDiffDataType(Map<String, Object> leftQueryResult, Map<String, Object> rightQueryResult,
                                   DataItemTypeMonitorParam param) {

        Map<String, Map<String, Object>> rightDataItemTypeMap = buildPathAndTypesMap(rightQueryResult);
        Map<String, Map<String, Object>> leftDataItemTypeMap = buildPathAndTypesMap(leftQueryResult);
        Map<String, DataItemDiffTypeEntity> newDataItemTypeMap = new HashMap<>();

        for (Map.Entry<String, Map<String, Object>> entry : leftDataItemTypeMap.entrySet()) {
            Map<String, Object> leftTypeAndCountMap = entry.getValue();
            Map<String, Object> rightTypeAndCountMap = rightDataItemTypeMap.get(entry.getKey());
            if (MapUtils.isEmpty(rightTypeAndCountMap)) {
                continue;
            }
            Map<String, Object> rightTypeAndCountMapCopy = deepCopy(rightTypeAndCountMap, Map.class);
            rightTypeAndCountMapCopy.keySet().removeAll(leftTypeAndCountMap.keySet());
            //有新的类型就报警
            if (MapUtils.isNotEmpty(rightTypeAndCountMapCopy)) {
                DataItemDiffTypeEntity dataItemDiffTypeEntity = new DataItemDiffTypeEntity(rightTypeAndCountMap,
                        leftTypeAndCountMap);
                newDataItemTypeMap.put(entry.getKey(), dataItemDiffTypeEntity);
            }
        }
        LoggerProxy.info("diffDataType", logger, "filter pre, data={}", JSON.toJSONString(newDataItemTypeMap));
        filterByApolloConfig(newDataItemTypeMap);
        newDataItemTypeMap = filterByXxlJobParam(newDataItemTypeMap, param.getCountThreshold(), param.getPercentThreshold());
        LoggerProxy.info("diffDataType", logger, "filter after, data={}", JSON.toJSONString(newDataItemTypeMap));
        for (String path : newDataItemTypeMap.keySet()) {
            retryableJedis.incrBy(path, 1);
            retryableJedis.expire(path, param.getRedisKeyExpireSeconds());
        }

        return transferToAlertMsg(newDataItemTypeMap, param.getDays());
    }

    private Map<String, DataItemDiffTypeEntity> filterByXxlJobParam(Map<String, DataItemDiffTypeEntity> newDateItemTypeMap,
                                                                    int countThreshold, double percentThreshold) {
        Map<String, DataItemDiffTypeEntity> result = new HashMap<>(16);

        for (Map.Entry<String, DataItemDiffTypeEntity> entry : newDateItemTypeMap.entrySet()) {
            DataItemDiffTypeEntity diffTypeEntity = entry.getValue();
            Map<String, Object> today = (Map) diffTypeEntity.getLeft();
            Map<String, Object> yes = (Map) diffTypeEntity.getRight();
            //小于十条无需报警且小于20%
            Map<String, Object> todayCopy = deepCopy(today, Map.class);
            todayCopy.keySet().removeAll(yes.keySet());
            long sum = today.values().stream().mapToLong(e -> ((Number) e).longValue()).sum();
            long yesSum = yes.values().stream().mapToLong(e -> ((Number) e).longValue()).sum();
            boolean flag = false;
            for (Map.Entry<String, Object> typeAndCountMap : todayCopy.entrySet()) {
                Number value = (Number) typeAndCountMap.getValue();
                //新增类型数量小于countThreshold 或者 新增类型数量/当前时间段总数量<=percentThreshold 或者 之前时间段总数量<countThreshold  过滤掉无需报警
                if (value.intValue() < countThreshold || 1.0 * value.intValue() / sum <= percentThreshold || yesSum < countThreshold) {
                    flag = true;
                }
            }
            if (flag) {
                continue;
            }
            result.put(entry.getKey(), entry.getValue());
        }
        return result;
    }

    /**
     * 深拷贝
     * @param data
     * @param clazz
     * @return
     */
    private <T> T deepCopy(Object data, Class<T> clazz) {
        String str = JSON.toJSONString(data);
        return JSONObject.parseObject(str, clazz);
    }

    private void alert(String title, String msg, String robotId) {
        AlertPolicy policy = new AlertPolicy();
        policy.setRobotKey(robotId);
        policy.setPolicyName("数据项（缺失）类型（变更）报警");
        AlertEvent alertEvent = new AlertEvent(AlertSourceEnum.riskAlert, msg);
        alertEvent.setTitle(title);
        alertEvent.setAlertPolicy(policy);
        sender.send(alertEvent);
    }


    /**
     * 转换为报警信息
     * @param alertMsgMap
     * @param days
     * @return
     */
    private String transferToAlertMsg(Map<String, DataItemDiffTypeEntity> alertMsgMap, Double days) {
        StringBuilder sb = new StringBuilder();
        if (MapUtils.isEmpty(alertMsgMap)) {
            return sb.toString();
        }
        //根据重复告警次数倒排
        Map<String, Integer> pathAndCountMap = new HashMap<>(16);
        for (String key : alertMsgMap.keySet()) {
            pathAndCountMap.put(key, Integer.parseInt(retryableJedis.get(key)));
        }
        List<String> sortKeys = pathAndCountMap.entrySet().stream()
                .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
                .map(Map.Entry::getKey).collect(Collectors.toList());
        for (String key : sortKeys) {
            if (sb.length() > 1000) {
                break;
            }

            sb.append(key).append(":").append("\n")
                    .append(String.format("当前/%s天前：", days)).append(alertMsgMap.get(key))
                    .append(String.format(" 已重复告警%s次!", pathAndCountMap.get(key)))
                    .append("\n");
        }
        return sb.toString();
    }

    List<QueryResultModel> buildResultModelList(Map<String, Object> retNowMap) {
        String status = (String) retNowMap.get("status");
        if (!Collecter.ParseStatusEnum.no_data.toString().equals(status) && !Collecter.ParseStatusEnum.OK.toString().equals(status)) {
            LoggerProxy.warn("queryMabyeFailed", logger, "status = {}", status);
            return new ArrayList<>();
        }
//        retNowMap.remove("status");
        List<QueryResultModel> resultModelList = new ArrayList<>();
        for (String key : retNowMap.keySet()) {
            if ("status".equals(key)) {
                continue;
            }
            QueryResultModel nowModel = (QueryResultModel) retNowMap.get(key);
            if (null == nowModel) {
                LoggerProxy.error("queryMabyeFailed", logger, "");
                continue;
            }
            resultModelList.add(nowModel);
        }
        return resultModelList;
    }
    String buildAllPath(QueryResultModel queryResultModel) {
        JSONObject jsonTags = JSON.parseObject(queryResultModel.getTags());
        String eventCode = jsonTags.getString("eventCode");
        String serviceCode = jsonTags.getString("serviceCode");
        String path = jsonTags.getString("path");
        return eventCode + PTAH_SPLIT + serviceCode + PTAH_SPLIT + path;
    }

    /**
     * key=serviceCode+"-"+path， value=type集合
     *
     * @param retNowMap
     * @return
     */
    private Map<String, Map<String, Object>> buildPathAndTypesMap(Map<String, Object> retNowMap) {
        List<QueryResultModel> resultModelList = buildResultModelList(retNowMap);
        Map<String, Map<String, Object>> resultMap = new HashMap<>();
        for (QueryResultModel resultModel : resultModelList) {
            JSONObject jsonTags = JSON.parseObject(resultModel.getTags());
            String type = jsonTags.getString("type");
            String allPath = buildAllPath(resultModel);
            resultMap.computeIfAbsent(allPath, e -> new HashMap<>(4)).put(type, resultModel.getResult());
        }
        return resultMap;
    }


    private Map<String, Double> buildPathAndCountMap(Map<String, Object> retNowMap) {
        List<QueryResultModel> resultModelList = buildResultModelList(retNowMap);
        Map<String, Double> resultMap = new TreeMap<>();
        for (QueryResultModel resultModel : resultModelList) {
            String allPath = buildAllPath(resultModel);
            resultMap.merge(allPath, resultModel.getResult(), Double::sum);
        }
        return resultMap;
    }

    private String getCommand(int window) {
        return "SELECT sum(count) FROM third_data_item_type_7d " +
                "where time > now() - " + window + "m GROUP BY eventCode,serviceCode,path,type fill(0) " +
                "&db=antifraud_risk_data_monitor";
    }

    private static String getYesCommand(int window, double days) {
        return "SELECT sum(count) FROM third_data_item_type_7d " +
                "where time > now() - " + (int)(days * 24 * 60 + window) + "m and time < now() - " + (int)(days * 24 * 60) + "m GROUP BY eventCode,serviceCode,path,type fill(0) &db=antifraud_risk_data_monitor";
    }

    public static void main(String[] args) throws IOException {
        String param = "{\n" +
                "    \"window\": 10,\n" +
                "    \"days\": 0.1,\n" +
                "    \"redisKeyExpireSeconds\": 720,\n" +
                "    \"countThreshold\": 10,\n" +
                "    \"percentThreshold\": 0.2,\n" +
                "    \"robotId\": \"25ecb049-bad4-48ff-b150-7f783e1618ac\"\n" +
                "}";
        //根据重复告警次数倒排
        Map<String, Integer> pathAndCountMap = new HashMap<>(16);
        pathAndCountMap.put("a", 1);
        pathAndCountMap.put("b", 3);
        pathAndCountMap.put("c", 2);
        pathAndCountMap.put("d", 4);
        List<String> sortKeys = pathAndCountMap.entrySet().stream()
                .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
                .map(Map.Entry::getKey).collect(Collectors.toList());
        System.out.println(sortKeys);

//        Integer timeWindow = (Integer) configMap.getOrDefault("window", 60);
//        Integer redisKeyExpireSeconds = (Integer) configMap.getOrDefault("redisKeyExpireSeconds", 12 * 60);
//        Integer countThreshold = (Integer) configMap.getOrDefault("countThreshold", 10);
//        Double days = (Double) configMap.getOrDefault("days", 3);
//        Double percentThreshold = (Double) configMap.getOrDefault("percentThreshold", 0.2);
//        String robotId = (String) configMap.getOrDefault("robotId", "25ecb049-bad4-48ff-b150-7f783e1618ac");


        // 查询数据且结构化
        Collecter collecter = CollecterFactory.getCollecter(CollecterFactory.DatasourceType.influxdb);
        byte[] bytes = Files.readAllBytes(Paths.get("C:\\Users\\<USER>\\Desktop\\Untitled-42.json"));
        String yesStr = new String(bytes, StandardCharsets.UTF_8);
        byte[] bytes1 = Files.readAllBytes(Paths.get("C:\\Users\\<USER>\\Desktop\\Untitled-44.json"));
        String currStr = new String(bytes1, StandardCharsets.UTF_8);
        Map<String, Object> currentQueryResult =
                new InfluxDBCollecter().parseResult(currStr);
        Map<String, Object> beforeQueryResult = new InfluxDBCollecter().parseResult(yesStr);
//        LoggerProxy.info("dataItemTypeModifyMonitor", logger, "currentQueryResult size={}, " +
//                "beforeQueryResult size={}", currentQueryResult.size(), beforeQueryResult.size());
        //获取新增的数据项类型
        DataItemTypeModifyMonitorJob dataItemTypeModifyMonitorJob = new DataItemTypeModifyMonitorJob();
        String diffDataType = dataItemTypeModifyMonitorJob.getDiffDataType(beforeQueryResult,
                currentQueryResult, JSONObject.parseObject(param,DataItemTypeMonitorParam.class));

        //获取缺失的数据项
        String missDataItem = dataItemTypeModifyMonitorJob.getMissDataItem(beforeQueryResult, currentQueryResult,
                JSONObject.parseObject(param,DataItemTypeMonitorParam.class));
        System.out.println("ss");
    }
}
