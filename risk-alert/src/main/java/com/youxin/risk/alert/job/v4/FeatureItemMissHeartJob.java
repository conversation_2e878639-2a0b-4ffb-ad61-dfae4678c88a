package com.youxin.risk.alert.job.v4;

import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create  2022/3/10 10:08
 * @desc    特征项缺失率监控任务附带的心跳检测job
 **/
@Component
public class FeatureItemMissHeartJob extends FeatureItemMissMonitorJob {


    private final Logger logger = LoggerFactory.getLogger(FeatureItemMissHeartJob.class);

    @Override
    @XxlJob(value = "featureItemMissHeartJob")
    public ReturnT<String> execJobHandler(String param) {
        JSONObject jsonObject = JSONObject.parseObject(param);
        String robotId = jsonObject.getString("robotId");
        String isHeart = jsonObject.getString("isHeart");
        String featureName = jsonObject.getString("featureName");
        logger.info("featureItemMissHeartJob start");
        process(robotId, isHeart, featureName, 1);
        return ReturnT.SUCCESS;
    }
}
