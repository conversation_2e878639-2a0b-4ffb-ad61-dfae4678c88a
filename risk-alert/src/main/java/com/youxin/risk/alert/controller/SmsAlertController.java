package com.youxin.risk.alert.controller;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.alert.dto.SmsAlertDTO;
import com.youxin.risk.alert.grafana.dto.GrafanaAlertMessage;
import com.youxin.risk.alert.sender.impl.SmsAlertSender;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/sms")
public class SmsAlertController {

    @Autowired
    private SmsAlertSender alertSender;

    private static final Logger LOGGER = LoggerFactory.getLogger(SmsAlertController.class);


    @RequestMapping(value = "/sendSms", method = RequestMethod.POST)
    public String sendSms(@RequestBody SmsAlertDTO alertDTO) {
        LoggerProxy.info("sendSms", LOGGER, " sendSms receive requestBody={}", JSON.toJSONString(alertDTO));
        String res = alertSender.sendSms(alertDTO.getPhoneNos(), alertDTO.getSmsType().toString(), alertDTO.getTitle());

        return res;
    }
}
