package com.youxin.risk.alert.collecter;

import com.youxin.risk.alert.collecter.impl.InfluxDBCollecter;

public class CollecterFactory {

    public static enum DatasourceType {
        influxdb;
    }

    public static Collecter getCollecter(DatasourceType datasourceType) {
        if (DatasourceType.influxdb == datasourceType) {
            return InfluxDBCollecter.getInstance();
        }
        throw new IllegalArgumentException("not support '" + datasourceType + "'");
    }

}
