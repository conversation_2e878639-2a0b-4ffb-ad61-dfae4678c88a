package com.youxin.risk.alert.grafana.dto;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.alert.exception.ParseAlertTypeException;
import com.youxin.risk.commons.utils.LoggerProxy;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 封装grafana报警信息
 *
 * <AUTHOR>
 */
@Data
public class GrafanaAlertMessage {
    private static final Logger LOGGER = LoggerFactory.getLogger(GrafanaAlertMessage.class);
    public static final String ALERT_TYPE = "alertType";

    private String message;
    private Integer ruleId;
    private String ruleName;
    private String ruleUrl;
    private String state;
    private String title;

    public String parseAlertType(GrafanaAlertMessage alertMessage) {
        try {
            return  (String) JSON.parseObject(alertMessage.getMessage()).get(ALERT_TYPE);
        } catch (Exception e) {
            LoggerProxy.error("parseAlertTypeError", LOGGER, "", e);
            throw new ParseAlertTypeException(message, e);
        }
    }
}
