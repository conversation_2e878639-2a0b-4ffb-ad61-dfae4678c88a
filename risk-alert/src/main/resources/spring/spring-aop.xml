<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns="http://www.springframework.org/schema/beans"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:cache="http://www.springframework.org/schema/cache"
	xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
     http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
     http://www.springframework.org/schema/aop
     http://www.springframework.org/schema/aop/spring-aop-4.0.xsd
     http://www.springframework.org/schema/context
     http://www.springframework.org/schema/context/spring-context-4.0.xsd
     http://www.springframework.org/schema/util
     http://www.springframework.org/schema/util/spring-util-4.0.xsd 
     http://www.springframework.org/schema/tx
     http://www.springframework.org/schema/tx/spring-tx-4.0.xsd
     http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-4.0.xsd">

	<bean id="logElapsedTimeHandler" class="com.youxin.risk.commons.aop.LogElapsedTimeHandler">
		<property name="threshold" value="1" />
	</bean>
	
	<bean id="retryInvokeHandler" class="com.youxin.risk.commons.aop.RetryInvokeHandler">
		<property name="retryNum" value="3" />
		<property name="period" value="50" />
	</bean>
	
	<aop:config proxy-target-class="true">
		<aop:advisor advice-ref="logElapsedTimeHandler" pointcut="execution(* com.youxin..*Mapper.*(..)) " />
<!-- 		<aop:advisor advice-ref="retryInvokeHandler" -->
<!-- 			pointcut="execution(* com.ris..GwUnionDispatchTmpMapper.*(..)) || execution(* com.ris..GwProxyAgencyAnalyzeLogMapper.*(..))" /> -->
	</aop:config>

</beans>