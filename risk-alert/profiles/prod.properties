mode.name=prod
app.name=risk-alert

home.base=/opt/app/tomcat

app.home=${home.base}/webapps/ROOT
app.log.home=${catalina.base}/logs

tomcat.home=${home.base}

tomcat.port=8051
tomcat.shutdown.port=8052
tomcat.connection.timeout=5000
tomcat.doc.base=${app.home}
tomcat.allow.ips=172.*.*.*||127.0.0.1||10.*.*.*

java.opts=-Xmx2000m -Xms2000m -Xmn1000m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=128m \
		-verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xloggc:$CATALINA_HOME/logs/gc.log \
		-XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$CATALINA_HOME/logs/oom.log \
		-Djava.nio.channels.spi.SelectorProvider=sun.nio.ch.EPollSelectorProvider \
        -Dfile.encoding=UTF8  -Duser.timezone=GMT+08

console.log.level=OFF

datasource.maxActive=10
datasource.initialSize=2
datasource.minIdle=2
datasource.maxWait=2000
datasource.testOnBorrow=true
datasource.defaultTransactionIsolation=4
datasource.timeBetweenEvictionRunsMillis=30000
datasource.minEvictableIdleTimeMillis=300000
datasource.timeBetweenLogStatsMillis=300000
datasource.druid.remove.abandoned=false
datasource.druid.remove.abandoned.timeout=300
datasource.druid.log.abandoned=false
datasource.connectProperties.connectTimeout=1000
datasource.connectProperties.socketTimeout=5000
datasource.logAbandoned=false
datasource.removeAbandoned=true
datasource.removeAbandonedTimeout=120
datasource.poolPreparedStatements=false
#datasource.filters=stat,wall,log4j
datasource.filters=stat,wall

datasource.url.params=characterEncoding=utf8&amp;autoReconnect=true&amp;zeroDateTimeBehavior=convertToNull&amp;useUnicode=true&amp;useOldAliasMetadataBehavior=true

admin.datasource.url=****************************************?${datasource.url.params}
admin.datasource.username=${SEC_RISK_ADMIN_DB_USERNAME}
admin.datasource.pwd=${SEC_RISK_ADMIN_DB_PASSWORD}

alertlog.datasource.url=****************************************?${datasource.url.params}
alertlog.datasource.username=${SEC_RISK_ALERT_DB_USERNAME}
alertlog.datasource.pwd=${SEC_RISK_ALERT_DB_PASSWORD}

datacenter.datasource.url=**********************************************?${datasource.url.params}
datacenter.datasource.username=${SEC_RISK_DATACENTER_DB_USERNAME}
datacenter.datasource.pwd=${SEC_RISK_DATACENTER_DB_PASSWORD}

cp.datasource.url=************************************************?${datasource.url.params}
cp.datasource.username=${SEC_RISK_COMPUTING_PLATFORM_DB_USERNAME}
cp.datasource.pwd=${SEC_RISK_COMPUTING_PLATFORM_DB_PASSWORD}

risk.admin.url=http://antifraud-risk-admin.weicai.com.cn
risk.gateway.url=http://antifraud-risk-gateway.rrdbg.com

# Redis集群信息，不同类型的Redis集群！！！不要随意共用
# 如果访问量大，需要单独一个，量不大的可以共用一个。
# 比如：监控打点单独一个, 数据缓存单独的，
# http://wiki.youxin.com/pages/viewpage.action?pageId=48993218
redis.maxTotal=300
redis.maxIdle=30
redis.minIdle=10
redis.maxWaitMillis=5000
redis.testOnBorrow=true
redis.cluster.connectionTimeout=3000
redis.cluster.soTimeout=3000
redis.cluster.maxAttempts=1
redis.cluster.password=1c1zOc9cHCGE9xmOZ51jzM
redis.cluster.nodes=***********:6398,***********:6395,***********:6396,************:6393,************:6394,***********:6399

metrics.influxdb.server=http://************:8086
metrics.influxdb.server.username=risk_influx
metrics.influxdb.server.password=cVuyOkUwwlXzICqK

metrics.remote.queue.server=${redis.cluster.nodes}
metrics.remote.queue.redis.password=${redis.cluster.password}

#监控打点的kafka
metrics.point.kafka.hosts=kafka1.cdh.app.rrd:9092,kafka2.cdh.app.rrd:9092,kafka3.cdh.app.rrd:9092
#metrics.point.kafka.hosts=kafka5.cdh:9092,kafka6.cdh:9092,kafka7.cdh:9092
metrics.point.kafka.topic=metrics.point.kafka.topic
metrics.point.kafka.group.id=metrics.point.kafka.group.alert
metrics.point.kafka.topic.list=metrics.point.kafka.topic,metrics.point.kafka.topic.gateway
metrics.point.mirror.kafka.hosts=kafka5.cdh:9092,kafka6.cdh:9092,kafka7.cdh:9092

sms.appKey = aa61c0b2fbd70271fdc87cc1213c6c247b0c7c55
sms.appSecret = b81b523e2ea44e3a3f8f2edca008800ef11d44e4e19d65818a5a62a7acc67dacffe96342527fff4d97b26fb734c5f4b719ea6282cb2c0774a1ba7a3dae72b247
sms.text.appKey=4aa9ba32fce59864503e757ee82ce5061a64aa8c
sms.text.appSecret=21c28c40b1e820919dbab9fba4fe21e27e89f0b22e25a8eb678bfd9428a8d418e79f9708359b4f039cf8f1757ec8b65833035a296ebfa5920364a2c011bf3eb6
sms.url.base = http://sms.weicaitech.com
sms.send.url = /sms/v1/send
#企业微信配置
wechat.base.url = https://qyapi.weixin.qq.com/cgi-bin
wechat.refresh.accessToken.url = /gettoken
wechat.send.msg.url = /message/send?access_token
wechat.robot.url = https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=
wechat.robot.key = 035523b2-f049-4ae4-9c1e-289ad21ab4c8

xxl.job.admin.addresses=http://risk-xxl-job-manager.weicai.com.cn
xxl.job.accessToken=
xxl.job.executor.appname=risk-alert
xxl.job.executor.address=
xxl.job.executor.ip=
xxl.job.executor.port=-1
xxl.job.executor.logpath=/tmp/
xxl.job.executor.logretentiondays=-1

fp.url=http://antifraud-risk-fp.weicai.com.cn

youxin.env=PROD

influx.riskMonitorDatabase=antifraud_risk_monitor
