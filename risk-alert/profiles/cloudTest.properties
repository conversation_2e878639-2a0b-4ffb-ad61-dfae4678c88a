mode.name=test
app.name=risk-alert

home.base=/home/<USER>

app.home=${home.base}/risk-control/${app.name}
app.log.home=/opt/app/tomcat/logs

tomcat.home=${home.base}/products/tomcat/tomcat_risk_alert
tomcat.port=8051
tomcat.shutdown.port=8052
tomcat.connection.timeout=5000
tomcat.doc.base=${app.home}
tomcat.allow.ips=172.*.*.*||127.0.0.1||10.*.*.*

java.opts=-Xmx2000m -Xms2000m -Xmn1000m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=128m \
		-verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xloggc:$CATALINA_HOME/logs/gc.log \
		-XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$CATALINA_HOME/logs/oom.log \
		-Djava.nio.channels.spi.SelectorProvider=sun.nio.ch.EPollSelectorProvider \
        -Dfile.encoding=UTF8  -Duser.timezone=GMT+08

console.log.level=DEBUG

datasource.maxActive=200
datasource.initialSize=2
datasource.minIdle=2
datasource.maxWait=2000
datasource.testOnBorrow=true
datasource.defaultTransactionIsolation=4
datasource.timeBetweenEvictionRunsMillis=30000
datasource.minEvictableIdleTimeMillis=300000
datasource.timeBetweenLogStatsMillis=300000
datasource.druid.remove.abandoned=false
datasource.druid.remove.abandoned.timeout=300
datasource.druid.log.abandoned=false
datasource.connectProperties.connectTimeout=1000
datasource.connectProperties.socketTimeout=5000
datasource.logAbandoned=false
datasource.removeAbandoned=true
datasource.removeAbandonedTimeout=120
datasource.poolPreparedStatements=false
#datasource.filters=stat,wall,log4j
datasource.filters=stat,wall

datasource.url.params=characterEncoding=utf8&amp;autoReconnect=true&amp;zeroDateTimeBehavior=convertToNull&amp;useUnicode=true&amp;useOldAliasMetadataBehavior=true

admin.datasource.url=************************************************************************?${datasource.url.params}
admin.datasource.username=test_rw
admin.datasource.pwd=wLErsPqZ7eeQghdE

alertlog.datasource.url=************************************************************************?${datasource.url.params}
alertlog.datasource.username=test_rw
alertlog.datasource.pwd=wLErsPqZ7eeQghdE

datacenter.datasource.url=*****************************************************************************?${datasource.url.params}
datacenter.datasource.username=test_rw
datacenter.datasource.pwd=wLErsPqZ7eeQghdE

cp.datasource.url=****************************************************************?${datasource.url.params}
cp.datasource.username=test_rw
cp.datasource.pwd=wLErsPqZ7eeQghdE

# todo admin test cloud config
risk.admin.url=http://***********:8040
risk.gateway.url=http://antifraud-risk-gateway.test.rrdbg.com

redis.maxTotal=8
redis.maxIdle=8
redis.minIdle=4
redis.maxWaitMillis=5000
redis.testOnBorrow=true
redis.cluster.connectionTimeout=3000
redis.cluster.soTimeout=3000
redis.cluster.maxAttempts=1
redis.cluster.password=d3799e0d-4e40-42
redis.cluster.nodes=r-2ze3b4116f021d94.redis.rds.aliyuncs.com:6379


metrics.influxdb.server=http://***********:8086
metrics.influxdb.server.username=admin
metrics.influxdb.server.password=admin
influx.riskMonitorDatabase=antifraud_risk_monitor


metrics.point.kafka.hosts=haofenqi-kafka-cname.test.weicai.com.cn:29092
metrics.point.kafka.topic=metrics.point.kafka.topic_test
metrics.point.kafka.group.id=metrics.point.kafka.group_test
metrics.point.kafka.topic.list=metrics.point.kafka.topic_test,metrics.point.kafka.topic.gateway_test
metrics.point.mirror.kafka.hosts=haofenqi-kafka-cname.test.weicai.com.cn:29092

sms.appKey = cff6f51530a9b40611e8e307e3716d2e24fd61f5
sms.appSecret = 1a73c0ebf803e4d49ac92fc1e5c9d02fa157d4d0a7a4a6c508fe9f4cf306d0821662abdb8a9920afb95bca6d1c05adb41a332fa5959b3bf2d114390642cc4e6b
sms.text.appKey=4aa9ba32fce59864503e757ee82ce5061a64aa8c
sms.text.appSecret=21c28c40b1e820919dbab9fba4fe21e27e89f0b22e25a8eb678bfd9428a8d418e79f9708359b4f039cf8f1757ec8b65833035a296ebfa5920364a2c011bf3eb6
sms.url.base = http://***********:7001
sms.send.url = /sms/v1/send
#\u4F01\u4E1A\u5FAE\u4FE1\u914D\u7F6E
wechat.base.url = https://qyapi.weixin.qq.com/cgi-bin
wechat.refresh.accessToken.url = /gettoken
wechat.send.msg.url = /message/send?access_token
wechat.robot.url = https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=
wechat.robot.key = f8695cff-e3b8-49dd-abb2-78693494d15e

xxl.job.admin.addresses=http://risk-xxl-job-manager.test.weicai.com.cn
xxl.job.accessToken=
xxl.job.executor.appname=risk-alert
xxl.job.executor.address=
xxl.job.executor.ip=
xxl.job.executor.port=9999
xxl.job.executor.logpath=/tmp/
xxl.job.executor.logretentiondays=-1

fp.url=http://antifraud-risk-fp.weicai.com.cn

youxin.env=DEV

zeus.api.url=https://zeus.weicai.com.cn
zeus.api.timeout=60000

block.qw.ext.op.url=http://antifraud-risk-admin.test.weicai.com.cn/api/block/qw_ext
block.qw.ext.robot.key=77175507-2ae6-4eb1-8e05-05210e58ccaf