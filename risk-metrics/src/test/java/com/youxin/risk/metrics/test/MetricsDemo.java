package com.youxin.risk.metrics.test;

import com.youxin.risk.metrics.MetricsAPI;
import com.youxin.risk.metrics.config.ConfigUtils;
import com.youxin.risk.metrics.helpers.Utils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;

public class MetricsDemo {

    // 使用说明
    // 0：必须依赖：slf4j、logback（或log4j）、fastjson
    //    0.1: 如果远程队列使用redis，则需要依赖jedis
    //    0.2: 如果需要kafka则依赖kafka相关api
    //    0.3: 自定义的消息推送器自行添加依赖
    // 1: 使用说明
    //    1.1: 在项目resources下增加metrics.properties配置，参数说明见default-metrics.properties
    //    1.2: metrics.properties会覆盖default-metrics.properties中相同配置
    //    1.3: 打点使用MetricsAPI.point(point, tags, value)即可
    //    1.4: metrics相关的异常信息只会输出日志，不会影响业务
    //    1.5: 可通过metrics.stop=true关闭metrics
    // 2: Demo
    //    2.1: 本demo的metrics.properties在test/resources下
    //    2.2: 测试代码在risk-metrics/src/test/java/com.../MetricsDemo.java
    public static void main(String[] args) throws IOException {

        String point = "demo_liujy";
        Map<String, String> tags = new HashMap<>();
        Map<String, Number> value = new HashMap<>();
        tags.put("retcode", "S1000");
        tags.put("riscode", "12000");

        value.put("amount", 1300);
        value.put("cost", 1111);

        AtomicInteger count = new AtomicInteger(0);

        int size = 1;

        int tsize = 1;
        CountDownLatch l = new CountDownLatch(tsize);

        long start = System.currentTimeMillis();
        for (int i = 0; i < tsize; i++) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    String tid = "" + Thread.currentThread().getId();
                    for (; ; ) {
                        HashMap<String, Number> vs = new HashMap<>(value);
                        vs.put("cost", new Random().nextInt(10000));
                        MetricsAPI.point(point, tags, vs);
                        Utils.threadSleep(10);
                        if (count.incrementAndGet() >= size) {
                            l.countDown();
                            break;
                        }
                    }
                }
            }).start();
        }

        try {
            l.await();
            System.err.println("sendmsgsize=" + count.get() + ",cost=" + (System.currentTimeMillis() - start));
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        Utils.threadSleep(2000);
        System.exit(0);

    }
}