package com.youxin.risk.metrics.helpers;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LoggerUtils {

    public static final Logger METRICS_LOGGER = LoggerFactory.getLogger("com.youxin.risk.Metrics");


    public static final void reportPoint(String format, Object... args) {
        if (Utils.isIsLogPoint()) {
            METRICS_LOGGER.info(format, args);
        }
    }

    public static final void report(String format, Object... args) {
        METRICS_LOGGER.info(format, args);
    }

    public static final void report(String msg, Throwable t) {
        METRICS_LOGGER.warn(msg, t);
    }


    public static final void debug(String format, Object... args) {
        if (METRICS_LOGGER.isDebugEnabled()) {
            METRICS_LOGGER.debug("[debug] " + format, args);
        }
    }
}
