package com.youxin.risk.metrics;

import com.youxin.risk.metrics.constants.Constent;
import com.youxin.risk.metrics.enums.MetricsOpType;
import com.youxin.risk.metrics.helpers.DestroyUtils;
import com.youxin.risk.metrics.helpers.InitializingUtils;
import com.youxin.risk.metrics.helpers.LoggerUtils;
import com.youxin.risk.metrics.model.PointModel;

import java.util.HashMap;
import java.util.Map;

public class MetricsAPI {

    private static boolean isOK = false;

    static {
        isOK = InitializingUtils.init();
        DestroyUtils.addHook();
        LoggerUtils.report("metrics init: " + isOK);
    }

    /**
     * 适合某一方法或接口的qps监控
     *
     * @param point
     */
    public static void point(String point) {//1  (无调用)
        point(point, 1);
    }

    /**
     * 适合某一方法或接口的qps监控（可以加条件，比如retCode等）
     *
     * @param point
     */
    public static void point(String point, Map<String, String> tags) {
        HashMap<String, Number> values = new HashMap<>();
        values.put(Constent.INFLUXDB_VALUE_FIELD, 1);
        point(point, tags, values);
    }

    /**
     * 适合某一方法或接口的qps监控（可以加条件，比如retCode等）
     *
     * @param isCollect  是否聚合
     */
    public static void point(String point, Map<String, String> tags ,boolean isCollect) {
        HashMap<String, Number> values = new HashMap<>();
        values.put(Constent.INFLUXDB_VALUE_FIELD, 1);
        point(point, tags, values,isCollect,MetricsOpType.count);
    }

    /**
     * 适合某一方法或接口的qps、耗时监控
     *
     * @param point
     */
    public static void point(String point, Number value) {
        point(point, new HashMap<String, String>(), value);
    }

    /**
     * 适合某一方法或接口的qps、耗时监控（可以加条件，比如retCode等）
     *
     * @param point
     */
    public static void point(String point, Map<String, String> tags, Number value) {
        HashMap<String, Number> values = new HashMap<>();
        values.put(Constent.INFLUXDB_VALUE_FIELD, value);
        point(point, tags, values);
    }

    /**
     * 适合某一方法或接口的qps、耗时监控（可以加条件，比如retCode等）
     *
     * @param isCollect  是否聚合
     * @param type  操作类型
     */
    public static void point(String point, Map<String, String> tags, Number value,boolean isCollect,MetricsOpType type) {
        HashMap<String, Number> values = new HashMap<>();
        values.put(Constent.INFLUXDB_VALUE_FIELD, value);
        point(point, tags, values,isCollect,type);
    }


    /**
     * 适合多维度监控，例如：风控对外的成功率、通过率、耗时、金额等监控
     *
     * @param point  埋点，与namespace组合全局唯一，长度不大于128
     * @param tags   标签，一般是查询的条件
     * @param values 埋点数据，一般是需要监控的数据，比如耗时、金额等
     */
    public static void point(String point, Map<String, String> tags, Map<String, Number> values) {
        if (false == isOK) {
            return;
        }
        pointCheck(point);
        PointModel pointModel = new PointModel();
        pointModel.setPoint(point);
        pointModel.setTags(new HashMap<String, String>(tags));
        pointModel.setValue(new HashMap<String, Number>(values));

        Metricser.getMetricser().append(pointModel);
    }

    /**
     * 适合多维度监控，例如：风控对外的成功率、通过率、耗时、金额等监控
     *
     * @param point     埋点，与namespace组合全局唯一，长度不大于128
     * @param tags      标签，一般是查询的条件
     * @param values    埋点数据，一般是需要监控的数据，比如耗时、金额等
     * @param isCollect 是否聚合
     * @param type      操作类型
     */
    public static void point(String point, Map<String, String> tags, Map<String, Number> values, boolean isCollect,
                             MetricsOpType type,String namespace) {

        if (false == isOK) {
            return;
        }
        pointCheck(point);
        PointModel pointModel = new PointModel();
        pointModel.setNamespace(namespace);
        pointModel.setPoint(point);
        pointModel.setTags(new HashMap<String, String>(tags));
        pointModel.setValue(new HashMap<String, Number>(values));

        pointModel.setToBeCollected(isCollect);
        pointModel.setOpType(type.name());
        Metricser.getMetricser().append(pointModel);
    }


    /**
     * 适合多维度监控，例如：风控对外的成功率、通过率、耗时、金额等监控
     *
     * @param point  埋点，与namespace组合全局唯一，长度不大于128
     * @param tags   标签，一般是查询的条件
     * @param values 埋点数据，一般是需要监控的数据，比如耗时、金额等
     * @param isCollect  是否聚合
     * @param type  操作类型
     */
    public static void point(String point, Map<String, String> tags, Map<String, Number> values, boolean isCollect, MetricsOpType type) {
        if (false == isOK) {
            return;
        }
        pointCheck(point);
        PointModel pointModel = new PointModel();
        pointModel.setPoint(point);
        pointModel.setTags(new HashMap<String, String>(tags));
        pointModel.setValue(new HashMap<String, Number>(values));

        pointModel.setToBeCollected(isCollect);
        pointModel.setOpType(type.name());
        Metricser.getMetricser().append(pointModel);
    }


    private static void pointCheck(String point){
        if (null == point) {
            throw new IllegalArgumentException("'point' can't be null");
        }
        point = point.trim();
        if (0 == point.length() || 128 < point.length()) {
            throw new IllegalArgumentException("'point' is invalid");
        }
    }



}