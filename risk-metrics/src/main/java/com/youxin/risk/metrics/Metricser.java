package com.youxin.risk.metrics;

import com.youxin.risk.metrics.config.ConfigUtils;
import com.youxin.risk.metrics.constants.PropertiesKey;
import com.youxin.risk.metrics.constants.RemoteQueueEnum;
import com.youxin.risk.metrics.exception.MetricsRuntimeException;
import com.youxin.risk.metrics.helpers.LoggerUtils;
import com.youxin.risk.metrics.helpers.ThreadUtils;
import com.youxin.risk.metrics.model.PointModel;
import com.youxin.risk.metrics.sender.Sender;
import com.youxin.risk.metrics.sender.SenderFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

public class Metricser {

    private static final Metricser metricser = new Metricser();

    /**
     * 批量处理大小
     */
    private int batchSize;

    /**
     * 批量间隔,单位:毫秒. 只有batchSize大于1时才有效
     */
    private static final long BATCH_INTERVAL = 100;

    private ArrayBlockingQueue<PointModel> pointLocalQueue;

    private Executor consumerPool;

    private Sender sender;

    private boolean isInit;

    private Metricser() {

    }

    public static Metricser getMetricser() {
        return metricser;
    }

    public void init() {
        if (isInit) {
            return;
        }
        synchronized (Metricser.class) {
            if (isInit) {
                return;
            }
            try {
                batchSize = ConfigUtils.getInteger(PropertiesKey.POINT_PULL_BATCH_SIZE);
                sender = SenderFactory.builderSender();
                initConsumePool();
                startBatchExtraThread();
            } catch (Exception e) {
                throw new MetricsRuntimeException("init sender failed, errmsg=" + e.getMessage(), e);
            } finally {
                isInit = true;
            }
        }
    }

    void append(PointModel model) {
        // 失败就丢弃
        boolean ret = pointLocalQueue.offer(model);
        if (!ret) {
            LoggerUtils.debug("offer local queue false");
        }
        if (pointLocalQueue.size() < batchSize) {
            return;
        }
        // 立即执行
        doAppend();
    }

    private void doAppend() {
        if (pointLocalQueue == null || pointLocalQueue.isEmpty()) {
            return;
        }
        List<PointModel> ms = new ArrayList<>();
        pointLocalQueue.drainTo(ms, batchSize);
        if (ms.isEmpty()) {
            return;
        }
        consumerPool.execute(new Runnable() {
            public void run() {
                try {
                    sender.pull(ms);
                } catch (Exception e) {
                    LoggerUtils.report("queue message pull error, errmsg=" + e.getMessage());
                }
            }
        });
    }

    /**
     * 启动批处理补偿线程
     */
    private void startBatchExtraThread() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                while (!Thread.interrupted()) {
                    try {
                        doAppend();
                    } catch (Exception e) {
                        LoggerUtils.report("batch extra thread process error， errmsg=" + e.getMessage());
                    }
                    try {
                        Thread.sleep(BATCH_INTERVAL);
                    } catch (InterruptedException e) {
                        // nothing
                    }
                }
            }
        }).start();
    }

    private void initConsumePool() {
        int size = ConfigUtils.getInteger(PropertiesKey.POINT_PULL_THREAD_SIZE);
        int queueSize = ConfigUtils.getInteger(PropertiesKey.LOCAL_QUEUE_SIZE);
        ThreadPoolExecutor consumerPool = new ThreadPoolExecutor(size, size,
                1L, TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(queueSize),
                ThreadUtils.defaultThreadFactory(), ThreadUtils.defaultHandler());
        this.consumerPool = consumerPool;
        this.pointLocalQueue = new ArrayBlockingQueue<PointModel>(queueSize);
    }
}