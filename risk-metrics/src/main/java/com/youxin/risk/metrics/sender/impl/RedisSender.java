package com.youxin.risk.metrics.sender.impl;

import com.youxin.risk.metrics.config.ConfigUtils;
import com.youxin.risk.metrics.constants.PropertiesKey;
import com.youxin.risk.metrics.helpers.LoggerUtils;
import com.youxin.risk.metrics.helpers.Utils;
import com.youxin.risk.metrics.helpers.redis.MJedis;
import com.youxin.risk.metrics.model.PointModel;
import com.youxin.risk.metrics.sender.Sender;

import java.util.List;

public class RedisSender implements Sender {

    private int retries;

    private String queueKey;

    private MJedis mJedis = MJedis.getMJedis();

    private long queueMaxSize;

    public RedisSender() {
        this.retries = ConfigUtils.getInteger(PropertiesKey.REMOTE_QUEUE_RETRIES);
        this.queueKey = ConfigUtils.getString(PropertiesKey.REMOTE_QUEUE_REDIS_KEY);
        this.queueMaxSize = ConfigUtils.getLong(PropertiesKey.REMOTE_QUEUE_REDIS_MAX_SIZE);
    }

    @Override
    public void pull(List<PointModel> points) {
        if (null == points || points.isEmpty()) {
            return;
        }
        String[] values = new String[points.size()];
        for (int i = 0, size = points.size(); i < size; i++) {
            values[i] = points.get(i).toJson();
        }
        int i = 0;
        do {
            i++;
            try {
                long size = mJedis.llen(queueKey);
                if (size >= queueMaxSize) {
                    LoggerUtils.report("OutOfRemoteQueueMaxSize, size={},maxSize={}, queuekey={}", size, queueMaxSize, queueKey);
                    break;
                }
                mJedis.rpushAndClose(queueKey, values);
                LoggerUtils.debug("pull.key={}, values.size={}", queueKey, values.length);
                break;
            } catch (Exception e) {
                if (i < retries) {
                    Utils.threadSleep(50);
                } else {
                    LoggerUtils.report("pull to redis failed, errmsg=" + e.getMessage());
                }
            }
        } while (i <= retries);
    }
}