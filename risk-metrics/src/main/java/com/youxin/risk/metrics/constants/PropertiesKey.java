package com.youxin.risk.metrics.constants;

public class PropertiesKey {

    public static final String NAMESPACE = "metrics.namespace";
    public static final String METRICS_STOP = "metrics.stop";

    public static final String REMOTE_QUEUE_TYPE = "metrics.remote.queue";
    public static final String REMOTE_QUEUE_SERVER = "metrics.remote.queue.server";

    public static final String REMOTE_PULL_SENDER_TYPE = "metrics.pull.sender.type";

    public static final String REMOTE_QUEUE_RETRIES = "metrics.remote.queue.retries";
    public static final String REMOTE_QUEUE_TIMEOUT = "metrics.remote.queue.timeout";

    public static final String LOCAL_QUEUE_SIZE = "metrics.local.queue.size";
    public static final String POINT_PULL_THREAD_SIZE = "metrics.pull.thread.size";
    public static final String POINT_PULL_BATCH_SIZE = "metrics.pull.batch.size";

    public static final String REMOTE_REDIS_IS_CLUSTER = "metrics.remote.redis.is.cluster";
    public static final String REMOTE_QUEUE_REDIS_KEY = "metrics.remote.queue.redis.key";
    public static final String REMOTE_QUEUE_REDIS_MAX_SIZE = "metrics.remote.queue.redis.max.size";
    public static final String REMOTE_QUEUE_REDIS_PASSWORD = "metrics.remote.queue.redis.password";

    public static final String REMOTE_QUEUE_REDIS_MAXTOTAL = "metrics.remote.queue.redis.max.total";
    public static final String REMOTE_QUEUE_REDIS_MINIDLE = "metrics.remote.queue.redis.min.idle";
    public static final String REMOTE_QUEUE_REDIS_MAXWAITMILLIS = "metrics.remote.queue.redis.max.wait.millis";
    public static final String REMOTE_QUEUE_REDIS_TESTONBORROW = "metrics.remote.queue.redis.testOnBorrow";
    public static final String REMOTE_QUEUE_REDIS_TESTONRETURN = "metrics.remote.queue.redis.testOnReturn";
    public static final String REMOTE_QUEUE_REDIS_TESTWHILEIDLE = "metrics.remote.queue.redis.testWhileIdle";
    public static final String REMOTE_QUEUE_REDIS_TIMEBETWEENEVICTIONRUNSMILLIS = "metrics.remote.queue.redis.timeBetweenEvictionRunsMillis";
    public static final String REMOTE_QUEUE_REDIS_NUMTESTSPEREVICTIONRUN = "metrics.remote.queue.redis.numTestsPerEvictionRun";
    public static final String REMOTE_QUEUE_REDIS_MINEVICTABLEIDLETIMEMILLIS = "metrics.remote.queue.redis.minEvictableIdleTimeMillis";


    public static final String DPC_REMOTE_QUEUE_REDIS_KEYS = "metrics.remote.queue.redis.keys";
    public static final String DPC_REMOTE_QUEUE_TASK_SIZE = "metrics.remote.queue.task.size";

    public static final String DPC_NAMESPACES = "metrics.namespaces";


    public static final String INFLUXDB_URL = "metrics.influxdb.server";
    public static final String INFLUXDB_USERNAME = "metrics.influxdb.server.username";
    public static final String INFLUXDB_PASSWORD = "metrics.influxdb.server.password";

    public static final String INFLUXDB_URL_BACKUP = "metrics.influxdb2.server";
    public static final String INFLUXDB_USERNAME_BACKUP = "metrics.influxdb2.server.username";
    public static final String INFLUXDB_PASSWORD_BACKUP = "metrics.influxdb2.server.password";


    public static final String POINT_KAFKA_HOSTS = "metrics.point.kafka.hosts";
    public static final String POINT_KAFKA_MIRROR_HOSTS = "metrics.point.mirror.kafka.hosts";
    public static final String POINT_KAFKA_TOPIC = "metrics.point.kafka.topic";
    public static final String POINT_KAFKA_GROUP_ID = "metrics.point.kafka.group.id";


    public static final String POINT_KAFKA_TOPIC_LIST = "metrics.point.kafka.topic.list";


    public static final String POINT_SENDER_TYPE = "metric.point.sender.type";

}
