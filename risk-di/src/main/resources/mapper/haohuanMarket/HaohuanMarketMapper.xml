<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.di.mapper.haohuanMarket.HaohuanMarketMapper">


    <select id="queryInviterUid" resultType="string">
        select r1.uid
          from invitation_relationship r1
         where r1.invited_uid = #{uid}
         limit 1
    </select>

    <select id="queryInvitedList" resultType="string">
        select r1.invited_uid
          from invitation_relationship r1
         where r1.uid = #{uid}
         limit ${max}
    </select>

    <select id="queryInviteUserCount" resultType="java.lang.Integer">
        select count(distinct r1.invited_uid)
        from invitation_relationship r1
        where r1.uid = #{uid}
        <if test="month != null and month != ''">
          and DATE_FORMAT(create_time,'%Y-%m') = #{month}
        </if>
    </select>

</mapper>