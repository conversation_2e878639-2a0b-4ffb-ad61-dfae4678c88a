package com.paydayloan.verify.dao;

import org.hibernate.*;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 基础Dao
 */
public abstract class AbstractBaseDao<T> extends HibernateDaoSupport {

    protected abstract Class<T> getEntityClass();

    @Autowired
    private void init(SessionFactory sessionFactory) {
        setSessionFactory(sessionFactory);
    }

    /**
     * getCurrentSession
     */
    protected Session getCurrentSession() {
        return getHibernateTemplate().getSessionFactory().getCurrentSession();
    }

    /**
     * 查找唯一
     * 
     * @param id
     * @return
     */
    public T get(Serializable id) {
        return getHibernateTemplate().get(getEntityClass(), id);
    }

    /**
     * 查找全部列表
     * 
     * @return
     */
    public List<T> getAll() {
        return find(new Criterion[0]);
    }

    /**
     * 按条件查找列表
     * 
     * @param criterions
     * @return
     */
    @SuppressWarnings("unchecked")
    protected List<T> find(Criterion[] criterions) {
        return createCriteria(criterions).list();
    }

    /**
     * 按属性查找列表
     * 
     * @param propertyName
     * @param value
     * @return
     */
    protected List<T> findBy(String propertyName, Object value) {
        Criterion criterion = Restrictions.eq(propertyName, value);
        return find(new Criterion[] { criterion });
    }

    /**
     * 按HQL查找列表
     * 
     * @param hql
     * @param values
     * @return
     */
    @SuppressWarnings("unchecked")
    protected List<T> find(String hql, Object... values) {
        return createQuery(hql, values).list();
    }

    /**
     * 按HQL查找列表
     * 
     * @param hql
     * @param values
     * @return
     */
    @SuppressWarnings("unchecked")
    protected List<T> find(String hql, Map<String, ?> values) {
        return createQuery(hql, values).list();
    }

    /**
     * 创建Query
     * 
     * @param queryString
     * @return
     */
    protected Query createQuery(String queryString) {
        return createQuery(queryString, new Object[0]);
    }

    /**
     * 创建Query
     * 
     * @param queryString
     * @param values
     * @return
     */
    protected Query createQuery(String queryString, Object... values) {
        Query query = getCurrentSession().createQuery(queryString);
        if (values != null) {
            for (int i = 0; i < values.length; i++) {
                query.setParameter(i, values[i]);
            }
        }
        return query;
    }

    /**
     * 创建Query
     * 
     * @param queryString
     * @param values
     * @return
     */
    protected Query createQuery(String queryString, Map<String, ?> values) {
        Query query = getCurrentSession().createQuery(queryString);
        if (values != null) {
            query.setProperties(values);
        }
        return query;
    }

    /**
     * 创建SQLQuery
     * 
     * @param queryString
     * @return
     */
    protected SQLQuery createSQLQuery(String queryString) {
        return createSQLQuery(queryString, new Object[0]);
    }

    /**
     * 创建SQLQuery
     * 
     * @param queryString
     * @param values
     * @return
     */
    protected SQLQuery createSQLQuery(String queryString, Object... values) {
        SQLQuery query = getCurrentSession().createSQLQuery(queryString);
        if (values != null) {
            for (int i = 0; i < values.length; i++) {
                query.setParameter(i, values[i]);
            }
        }
        return query;
    }

    /**
     * 创建条件
     * 
     * @param criterions
     * @return
     */
    protected Criteria createCriteria(Criterion[] criterions) {
        Criteria criteria = getCurrentSession()
            .createCriteria(getEntityClass());
        for (Criterion c : criterions) {
            criteria.add(c);
        }
        return criteria;
    }

}