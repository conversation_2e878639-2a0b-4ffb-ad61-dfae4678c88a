package com.youxin.risk.di.service.adapter.datavo;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.adapter.di.ServiceAdapter;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.adapter.di.ServiceResponse;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.utils.AddressUtils;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/8 16:00
 */
public class SelfDataVOAdapter extends ServiceAdapter {

    private Logger LOGGER = LoggerFactory.getLogger(SelfDataVOAdapter.class);

    @Override
    protected ServiceResponse callService(ServiceRequest request) {
        ServiceResponse response = new ServiceResponse();
        response.setRequestId(request.getRequestId());
        Map<String, Object> params = request.getParams();
        LoggerProxy.info("SelfDataVOAdapter", LOGGER, "params={}", JSONObject.toJSONString(params));

        Map<String, Object> result = new HashMap<>();

        //计算 住址与进件位置距离的定位是否大于1km
        String checkGps1kmLivingAddress = AddressUtils.inRange((String) params.get("liveAddress"),
                (String) params.get("city"), Double.parseDouble((String) params.get("longitude")), Double.parseDouble((String) params.get("latitude")), 1000);
        result.put("checkGps1kmLivingAddress", checkGps1kmLivingAddress);

        response.setRetCode(RetCodeEnum.SUCCESS.getValue());
        response.setRetMsg(RetCodeEnum.SUCCESS.getRetMsg());
        Map<String, Object> resultMap = Maps.newHashMap();
        String countResult = JSONObject.toJSONString(result);
        resultMap.put("data", countResult);
        response.setResult(resultMap);
        response.setData(countResult);

        return response;
    }
}
