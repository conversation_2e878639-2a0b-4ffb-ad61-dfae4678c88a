package com.youxin.risk.di.service.adapter.datavo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.adapter.di.ServiceAdapter;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.adapter.di.ServiceResponse;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.remote.model.datacenter.DcRequest;
import com.youxin.risk.commons.remote.model.datacenter.DcRequestService;
import com.youxin.risk.commons.utils.JsonUtils;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringManager;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/11/19 16:00
 */
public class RecertificationContactInfoVOAdapter extends ServiceAdapter {

    private Logger LOGGER = LoggerFactory.getLogger(RecertificationContactInfoVOAdapter.class);

    private final static StringManager SM = StringManager.getManager("conf/conf");

    private static String dcUrl = SM.getString("dc.url");

    private static String dcInsideUrl = SM.getString("dc.inside.url");

    private static final String QUERY_URL = "/dc/queryServices";

    private static int TIMEOUT_MILLIS = 60000;

    @Override
    protected ServiceResponse callService(ServiceRequest request) {

        ServiceResponse response = new ServiceResponse();
        String requestId = request.getRequestId();
        Map<String, Object> params = request.getParams();
        LoggerProxy.info("RecertificationContactInfoVOAdapter", LOGGER, "params={}", JSONObject.toJSONString(params));

        String systemId = (String) params.get("systemid");
        String userKey = request.getUserKey();

        String dcResponse = getDataFromDataCenter(userKey, systemId);
        if (StringUtils.isBlank(dcResponse)) {
            return buildNoDataResponse(requestId);
        }

        response.setRequestId(requestId);
        response.setRetCode(RetCodeEnum.SUCCESS.getValue());
        response.setRetMsg(RetCodeEnum.SUCCESS.getRetMsg());
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("data", dcResponse);
        response.setResult(resultMap);
        response.setData(dcResponse);

        return response;
    }

    /**
     * 构建请求 dc 的参数
     *
     * @param userKey
     * @param systemId
     * @return
     */
    private DcRequest buildParams(String userKey, String systemId) {

        ArrayList<DcRequestService> services = Lists.newArrayList();
        DcRequestService serviceReq = new DcRequestService();
        //重新提交的联系人证信息
        serviceReq.setServiceCode("RECERTIFICATION_CONTACT");
        Map<String, Object> params = new HashMap<>();
        params.put("sourceSystem", systemId);
        params.put("userKey", userKey);
        serviceReq.setParams(params);
        services.add(serviceReq);
        DcRequest request = new DcRequest();
        request.setServices(services);
        request.setUserKey(userKey);
        return request;
    }

    /**
     * 调用 dc 获取重新认证的 联系人信息
     *
     * @param userKey
     * @param systemId
     * @return
     */
    private String getDataFromDataCenter(String userKey, String systemId) {
        DcRequest request = buildParams(userKey, systemId);
        String response = SyncHTTPRemoteAPI.postJson(dcInsideUrl + QUERY_URL, JsonUtils.toJson(request), TIMEOUT_MILLIS);
        return resolveResponse(response);
    }


    /**
     * 处理 调用 dc 的返回结果
     *
     * @param response
     * @return
     */
    private String resolveResponse(String response) {
        if (StringUtils.isBlank(response)) {
            return null;
        }
        JSONObject responseJson = JSONObject.parseObject(response);
        String retCode = responseJson.getString("retCode");
        if (!RetCodeEnum.SUCCESS.equals(retCode)) {
            return null;
        }
        JSONObject resultJson = responseJson.getJSONObject("result");
        JSONArray servicesJson = resultJson.getJSONArray("services");
        if (servicesJson == null || servicesJson.size() == 0) {
            return null;
        }
        JSONObject serviceJson = servicesJson.getJSONObject(0);
        if (!RetCodeEnum.SUCCESS.equals(serviceJson.getString("retCode"))) {
            return null;
        }
        JSONObject dataResult = serviceJson.getJSONObject("result");
        if (dataResult == null) {
            return null;
        }
        String data = dataResult.getString("data");
        if (StringUtils.isBlank(data)) {
            return null;
        }
        return data;
    }

    /**
     * 返回空响应
     *
     * @param requestId
     * @return
     */
    private ServiceResponse buildNoDataResponse(String requestId) {
        ServiceResponse response = new ServiceResponse();
        response.setRequestId(requestId);
        response.setRetCode(RetCodeEnum.NO_DATA.getValue());
        response.setRetMsg(RetCodeEnum.NO_DATA.getRetMsg());
        return response;
    }
}
