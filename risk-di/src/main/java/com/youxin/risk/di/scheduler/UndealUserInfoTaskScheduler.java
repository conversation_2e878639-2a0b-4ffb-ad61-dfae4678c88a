package com.youxin.risk.di.scheduler;

import com.youxin.risk.commons.scheduler.BaseScheduler;
import com.youxin.risk.di.service.handler.DiTaskHandler;

import javax.annotation.Resource;

/**
 * Kafka通知先于task创建处理()
 *
 * <AUTHOR>
 * @version
 */
public class UndealUserInfoTaskScheduler extends BaseScheduler {
    @Resource
    private DiTaskHandler diTaskHandler;

    @Override
    protected void process() {
        diTaskHandler.handleUndealMessage(true);
    }
}
