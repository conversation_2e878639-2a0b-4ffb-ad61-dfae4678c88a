package com.youxin.risk.di.service.adapter.blacklist;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.youxin.apollo.client.NacosClient;
import com.youxin.risk.commons.adapter.di.ServiceAdapter;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.adapter.di.ServiceResponse;
import com.youxin.risk.commons.constants.ApolloNamespace;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.model.datacenter.DcSubmitPlist;
import com.youxin.risk.commons.service.datacenter.DatacenterService;
import com.youxin.risk.commons.utils.ContextUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/15 16:16
 * @desc  首贷黑名单：http://wiki.weicai.com.cn/pages/viewpage.action?pageId=59626092
 */
public class AppBlackListServiceAdapter extends ServiceAdapter {

    private static final Logger LOGGER = LoggerFactory.getLogger(AppBlackListServiceAdapter.class);

    private static final List<String> BLACK_LIST = new ArrayList<>();

    static{
        BLACK_LIST.add("微圈");
        BLACK_LIST.add("小海卡充");
        BLACK_LIST.add("安康版");
        BLACK_LIST.add("CMDC分身");
        BLACK_LIST.add("Wyth");
        BLACK_LIST.add("cp1991");
        BLACK_LIST.add("财悟咨询");
        BLACK_LIST.add("应急宝");
        BLACK_LIST.add("星乐桃");
        BLACK_LIST.add("WXB");
        BLACK_LIST.add("XIN");
        BLACK_LIST.add("挖矿盒子");
        BLACK_LIST.add("公海彩船");
        BLACK_LIST.add("微知秀");
        BLACK_LIST.add("锦郁商城");
        BLACK_LIST.add("ZBX.Plus");
        BLACK_LIST.add("云讯");
        BLACK_LIST.add("鼎刷-云店");
        BLACK_LIST.add("北斗生态圈");
        BLACK_LIST.add("DCEPAI");
        BLACK_LIST.add("BCONE");
        BLACK_LIST.add("国品通");
        BLACK_LIST.add("CASIO+");
        BLACK_LIST.add("UBIEX");
        BLACK_LIST.add("CEEX");
        BLACK_LIST.add("牧牛财神驾到");
        BLACK_LIST.add("ZGZJ");
        BLACK_LIST.add("世界Online");
        BLACK_LIST.add("51兼职");
        BLACK_LIST.add("诚意自助贷");
        BLACK_LIST.add("VivaCut");
        BLACK_LIST.add("E支付");
        BLACK_LIST.add("中国搜索");
        BLACK_LIST.add("Ystar");
        BLACK_LIST.add("Hi Cat");
        BLACK_LIST.add("VivaCut");
        BLACK_LIST.add("万泰通");
        BLACK_LIST.add("信诚生活");
    }

    private DatacenterService datacenterService = (DatacenterService) ContextUtil.getBean("datacenterService");


    @Override
    public ServiceResponse callService(ServiceRequest request) {

        ServiceResponse response = new ServiceResponse();
        String userKey = request.getUserKey();
        if (StringUtils.isBlank(userKey)) {
            LoggerProxy.error("AppBlackListServiceAdapter", LOGGER, "userKey is empty");
            response.setRetCode(RetCodeEnum.RESPONSE_EXCEPTION.getValue());
            response.setRetMsg(RetCodeEnum.RESPONSE_EXCEPTION.getRetMsg());
            return response;
        }

        LoggerProxy.info("AppBlackListServiceAdapter", LOGGER, "userKey is {}", userKey);
        List<DcSubmitPlist> allPlist = datacenterService.getAllPlsitByUserkey(userKey);
        if (CollectionUtils.isEmpty(allPlist)) {
            LoggerProxy.info("AppBlackListServiceAdapter", LOGGER, "allPlist is empty, userKey is {}", userKey);
            return buildResponse(response, 0, 0, new ArrayList<>());
        }

        Set<String> appNames = allPlist.stream().map(DcSubmitPlist::getAppName).collect(Collectors.toSet());
        LoggerProxy.info("AppBlackListServiceAdapter", LOGGER, "userKey:{}, appNames:{}", userKey,
                JSON.toJSONString(appNames));


        String blackListStr = NacosClient.getByNameSpace(ApolloNamespace.diSpace, "black.list.app",
                "");
        List<String> blackList = null;
        if (StringUtils.isBlank(blackListStr)) {
            blackList = BLACK_LIST;
        } else {
            String[] blacks = blackListStr.split(",");
            blackList = new ArrayList<>(Arrays.asList(blacks));
        }

        //取当前用户的所有app列表和黑名单列表的交集
        appNames.retainAll(blackList);

        LoggerProxy.info("AppBlackListServiceAdapter", LOGGER, "userKey:{}, hit appNames:{}", userKey,
                JSON.toJSONString(appNames));
        if (appNames.size() == 0) {
            return buildResponse(response, 0, 0, new ArrayList<>());
        }

        return buildResponse(response, 1, appNames.size(), appNames);
    }

    /**
     * 构建返回结果
     *
     * @param response
     * @param hitFlag    是否命中，命中为1，反之为0
     * @param hitNum     命中个数
     * @param hitDetails 命中哪些
     */
    private ServiceResponse buildResponse(ServiceResponse response, int hitFlag, int hitNum, Collection hitDetails) {
        Map<String, Object> data = Maps.newHashMap();
        data.put("plistBlack", hitFlag);
        data.put("plistBlackNum", hitNum);
        data.put("plistBlackDetail", hitDetails);
        String dataStr = JSON.toJSONString(data);
        Map<String, Object> result = Maps.newHashMap();
        result.put("data", dataStr);
        response.setRetMsg(RetCodeEnum.SUCCESS.getRetMsg());
        response.setRetCode(RetCodeEnum.SUCCESS.getValue());
        response.setData(dataStr);
        response.setResult(result);
        return response;
    }
}
