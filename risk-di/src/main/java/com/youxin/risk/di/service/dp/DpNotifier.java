package com.youxin.risk.di.service.dp;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * dp 平台通知类
 * <AUTHOR>
 */
@Service
public class DpNotifier {

    private static final Logger LOGGER = LoggerFactory.getLogger(DpNotifier.class);

    @Value("${dp.crawlers.callback.url}")
    private String dpCrawlersCallBackUrl;


    public String resendKafkaData(String jobId,String type){
        String url = dpCrawlersCallBackUrl;
        Map<String, String> params = new HashMap<>();
        params.put("jobId",jobId);
        params.put("type",type);
        return SyncHTTPRemoteAPI.postJson(url, JSON.toJSONString(params),null,30000,true);
    }
}
