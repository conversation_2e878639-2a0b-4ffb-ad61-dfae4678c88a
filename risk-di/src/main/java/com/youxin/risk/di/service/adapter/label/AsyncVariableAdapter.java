package com.youxin.risk.di.service.adapter.label;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.adapter.di.ServiceAdapter;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.adapter.di.ServiceResponse;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.SpringContext;
import com.youxin.risk.di.client.RiskCenterClient;

import com.youxin.risk.di.mapper.accountProxy.AccountProxyMapper;
import com.youxin.risk.di.service.adapter.dp.DpAsyncServiceAdapter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.youxin.risk.commons.apollo.ApolloNamespaceEnum.DI_SPACE;

/**
 * @description: 变量中心异步适配器
 * @author: juxiang
 * @create: 2024-01-08 15:03
 **/
public class AsyncVariableAdapter extends DpAsyncServiceAdapter {
    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncVariableAdapter.class);
    private static final String callbackSystem="risk-di";
    private static final int STATUS_SUCCESS_CODE = 0;
    private static final int SUCCESS_CODE = 666;
    private static final String verifyAdChannelService="verifyAdChannelService";

    private RiskCenterClient riskCenterClient= SpringContext.getBean(RiskCenterClient.class);
    @Override
    protected ServiceResponse callService(ServiceRequest request) {
        String loanKey = request.getLoanKey();
        String userKey = request.getUserKey();
        String step = request.getStep();
        String eventCode = request.getSystemEventCode();
        String serviceCode = request.getServiceCode();
        ServiceResponse response = new ServiceResponse();
        response.setRequestId(request.getRequestId());
        LoggerProxy.info("AsyncVariableAdapter_callService", LOGGER,
                "loanKey={} eventCode = {} step = {} userKey={} serviceCode = {} url = {}",loanKey,eventCode,step,userKey,serviceCode,diService.getServiceUrl());
        Map<String,Object> params=request.getParams();
        String url=diService.getServiceUrl()+"/"+eventCode;;
        if(verifyAdChannelService.equals(serviceCode)){
            RiskCenterClient.RiskCenterRes riskCenterRes = riskCenterClient.queryCaidByUserKey(userKey);
            if(!riskCenterRes.success()){
                /** 查询不到caid **/
                response.setRetCode(RetCodeEnum.RESPONSE_EXCEPTION.getValue());
                response.setRetMsg("can not find caid!");
                LoggerProxy.info("AsyncVariableAdapter_callService", LOGGER,
                        "loanKey={},userKey={} not find caid!",loanKey,userKey);
                return response;
            }
            JSONObject dataInput=JSONObject.parseObject(JSON.toJSONString(params.get("dataInput")));
            dataInput.put("originSource",dataInput.remove("originSourceWhole"));
            String caid = riskCenterRes.getData().getString("caid");
            if(StringUtils.isNotBlank(caid)){
                dataInput.put("caid",caid);
            }
            this.transPlatform(dataInput);
            params.put("dataInput",dataInput);
        }
        JSONObject jsonObject = new JSONObject(params);
        jsonObject.put("loanKey",loanKey);
        jsonObject.put("eventCode",eventCode);
        jsonObject.put("strategyId",step);
        jsonObject.put("userKey",userKey);
        jsonObject.put("serviceCode", serviceCode);
        jsonObject.put("callbackSystem",callbackSystem);
        jsonObject.put("baseParams",new HashMap<>());
        jsonObject.put("requestId",request.getRequestId());
        Map<String,Long> dataSourceParamMap= new HashMap<>();
        dataSourceParamMap.put(serviceCode,999999L);
        jsonObject.put("dataSourceParamMap",dataSourceParamMap);
        jsonObject.put("varCodes",ApolloClientAdapter.getMapConfig(DI_SPACE,"asyncVariableCodes", JSONArray.class).get(serviceCode));
        JSONObject result = JSONObject.parseObject(SyncHTTPRemoteAPI.postJson(url, JSON.toJSONString(jsonObject), diService.getServiceTimeout().intValue()));
        Integer status = result.getInteger("status");
        Integer code = result.getInteger("code");
        if (status!=STATUS_SUCCESS_CODE || code!=SUCCESS_CODE){
            /** 标签数据源不可以用时候,返回错误 **/
            response.setRetCode(RetCodeEnum.RESPONSE_EXCEPTION.getValue());
            response.setRetMsg("variable center error!");
            LoggerProxy.info("AsyncVariableAdapter_callService", LOGGER,
                    "loanKey={},userKey={},serviceCode = {} result = {}",loanKey,userKey,serviceCode,result);
            return response;
        }
        LoggerProxy.info("AsyncVariableAdapter_callService", LOGGER,
                "loanKey={} eventCode = {} step = {} userKey={} serviceCode = {} getDataSuccess!"
                ,loanKey,eventCode,step,userKey,serviceCode);
        response.setRetCode(RetCodeEnum.PROCESSING.getValue());
        response.setRetMsg(RetCodeEnum.PROCESSING.getRetMsg());
        response.setJobId(String.valueOf(request.getParams().get("jobId")));
        return response;
    }

    private void transPlatform(JSONObject dataInput) {
        try {
            String platform = dataInput.getString("platform");
            if(StringUtils.equals(platform,"Android")){
                dataInput.put("platform","1");
            }else {
                dataInput.put("platform","2");
            }
        }catch (Exception e ){
            LoggerProxy.error("AsyncVariableAdapter_transPlatform", LOGGER, "error:",e);
        }
    }
}
