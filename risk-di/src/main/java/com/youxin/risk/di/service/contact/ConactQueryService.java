package com.youxin.risk.di.service.contact;

import com.google.common.base.Preconditions;
import com.youxin.risk.commons.dao.datacenter.DcSubmitContactInfoMapper;
import com.youxin.risk.commons.model.datacenter.DcSubmitContactInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static com.youxin.risk.commons.utils.StringUtils.replaceBlank;

/**
 * @Desc 获取紧急联系人信息
 * @Auth linchongbin
 * @Date 2021/12/10 13:01
 */
@Component
public class ConactQueryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ConactQueryService.class);

    @Autowired
    private DcSubmitContactInfoMapper contactInfoMapper;

    public List<DcSubmitContactInfo> queryByUserKey(Map<String, Object> params) {
        String userKey = (String) params.get("userKey");
        Preconditions.checkArgument(!StringUtils.isBlank(userKey));

        List<DcSubmitContactInfo> dcSubmitContactInfoList = contactInfoMapper.getByUserKey(userKey);
        if (CollectionUtils.isNotEmpty(dcSubmitContactInfoList)) {
            for (DcSubmitContactInfo dcSubmitContactInfo : dcSubmitContactInfoList) {
                String pre = dcSubmitContactInfo.getMobile();
                //LOGGER.info("ConactQueryService.queryByUserKey", "replaceBlank_mobile_pre={}", pre);
                String post = replaceBlank(pre);
                LOGGER.info("ConactQueryService.queryByUserKey", "replaceBlank_mobile pre={} post={}", pre,post);
                dcSubmitContactInfo.setMobile(post);
            }
        }
        return dcSubmitContactInfoList;
    }
}
