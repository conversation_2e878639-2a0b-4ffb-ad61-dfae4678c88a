package com.youxin.risk.di.service.adapter.datavo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.adapter.di.ServiceAdapter;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.adapter.di.ServiceResponse;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.JacksonUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * 白名单adapter
 * <AUTHOR>
 */
public class WhiteListDataVoAdapter extends ServiceAdapter {
    private static final Logger LOGGER = LoggerFactory.getLogger(WhiteListDataVoAdapter.class);
    @Override
    protected ServiceResponse callService(ServiceRequest request) {
        ServiceResponse response = new ServiceResponse();
        response.setRequestId(request.getRequestId());
        Map<String, Object> params = request.getParams();
        LoggerProxy.info("WhiteListDataVoAdapter", LOGGER, "params={}", JSONObject.toJSONString(params));

        LoggerProxy.info("WhiteListDataVoAdapter_callDcServiceParams", LOGGER, "before call service, serviceCode={},params={}", request.getServiceCode());
        String url;
        String resultStr;
        LoggerProxy.info("requestKeyParam", LOGGER, "key={}", request.getParams().get("key"));
        if (request.getParams().get("key") != null) {
            url = this.diService.getServiceUrl();
            Map<String, String> requestParams = new HashMap<>(4);
            requestParams.put("eventCode",request.getSystemEventCode());
            requestParams.put("key", (String)request.getParams().get("key"));
            requestParams.put("userKey", request.getUserKey());
//            requestParams.put("mobile", (String)request.getParams().get("mobile"));
            resultStr = SyncHTTPRemoteAPI.postJson(url, JSON.toJSONString(requestParams), this.diService.getServiceTimeout().intValue());
        } else {
            url = this.diService.getServiceUrl() + "/" + request.getSystemEventCode();
            resultStr = SyncHTTPRemoteAPI.get(url, this.diService.getServiceTimeout().intValue());
        }
        if (StringUtils.isBlank(resultStr)) {
            LoggerProxy.error("WhiteListDataVoAdapter_dcServiceResponseError", LOGGER, "service response status error, request={},result={}", JacksonUtil.toJson(request), resultStr);
            response.setRetCode(RetCodeEnum.RESPONSE_EXCEPTION.getValue());
            response.setRetMsg(RetCodeEnum.RESPONSE_EXCEPTION.getRetMsg());
            return response;
        }
        JSONObject jsonObject = JSONObject.parseObject(resultStr);
        Object state = jsonObject.get("status");
        if(state != null && !(0 == (Integer) jsonObject.get("status"))){
            LoggerProxy.error("WhiteListDataVoAdapter_dcServiceResponseError", LOGGER, "service response status error, request={},result={}", JacksonUtil.toJson(request), resultStr);
            response.setRetCode(RetCodeEnum.RESPONSE_EXCEPTION.getValue());
            response.setRetMsg(RetCodeEnum.RESPONSE_EXCEPTION.getRetMsg());
            return response;
        }
        JSONArray whitelist = jsonObject.getJSONObject("data").getJSONArray("data");
        if(CollectionUtils.isEmpty(whitelist)){
            LoggerProxy.warn("WhiteListDataVoAdapter_dcServiceResponseNoData", LOGGER, "request={},result={}", JacksonUtil.toJson(request), resultStr);
            response.setRetCode(RetCodeEnum.NO_DATA.getValue());
            response.setRetMsg(RetCodeEnum.NO_DATA.getRetMsg());
            return response;
        }
        response.setRetCode(RetCodeEnum.SUCCESS.getValue());
        response.setRetMsg(RetCodeEnum.SUCCESS.getRetMsg());
        Map<String, Object> resultMap = Maps.newHashMap();
        String countResult = JSONObject.toJSONString(whitelist);
        resultMap.put("data", countResult);
        response.setResult(resultMap);
        response.setData(countResult);
        return response;
    }
}
