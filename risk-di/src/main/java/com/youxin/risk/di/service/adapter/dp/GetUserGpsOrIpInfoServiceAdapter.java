package com.youxin.risk.di.service.adapter.dp;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.adapter.di.ServiceAdapter;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.adapter.di.ServiceResponse;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.JacksonUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * @description: 根据userkey获取gps或者ip信息的adapter
 * @author: juxiang
 * @create: 2021-07-02 15:58
 **/
public class GetUserGpsOrIpInfoServiceAdapter  extends ServiceAdapter {
    private static final Logger logger = LoggerFactory.getLogger(GetUserGpsOrIpInfoServiceAdapter.class);
    @Override
    protected ServiceResponse callService(ServiceRequest request) {
        ServiceResponse response = new ServiceResponse();
        response.setRequestId(request.getRequestId());
        String userkey=request.getUserKey();
        Map<String, Object> params = request.getParams();
        String systemid = params.get("systemId").toString();
        String url =this.diService.getServiceUrl()+ String.format("?systemid=%s&userkey=%s",systemid ,userkey);
        String result = null;
        try {
            result = SyncHTTPRemoteAPI.get(url,this.diService.getServiceTimeout().intValue());
        } catch (Exception e) {
            LoggerProxy.error("GetUserGpsOrIpInfoServiceAdapter.get", logger, "SyncHTTPRemoteAPI.get error,url={},error={}", url, e.getMessage());
        }

        if (StringUtils.isBlank(result)) {
            LoggerProxy.error("serviceResponseError", logger, "service response status error, request={},result={}", JacksonUtil.toJson(request), result);
            response.setRetCode(RetCodeEnum.RESPONSE_EXCEPTION.getValue());
            response.setRetMsg(RetCodeEnum.RESPONSE_EXCEPTION.getRetMsg());
            return response;
        }

        JSONObject resultJson = JSONObject.parseObject(result);
        if ("200".equals(resultJson.getString("status"))) {
            String data = resultJson.getString("records");
            response.setRetCode(RetCodeEnum.SUCCESS.getValue());
            response.setRetMsg(RetCodeEnum.SUCCESS.getRetMsg());
            Map<String, Object> resultMap = Maps.newHashMap();
            resultMap.put("data", data);
            response.setResult(resultMap);
            response.setData(data);
        } else {
            LoggerProxy.error("GetUserGpsOrIpInfoServiceAdapter", logger, "call service result, result={},params={}", result, JSONObject.toJSONString(params));
            super.logStatusError(logger,  JacksonUtil.toJson(request), result);
            response.setRetCode(RetCodeEnum.RESPONSE_EXCEPTION.getValue());
            response.setRetMsg(RetCodeEnum.RESPONSE_EXCEPTION.getRetMsg());
        }
        return response;
    }
}
