package com.youxin.risk.di.service.adapter;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.ucredit.riskanalysis.data.*;
import com.ucredit.riskanalysis.data.vo.IdNumberBasicInfo;
import com.ucredit.riskanalysis.data.vo.LendRequestInfo;
import com.youxin.risk.commons.adapter.di.ServiceAdapter;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.adapter.di.ServiceResponse;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.dao.datacenter.DcCcCallLogMapper;
import com.youxin.risk.commons.dao.datacenter.DcSubmitIdcardMapper;
import com.youxin.risk.commons.dao.haofenqi.HaoHuanInstitutionFundRecordMapper;
import com.youxin.risk.commons.dao.verify.VerifyUserLineManagementMapper;
import com.youxin.risk.commons.dao.verifySharding.VerifyUserLineManagementShardingMapper;
import com.youxin.risk.commons.model.verify.VerifyUserLineManagement;
import com.youxin.risk.commons.utils.*;
import com.youxin.risk.di.mapper.account.AccountMapper;
import com.youxin.risk.di.mapper.accountProxy.AccountProxyMapper;
import com.youxin.risk.di.mapper.haohuan.HaohuanMapper;
import com.youxin.risk.di.mapper.shadow.ShadowMapper;
import com.youxin.risk.di.model.PayMessage;
import com.youxin.risk.di.service.hbase.RiskUserPayNewService;
import com.youxin.risk.di.utils.CompareJson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.sql.*;
import java.util.Date;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.youxin.risk.di.common.Constant.*;

/**
 * <AUTHOR>
 */
@SuppressWarnings({"unused", "AlibabaUndefineMagicConstant"})
public class MysqlServiceNewAdapter extends ServiceAdapter {

    private static final Logger logger = LoggerFactory.getLogger(MysqlServiceNewAdapter.class);
    private static final String ACCOUNT_PROXY_DATA_SOURCE_NAME = "accountProxyDataSource";
    private static final String ACCOUNT_DATA_SOURCE_NAME = "accountDataSource";
    private static final String KEY_SQL = "sql";

    private ThreadPoolTaskExecutor serviceThreadPool = (ThreadPoolTaskExecutor) SpringContext.getBean("mysqlDbServiceThreadPool");

    private AccountProxyMapper accountProxyMapper = SpringContext.getBean(AccountProxyMapper.class);
    private AccountMapper accountMapper = SpringContext.getBean(AccountMapper.class);
    private ShadowMapper shadowMapper = SpringContext.getBean(ShadowMapper.class);
    private HaohuanMapper haohuanMapper = SpringContext.getBean(HaohuanMapper.class);
    private DcSubmitIdcardMapper submitIdcardMapper = SpringContext.getBean(DcSubmitIdcardMapper.class);
    private IdNumberLendRequestInfoCalculator idNumberLendRequestInfoCalculator = SpringContext.getBean(IdNumberLendRequestInfoCalculator.class);
    private DeviceLendRequestInfoCalculator deviceLendRequestInfoCalculator = SpringContext.getBean(DeviceLendRequestInfoCalculator.class);
    private RegisterPhoneLendRequestInfoCalculator registerPhoneLendRequestInfoCalculator = SpringContext.getBean(RegisterPhoneLendRequestInfoCalculator.class);
    private BankCardLendRequestInfoCalculator bankCardLendRequestInfoCalculator = SpringContext.getBean(BankCardLendRequestInfoCalculator.class);
    private DeviceIdUserNumberHhCalculator deviceIdUserNumberHhCalculator = SpringContext.getBean(DeviceIdUserNumberHhCalculator.class);
    private MobileUserNumberCalculator mobileUserNumberCalculator = SpringContext.getBean(MobileUserNumberCalculator.class);
    private OverDueDaysCalculator maxOverDueDaysCalculator = SpringContext.getBean(OverDueDaysCalculator.class);
    private RepayAmountNewCalculator repayAmountCalculator = SpringContext.getBean(RepayAmountNewCalculator.class);
    private RepayTimesNewCalculator repayTimesCalculator = SpringContext.getBean(RepayTimesNewCalculator.class);
    private RepayDateNewCalculator repayDateCalculator = SpringContext.getBean(RepayDateNewCalculator.class);
    private RepayRateNewCalculator repayRateCalculator = SpringContext.getBean(RepayRateNewCalculator.class);
    private PayOffDealCalculator payOffDealCalculator = SpringContext.getBean(PayOffDealCalculator.class);
    private DeductionRateNewCalculator deductionRateCalculator = SpringContext.getBean(DeductionRateNewCalculator.class);
    private B180HavePayPrincipalAvgCalculator b180HavePayPrincipalAvgCalculator = SpringContext.getBean(B180HavePayPrincipalAvgCalculator.class);
    private B180PrepayPeriodCntAvgCalculator b180PrepayPeriodCntAvgCalculator = SpringContext.getBean(B180PrepayPeriodCntAvgCalculator.class);
    private B90EalistPayDayMinCntRateCalculator b90EalistPayDayMinCntRateCalculator = SpringContext.getBean(B90EalistPayDayMinCntRateCalculator.class);
    private B90HavePayPeriodCntAvgCalculator b90HavePayPeriodCntAvgCalculator = SpringContext.getBean(B90HavePayPeriodCntAvgCalculator.class);
    private B90HavePayPrincipalMaxCalculator b90HavePayPrincipalMaxCalculator = SpringContext.getBean(B90HavePayPrincipalMaxCalculator.class);
    private VerifyUserLineManagementMapper verifyUserLineManagementMapper = SpringContext.getBean(VerifyUserLineManagementMapper.class);
    private VerifyUserLineManagementShardingMapper verifyUserLineManagementShardingMapper = SpringContext.getBean(VerifyUserLineManagementShardingMapper.class);
    private DcCcCallLogMapper dcCcCallLogMapper = SpringContext.getBean(DcCcCallLogMapper.class);
    private HaoHuanInstitutionFundRecordMapper haoHuanInstitutionFundRecordMapper = SpringContext.getBean(HaoHuanInstitutionFundRecordMapper.class);
    private MGMSameInviterCalculator mgmSameInviterCalculator = SpringContext.getBean(MGMSameInviterCalculator.class);
    private EmergencyContactCalculator emergencyContactCalculator = SpringContext.getBean(EmergencyContactCalculator.class);
    private IdNumberBasicInfoCalculator idNumberBasicInfoCalculator = SpringContext.getBean(IdNumberBasicInfoCalculator.class);

    private GetUserPayNewCalculator getUserPayCalculator = SpringContext.getBean(GetUserPayNewCalculator.class);

    private RiskUserPayNewService riskUserPayService = SpringContext.getBean(RiskUserPayNewService.class);

    private final List<String> needQueryHbaseList = Lists.newArrayList("6mChargeTotalCount",
            "6mChargeTotalCountT1",
            "rrd3mChargeFailCount",
            "6mChargeSuccessCount",
            "6mChargeSuccessCountT1",
            "3mChargeTotalAmount",
            "3mChargeTotalFailAmount",
            "3mChargeTotalAmountT1",
            "3mChargeTotalFailAmountT1",
            "payActiveSuccssCnt3M",
            "six_month_payment_cnt",
            "payoff_time_17_cnt_b_180_rate",
            "d180_pay_launch_type_1_weekday_cnt_rate",
            "D180_success_nosystem_payment_cnt_rate",
            "D60_success_nosystem_payment_amount_sum_rate",
            "D90_success_nosystem_payment_amount_sum_rate",
            "D180_pay_launch_type_hour_avg",
            "payActiveAmtSum3M",
            "d30_no_balance_amount_max",
            "d30_pay_launch_type_0_amount_sum",
            "d30_pay_launch_type_1_amount_min",
            "d60_payment_amount_avg",
            "d90_no_balance_amount_avg",
            "d90_no_balance_amount_sum",
            "three_month_pay_launch_type_1_succ_amount_sum",
            "D30_success_nosystem_payment_amount_sum",
            "user_pay",
            "three_month_success_nosystem_payment_cnt_rate");

    @Override
    protected ServiceResponse callService(ServiceRequest request) {
        Stopwatch stopwatch = Stopwatch.createStarted();

        ServiceResponse response = new ServiceResponse();
        response.setRequestId(request.getRequestId());
        Map<String, Object> params = request.getParams();
        ConcurrentHashMap<String, Object> data = new ConcurrentHashMap<>();
        //noinspection unchecked
        Map<String, Object> action = (Map<String, Object>) params.get("action");
        CountDownLatch countDownLatch = new CountDownLatch(action.size());
        List<Future<?>> tasks = new ArrayList<>(action.size());
        String logId = LogUtil.getLogIdFromContext();
        AtomicBoolean isFailed = new AtomicBoolean(false);
        //  如果action包含userPay则在循环之前获取一次user_pay的数据
        try {
            for (String dataName : action.keySet()) {
                if (needQueryHbaseList.contains(dataName)) {
                    riskUserPayService.getAndSaveUserPayDataFromHbase(request.getUserKey());
                    break;
                }
            }
        } catch (Exception ex) {
            LoggerProxy.error("MysqlServiceNewAdapter", logger, "getAndSaveUserPayDataFromHbase error", ex);
        }

        for (Map.Entry<String, Object> entry : action.entrySet()) {
            try {
                Future<?> task = serviceThreadPool.submit(() -> {
                    LogUtil.bindLogId(logId);
                    String dataName = entry.getKey();
                    Object dataConfig = entry.getValue();
                    try {
                        if (dataConfig instanceof Map) {
                            //noinspection unchecked
                            Map<String, String> dataSourceMap = (Map<String, String>) dataConfig;
                            //noinspection unchecked
                            if (((Map<String, String>) dataConfig).containsKey(KEY_SQL)) {
                                JSONArray value = executeSql(dataSourceMap.get("dataSource") + "DataSource", dataSourceMap.get(KEY_SQL));
                                data.put(dataName, value);
                                return;
                            }
                        }
                        Object value = collectData(dataName, request, dataConfig);
                        if (null != value) {
                            data.put(dataName, value);
                        }
                    } catch (Exception e) {
                        logger.error("collectData error, dataName: {}", dataName, e);
                        isFailed.compareAndSet(false, true);
                    } finally {
                        countDownLatch.countDown();
                        LogUtil.unbindLogId();
                    }
                });
                tasks.add(task);
            } catch (Exception e) {
                logger.warn("submit task failed!", e);
                countDownLatch.countDown();
            }
        }
        try {
            countDownLatch.await(50, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            logger.warn("", e);
        }
        for (Future<?> task : tasks) {
            if (!task.isDone()) {
                task.cancel(true);
            }
        }

        // 有一个数据源获取不到的话 那么就返回错误状态码
        if(isFailed.get()){
            LoggerProxy.error("MysqlServiceAdapterError", logger, "");
            response.setRetCode(RetCodeEnum.FAILED.getValue());
            response.setRetMsg(RetCodeEnum.FAILED.getRetMsg());
            return  response;
        }

        response.setRetCode(RetCodeEnum.SUCCESS.getValue());
        response.setRetMsg(RetCodeEnum.SUCCESS.getRetMsg());
        HashMap<String, Object> result = new HashMap<>(1);
        result.put("data", data);
        response.setResult(result);
        response.setData(JSONObject.toJSONString(data));
        response.setJobId(UUID.randomUUID().toString());
        LoggerProxy.info("MysqlServiceNewAdapter", logger, "cost userKey:{}, step:{}, time:{} ",request.getUserKey(),request.getStep(), stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
        return response;
    }

    @SuppressWarnings({"AlibabaUndefineMagicConstant", "unchecked"})
    public Object collectData(String dataName, ServiceRequest request, Object dataConfig) {
        // 注意，MySQL会自动把列名字转为小写
        String businessUserKey = request.getUserKey();
        String accountUserKey = accountProxyMapper.selectRealUserKey(businessUserKey);
        if ("6mChargeTotalCount".equals(dataName)) {
            Long totalCount = (Long) riskUserPayService.getNewUserPayData(SELECT_6_M_CHARGE_TOTAL_COUNT, businessUserKey);
            return totalCount + "";
        } else if ("6mChargeTotalCountT1".equals(dataName)) {
            Long totalCountT1 = (Long) riskUserPayService.getNewUserPayData(SELECT_6_M_CHARGE_TOTAL_COUNT_T_1, businessUserKey);
            return totalCountT1 + "";
        } else if ("rrd3mChargeFailCount".equals(dataName)) {
            Integer uid = (Integer) request.getParams().get("uid");
            Long aLong = (Long) riskUserPayService.getNewUserPayData(SELECT_RRD_3_M_CHARGE_FAIL_COUNT, String.valueOf(uid));
            return aLong;
        } else if ("6mChargeSuccessCount".equals(dataName)) {
            Long successCount = (Long) riskUserPayService.getNewUserPayData(SELECT_6_M_CHARGE_SUCCESS_COUNT, businessUserKey);
            return successCount + "";
        } else if ("6mChargeSuccessCountT1".equals(dataName)) {
            Long aLong = (Long) riskUserPayService.getNewUserPayData(SELECT_6_M_CHARGE_SUCCESS_COUNT_T_1, businessUserKey);
            return aLong + "";
        } else if ("6mMaxOverdueDays".equals(dataName)) {
            if (Strings.isNullOrEmpty(accountUserKey)) {
                logger.warn("not found user: {}", businessUserKey);
                return "";
            }
            return maxOverDueDaysCalculator.getMaxOverdueDays(accountUserKey, 180);
        } else if ("3mChargeTotalAmount".equals(dataName)) {
            Double amount = (Double) riskUserPayService.getNewUserPayData(SELECT_3_M_CHARGE_TOTAL_AMOUNT, businessUserKey);
            return amount == null ? "0.0" : amount.toString();
        } else if ("3mChargeTotalFailAmount".equals(dataName)) {
            Double amount = (Double) riskUserPayService.getNewUserPayData(SELECT_3_M_CHARGE_TOTAL_FAIL_AMOUNT, businessUserKey);
            return amount == null ? "0.0" : amount.toString();
        } else if ("3mChargeTotalAmountT1".equals(dataName)) {
            Double amount = (Double) riskUserPayService.getNewUserPayData(SELECT_3_M_CHARGE_TOTAL_AMOUNT_T_1, businessUserKey);
            return amount == null ? "0.0" : amount.toString();
        } else if ("3mChargeTotalFailAmountT1".equals(dataName)) {
            Double amount = (Double) riskUserPayService.getNewUserPayData(SELECT_3_M_CHARGE_TOTAL_FAIL_AMOUNT_T_1, businessUserKey);
            return amount == null ? "0.0" : amount.toString();
        } else if ("6mOverdueAmounts".equals(dataName)) {
            JSONArray loans = new JSONArray();
            if (Strings.isNullOrEmpty(accountUserKey)) {
                logger.warn("not found user: {}", businessUserKey);
                return loans;
            }

            List<String> loanKeys = accountMapper.selectLoanKeysByUserInDays(accountUserKey, 180);
            for (String loanKey : loanKeys) {
                Double principal = accountMapper.selectPrincipalByLoan(loanKey);
                Double overdue = accountMapper.selectOverdueByLoan(loanKey);

                JSONObject loan = new JSONObject();
                loan.put("principal", principal == null ? "0.0" : principal.toString());
                loan.put("overdue", overdue == null ? "0.0" : overdue.toString());
                loans.add(loan);
            }
            return loans;
        } else if ("3mAheadPlans".equals(dataName)) {
            JSONArray plans = new JSONArray();
            if (Strings.isNullOrEmpty(accountUserKey)) {
                logger.warn("not found user: {}", businessUserKey);
                return plans;
            }

            List<String> loanKeys = accountMapper.selectPayoffLoanKeysByUser(accountUserKey);
            for (String loanKey : loanKeys) {
                Long payoffCount = accountMapper.selectPayoffCountByLoanInDays(loanKey, 90);
                Long aheadCount = accountMapper.selectAheadCountByLoan(loanKey);
                JSONObject plan = new JSONObject();
                plan.put("payoff", payoffCount == 0 ? "0" : payoffCount.toString());
                plan.put("ahead", aheadCount == 0 ? "0" : aheadCount.toString());
                plans.add(plan);
            }

            return plans;
        } else if ("6mAvgAhead".equals(dataName)) {
            if (Strings.isNullOrEmpty(accountUserKey)) {
                logger.warn("not found user: {}", businessUserKey);
                return "";
            }

            List<String> loanKeys = accountMapper.selectLoanKeysByUser(accountUserKey);
            if (loanKeys == null || loanKeys.size() == 0) {
                return "0";
            } else {
                Long aheadCount = accountMapper.selectAheadCountByUser(accountUserKey);
                return (aheadCount.doubleValue() / loanKeys.size()) + "";
            }
        } else if ("6mPayoffs".equals(dataName)) {
            JSONArray plans = new JSONArray();
            if (Strings.isNullOrEmpty(accountUserKey)) {
                logger.warn("not found user: {}", businessUserKey);
                return plans;
            }
            List<String> loanKeys = accountMapper.selectLoanKeysByUser(accountUserKey);
            for (String loanKey : loanKeys) {
                Long shouldCount = accountMapper.selectShouldCountByLoan(loanKey);
                Long payoffCount = accountMapper.selectPayoffCountByLoanInDays(loanKey, 180);

                JSONObject plan = new JSONObject();
                plan.put("should", shouldCount.toString());
                plan.put("payoff", payoffCount.toString());
                plans.add(plan);
            }
            return plans;
        } else if ("idNumberLendRequestInfo".equals(dataName)) {
            Map<String, String> dataConfigMap = (Map<String, String>) dataConfig;
            String idCardNo = dataConfigMap.get("idCardNo");
            Boolean idNumberSwitchToNewDataEnabled = NacosClientAdapter.getBooleanConfig("idNumberSwitchToNewDataEnabled", false);
            if (idNumberSwitchToNewDataEnabled) {
                return idNumberLendRequestInfoCalculator
                        .getValue(request.getLoanKey(), idCardNo, true);
            }
            LendRequestInfo oldLendRequestInfo = idNumberLendRequestInfoCalculator
                    .getValue(request.getLoanKey(), idCardNo, false);
            LendRequestInfo newLendRequestInfo = idNumberLendRequestInfoCalculator
                    .getValue(request.getLoanKey(), idCardNo, true);
            CompareJson.compareData(oldLendRequestInfo, newLendRequestInfo);
            return oldLendRequestInfo;
        } else if ("bankCardLendRequestInfo".equals(dataName)) {
            Map<String, String> dataConfigMap = (Map<String, String>) dataConfig;
            String bankCardNo = dataConfigMap.get("bankCardNo");
            return bankCardLendRequestInfoCalculator.getValue(request.getLoanKey(), bankCardNo);
        } else if ("deviceLendRequestInfo".equals(dataName)) {
            Map<String, String> dataConfigMap = (Map<String, String>) dataConfig;
            String deviceId = dataConfigMap.get("deviceId");
            return deviceLendRequestInfoCalculator.getValue(request.getLoanKey(), deviceId);
        } else if ("registerPhoneLendRequestInfo".equals(dataName)) {
            Map<String, String> dataConfigMap = (Map<String, String>) dataConfig;
            String registerPhone = dataConfigMap.get("registerPhone");
            return registerPhoneLendRequestInfoCalculator.getValue(request.getLoanKey(), registerPhone);
        } else if ("reservedPhoneLendRequestInfo".equals(dataName)) {
            Map<String, String> dataConfigMap = (Map<String, String>) dataConfig;
            String reservedPhone = dataConfigMap.get("reservedPhone");
            return registerPhoneLendRequestInfoCalculator.getValue(request.getLoanKey(), dataConfigMap.get("reservedPhone"));
        } else if ("deviceIdUserNumberHh".equals(dataName)) {
            Map<String, String> dataConfigMap = (Map<String, String>) dataConfig;
            String idfa = dataConfigMap.get("idfa");
            String imei = dataConfigMap.get("imei");
            return deviceIdUserNumberHhCalculator.getValue(idfa, imei);
        } else if ("mobileUserNumber".equals(dataName)) {
            Map<String, String> dataConfigMap = (Map<String, String>) dataConfig;
            return mobileUserNumberCalculator.getValue(dataConfigMap);
        } else if ("b_180_account_max_dpd_prepay".equals(dataName)) {
            return maxOverDueDaysCalculator.getAccountMaxDpdPrepay(accountUserKey, 180);
        } else if ("payActiveAmtSum3M".equals(dataName)) {
            return repayAmountCalculator.getRepayAmount(businessUserKey, 90);
        } else if ("payActiveSuccssCnt3M".equals(dataName)) {
            return repayTimesCalculator.getRepayTimes(businessUserKey, 90);
        } else if ("three_month_success_nosystem_payment_cnt_rate".equals(dataName)) {
            return deductionRateCalculator.getValue(businessUserKey, 90);
        } else if ("b_30_account_max_dpd_max".equals(dataName)) {
            return maxOverDueDaysCalculator.getMaxOverdueDays(accountUserKey, 30);
        } else if ("payoff_deal_cnt".equals(dataName)) {
            return payOffDealCalculator.getPayOffDealCnt(accountUserKey);
        } else if ("payoff_time_17_cnt_b_180_rate".equals(dataName)) {
            return repayRateCalculator.getRepayRateAfterHour(accountUserKey, 180, 17);
        } else if ("d180_pay_launch_type_1_weekday_cnt_rate".equals(dataName)) {
            return repayRateCalculator.getRepayRateInWeekend(businessUserKey, 180);
        } else if ("D180_pay_launch_type_hour_avg".equals(dataName)) {
            return repayDateCalculator.getPayLaunchTypeHourAvg(businessUserKey, 180);
        } else if ("d30_no_balance_amount_max".equals(dataName)) {
            return repayAmountCalculator.getNoBalanceAmountMax(businessUserKey, 30);
        } else if ("d30_pay_launch_type_0_amount_sum".equals(dataName)) {
            return repayAmountCalculator.getPayLaunchTypeZeroAmountSum(businessUserKey, 30);
        } else if ("d30_pay_launch_type_1_amount_min".equals(dataName)) {
            return repayAmountCalculator.getPayLaunchTypeOneAmountMin(businessUserKey, 30);
        } else if ("d60_payment_amount_avg".equals(dataName)) {
            return repayAmountCalculator.getPaymentAmountAvg(businessUserKey, 60);
        } else if ("d90_no_balance_amount_avg".equals(dataName)) {
            return repayAmountCalculator.getNoBalanceAmountAvg(businessUserKey, 90);
        } else if ("d90_no_balance_amount_sum".equals(dataName)) {
            return repayAmountCalculator.getNoBalanceAmountSum(businessUserKey, 90);
        } else if ("six_month_payment_cnt".equals(dataName)) {
            return repayTimesCalculator.getPaymentCtn(businessUserKey, 180);
        } else if ("three_month_pay_launch_type_1_succ_amount_sum".equals(dataName)) {
            return repayAmountCalculator.getPayLaunchTypeOneSuccessAmountSum(businessUserKey, 90);
        } else if ("D180_success_nosystem_payment_cnt_rate".equals(dataName)) {
            return repayRateCalculator.getSuccessNoSystemPaymentCountRate(businessUserKey, 180);
        } else if ("D30_success_nosystem_payment_amount_sum".equals(dataName)) {
            return repayAmountCalculator.getSuccessNoSystemPaymentAmountSum(businessUserKey, 30);
        } else if ("D60_success_nosystem_payment_amount_sum_rate".equals(dataName)) {
            return repayRateCalculator.getSuccessNoSystemPaymentAmountSumRate(businessUserKey, 60);
        } else if ("D90_success_nosystem_payment_amount_sum_rate".equals(dataName)) {
            return repayRateCalculator.getSuccessNoSystemPaymentAmountSumRate(businessUserKey, 90);
        } else if ("repaykey_b1d_amt".equals(dataName)) {
            return String.valueOf(accountMapper.repaykeyB1dAmt(accountUserKey));
        } else if ("repaykey_b0d_cnt".equals(dataName)) {
            return String.valueOf(accountMapper.repaykeyB0dCnt(accountUserKey));
        } else if ("repaykey_b3d_cnt".equals(dataName)) {
            return String.valueOf(accountMapper.repaykeyB3dCnt(accountUserKey));
        } else if ("repaykey_pay7_cnt".equals(dataName)) {
            return String.valueOf(accountMapper.repaykeyPay7Cnt(accountUserKey));
        } else if ("repaykey_0d_cnt".equals(dataName)) {
            return String.valueOf(accountMapper.repaykey0dCnt(accountUserKey));
        } else if ("repaykey_weekend_cnt".equals(dataName)) {
            return String.valueOf(accountMapper.repaykeyWeekendCnt(accountUserKey));
        } else if ("repaykey_a3s_cnt".equals(dataName)) {
            return String.valueOf(accountMapper.repaykeyA3sCnt(accountUserKey));
        } else if ("repaykey_payed_cnt".equals(dataName)) {
            return String.valueOf(accountMapper.repaykeyPayedCnt(accountUserKey));
        } else if ("repaykey_amt".equals(dataName)) {
            return String.valueOf(accountMapper.repaykeyAmt(accountUserKey));
        } else if ("b_180_have_pay_principal_avg".equals(dataName)) {
            return b180HavePayPrincipalAvgCalculator.getValue(businessUserKey);
        } else if ("b_180_prepay_period_cnt_avg".equals(dataName)) {
            return b180PrepayPeriodCntAvgCalculator.getValue(businessUserKey);
        } else if ("b_90_ealist_pay_day_min_cnt_rate".equals(dataName)) {
            return b90EalistPayDayMinCntRateCalculator.getValue(businessUserKey);
        } else if ("b_90_have_pay_period_cnt_avg".equals(dataName)) {
            return b90HavePayPeriodCntAvgCalculator.getValue(businessUserKey);
        } else if ("b_90_have_pay_principal_max".equals(dataName)) {
            return b90HavePayPrincipalMaxCalculator.getValue(businessUserKey);
        } else if ("b_90_prepay_principal_sum_avg".equals(dataName)) {
            return String.valueOf(accountMapper.b90PrepayPrincipalSumAvg(businessUserKey));
        } else if ("b_90_prepay_principal_sum_sum_rate".equals(dataName)) {
            return String.valueOf(accountMapper.b90PrepayPrincipalSumSumRate(businessUserKey));
        } else if ("payoff_time_9_14_cnt_b_180_rate".equals(dataName)) {
            return String.valueOf(accountMapper.payoffTime914CntB180Rate(businessUserKey));
        } else if ("manual_input_submit_idcard".equals(dataName)) {
            return submitIdcardMapper.getManualInputByUserKey(businessUserKey);
        } else if ("is_clear".equals(dataName)) {
            return String.valueOf(accountMapper.isClear(accountUserKey));
        } else if ("clear_timestamp".equals(dataName)) {
            return String.valueOf(accountMapper.clearTimestamp(accountUserKey));
        } else if ("clear2now".equals(dataName)) {
            return String.valueOf(accountMapper.clear2now(accountUserKey));
        } else if ("ever_max_dpd".equals(dataName)) {
            return String.valueOf(accountMapper.everMaxDpd(accountUserKey));
        } else if ("ahead_repay_cnt_inst".equals(dataName)) {
            return String.valueOf(accountMapper.aheadRepayCntInst(accountUserKey));
        } else if ("ahead_repay_pct_tm1".equals(dataName)) {
            return String.valueOf(accountMapper.aheadRepayPctTm1(accountUserKey));
        } else if ("loan_cnt_inst".equals(dataName)) {
            return String.valueOf(accountMapper.loanCntInst(accountUserKey));
        } else if ("loan_cnt_tm1".equals(dataName)) {
            return String.valueOf(accountMapper.loanCntTm1(accountUserKey));
        } else if ("bill_cnt".equals(dataName)) {
            return String.valueOf(accountMapper.billCnt(accountUserKey));
        } else if ("begin_clear2now".equals(dataName)) {
            return String.valueOf(accountMapper.beginClear2now(accountUserKey));
        } else if ("inlast1m_repay_cnt_inst".equals(dataName)) {
            return String.valueOf(accountMapper.inlast1mRepayCntInst(accountUserKey));
        } else if ("inlast1m_repay_cnt_tm1".equals(dataName)) {
            return String.valueOf(accountMapper.inlast1mRepayCntTm1(accountUserKey));
        } else if ("inlast3m_due_cnt_inst".equals(dataName)) {
            return String.valueOf(accountMapper.inlast3mDueCntInst(accountUserKey));
        } else if ("inlast3m_due_cnt_tm1".equals(dataName)) {
            return String.valueOf(accountMapper.inlast3mDueCntTm1(accountUserKey));
        } else if ("inlast6m_repay_cnt".equals(dataName)) {
            return String.valueOf(accountMapper.inlast6mRepayCnt(accountUserKey));
        } else if ("recent_due2now".equals(dataName)) {
            return String.valueOf(accountMapper.recentDue2now(accountUserKey));
        } else if ("begin_loan2now_inst".equals(dataName)) {
            return accountMapper.beginLoan2nowInst(accountUserKey);
        } else if ("begin_loan2now_tm1".equals(dataName)) {
            return accountMapper.beginLoan2nowTm1(accountUserKey);
        } else if ("inlast180d_loan_cnt_inst".equals(dataName)) {
            return accountMapper.inlast180dLoanCntInst(accountUserKey);
        } else if ("inlast180d_loan_cnt_tm1".equals(dataName)) {
            return accountMapper.inlast180dLoanCntTm1(accountUserKey);
        } else if ("inlast30d_loan_cnt".equals(dataName)) {
            return accountMapper.inlast30dLoanCnt(accountUserKey);
        } else if ("inlast90d_loan_cnt".equals(dataName)) {
            return accountMapper.inlast90dLoanCnt(accountUserKey);
        } else if ("recent_loan2now_inst".equals(dataName)) {
            return accountMapper.recentLoan2nowInst(accountUserKey);
        } else if ("recent_loan2now_tm1".equals(dataName)) {
            return accountMapper.recentLoan2nowTm1(accountUserKey);
        } else if ("inlast15d_apply_daycnt_inst".equals(dataName)) {
            return shadowMapper.inlast15dApplyDaycntInst(businessUserKey);
        } else if ("inlast15d_apply_daycnt_tm1".equals(dataName)) {
            return shadowMapper.inlast15dApplyDaycntTm1(businessUserKey);
        } else if ("inlast180d_apply_daycnt_inst".equals(dataName)) {
            return shadowMapper.inlast180dApplyDaycntInst(businessUserKey);
        } else if ("inlast180d_apply_daycnt_tm1".equals(dataName)) {
            return shadowMapper.inlast180dApplyDaycntTm1(businessUserKey);
        } else if ("inlast30d_apply_daycnt_inst".equals(dataName)) {
            return shadowMapper.inlast30dApplyDaycntInst(businessUserKey);
        } else if ("inlast30d_apply_daycnt_tm1".equals(dataName)) {
            return shadowMapper.inlast30dApplyDaycntTm1(businessUserKey);
        } else if ("inlast90d_apply_daycnt_inst".equals(dataName)) {
            return shadowMapper.inlast90dApplyDaycntInst(businessUserKey);
        } else if ("inlast90d_apply_daycnt_tm1".equals(dataName)) {
            return shadowMapper.inlast90dApplyDaycntTm1(businessUserKey);
        } else if ("recent_apply2now_inst".equals(dataName)) {
            return shadowMapper.recentApply2nowInst(businessUserKey);
        } else if ("recent_apply2now_tm1".equals(dataName)) {
            return shadowMapper.recentApply2nowTm1(businessUserKey);
        } else if ("repay_plan".equals(dataName)) {
            return accountMapper.getRepayPlan(accountUserKey);
        } else if ("loan".equals(dataName)) {
            return accountMapper.getLoan(accountUserKey);
        } else if ("user_pay".equals(dataName)) {
            List<Map<String, Object>> userPay = getUserPayCalculator.getValue(businessUserKey);
            return userPay;
        } else if ("loan_audit_strategy_result".equals(dataName)) {
            return shadowMapper.getLoanAuditStrategyResult(businessUserKey);
        } else if ("template_log".equals(dataName)) {
            return haohuanMapper.getTemplateLogWithOutBaoGuoCard(businessUserKey);
        } else if ("user_level".equals(dataName)) {
            return haohuanMapper.getUserLevel(businessUserKey);
        } else if ("daily_active_user".equals(dataName)) {
            return haohuanMapper.getDailyActiveUser(businessUserKey);
        } else if ("cc_call_log".equals(dataName)) {
            return dcCcCallLogMapper.selectCcCallLog(businessUserKey);
        } else if ("verify_user_line_management".equals(dataName)) {

            String shardingTableName = VerifyUserLineManagement.getShardingTableName(businessUserKey);
            return this.verifyUserLineManagementShardingMapper.getVerifyUserLineManagementMapper(shardingTableName, businessUserKey);

        } else if ("line_data".equals(dataName)) {
            List<Map<String, Object>> lineDatas = shadowMapper.getLineDataFromLoanAuditStrategyResult(businessUserKey);
//            List<Map<String, Object>> lineHisDatas = shadowMapper.getLineDataFromLoanAuditRecordHistoryDz(businessUserKey);
//            if(CollectionUtils.isNotEmpty(lineHisDatas)){
//                lineDatas = mergeLineData(lineDatas,lineHisDatas);
//            }
            for (Map<String, Object> lineData : lineDatas) {
                int loanId = Integer.parseInt(lineData.get("loan").toString());
                lineData.remove("loan");
                Map<String, Object> templateLog = haohuanMapper.getLineDataFromTemplateLogWithOutBaoGuoCard(loanId);
                if (templateLog != null) {
                    lineData.putAll(templateLog);
                    if(lineData.get("borrow_time") != null){
                        Date borrowTime = new Timestamp(((Integer) lineData.get("borrow_time")).longValue() * 1000);
                        // 查询create_time小于borrow_time的最新一条额度表记录
                        String shardingTableName = VerifyUserLineManagement.getShardingTableName(businessUserKey);
                        Map<String, Object> userLineManagement = verifyUserLineManagementShardingMapper.getLineDataFromVerifyUserLineManagement(shardingTableName, businessUserKey, borrowTime);
                        if (null != userLineManagement) {
                            lineData.putAll(userLineManagement);
                        }
                    }
                }

            }
            String loanIdStr = (String) request.getParams().get("loanId");
            if(StringUtils.isNotBlank(loanIdStr)){
                Integer loanId = Integer.parseInt(loanIdStr);
                Map<String, Object> templateLog = haohuanMapper.getLineDataFromTemplateLogWithOutBaoGuoCard(loanId);
                if (templateLog != null) {
                    if(templateLog.get("borrow_time") != null){
                        Date borrowTime = new Timestamp(((Integer) templateLog.get("borrow_time")).longValue() * 1000);
                        // 查询create_time小于borrow_time的最新一条额度表记录
                        String shardingTableName = VerifyUserLineManagement.getShardingTableName(businessUserKey);
                        Map<String, Object> userLineManagement = verifyUserLineManagementShardingMapper.getLineDataFromVerifyUserLineManagement(shardingTableName, businessUserKey, borrowTime);
                        if (null != userLineManagement) {
                            templateLog.putAll(userLineManagement);
                        }
                    }
                    lineDatas.add(templateLog);
                }
            }
            return lineDatas;
        } else if ("lst1m_login_daycnt_da_inst".equals(dataName)) {
            return haohuanMapper.lst1m_login_daycnt_da_inst(businessUserKey);
        } else if ("lst3m_login_daycnt_da_inst".equals(dataName)) {
            return haohuanMapper.lst3m_login_daycnt_da_inst(businessUserKey);
        } else if ("lst6m_login_daycnt_da_inst".equals(dataName)) {
            return haohuanMapper.lst6m_login_daycnt_da_inst(businessUserKey);
        } else if ("lst1m_login_daycnt_da_tm1".equals(dataName)) {
            return haohuanMapper.lst1m_login_daycnt_da_tm1(businessUserKey);
        } else if ("lst3m_login_daycnt_da_tm1".equals(dataName)) {
            return haohuanMapper.lst3m_login_daycnt_da_tm1(businessUserKey);
        } else if ("lst6m_login_daycnt_da_tm1".equals(dataName)) {
            return haohuanMapper.lst6m_login_daycnt_da_tm1(businessUserKey);
        } else if ("login_device_count".equals(dataName)) {
            return haohuanMapper.login_device_count(businessUserKey);
        } else if ("inlast30d_loan_cnt_inst".equals(dataName)) {
            return accountMapper.inlast30d_loan_cnt_inst(accountUserKey);
        } else if ("inlast15d_loan_cnt_inst".equals(dataName)) {
            return accountMapper.inlast15d_loan_cnt_inst(accountUserKey);
        } else if ("ahead_repay_pct_inst".equals(dataName)) {
            return accountMapper.ahead_repay_pct_inst(accountUserKey);
        } else if ("inlast6m_due_cnt_inst".equals(dataName)) {
            return accountMapper.inlast6m_due_cnt_inst(accountUserKey);
        } else if ("inlast180d_avg_ahead_days_inst".equals(dataName)) {
            return accountMapper.inlast180d_avg_ahead_days_inst(accountUserKey);
        } else if ("funder_reject_reason".equals(dataName)) {

            Map<String, String> dataConfigMap = (Map<String, String>) dataConfig;
            // 保留多少天
            String retainDayStr = dataConfigMap.get("retainDay");
            int limitDay = Integer.parseInt(retainDayStr);
            // 转换成那天的秒数
            int dayTimeSeconds = (int) (System.currentTimeMillis() / 1000 - limitDay * 86400);

            return haoHuanInstitutionFundRecordMapper.queryRejectRecordList(businessUserKey, dayTimeSeconds);

        } else if("mgm_same_inviter".equals(dataName)){
            Map<String, Object> params = request.getParams();
            if(params != null && params.containsKey("channelCode")){
                String channelCode = params.get("channelCode").toString();
                logger.info("mgm_same_inviter channelCode={}",channelCode);
            }
            return mgmSameInviterCalculator.getValue(businessUserKey);
        } else if("emergency_contact".equals(dataName)){
            return emergencyContactCalculator.getValue(businessUserKey);
        } else if("id_number_basic_info".equals(dataName)){
            Map<String, String> dataConfigMap = (Map<String, String>) dataConfig;
            String idCardNo = dataConfigMap.get("idCardNo");
            Boolean idNumberSwitchToNewDataEnabled = NacosClientAdapter.getBooleanConfig("idNumberSwitchToNewDataEnabled", false);
            if (idNumberSwitchToNewDataEnabled) {
                return idNumberBasicInfoCalculator
                        .getValue(request.getLoanKey(), idCardNo, true);
            }
            IdNumberBasicInfo oldIdNumberBasicInfo = idNumberBasicInfoCalculator
                    .getValue(request.getLoanKey(), idCardNo, false);

            IdNumberBasicInfo newIdNumberBasicInfo = idNumberBasicInfoCalculator
                    .getValue(request.getLoanKey(), idCardNo, true);

            CompareJson.compareData(oldIdNumberBasicInfo, newIdNumberBasicInfo);
            return oldIdNumberBasicInfo;
        }else {
            logger.warn("unkonwn dataName: {}", dataName);
            return "";
        }
    }

    private String format(List<PayMessage> resultDetail) {
        StringBuilder sb = new StringBuilder();
        sb.append("row count: ").append(resultDetail.size()).append("\n");
        for (PayMessage row : resultDetail) {
            sb.append(row.getId()).append("|");
            sb.append(row.getCreateTime()).append("|");
            sb.append(row.getUpdateTime()).append("|");
            sb.append(row.getPayMessage()).append("\n");
        }
        return sb.toString();
    }

    private String format(JSONArray resultDetail) {
        StringBuilder sb = new StringBuilder();
        sb.append("row count: ").append(resultDetail.size()).append("\n");
        for (Object o : resultDetail) {
            JSONObject row = (JSONObject) o;
            sb.append(row.getString("id")).append("|");
            sb.append(row.getString("create_time")).append("|");
            sb.append(row.getString("update_time")).append("|");
            sb.append(row.getString("pay_message")).append("\n");
        }
        return sb.toString();
    }

    private Long extractLong(JSONArray result, String key) {
        Long c = 0L;
        if (result.size() != 0) {
            Long resultCount1 = result.getJSONObject(0).getLong(key);
            if (resultCount1 != null) {
                c = resultCount1;
            }
        }
        return c;
    }

    private Long extractLongMaybeNull(JSONArray result, String key) {
        Long c = null;
        if (result.size() != 0) {
            Long resultCount1 = result.getJSONObject(0).getLong(key);
            if (resultCount1 != null) {
                c = resultCount1;
            }
        }
        return c;
    }

    private Double extractDouble(JSONArray result, String key) {
        Double c = 0D;
        if (result.size() != 0) {
            Double resultCount1 = result.getJSONObject(0).getDouble(key);
            if (resultCount1 != null) {
                c = resultCount1;
            }
        }
        return c;
    }

    private String extractString(JSONArray result, String key) {
        String c = "";
        if (result.size() != 0) {
            String resultCount1 = result.getJSONObject(0).getString(key);
            if (resultCount1 != null) {
                c = resultCount1;
            }
        }
        return c;
    }


    private List<String> getLoanKeysForUser(String userKey) {
        String loanSql = String.format("\tSELECT\n" +
                "\t\trp.loan_key\n" +
                "\tFROM\n" +
                "\t\trepay_plan rp \n" +
                "\tWHERE\n" +
                "\t\trp.user_key = '%s' \n" +
                "\t\t\n" +
                "\t\tand rp.due_date > DATE_ADD( NOW( ), INTERVAL - 180 DAY )\n" +
                "\t\tAND rp.due_date < NOW()\n" +
                "\t\tgroup by rp.loan_key limit 10000;", userKey);
        JSONArray result = executeSql(ACCOUNT_DATA_SOURCE_NAME, loanSql);
        List<String> loanKeys = new ArrayList<>(result.size());
        for (Object record : result) {
            String loanKey = ((JSONObject) record).getString("loan_key");
            loanKeys.add(loanKey);
        }

        return loanKeys;
    }

    private List<String> getLoanKeysForUserPayOff(String userKey) {
        String loanSql = String.format("\tSELECT\n" +
                "\t\trp.loan_key\n" +
                "\tFROM\n" +
                "\t\trepay_plan rp \n" +
                "\tWHERE\n" +
                "\t\trp.user_key = '%s' \n" +
                "\t\t\n" +
                "\t\tand rp.payoff_time > DATE_ADD( NOW( ), INTERVAL - 90 DAY )\n" +
                "\t\tAND rp.payoff_time < NOW()\n" +
                "\t\tgroup by rp.loan_key limit 10000;", userKey);
        JSONArray result = executeSql(ACCOUNT_DATA_SOURCE_NAME, loanSql);
        List<String> loanKeys = new ArrayList<>(result.size());
        for (Object record : result) {
            String loanKey = ((JSONObject) record).getString("loan_key");
            loanKeys.add(loanKey);
        }

        return loanKeys;
    }

    private List<String> getLoanKeysForUserCreate(String userKey) {
        String loanSql = String.format("\tSELECT\n" +
                "\t\trp.loan_key\n" +
                "\tFROM\n" +
                "\t\trepay_plan rp \n" +
                "\tWHERE\n" +
                "\t\trp.user_key = '%s' \n" +
                "\t\tgroup by rp.loan_key limit 10000;", userKey);
        JSONArray result = executeSql(ACCOUNT_DATA_SOURCE_NAME, loanSql);
        List<String> loanKeys = new ArrayList<>(result.size());
        for (Object record : result) {
            String loanKey = ((JSONObject) record).getString("loan_key");
            loanKeys.add(loanKey);
        }

        return loanKeys;
    }

    private List<String> getLoanKeysForUserAll(String userKey) {
        String loanSql = String.format("\tSELECT\n" +
                "\t\trp.loan_key\n" +
                "\tFROM\n" +
                "\t\trepay_plan rp \n" +
                "\tWHERE\n" +
                "\t\trp.user_key = '%s' \n" +
                "\t\tgroup by rp.loan_key limit 10000;", userKey);
        JSONArray result = executeSql(ACCOUNT_DATA_SOURCE_NAME, loanSql);
        List<String> loanKeys = new ArrayList<>(result.size());
        for (Object record : result) {
            String loanKey = ((JSONObject) record).getString("loan_key");
            loanKeys.add(loanKey);
        }

        return loanKeys;
    }


    private String getRealUserKey(String userKey) {
        String userKeySql = String.format("\tSELECT\n" +
                "\t\tu.account_user_id \n" +
                "\tFROM\n" +
                "\t\t`user` u \n" +
                "\tWHERE\n" +
                "\t\tu.partner_id = 5 \n" +
                "\t\tAND u.partner_user_id = '%s' limit 1;", userKey);
        JSONArray result = executeSql(ACCOUNT_PROXY_DATA_SOURCE_NAME, userKeySql);
        if (result.size() == 0) {
            return "";
        } else {
            return result.getJSONObject(0).getString("account_user_id");
        }
    }

    private JSONArray executeSql(String dataSourceName, String sql) {
        JSONArray array = new JSONArray();


        if (!SpringContext.CONTEXT.containsBean(dataSourceName)) {
            logger.error("dataSource不存在: {}", dataSourceName);
            return array;
        }
        DruidDataSource dataSource = SpringContext.CONTEXT.getBean(dataSourceName, DruidDataSource.class);

        if (StringUtils.isEmpty(sql)) {
            return array;
        }

        sql = sql.trim().toLowerCase();
        if (!sqlCheck(sql)) {
            logger.warn("sql illegal,sql ={}", sql);
            return array;
        }

        Connection conn = null;
        try {
            conn = dataSource.getConnection(5000);
            Statement stat = conn.createStatement();
            String explain = "explain " + sql;
            ResultSet explainRet = stat.executeQuery(explain);
            boolean checkRet = checkExplainRet(explainRet);
            if (!checkRet) {
                logger.warn("explainRet illegal sql ={}", sql);
                return array;
            }

            ResultSet rs = stat.executeQuery(sql);
            if (rs == null) {
                return array;
            }
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            while (rs.next()) {
                JSONObject jsonObj = new JSONObject();
                // 遍历每一列
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnLabel(i);
                    String value = rs.getString(columnName);
                    jsonObj.put(columnName, value);
                }
                array.add(jsonObj);
            }
        } catch (Exception e) {
            logger.error("", e);
        } finally {
            if (null != conn) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    logger.info("", e);
                }
            }
        }

        return array;
    }

    /**
     * 校验sql格式
     *
     * @param sql
     * @return
     */
    private boolean sqlCheck(String sql) {
        if (!sql.startsWith("select")) {
            return false;
        }
        if (!sql.contains("limit")) {
            return false;
        }
        if (!sql.contains("where")) {
            return false;
        }
        int limit = sql.indexOf("limit");
        String limitEnd = sql.substring(limit);
        String regEx = "[^0-9]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(limitEnd);
        String num = m.replaceAll("").trim();
        int n = Integer.parseInt(num);
        return n <= 100000;
    }


    /**
     * 检查exlpain结果
     *
     * @param explainRet
     * @return
     */
    private boolean checkExplainRet(ResultSet explainRet) {
        try {
            int num = 0;
            while (explainRet.next()) {
                num++;
                if (num > 5) {
                    return false;
                }
                String selectType = explainRet.getString("select_type");
                if ("DEPENDENT SUBQUERY".equals(selectType)) {
                    return false;
                }
            }
        } catch (Exception e) {
            logger.error("error", e);
        }
        return true;
    }

    private static List<Map<String,Object>> mergeLineData( List<Map<String, Object>> lineDatas, List<Map<String, Object>> lineHisDatas){
        try{
            List<Map<String, Object>> tmp = new ArrayList<>();
            lineHisDatas.forEach(lineHisData->{

                boolean isContain = false;
                for(Map<String, Object> lineData : lineDatas){
                    if(lineData.get("loan").equals(lineHisData.get("loan"))){
                        isContain=true;
                    }
                }
                if(!isContain){
                    tmp.add(lineHisData);
                }
            });
            lineDatas.addAll(tmp);
        }catch (Exception e){
            LoggerProxy.error("mergeLineData",logger,e.getMessage(),e);
        }
        return lineDatas;
    }

}
