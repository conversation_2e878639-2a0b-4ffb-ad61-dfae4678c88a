package com.youxin.risk.di.service.adapter.smsfeature.tagger;

import com.youxin.risk.di.service.adapter.smsfeature.BaseTagger;
import com.youxin.risk.di.service.adapter.smsfeature.SmsFeatureTaggerProcessor;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 抽象打标器
 * <AUTHOR>
 * @since 2022/3/11 10:13
 */
public abstract class AbstractTagger implements BaseTagger {

    @Resource
    private SmsFeatureTaggerProcessor processor;

    /**
     * 保证初始化注册map
     * <AUTHOR>
     * @since 2022/3/11 10:48
     */
    @PostConstruct
    public void registerTagger(){
        processor.registerTagger(getClass().getSimpleName(),this);
    }
}
