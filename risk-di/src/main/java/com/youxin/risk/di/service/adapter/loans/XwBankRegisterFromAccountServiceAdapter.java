package com.youxin.risk.di.service.adapter.loans;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.adapter.di.ServiceAdapter;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.adapter.di.ServiceResponse;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * <AUTHOR>
 */
public class XwBankRegisterFromAccountServiceAdapter extends ServiceAdapter {
    private static final Logger LOGGER = LoggerFactory.getLogger(XwBankRegisterFromAccountServiceAdapter.class);


    @Override
    protected ServiceResponse callService(ServiceRequest request) {
        Map<String, Object> params = this.generatorParams(request);
        ServiceResponse response = new ServiceResponse();
        response.setRequestId(request.getRequestId());
        Properties property = (Properties)ContextUtil.getBean("configProperties");
        String salt = getSaltBySourceSystem((String)params.get("systemId"),property);
        String sign = null;
        try {
            sign = getSign(MapUtils.objectToMapString(params), salt);
        } catch (Exception e) {
            LoggerProxy.error("XwBankRegisterFromAccountServiceAdapter getSign", LOGGER, "params={},error={}", params, e.getMessage());
        }
        params.put("sign", sign);
        String result = null;
        try {
            result = SyncHTTPRemoteAPI.postJson(this.diService.getServiceUrl(), JSONObject.toJSONString(params), this.diService.getServiceTimeout().intValue());
        } catch (Exception e) {
            LoggerProxy.info("xwBankRegisterFromAccount SyncHTTPRemoteAPI.post", LOGGER, "SyncHTTPRemoteAPI.post "
					+ "error,params={},error={}", params, e.getMessage());
        }
        if (StringUtils.isBlank(result)) {
            LoggerProxy.error("serviceResponseError", LOGGER, "service response status error, request={},result={}", JacksonUtil.toJson(request), result);
            response.setRetCode(RetCodeEnum.RESPONSE_EXCEPTION.getValue());
            response.setRetMsg(RetCodeEnum.RESPONSE_EXCEPTION.getRetMsg());
            return response;
        }
        response.setRetCode(RetCodeEnum.SUCCESS.getValue());
        response.setRetMsg(RetCodeEnum.SUCCESS.getRetMsg());
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("data", result);
        response.setResult(resultMap);
        response.setData(result);
        return response;
    }

    protected Map<String, Object> generatorParams(ServiceRequest request) {
        Map<String, Object> params = Maps.newHashMap();
        params.putAll(request.getParams());
        return params;
    }

    private String getSaltBySourceSystem(String sourceSystem,Properties property) {
        if ("HAO_HUAN".equals(sourceSystem)) {
            return property.getProperty("account.salt.haohuan");
        } else if ("REN_REN_DAI".equals(sourceSystem)) {
            return property.getProperty("account.salt.RRD");
        }
        return property.getProperty("account.salt");
    }
    private String getSign(Map<String, String> map, String salt) {
        List<Map.Entry<String, String>> entryList = MapUtils.sortMapList(map);
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : entryList) {
            sb.append(entry.getKey());
            sb.append("=");
            sb.append(entry.getValue());
            sb.append("&");
        }
        String sign = sb.substring(0, sb.length() - 1);
        return MD5Util.md5Hex(sign + salt);
    }
}
