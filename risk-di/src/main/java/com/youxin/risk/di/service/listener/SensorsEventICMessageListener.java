//package com.youxin.risk.di.service.listener;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.base.Stopwatch;
//import com.youxin.apollo.client.NacosClient;
//import com.youxin.risk.commons.apollo.ApolloClientAdapter;
//import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
//import com.youxin.risk.commons.constants.ApolloNamespace;
//import com.youxin.risk.commons.utils.LoggerProxy;
//import com.youxin.risk.commons.utils.StringUtils;
//import com.youxin.risk.di.common.Constant;
//import com.youxin.risk.di.service.hbase.*;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.BooleanUtils;
//import org.apache.kafka.clients.consumer.ConsumerRecord;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.kafka.listener.BatchAcknowledgingMessageListener;
//import org.springframework.kafka.support.Acknowledgment;
//
//import java.util.*;
//import java.util.concurrent.TimeUnit;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR>
// * @date 2023/04/19 18:28
// * @desc 消费神策中的指定数据，用于反欺诈特征
// * 【反欺诈自填信息相关特征】
// * https://www.tapd.cn/48583176/prong/stories/view/1148583176001016982
// */
//public class SensorsEventICMessageListener implements BatchAcknowledgingMessageListener<String, String> {
//    private final static Logger LOGGER = LoggerFactory.getLogger(SensorsEventICMessageListener.class);
//
//    /**
//     * 在xml文件中定义bean的时候设置，用于kafka数据与本地java bean的字段映射
//     */
//    private Map<String, Object> resultMap = new HashMap<>();
//
//    /**
//     * hbase是否备份集群
//     */
//    private boolean isSlave;
//    private static final String ITEM_CHOICE = "ItemChoice";
//    private static final String UPLOAD_FRONT_IDCARD = "UploadFrontIdCard";
//    private static final String MODIFY_LOAN_AMOUNT = "ModifyLoanAmount";
//
//    // private final static List<String> EVENTS = Lists.newArrayList(ITEM_CHOICE, UPLOAD_FRONT_IDCARD, MODIFY_LOAN_AMOUNT);
//    private final Map<String, String> IDCARD_MAP = new HashMap(){{
//        put("AfrResult","01");
//        put("AfrStart","02");
//        put("AuditConfirmInterface_new","03");
//        put("BackButton","04");
//        put("BackTip","05");
//        put("ChoiceProtocol","06");
//        put("GetCode","07");
//        put("HelpButton","08");
//        put("IdentificationView","09");
//        put("InputBankCard","10");
//        put("ItemChoice","11");
//        put("ItemInput","12");
//        put("ModifyValidityPeriod","13");
//        put("NoBankCardEntryClick","14");
//        put("SubmitBankInfo_new","15");
//        put("SubmitIdInfo_new","16");
//        put("TapProtocol","17");
//        put("UploadBackIdCard","18");
//        put("UploadFrontIdCard","19");
//        put("ViewProtocol","20");
//    }};
//
//    @Autowired
//    private HbaseService hbaseService;
//
//    @Override
//    public void onMessage(List<ConsumerRecord<String, String>> records, Acknowledgment acknowledgment) {
//        Boolean logDetail = ApolloClientAdapter.getBooleanConfig(ApolloNamespaceEnum.DI_SPACE, "logDetail", false);
//        if (logDetail) {
//            LoggerProxy.info("SensorsEventICMessageListener", LOGGER, "_isSlave={}, messages num:{}", isSlave, records.size());
//        }
//        //根据指定的event 和items 过滤出想要的数据
//        Stopwatch stopwatch = Stopwatch.createStarted();
//        List<HbaseTable> eventRecords = filterByEventsAndItem(records);
//
//        // 如果没有过滤出数据，直接返回
//        if (CollectionUtils.isEmpty(eventRecords)) {
//            acknowledgment.acknowledge();
//            LoggerProxy.info("SensorsEventICMessageListener", LOGGER, "_isSlave={}, records is empty", isSlave);
//            return;
//        }
//
//        // 直接复用
//        String printDataLogFlagStr = NacosClient.getByNameSpace(ApolloNamespace.diSpace, "sensors.events.kafka" +
//                ".print", "false");
//        if (BooleanUtils.toBoolean(printDataLogFlagStr)) {
//            LoggerProxy.info("SensorsEventICMessageListener", LOGGER, " message info:{}", JSON.toJSONString(eventRecords));
//        }
//        Map<Class<?>, List<HbaseTable>> map = eventRecords.stream()
//                .collect(Collectors.groupingBy(Object::getClass));
//        if (logDetail) {
//            LoggerProxy.info("SensorsEventICMessageListener", LOGGER, "_isSlave={}, filterByEvents records size:{}, mapSize:{}",
//                    isSlave, eventRecords.size(), map.values().size());
//        }
//        //保存数据到HBase
//        map.values().forEach(list -> hbaseService.puts(list, isSlave));
//
//        acknowledgment.acknowledge();
//        Long cost = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
//        LoggerProxy.info("SensorsEventICMessageListener", LOGGER, "_isSlave={}, cost={}, ack success!",isSlave, cost);
//    }
//
//    /**
//     * 生成rowkey
//      */
//    private String generateItemEventRowKey(Map<String, Object> record) {
//        //设置默认值为当前时间
//        long time = System.currentTimeMillis();
//        try {
//            time = (long) record.get("time");
//        } catch (Exception e) {
//            LoggerProxy.error("SensorsDataMessageListener", LOGGER, " time:{} trans failed!", time);
//        }
//        String userKey = record.get("distinctId").toString();
//        String event = record.get("event").toString();
//        int index = 8;
//
//        if(ITEM_CHOICE.equals(event)){
//            String item = record.get("item").toString();
//            index = Constant.SENSOR_ITEMS.indexOf(item);
//        }
//
//        // 增多不同类型rowkey不同，防止不同类型相同时间点
//        String rowKey = userKey + time + index;
//        return rowKey;
//    }
//
//    /**
//     * 生成rowkey
//     */
//    private String generateIdCardEventRowKey(Map<String, Object> record) {
//        //设置默认值为当前时间
//        long time = System.currentTimeMillis();
//        try {
//            time = (long) record.get("time");
//        } catch (Exception e) {
//            LoggerProxy.error("SensorsDataMessageListener", LOGGER, " time:{} trans failed!", time);
//        }
//        String userKey = record.get("distinctId").toString();
//        String event = record.get("event").toString();
//        String index = IDCARD_MAP.get(event);
//
//        // 增多不同类型rowkey不同，防止不同类型相同时间点
//        String rowKey = userKey + time + index;
//        return rowKey;
//    }
//
//    /**
//     * 根据resultMap中的key(kafka中的项),value(本地javabean中的项),转化数据
//     *
//     * @return
//     */
//    private Map<String, Object> transByMap(Map<String, Object> record) {
//        Map<String, Object> result = new HashMap<>(resultMap.size());
//
//        for (Map.Entry<String, Object> entry : resultMap.entrySet()) {
//            result.put((String) entry.getValue(), getItem(record, entry.getKey()));
//        }
//
//        return result;
//    }
//
//    /**
//     * 获取map的指定key对应的value
//     *
//     * @param map
//     * @param key
//     * @return
//     */
//    private static Object getItem(Map<String, Object> map, String key) {
//        if (map.containsKey(key)) {
//            return map.get(key);
//        }
//        for (Map.Entry<String, Object> entry : map.entrySet()) {
//            if (entry.getValue() instanceof Map) {
//                Object item = getItem((Map<String, Object>) entry.getValue(), key);
//                if (item != null) {
//                    return item;
//                }
//            }
//        }
//        return null;
//
//    }
//
//    /**
//     * 根据事件 和item过滤数据
//     * 只存需要的数据，防止数据量过多
//     * @param records
//     * @return
//     */
//    private List<HbaseTable> filterByEventsAndItem(List<ConsumerRecord<String, String>> records) {
//        List<HbaseTable> recordList = new ArrayList<>();
//        for (ConsumerRecord<String, String> record : records) {
//            try {
//                JSONObject jsonObject = JSONObject.parseObject(record.value());
//
//                String event = (String) jsonObject.get("event");
//                if (!MODIFY_LOAN_AMOUNT.equals(event) && !IDCARD_MAP.keySet().contains(event)) {
//                    continue;
//                }
//                Map<String, Object> map = transByMap(jsonObject);
//                // 过滤time为null的值
//                if (map.get("time") == null) {
//                    continue;
//                }
//
//                //LoggerProxy.info("SensorsEventICMessageListener", LOGGER, " map:{}", JSON.toJSONString(map));
//                switch (event) {
//                    case ITEM_CHOICE:
//                        dealItemChoice(map, recordList);
//                        break;
//                    case MODIFY_LOAN_AMOUNT:
//                        dealAmount(map, recordList);
//                        break;
//                    default:
//                        // 只有落库
//                        dealIdCard(map, recordList);
//                }
//
//            } catch (Exception e) {
//                LoggerProxy.error("SensorsEventICMessageListenerError", LOGGER, " filterByEventsAndItem ERROR!", e);
//            }
//
//        }
//        return recordList;
//    }
//
//    /**
//     * 处理ItemChoice 事件
//     * @param map
//     * @param recordList
//     */
//    private void dealItemChoice(Map<String, Object> map, List<HbaseTable> recordList) {
//        // 先保存一份
//        dealIdCard(map, recordList);
//
//        String item = (String) map.get("item");
//        //选择指定的field，并转化fieldName
//        if (!Constant.SENSOR_ITEMS.contains(item)) {
//            return;
//        }
//
//        String rowKey = generateItemEventRowKey(map);
//        SensorsEventICHB sensorsEventsHB = new SensorsEventICHB(rowKey);
//        sensorsEventsHB.setSensorMap(map);
//        recordList.add(sensorsEventsHB);
//    }
//
//    /**
//     * 处理ItemChoice 事件
//     * @param map
//     * @param recordList
//     */
//    private void dealIdCard(Map<String, Object> map, List<HbaseTable> recordList) {
//        String rowKey = generateIdCardEventRowKey(map);
//        SensorsEventIdCardHB sensorsEventsHB = new SensorsEventIdCardHB(rowKey);
//        //某一用户某一天的数据 事件+雪花id
//        sensorsEventsHB.setSensorMap(map);
//        recordList.add(sensorsEventsHB);
//    }
//
//    private void dealAmount(Map<String, Object> map, List<HbaseTable> recordList) {
//        String operationContent = (String) map.get("operationContent");
//        if (StringUtils.isBlank(operationContent) ||
//                "NULL".equals(operationContent) || "null".equals(operationContent)) {
//            return;
//        }
//        String rowKey = generateItemEventRowKey(map);
//        SensorsEventICHB sensorsEventsHB = new SensorsEventICHB(rowKey);
//        sensorsEventsHB.setSensorMap(map);
//        recordList.add(sensorsEventsHB);
//    }
//
//
//    public Map<String, Object> getResultMap() {
//        return resultMap;
//    }
//
//    public void setResultMap(Map<String, Object> resultMap) {
//        this.resultMap = resultMap;
//    }
//
//    public void setSlave(boolean slave) {
//        isSlave = slave;
//    }
//
//}
