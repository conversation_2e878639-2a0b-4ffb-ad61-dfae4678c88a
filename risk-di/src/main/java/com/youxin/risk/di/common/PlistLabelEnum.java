package com.youxin.risk.di.common;

import org.apache.commons.lang3.StringUtils;

import static com.youxin.risk.di.common.PlistConstant.*;

public enum PlistLabelEnum {

    workAppCnt("workAppCnt", "办公软件安装数_新", ALL, true),
    websearchLoanAppCnt("websearchLoanAppCnt", "借贷软件（网查）安装数_新", ALL, true),
    websearchBetloanAppCnt("websearchBetloanAppCnt", "赌博借贷类软件（网查）安装数_新", ALL, true),
    betloanAppCnt("betloanAppCnt", "赌博借贷类软件（根据name识别）安装数_新", ALL, true),
    teachAppCnt("teachAppCnt", "教育软件安装数_新", ALL, true),
    arankAppCnt("arankAppCnt", "A类软件安装数", ALL, true),
    crankAppCnt("crankAppCnt", "C类软件安装数", ALL, true),
    arank2AppCnt("arank2AppCnt", "A2类软件安装数", ALL, true),
    crank2AppCnt("crank2AppCnt", "C2类软件安装数", ALL, true),
    financeAppCnt("financeAppCnt", "理财软件安装数_新", ALL, true),
    excellentAppCnt("excellentAppCnt", "优质软件安装数", ALL, true),
    eduAppCnt("eduAppCnt", "教学软件安装数_新", ALL, true),
    taxAppCnt("taxAppCnt", "个税软件安装数_新", ALL, true),
    newsAppCnt("newsAppCnt", "新闻软件安装数_新", ALL, true),
    appCnt("appCnt", "app安装数(不去重)", ALL, true),
    distinctAppCnt("distinctAppCnt", "app安装数(去重)", ALL, true),
    in7dTotalAppCnt("in7dTotalAppCnt", "全部近7天安装数_新", IN_7_DAYS, true),
    in7dCrank2AppPer("in7dCrank2AppPer", "近7天C2类软件安装占比", IN_7_DAYS, true),
    in7dArank2AppCnt("in7dArank2AppCnt", "近7天A2类软件安装数量", IN_7_DAYS, false),
    in7dBrank2AppCnt("in7dBrank2AppCnt", "近7天B2类软件安装数量", IN_7_DAYS, false),
    in7dCrank2AppCnt("in7dCrank2AppCnt", "近7天C2类软件安装数量", IN_7_DAYS, true),
    in7dBadAppCnt("in7dBadAppCnt", "近7天劣质软件安装数", IN_7_DAYS, true),
    in7dTier234AppCnt("in7dTier234AppCnt", "近7天tier234安装次数", IN_7_DAYS, true),
    in7dTier1234AppCnt("in7dTier1234AppCnt", "近7天tier1234安装次数", IN_7_DAYS, true),
    in7dTier1AppCnt("in7dTier1AppCnt", "近7天tier1安装次数", IN_7_DAYS, true),
    in7dTier2AppCnt("in7dTier2AppCnt", "近7天tier2安装次数", IN_7_DAYS, false),
    in7dTier3AppCnt("in7dTier3AppCnt", "近7天tier3安装次数", IN_7_DAYS, false),
    in7dTier4AppCnt("in7dTier4AppCnt", "近7天tier4安装次数", IN_7_DAYS, false),
    in15dTier234AppCnt("in15dTier234AppCnt", "近15天tier234安装次数", IN_15_DAYS, true),
    in15dTier2AppCnt("in15dTier2AppCnt", "近15天tier2安装次数", IN_15_DAYS, false),
    in15dTier3AppCnt("in15dTier3AppCnt", "近15天tier3安装次数", IN_15_DAYS, true),
    in15dTier4AppCnt("in15dTier4AppCnt", "近15天tier4安装次数", IN_15_DAYS, false),
    in15dTier34AppCnt("in15dTier34AppCnt", "近15天tier34安装次数", IN_15_DAYS, true),
    in30dTotalAppCnt("in30dTotalAppCnt", "全部近30天安装数_新", IN_30_DAYS, true),
    in30dTier1AppCnt("in30dTier1AppCnt", "近30天tier1安装次数", IN_30_DAYS, true),
    in30dTier2AppCnt("in30dTier2AppCnt", "近30天tier2安装次数", IN_30_DAYS, true),
    in30dTier3AppCnt("in30dTier3AppCnt", "近30天tier3安装次数", IN_30_DAYS, false),
    in30dTier4AppCnt("in30dTier4AppCnt", "近30天tier4安装次数", IN_30_DAYS, false),
    in30dTier34AppCnt("in30dTier34AppCnt", "近30天tier34安装次数", IN_30_DAYS, true),
    in30dBadAppCnt("in30dBadAppCnt", "近30天劣质软件安装数", IN_30_DAYS, true),
    in30dCrank2AppCnt("in30dCrank2AppCnt", "近30天C2类软件安装数", IN_30_DAYS, true),
    in30dCrankAppCnt("in30dCrankAppCnt", "近30天C类软件安装数", IN_30_DAYS, true),
    in60dTier34AppCnt("in60dTier34AppCnt", "近60天tier34安装次数", IN_60_DAYS, true),
    in60dTier1234AppCnt("in60dTier1234AppCnt", "近60天tier1234安装次数", IN_60_DAYS, true),
    in60dTier1AppCnt("in60dTier1AppCnt", "近60天tier1安装次数", IN_60_DAYS, false),
    in60dTier2AppCnt("in60dTier2AppCnt", "近60天tier2安装次数", IN_60_DAYS, true),
    in60dTier3AppCnt("in60dTier3AppCnt", "近60天tier3安装次数", IN_60_DAYS, true),
    in60dTier4AppCnt("in60dTier4AppCnt", "近60天tier4安装次数", IN_60_DAYS, false),
    in90dArankAppPer("in90dArankAppPer", "近90天A类软件安装占比", IN_90_DAYS, true),
    in90dCrankAppPer("in90dCrankAppPer", "近90天C类软件安装数占比", IN_90_DAYS, true),
    in90dArankAppCnt("in90dArankAppCnt", "近90天A类软件安装数", IN_90_DAYS, false),
    in90dCrankAppCnt("in90dCrankAppCnt", "近90天C类软件安装数", IN_90_DAYS, false),
    in90dCrank2AppPer("in90dCrank2AppPer", "近90天C2类软件安装占比", IN_90_DAYS, true),
    in90dArank2AppPer("in90dArank2AppPer", "近90天A2类软件安装数占比", IN_90_DAYS, true),
    in90dArank2AppCnt("in90dArank2AppCnt", "近90天A2类软件安装数", IN_90_DAYS, false),
    in90dBrank2AppCnt("in90dBrank2AppCnt", "近90天B2类软件安装数", IN_90_DAYS, false),
    in90dCrank2AppCnt("in90dCrank2AppCnt", "近90天C2类软件安装数", IN_90_DAYS, false),
    in180dTaxAppCnt("in180dTaxAppCnt", "个税软件近180天安装数_新", IN_180_DAYS, true),
    in180dArank2AppCnt("in180dArank2AppCnt", "近180天A2类软件安装数", IN_180_DAYS, true),
    in180dCrank2AppCnt("in180dCrank2AppCnt", "近180天C2类软件安装数", IN_180_DAYS, true),
    in180dArankAppCnt("in180dArankAppCnt", "近180天A类软件安装数", IN_180_DAYS, true),
    over180dTier1AppCnt("over180dTier1AppCnt", "超过180天tier1安装个数", OVER_180_DAYS, true),
    over180dTier2AppCnt("over180dTier2AppCnt", "超过180天tier2安装个数", OVER_180_DAYS, false),
    over180dTier3AppCnt("over180dTier3AppCnt", "超过180天tier3安装个数", OVER_180_DAYS, false),
    over180dTier4AppCnt("over180dTier4AppCnt", "超过180天tier4安装个数", OVER_180_DAYS, false),
    over180dTier1234AppCnt("over180dTier1234AppCnt", "超过180天tier1234安装个数", OVER_180_DAYS, true),
    over720dTier1AppCnt("over720dTier1AppCnt", "超过720天tier1安装个数", OVER_720_DAYS, true),
    ;

    private String code;
    private String name;
    private String timeRangeKey;
    private boolean inResult;

    PlistLabelEnum(String code, String name, String timeRangeKey, boolean inResult) {
        this.code = code;
        this.name = name;
        this.timeRangeKey = timeRangeKey;
        this.inResult = inResult;
    }

    public static PlistLabelEnum getByCode(String code) {
        for (PlistLabelEnum e : PlistLabelEnum.values()) {
            if (StringUtils.equals(e.getCode(), code)) {
                return e;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTimeRangeKey() {
        return timeRangeKey;
    }

    public void setTimeRangeKey(String timeRangeKey) {
        this.timeRangeKey = timeRangeKey;
    }

    public boolean isInResult() {
        return inResult;
    }

    public void setInResult(boolean inResult) {
        this.inResult = inResult;
    }
}
