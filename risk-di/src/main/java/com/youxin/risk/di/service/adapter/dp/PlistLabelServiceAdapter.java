package com.youxin.risk.di.service.adapter.dp;

import cn.hutool.core.util.ReUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.adapter.di.ServiceAdapter;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.adapter.di.ServiceResponse;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.constants.WaitTypeEnum;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.*;
import com.youxin.risk.di.common.PlistConstant;
import com.youxin.risk.di.common.PlistLabelEnum;
import com.youxin.risk.di.service.impl.DcPlistTagsCacheServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.youxin.risk.commons.constants.DiConstant.UPLOAD_TIME;
import static com.youxin.risk.commons.constants.DiConstant.WAIT_TYPE;
import static com.youxin.risk.commons.utils.DateUtil.TIME_MILLIS;
import static com.youxin.risk.commons.utils.DateUtil.parsePlus;
import static com.youxin.risk.di.common.PlistConstant.*;
import static com.youxin.risk.di.common.PlistLabelEnum.*;
import static com.youxin.risk.di.service.impl.DcPlistTagsCacheServiceImpl.*;
import static java.math.BigDecimal.ROUND_HALF_UP;

/**
 * @Desc Ahttp://wiki.weicai.com.cn/pages/viewpage.action?pageId=75892703
 * @Auth lcb
 * @Date 2023/06/09 13:17
 */
public class PlistLabelServiceAdapter extends ServiceAdapter {

    private static final Logger LOGGER = LoggerFactory.getLogger(PlistLabelServiceAdapter.class);

    private DcPlistTagsCacheServiceImpl dcPlistTagsCacheService = SpringContext.getBean(DcPlistTagsCacheServiceImpl.class);

    ThreadPoolExecutor threadPool = new ThreadPoolExecutor(10, 20, 30, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(100), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());

    @Override
    protected ServiceResponse callService(ServiceRequest request) {
        ServiceResponse response = new ServiceResponse();
        String url = generatorUrl(request);
        Map<String, Object> params = this.generatorParams(request);
        int limit = request.getParams().get("limit") == null ? 300 : Integer.parseInt((String) request.getParams().get("limit"));
        response.setRequestId(request.getRequestId());
        String requestBody = JacksonUtil.toJson(params);
        LoggerProxy.info("PlistLabelServiceAdapter", LOGGER, "before call service, serviceCode={}, params={}", request.getServiceCode(), requestBody);
        // 获取用户的最新的plist:https://babel.we.cn/babel/v1/record/user/HFQ_UPLOAD_PLIST_RECORD?userkey=3caf76194a7ba951a8881a7ee116db9b&systemid=HAO_HUAN
        String result = SyncHTTPRemoteAPI.get(url, diService.getServiceTimeout().intValue());
        if (org.apache.commons.lang.StringUtils.isBlank(result)) {
            LoggerProxy.error("PlistLabelServiceAdapter", LOGGER, "获取dp的plist异常, request={},result={}", JacksonUtil.toJson(request), result);
            response.setRetCode(RetCodeEnum.RESPONSE_EXCEPTION.getValue());
            response.setRetMsg(RetCodeEnum.RESPONSE_EXCEPTION.getRetMsg());
            return response;
        }
        JSONObject dataResult = JSON.parseObject(result);
        JSONArray records = dataResult.getJSONArray("records");
        if (records == null || records.isEmpty()) {
            LoggerProxy.warn("PlistLabelServiceAdapter", LOGGER, "获取dp的plist为空, request={},result={}", JacksonUtil.toJson(request), result);
            response.setRetCode(RetCodeEnum.NO_DATA.getValue());
            response.setRetMsg(RetCodeEnum.NO_DATA.getRetMsg());
            return response;
        }
        JSONObject data = records.getJSONObject(0).getJSONObject("data");
        if (data == null || data.getJSONArray("appList") == null || CollectionUtils.isEmpty(data.getJSONArray("appList"))) {
            LoggerProxy.warn("PlistLabelServiceAdapter", LOGGER, "获取dp的plist的data为空, request={},result={}", JacksonUtil.toJson(request), result);
            response.setRetCode(RetCodeEnum.NO_DATA.getValue());
            response.setRetMsg(RetCodeEnum.NO_DATA.getRetMsg());
            return response;
        }
        // 构造默认值
        Map<String, Object> resultMap = new ConcurrentHashMap<>();
        String jobId = (String) JSONPath.eval(records.getJSONObject(0), "$.job.jobID");
        response.setJobId(jobId);
        resultMap.put("jobId", jobId);
        Object httpTimestampObj = JSONPath.eval(records.getJSONObject(0), "$.data.baseData.httpTimestamp");
        if (Objects.nonNull(httpTimestampObj)) {
            resultMap.put("httpTimestamp", Long.parseLong(httpTimestampObj.toString()));
        }
        String os = (String) JSONPath.eval(records.getJSONObject(0), "$.data.baseData.os");
        resultMap.put("os", os);
        JSONArray appList = data.getJSONArray("appList");
        // 如果是IOS，则全部输出-999
        if (isIos(os)) {
            for (int i = 0; i < PlistLabelEnum.values().length; i++) {
                PlistLabelEnum plistLabelEnum = PlistLabelEnum.values()[i];
                if (plistLabelEnum.isInResult()) {
                    resultMap.put(plistLabelEnum.getCode(), -999);
                }
            }
            setResultMapAllDetail(resultMap, appList, limit);
            response.setRetCode(RetCodeEnum.SUCCESS.getValue());
            response.setRetMsg(RetCodeEnum.SUCCESS.getRetMsg());
            response.setResult(resultMap);
            response.setData(JSON.toJSONString(resultMap));
            return response;
        }
        // 获取当前日期
        Date nowDate = DateUtil.getCurrentDateZero();
        Date date7DayAgo = DateUtil.subtractDay(nowDate, 7);
        Date date15DayAgo = DateUtil.subtractDay(nowDate, 15);
        Date date1MonAgo = DateUtil.subtractDay(nowDate, 30);
        Date date2MonAgo = DateUtil.subtractDay(nowDate, 60);
        Date date3MonAgo = DateUtil.subtractDay(nowDate, 90);
        Date date6MonAgo = DateUtil.subtractDay(nowDate, 180);
        Date date2YearAgo = DateUtil.subtractDay(nowDate, 720);
        List<JSONObject> listAll = Lists.newArrayList();
        List<JSONObject> listIn7Day = Lists.newArrayList();
        List<JSONObject> listIn15Day = Lists.newArrayList();
        List<JSONObject> listIn1Mon = Lists.newArrayList();
        List<JSONObject> listIn2Mon = Lists.newArrayList();
        List<JSONObject> listIn3Mon = Lists.newArrayList();
        List<JSONObject> listIn6Mon = Lists.newArrayList();
        List<JSONObject> listOver6Mon = Lists.newArrayList();
        List<JSONObject> listOver2Year = Lists.newArrayList();
        List<JSONObject> listAllDetail = Lists.newArrayList();
        for (int i = 0; i < appList.size(); i++) {
            JSONObject app = appList.getJSONObject(i);
            if (Objects.isNull(app)) {
                continue;
            }
            // appName 首尾去空格，老接口做了去空格处理
            String appName = app.getString("appName");
            if (StringUtils.isNotBlank(appName)) {
                app.put("appName", appName.trim());
            }
            listAll.add(app);
            String installTime = app.getString("lastOpenTime");
            if (StringUtils.isBlank(installTime)) {
                continue;
            }
            Date installDate = DateUtil.parseDateWithTimeZoneZero(installTime);
            if (installDate == null) {
                continue;
            }
            listAllDetail.add(app);
            if (isInTimeRange(installDate, date7DayAgo)) {
                listIn7Day.add(app);
            }
            if (isInTimeRange(installDate, date15DayAgo)) {
                listIn15Day.add(app);
            }
            if (isInTimeRange(installDate, date1MonAgo)) {
                listIn1Mon.add(app);
            }
            if (isInTimeRange(installDate, date2MonAgo)) {
                listIn2Mon.add(app);
            }
            if (isInTimeRange(installDate, date3MonAgo)) {
                listIn3Mon.add(app);
            }
            if (isInTimeRange(installDate, date6MonAgo)) {
                listIn6Mon.add(app);
            }
            // 超过180天
            if (!isInTimeRange(installDate, date6MonAgo)) {
                listOver6Mon.add(app);
            }
            // 超过720天
            if (!isInTimeRange(installDate, date2YearAgo)) {
                listOver2Year.add(app);
            }
        }
        Map<String, List<JSONObject>> listMap = Maps.newHashMap();
        listMap.put(ALL, listAll);
        listMap.put(ALLDetail, listAllDetail);
        listMap.put(IN_7_DAYS, listIn7Day);
        listMap.put(IN_15_DAYS, listIn15Day);
        listMap.put(IN_30_DAYS, listIn1Mon);
        listMap.put(IN_60_DAYS, listIn2Mon);
        listMap.put(IN_90_DAYS, listIn3Mon);
        listMap.put(IN_180_DAYS, listIn6Mon);
        listMap.put(OVER_180_DAYS, listOver6Mon);
        listMap.put(OVER_720_DAYS, listOver2Year);

        // 构造默认值
        for (int i = 0; i < PlistLabelEnum.values().length; i++) {
            PlistLabelEnum plistLabelEnum = PlistLabelEnum.values()[i];
            if (plistLabelEnum.isInResult()) {
                resultMap.put(plistLabelEnum.getCode(), 0);
                // 设置默认值：-999
                if (Lists.newArrayList(in7dCrank2AppPer.getCode(), in90dArankAppPer.getCode(), in90dCrankAppPer.getCode(),
                        in90dCrank2AppPer.getCode(), in90dArank2AppPer.getCode()).contains(plistLabelEnum.getCode())) {
                    resultMap.put(plistLabelEnum.getCode(), -999);
                }
            }
        }
        // 构造特征值
        Stopwatch stopwatch = Stopwatch.createStarted();
        getResultMap(listMap, resultMap, nowDate, limit);
        LoggerProxy.info("PlistLabelServiceAdapter", LOGGER, "getResultMap info,  cost:{}, request: {}",
                stopwatch.stop().elapsed(TimeUnit.MILLISECONDS), JacksonUtil.toJson(request));
        // 如果获取到的详单仅有1个好分期App
        if (isOnlyHfqApp(listAll)) {
            resultMap.put("isOnlyHfqApp", "1");
            response.setRetCode(RetCodeEnum.SUCCESS.getValue());
            response.setRetMsg(RetCodeEnum.SUCCESS.getRetMsg());
            response.setResult(resultMap);
            response.setData(JSON.toJSONString(resultMap));
            return response;
        }
        response.setRetCode(RetCodeEnum.SUCCESS.getValue());
        response.setRetMsg(RetCodeEnum.SUCCESS.getRetMsg());
        response.setResult(resultMap);
        response.setData(JSON.toJSONString(resultMap));

        return response;
    }

    /**
     * 详单只有1个好分期App
     *
     * @param listAll
     * @return
     */
    private boolean isOnlyHfqApp(List<JSONObject> listAll) {
        return listAll.size() == 1 && listAll.stream().anyMatch(app -> "好分期".equals(app.getString("appName")));
    }

    /**
     * 是否是ios
     *
     * @param os
     * @return
     */
    private boolean isIos(String os) {
        return Lists.newArrayList("ios", "IOS", "iOS").contains(os);
    }

    /**
     * 安装时间是否在指定时间范围内
     *
     * @param installDate 安装时间
     * @param dateDayAgo  指定时间
     * @return
     */
    private boolean isInTimeRange(Date installDate, Date dateDayAgo) {
        return DateUtil.format(installDate, DateUtil.WEB_FORMAT).compareTo(DateUtil.format(dateDayAgo, DateUtil.WEB_FORMAT)) >= 0;
    }

    /**
     * 获取特征map
     *
     * @param appMap
     * @param resultMap
     * @param nowDate
     */
    private void getResultMap(Map<String, List<JSONObject>> appMap, Map<String, Object> resultMap, Date nowDate, int limit) {
        // 获取app分类库
        if (plistTagMap == null) {
            // 获取app分类库
            Stopwatch stopwatch = Stopwatch.createStarted();
            dcPlistTagsCacheService.initPlistTagsMap();
            LoggerProxy.info("PlistLabelServiceAdapter", LOGGER, "get appStock, cost:{}", stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
        }
        CountDownLatch countDownLatch = new CountDownLatch(appMap.size());
        appMap.forEach((postfix, appList) -> {
            threadPool.execute(() -> {
                Stopwatch stopwatch = Stopwatch.createStarted();
                Map<String, Set<String>> setMap = new HashMap<>();
                try {
                    if (appList.isEmpty()) {
                        return;
                    }
                    // 初始化set
                    setDefault(postfix, setMap);
                    switch (postfix) {
                        // 全部
                        case ALL:
                            setResultMapAll(resultMap, postfix, appList, setMap);
                            break;
                        // 全部
                        case ALLDetail:
                            setResultMapAllDetail(resultMap, appList, nowDate, limit);
                            break;
                        case IN_7_DAYS:
                            setResultMapIn7d(resultMap, postfix, appList, setMap);
                            break;
                        case IN_30_DAYS:
                            setResultMapIn30d(resultMap, postfix, appList, setMap);
                            break;
                        // 近15天
                        case IN_15_DAYS:
                            setResultMapIn15d(resultMap, postfix, appList, setMap);
                            break;
                        // 近60天
                        case IN_60_DAYS:
                            setResultMapIn60d(resultMap, postfix, appList, setMap);
                            break;
                        // 近90天
                        case IN_90_DAYS:
                            setResultMapIn90d(resultMap, postfix, appList, setMap);
                            break;
                        // 近180天
                        case IN_180_DAYS:
                            setResultMapIn180d(resultMap, postfix, appList, setMap);
                            break;
                        // 超过180天
                        case OVER_180_DAYS:
                            setResultMapOver180d(resultMap, postfix, appList, setMap);
                            break;
                        // 超过720天
                        case OVER_720_DAYS:
                            setResultMapOver720d(resultMap, postfix, appList, setMap);
                            break;
                        default:
                            break;
                    }
                    LoggerProxy.info("PlistLabelServiceAdapter1", LOGGER, "get data, postfix: {}, nowDate: {}, cost:{}", postfix, DateUtil.format(nowDate, DateUtil.LONG_WEB_FORMAT), stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
                } catch (Exception ex) {
                    LoggerProxy.error("PlistLabelServiceAdapter1", LOGGER, "get data error, postfix: {}", postfix);
                } finally {
                    countDownLatch.countDown();
                }
            });
        });
        try {
            countDownLatch.await();
        } catch (Exception ex) {
            LoggerProxy.error("PlistLabelServiceAdapter", LOGGER, "await error", ex);
        }
        // 去掉不需要的输出
        for (int i = 0; i < PlistLabelEnum.values().length; i++) {
            PlistLabelEnum plistLabelEnum = PlistLabelEnum.values()[i];
            if (!plistLabelEnum.isInResult()) {
                resultMap.remove(plistLabelEnum.getCode());
            }
        }
    }

    private void setDefault(String postfix, Map<String, Set<String>> setMap) {
        for (int i = 0; i < PlistLabelEnum.values().length; i++) {
            PlistLabelEnum plistLabelEnum = PlistLabelEnum.values()[i];
            if (StringUtils.equals(plistLabelEnum.getTimeRangeKey(), postfix)) {
                String code = plistLabelEnum.getCode();
                setMap.put(code, new HashSet<>());
            }
        }
    }

    private void setResultMapByPostfix(Map<String, Object> resultMap, String postfix, Map<String, Set<String>> setMap) {
        for (int i = 0; i < PlistLabelEnum.values().length; i++) {
            PlistLabelEnum plistLabelEnum = PlistLabelEnum.values()[i];
            if (StringUtils.equals(plistLabelEnum.getTimeRangeKey(), postfix)) {
                String code = plistLabelEnum.getCode();
                if (CollectionUtils.isNotEmpty(setMap.get(code))) {
                    resultMap.put(code, setMap.get(code).size());
                }
            }
        }
    }

    private void setResultMapOver720d(Map<String, Object> resultMap, String postfix, List<JSONObject> appList, Map<String, Set<String>> setMap) {
        appList.forEach(app -> {
            String appName = app.getString("appName");
            // tier1安装个数
            if (tier1AppList.contains(appName) && !isBlack(appName)) {
                setMap.get(over720dTier1AppCnt.getCode()).add(appName);
            }
        });
        setResultMapByPostfix(resultMap, postfix, setMap);
    }

    private void setResultMapOver180d(Map<String, Object> resultMap, String postfix, List<JSONObject> appList, Map<String, Set<String>> setMap) {
        appList.forEach(app -> {
            String appName = app.getString("appName");
            // tier1安装个数
            if (tier1AppList.contains(appName) && !isBlack(appName)) {
                setMap.get(over180dTier1AppCnt.getCode()).add(appName);
            }
            // tier2安装个数
            if (tier2AppList.contains(appName) && !isBlack(appName)) {
                setMap.get(over180dTier2AppCnt.getCode()).add(appName);
            }
            // tier3安装个数
            if (tier3AppList.contains(appName) && !isBlack(appName)) {
                setMap.get(over180dTier3AppCnt.getCode()).add(appName);
            }
            // tier4安装个数
            if (tier4AppList.contains(appName) && !isBlack(appName)) {
                setMap.get(over180dTier4AppCnt.getCode()).add(appName);
            }
        });
        setResultMapByPostfix(resultMap, postfix, setMap);
        int tier1Count = setMap.get(over180dTier1AppCnt.getCode()).size();
        int tier2Count = setMap.get(over180dTier2AppCnt.getCode()).size();
        int tier3Count = setMap.get(over180dTier3AppCnt.getCode()).size();
        int tier4Count = setMap.get(over180dTier4AppCnt.getCode()).size();
        // tier1234安装次数
        resultMap.put(over180dTier1234AppCnt.getCode(), tier1Count + tier2Count + tier3Count + tier4Count);
    }

    private void setResultMapIn180d(Map<String, Object> resultMap, String postfix, List<JSONObject> appList, Map<String, Set<String>> setMap) {
        appList.forEach(app -> {
            String appName = app.getString("appName");
            // rank1A软件
            if (rank1AAppList.contains(appName)) {
                setMap.get(in180dArankAppCnt.getCode()).add(appName);
            }
            // rank2A软件
            if (rank2AAppList.contains(appName)) {
                setMap.get(in180dArank2AppCnt.getCode()).add(appName);
            }
            // rank2C软件
            if (rank2CAppList.contains(appName)) {
                setMap.get(in180dCrank2AppCnt.getCode()).add(appName);
            }
            // 个税软件近180天安装数_新
            if (ReUtil.contains(TAX_APP, appName)) {
                setMap.get(in180dTaxAppCnt.getCode()).add(appName);
            }
        });
        setResultMapByPostfix(resultMap, postfix, setMap);

    }

    private void setResultMapIn90d(Map<String, Object> resultMap, String postfix, List<JSONObject> appList, Map<String, Set<String>> setMap) {
        appList.forEach(app -> {
            String appName = app.getString("appName");
            // rank2A软件
            if (rank2AAppList.contains(appName)) {
                setMap.get(in90dArank2AppCnt.getCode()).add(appName);
            }
            // rank2B软件
            if (rank2BAppList.contains(appName)) {
                setMap.get(in90dBrank2AppCnt.getCode()).add(appName);
            }
            // rank2C软件
            if (rank2CAppList.contains(appName)) {
                setMap.get(in90dCrank2AppCnt.getCode()).add(appName);
            }
            // rankA软件
            if (rank1AAppList.contains(appName)) {
                setMap.get(in90dArankAppCnt.getCode()).add(appName);
            }
            // rankC软件
            if (rank1CAppList.contains(appName)) {
                setMap.get(in90dCrankAppCnt.getCode()).add(appName);
            }
        });
        setResultMapByPostfix(resultMap, postfix, setMap);
        int a2Count = setMap.get(in90dArank2AppCnt.getCode()).size();
        int b2Count = setMap.get(in90dBrank2AppCnt.getCode()).size();
        int c2Count = setMap.get(in90dCrank2AppCnt.getCode()).size();
        int all2Count = a2Count + b2Count + c2Count;
        if (all2Count != 0) {
            // C2类软件安装占比：取安装日期90天内C2类软件/（近90天内安装的A2+近90天内安装的B2+近90天内安装的C2类软件）
            resultMap.put(in90dCrank2AppPer.getCode(), new BigDecimal(c2Count).divide(new BigDecimal(all2Count), 4, ROUND_HALF_UP));
            // A2类软件安装占比：取安装日期90天内A2类软件/（近90天内安装的A2+近90天内安装的B2+近90天内安装的C2类软件）
            resultMap.put(in90dArank2AppPer.getCode(), new BigDecimal(a2Count).divide(new BigDecimal(all2Count), 4, ROUND_HALF_UP));
        }

        int aCount = setMap.get(in90dArankAppCnt.getCode()).size();
        int cCount = setMap.get(in90dCrankAppCnt.getCode()).size();
        int allCount = aCount + cCount;
        if (allCount != 0) {
            // rank1A类占比：取安装日期90天内A类软件/（近90天内安装的A+近90天内安装的C类软件）
            resultMap.put(in90dArankAppPer.getCode(), new BigDecimal(aCount).divide(new BigDecimal(allCount), 4, ROUND_HALF_UP));
            // rank1C类占比：取安装日期90天内C类软件/（近90天内安装的A+近90天内安装的C类软件）
            resultMap.put(in90dCrankAppPer.getCode(), new BigDecimal(cCount).divide(new BigDecimal(allCount), 4, ROUND_HALF_UP));
        }
    }

    private void setResultMapIn60d(Map<String, Object> resultMap, String postfix, List<JSONObject> appList, Map<String, Set<String>> setMap) {
        appList.forEach(app -> {
            String appName = app.getString("appName");
            // tier1
            if (tier1AppList.contains(appName) && !isBlack(appName)) {
                setMap.get(in60dTier1AppCnt.getCode()).add(appName);
            }
            // tier2
            if (tier2AppList.contains(appName) && !isBlack(appName)) {
                setMap.get(in60dTier2AppCnt.getCode()).add(appName);
            }
            // tier3
            if (tier3AppList.contains(appName) && !isBlack(appName)) {
                setMap.get(in60dTier3AppCnt.getCode()).add(appName);
            }
            // tier4
            if (tier4AppList.contains(appName) && !isBlack(appName)) {
                setMap.get(in60dTier4AppCnt.getCode()).add(appName);
            }
        });
        setResultMapByPostfix(resultMap, postfix, setMap);
        int tier1Count = setMap.get(in60dTier1AppCnt.getCode()).size();
        int tier2Count = setMap.get(in60dTier2AppCnt.getCode()).size();
        int tier3Count = setMap.get(in60dTier3AppCnt.getCode()).size();
        int tier4Count = setMap.get(in60dTier4AppCnt.getCode()).size();
        // tier34安装次数
        resultMap.put(in60dTier34AppCnt.getCode(), tier3Count + tier4Count);
        // tier1234安装次数
        resultMap.put(in60dTier1234AppCnt.getCode(), tier1Count + tier2Count + tier3Count + tier4Count);
    }

    private void setResultMapIn30d(Map<String, Object> resultMap, String postfix, List<JSONObject> appList, Map<String, Set<String>> setMap) {
        appList.forEach(app -> {
            String appName = app.getString("appName");
            // 近30天安装数
            setMap.get(in30dTotalAppCnt.getCode()).add(appName);
            // tier1
            if (tier1AppList.contains(appName) && !isBlack(appName)) {
                setMap.get(in30dTier1AppCnt.getCode()).add(appName);
            }
            // tier2
            if (tier2AppList.contains(appName) && !isBlack(appName)) {
                setMap.get(in30dTier2AppCnt.getCode()).add(appName);
            }
            // tier3
            if (tier3AppList.contains(appName) && !isBlack(appName)) {
                setMap.get(in30dTier3AppCnt.getCode()).add(appName);
            }
            // tier4
            if (tier4AppList.contains(appName) && !isBlack(appName)) {
                setMap.get(in30dTier4AppCnt.getCode()).add(appName);
            }
            // 劣质软件
            if (isBad(appName)) {
                setMap.get(in30dBadAppCnt.getCode()).add(appName);
            }
            // rank2C软件
            if (rank2CAppList.contains(appName)) {
                setMap.get(in30dCrank2AppCnt.getCode()).add(appName);
            }
            // rank1C软件
            if (rank1CAppList.contains(appName)) {
                setMap.get(in30dCrankAppCnt.getCode()).add(appName);
            }
        });
        setResultMapByPostfix(resultMap, postfix, setMap);
        int tier3Count = setMap.get(in30dTier3AppCnt.getCode()).size();
        int tier4Count = setMap.get(in30dTier4AppCnt.getCode()).size();
        // tier34安装次数
        resultMap.put(in30dTier34AppCnt.getCode(), tier3Count + tier4Count);
    }

    private void setResultMapIn15d(Map<String, Object> resultMap, String postfix, List<JSONObject> appList, Map<String, Set<String>> setMap) {
        appList.forEach(app -> {
            String appName = app.getString("appName");
            // tier2
            if (tier2AppList.contains(appName) && !isBlack(appName)) {
                setMap.get(in15dTier2AppCnt.getCode()).add(appName);
            }
            // tier3
            if (tier3AppList.contains(appName) && !isBlack(appName)) {
                setMap.get(in15dTier3AppCnt.getCode()).add(appName);
            }
            // tier4
            if (tier4AppList.contains(appName) && !isBlack(appName)) {
                setMap.get(in15dTier4AppCnt.getCode()).add(appName);
            }
        });
        setResultMapByPostfix(resultMap, postfix, setMap);
        // tier234安装次数
        int tier2Count = setMap.get(in15dTier2AppCnt.getCode()).size();
        int tier3Count = setMap.get(in15dTier3AppCnt.getCode()).size();
        int tier4Count = setMap.get(in15dTier4AppCnt.getCode()).size();
        resultMap.put(in15dTier234AppCnt.getCode(), tier2Count + tier3Count + tier4Count);
        // tier34安装次数
        resultMap.put(in15dTier34AppCnt.getCode(), tier3Count + tier4Count);
    }

    private void setResultMapIn7d(Map<String, Object> resultMap, String postfix, List<JSONObject> appList, Map<String, Set<String>> setMap) {
        appList.forEach(app -> {
            String appName = app.getString("appName");
            // 全部近7天安装数_新
            setMap.get(in7dTotalAppCnt.getCode()).add(appName);
            // 近7天劣质软件安装数
            if (isBad(appName)) {
                setMap.get(in7dBadAppCnt.getCode()).add(appName);
            }
            // tier1
            if (tier1AppList.contains(appName) && !isBlack(appName)) {
                setMap.get(in7dTier1AppCnt.getCode()).add(appName);
            }
            // tier2
            if (tier2AppList.contains(appName) && !isBlack(appName)) {
                setMap.get(in7dTier2AppCnt.getCode()).add(appName);
            }
            // tier3
            if (tier3AppList.contains(appName) && !isBlack(appName)) {
                setMap.get(in7dTier3AppCnt.getCode()).add(appName);
            }
            // tier4
            if (tier4AppList.contains(appName) && !isBlack(appName)) {
                setMap.get(in7dTier4AppCnt.getCode()).add(appName);
            }
            // rank2A软件
            if (rank2AAppList.contains(appName)) {
                setMap.get(in7dArank2AppCnt.getCode()).add(appName);
            }
            // rank2B软件
            if (rank2BAppList.contains(appName)) {
                setMap.get(in7dBrank2AppCnt.getCode()).add(appName);
            }
            // rank2C软件
            if (rank2CAppList.contains(appName)) {
                setMap.get(in7dCrank2AppCnt.getCode()).add(appName);
            }
        });
        setResultMapByPostfix(resultMap, postfix, setMap);
        // 占比类的数据处理
        int aCount = setMap.get(in7dArank2AppCnt.getCode()).size();
        int bCount = setMap.get(in7dBrank2AppCnt.getCode()).size();
        int cCount = setMap.get(in7dCrank2AppCnt.getCode()).size();
        if ((aCount + bCount + cCount) != 0) {
            // C2类软件安装占比：取安装日期7天内C2类软件/（近7天内安装的A2+近7天内安装的B2+近7天内安装的C2类软件）
            resultMap.put(in7dCrank2AppPer.getCode(), new BigDecimal(cCount).divide(new BigDecimal(aCount + bCount + cCount), 4, ROUND_HALF_UP));
        }
        int tier1Count = setMap.get(in7dTier1AppCnt.getCode()).size();
        int tier2Count = setMap.get(in7dTier2AppCnt.getCode()).size();
        int tier3Count = setMap.get(in7dTier3AppCnt.getCode()).size();
        int tier4Count = setMap.get(in7dTier4AppCnt.getCode()).size();
        // tier234安装次数
        resultMap.put(in7dTier234AppCnt.getCode(), tier2Count + tier3Count + tier4Count);
        // tier1234安装次数
        resultMap.put(in7dTier1234AppCnt.getCode(), tier1Count + tier2Count + tier3Count + tier4Count);
    }

    private void setResultMapAllDetail(Map<String, Object> resultMap, List<JSONObject> appList, Date nowDate, int limit) {
        Set<String> details = appList.stream().filter(app -> StringUtils.isNotBlank(app.getString("packageName"))).map(app -> {
            String packageName = app.getString("packageName");
            String installTime = app.getString("lastOpenTime");
            Date installDate = DateUtil.parseDateWithTimeZone(installTime);
            int diffDays = DateUtil.diffDays(installDate, nowDate);
            return String.format("%s#%d", packageName, diffDays);
        }).limit(limit).collect(Collectors.toSet());
        // 拼接：package_daydiff
        resultMap.put("packageDayDiff", StringUtils.join(details, ","));
    }

    private void setResultMapAllDetail(Map<String, Object> resultMap, JSONArray appList, int limit) {
        Set<String> details = appList.stream().filter(app -> StringUtils.isNotBlank(((JSONObject) app).getString("packageName")))
                .map(app -> ((JSONObject) app).getString("packageName")).limit(limit).collect(Collectors.toSet());
        // 拼接：package，逗号分割
        resultMap.put("packageDayDiff", StringUtils.join(details, ","));
    }

    private void setResultMapAll(Map<String, Object> resultMap, String postfix, List<JSONObject> appList, Map<String, Set<String>> setMap) {
        appList.forEach(app -> {
            String appName = app.getString("appName");
            setMap.get(distinctAppCnt.getCode()).add(appName);
            // rank1A软件
            if (rank1AAppList.contains(appName)) {
                setMap.get(arankAppCnt.getCode()).add(appName);
            }
            // rank1C软件
            if (rank1CAppList.contains(appName)) {
                setMap.get(crankAppCnt.getCode()).add(appName);
            }
            // rank2A软件
            if (rank2AAppList.contains(appName)) {
                setMap.get(arank2AppCnt.getCode()).add(appName);
            }
            // rank2C软件
            if (rank2CAppList.contains(appName)) {
                setMap.get(crank2AppCnt.getCode()).add(appName);
            }
            // 优质软件：'税|公积金|社保|强国|人社|党建|自律|习惯|目标|打卡|番茄钟|健身|跑步|学习|教育|健康|语言|乐器|Instagram|Twitter|FaceBook|YouTube|Uber|优步|Lyft|商旅'
            if (ReUtil.contains(EXCELLENT_APP, appName)) {
                setMap.get(excellentAppCnt.getCode()).add(appName);
            }
            // 借款软件:web_search_loan_tag=1
            if (loanAppList.contains(appName)) {
                setMap.get(websearchLoanAppCnt.getCode()).add(appName);
            }
            // 赌博借贷软件:web_search_loan_tag=1 or web_search_gamble_tag=1
            if (loanAppList.contains(appName) || betAppList.contains(appName)) {
                setMap.get(websearchBetloanAppCnt.getCode()).add(appName);
            }
            // 教学软件
            if (ReUtil.contains(EDU_APP, appName)) {
                setMap.get(eduAppCnt.getCode()).add(appName);
            }
            // 个税软件
            if (ReUtil.contains(TAX_APP, appName)) {
                setMap.get(taxAppCnt.getCode()).add(appName);
            }
            // 赌博借贷类软件（根据name识别）安装数_新
            if (isLoan(appName) || isBet(appName)) {
                setMap.get(betloanAppCnt.getCode()).add(appName);
            }
            // 办公软件
            if (plistTagMap.get(WORK_APP).contains(appName)) {
                setMap.get(workAppCnt.getCode()).add(appName);
                return;
            }
            // 教育软件
            if (plistTagMap.get(TEACH_APP).contains(appName)) {
                setMap.get(teachAppCnt.getCode()).add(appName);
                return;
            }
            // 理财软件
            if (plistTagMap.get(FINANCE_APP).contains(appName)) {
                setMap.get(financeAppCnt.getCode()).add(appName);
                return;
            }
            // 新闻软件
            if (plistTagMap.get(NEWS_APP).contains(appName)) {
                setMap.get(newsAppCnt.getCode()).add(appName);
            }
        });
        setResultMapByPostfix(resultMap, postfix, setMap);
        // app安装总数
        resultMap.put(appCnt.getCode(), appList.size());
    }

    @Override
    protected boolean isInUploadTime(ServiceRequest request, ServiceResponse response) {
        try {
            if (!UploadTimeJudgeUtil.isNeedLatestUploadData(request)) {
                return true;
            }
            if (response.getResult() != null) {
                // ios的数据不需要采集等待
                final String os = (String) response.getResult().get("os");
                if (isIos(os)) {
                    return true;
                }
                // 针对采集内容等待
                if (StringUtils.equals(WaitTypeEnum.DETAIL.getName(), (String) request.getParams().get(WAIT_TYPE))) {
                    // 针对获取到详单是1个好分期app的情况,需要等待
                    String isOnlyHfqApp = (String) response.getResult().get("isOnlyHfqApp");
                    return !StringUtils.equals("1", isOnlyHfqApp);
                }
                // 针对采集时间；采集时间和内容
                // 如果没有送采集开始时间，直接返回了
                String uploadTimeStr = (String) request.getParams().get(UPLOAD_TIME);
                if (StringUtils.isBlank(uploadTimeStr)) {
                    return true;
                }
                Date uploadTime = parsePlus(uploadTimeStr, TIME_MILLIS);
                final Long httpTimeStamp = (Long) response.getResult().get("httpTimestamp");
                if (Objects.isNull(httpTimeStamp)) {
                    return false;
                }
                Date latestUploadTime = new Date(httpTimeStamp);
                return uploadTime.compareTo(latestUploadTime) != 1;
            }
            return false;
        } catch (Exception e) {
            LoggerProxy.error("PlistLabelServiceAdapter#isInUploadTime", LOGGER, "error {}", e);
        }
        return false;
    }

    public boolean isBad(String appName) {
        return ReUtil.contains(PlistConstant.BAD_APP, appName);
    }

    public boolean isBlack(String appName) {
        return blackAppList.contains(appName);
    }

    public boolean isLoan(String appName) {
        return ReUtil.contains(LOAN_APP, appName) && !ReUtil.contains(NO_LOAN_APP, appName);
    }

    public boolean isBet(String appName) {
        return ReUtil.contains(BET_APP, appName);
    }

}
