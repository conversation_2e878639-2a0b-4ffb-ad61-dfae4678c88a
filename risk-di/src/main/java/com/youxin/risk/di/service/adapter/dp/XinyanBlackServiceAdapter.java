package com.youxin.risk.di.service.adapter.dp;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.adapter.di.ServiceResponse;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.JacksonUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class XinyanBlackServiceAdapter extends DpAsyncServiceAdapter {
    private static final Logger LOGGER = LoggerFactory.getLogger(XinyanBlackServiceAdapter.class);

    protected static final String RESPONSE_STATUS_SUCCESS = "0";
    protected static final String RESPONSE_STATUS_SUCCESS_1 = "1";
    protected static final String RESPONSE_STATUS_SUCCESS_2 = "2";
    protected static final String XINYAN_RADAR = "radarScreen";
    protected static final String NEXTMETHOD = "negativeBlack";
    private static final String RESPONSE_STATUS_SUCCESS_200 = "200";
    private static final String RESPONSE_STATUS_NO_DATA = "200011";

    @Override
    protected ServiceResponse callService(ServiceRequest request) {
        Map<String, Object> params = this.generatorParams(request);
        params.putIfAbsent("loanKey",request.getLoanKey());
        params.putIfAbsent("step",request.getStep());
        params.putIfAbsent("systemEventCode",request.getSystemEventCode());
        params.putIfAbsent("systemEventName",request.getSystemEventName());
        params.putIfAbsent("outApiCode",request.getServiceCode());
        params.putIfAbsent("outApiName",request.getServiceName());
        ServiceResponse response = new ServiceResponse();
        response.setRequestId(request.getRequestId());
        String requestBody = JacksonUtil.toJson(params);
        LoggerProxy.info("callServiceParams", LOGGER, "before call service, serviceCode={},params={}", request.getServiceCode(), requestBody);
        String result = SyncHTTPRemoteAPI.postJson(diService.getServiceUrl(), requestBody, diService.getServiceTimeout().intValue());
        if (StringUtils.isBlank(result)) {
            LoggerProxy.error("serviceResponseError", LOGGER, "service response status error, request={},result={}", JacksonUtil.toJson(request), result);
            response.setRetCode(RetCodeEnum.RESPONSE_EXCEPTION.getValue());
            response.setRetMsg(RetCodeEnum.RESPONSE_EXCEPTION.getRetMsg());
            return response;
        }
        JSONObject resultJson = JSONObject.parseObject(result);
        String status = resultJson.getString("status");
        response.setOriginalStatus(status);
        String nextMethod = params.get("nextMethod").toString();
        if (RESPONSE_STATUS_SUCCESS_200.equals(status) || RESPONSE_STATUS_SUCCESS.equals(status) || (RESPONSE_STATUS_SUCCESS_1.equals(status) && NEXTMETHOD.equals(nextMethod))) {
            response.setRetCode(RetCodeEnum.PROCESSING.getValue());
            response.setRetMsg(RetCodeEnum.PROCESSING.getRetMsg());
            response.setJobId(resultJson.getString("jobId"));
        } else if ((RESPONSE_STATUS_SUCCESS_1.equals(status) && XINYAN_RADAR.equals(nextMethod))
                || (RESPONSE_STATUS_SUCCESS_2.equals(status) && NEXTMETHOD.equals(nextMethod))
                || RESPONSE_STATUS_NO_DATA.equals(status)
        ){
            response.setRetCode(RetCodeEnum.NO_DATA.getValue());
            response.setRetMsg(RetCodeEnum.NO_DATA.getRetMsg());
        } else {
            super.logStatusError(LOGGER,  JacksonUtil.toJson(request), result);
            response.setRetCode(RetCodeEnum.RESPONSE_EXCEPTION.getValue());
            response.setRetMsg(RetCodeEnum.RESPONSE_EXCEPTION.getRetMsg());
        }
        return response;
    }
}
