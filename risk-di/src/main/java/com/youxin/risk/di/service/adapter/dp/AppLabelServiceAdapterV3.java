package com.youxin.risk.di.service.adapter.dp;

import cn.hutool.core.util.ReUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.youxin.risk.commons.adapter.di.ServiceAdapter;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.adapter.di.ServiceResponse;
import com.youxin.risk.commons.constants.DiInfluxType;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.dao.datacenter.DcAppStockMapper;
import com.youxin.risk.commons.model.datacenter.DcAppStock;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.*;
import com.youxin.risk.di.service.impl.DcAppStockCacheServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.youxin.risk.commons.constants.DiConstant.UPLOAD_TIME;
import static com.youxin.risk.commons.utils.DateUtil.TIME_MILLIS;
import static com.youxin.risk.commons.utils.DateUtil.parsePlus;

/**
 * @Desc APP特征V3:https://www.tapd.cn/48583176/prong/stories/view/1148583176001014393
 * @Auth linchongbin
 * @Date 2022/11/03 13:17
 */
@Deprecated
public class AppLabelServiceAdapterV3 extends ServiceAdapter {

    private static final Logger LOGGER = LoggerFactory.getLogger(AppLabelServiceAdapterV3.class);

    private static final Integer DEVAULT_0 = 0;
    // 黑名单类型
    private static final Integer STOCK_TYPE_BLACK = 1;
    // 灰名单类型
    private static final Integer STOCK_TYPE_GREY = 2;
    // app分类
    private static final Integer DEFINE_TYPE = 3;

    // 安装黑APP的个数
    private static final String APP_NUM_BLACK = "appNumBlack";
    // 安装灰APP的个数
    private static final String APP_NUM_GREY = "appNumGrey";
    // 非银借贷类APP的安装个数
    private static final String APP_NUM_LOAN_NONBANK = "appNumLoanNonbank";
    // 等级为1的非银借贷类APP的安装个数
    private static final String APP_NUM_LOAN_NONBANK_RANK1 = "appNumLoanNonbankRank1";
    // 等级为2的非银借贷类APP的安装个数
    private static final String APP_NUM_LOAN_NONBANK_RANK2 = "appNumLoanNonbankRank2";
    // 等级为3的非银借贷类APP的安装个数
    private static final String APP_NUM_LOAN_NONBANK_RANK3 = "appNumLoanNonbankRank3";
    // 等级为1和2的非银借贷类APP的安装个数
    private static final String APP_NUM_LOAN_NONBANK_RANK_IN_1_2 = "appNumLoanNonbankRankIn12";
    // 等级为2和3的非银借贷类APP的安装个数
    private static final String APP_NUM_LOAN_NONBANK_RANK_IN_2_3 = "appNumLoanNonbankRankIn23";
    // 家长亲子类APP的安装个数
    private static final String APP_NUM_KID = "appNumKid";
    // 学习提升类APP的安装个数
    private static final String APP_NUM_EDU = "appNumEdu";
    // 稳定收入类APP的安装个数
    private static final String APP_NUM_INC = "appNumInc";
    // 优质行职业APP的安装个数
    private static final String APP_NUM_PRO = "appNumPro";
    // 高端类APP的安装个数
    private static final String APP_NUM_SUPERIOR = "appNumSuperior";
    // 车主类APP的安装个数
    private static final String APP_NUM_CAR = "appNumCar";
    // 房主类APP的安装个数
    private static final String APP_NUM_HOME = "appNumHome";

    private static final List<String> FEATURE_KEY = Lists.newArrayList(APP_NUM_BLACK, APP_NUM_GREY, APP_NUM_LOAN_NONBANK, APP_NUM_LOAN_NONBANK_RANK1,
            APP_NUM_LOAN_NONBANK_RANK2, APP_NUM_LOAN_NONBANK_RANK3, APP_NUM_LOAN_NONBANK_RANK_IN_1_2, APP_NUM_LOAN_NONBANK_RANK_IN_2_3,
            APP_NUM_KID, APP_NUM_EDU, APP_NUM_INC, APP_NUM_PRO, APP_NUM_SUPERIOR, APP_NUM_CAR, APP_NUM_HOME);

    private static final String ALL = "All";
    private static final String TODAY = "Today";
    private static final String IN_1_DAYS = "In1Days";
    private static final String IN_7_DAYS = "In7Days";
    private static final String IN_30_DAYS = "In30Days";
    private static final String IN_90_DAYS = "In90Days";
    private static final String IN_120_DAYS = "In120Days";
    private static final String IN_180_DAYS = "In180Days";
    private static final List<String> postfixList = Lists.newArrayList(ALL, TODAY, IN_1_DAYS, IN_7_DAYS, IN_30_DAYS, IN_90_DAYS, IN_120_DAYS, IN_180_DAYS);

    private DcAppStockMapper dcAppStockMapper = SpringContext.getBean(DcAppStockMapper.class);

    ThreadPoolExecutor threadPool = new ThreadPoolExecutor(10, 20, 30, TimeUnit.SECONDS,
            new ArrayBlockingQueue<Runnable>(100), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());

    @Override
    protected ServiceResponse callService(ServiceRequest request) {
        ServiceResponse response = new ServiceResponse();
        String url = generatorUrl(request);
        Map<String, Object> params = this.generatorParams(request);
        response.setRequestId(request.getRequestId());
        String requestBody = JacksonUtil.toJson(params);
        LoggerProxy.info("AppLabelServiceAdapterV3", LOGGER, "before call service, serviceCode={},params={}", request.getServiceCode(), requestBody);
        // 获取用户的最新的plist:https://babel.we.cn/babel/v1/record/user/HFQ_UPLOAD_PLIST_RECORD?userkey=3caf76194a7ba951a8881a7ee116db9b&systemid=HAO_HUAN
        String result = SyncHTTPRemoteAPI.get(url, diService.getServiceTimeout().intValue());
        if (org.apache.commons.lang.StringUtils.isBlank(result)) {
            LoggerProxy.error("AppLabelServiceAdapterV3", LOGGER, "service response status error, request={},result={}", JacksonUtil.toJson(request), result);
            response.setRetCode(RetCodeEnum.RESPONSE_EXCEPTION.getValue());
            response.setRetMsg(RetCodeEnum.RESPONSE_EXCEPTION.getRetMsg());
            return response;
        }
        JSONObject dataResult = JSON.parseObject(result);
        JSONArray records = dataResult.getJSONArray("records");
        if (records == null || records.isEmpty()) {
            LoggerProxy.warn("AppLabelServiceAdapterV3", LOGGER, "records is empty, request={},result={}", JacksonUtil.toJson(request), result);
            response.setRetCode(RetCodeEnum.NO_DATA.getValue());
            response.setRetMsg(RetCodeEnum.NO_DATA.getRetMsg());
            return response;
        }
        JSONObject data = records.getJSONObject(0).getJSONObject("data");
        if (data == null || data.getJSONArray("appList") == null) {
            LoggerProxy.warn("AppLabelServiceAdapterV3", LOGGER, "data is empty, request={},result={}", JacksonUtil.toJson(request), result);
            response.setRetCode(RetCodeEnum.NO_DATA.getValue());
            response.setRetMsg(RetCodeEnum.NO_DATA.getRetMsg());
            return response;
        }
        JSONArray appList = data.getJSONArray("appList");
        // 获取当前日期
        Date nowDate = DateUtil.getCurrentDateZero();
        Date date1DayAgo = DateUtil.subtractDay(nowDate, 1);
        Date date7DayAgo = DateUtil.subtractDay(nowDate, 7);
        Date date1MonAgo = DateUtil.subtractDay(nowDate, 30);
        Date date3MonAgo = DateUtil.subtractDay(nowDate, 90);
        Date date4MonAgo = DateUtil.subtractDay(nowDate, 120);
        Date date6MonAgo = DateUtil.subtractDay(nowDate, 180);
        List<JSONObject> listAll = Lists.newArrayList();
        List<JSONObject> listToday = Lists.newArrayList();
        List<JSONObject> listIn1Day = Lists.newArrayList();
        List<JSONObject> listIn7Day = Lists.newArrayList();
        List<JSONObject> listIn1Mon = Lists.newArrayList();
        List<JSONObject> listIn3Mon = Lists.newArrayList();
        List<JSONObject> listIn4Mon = Lists.newArrayList();
        List<JSONObject> listIn6Mon = Lists.newArrayList();
        for (int i = 0; i < appList.size(); i++) {
            JSONObject app = appList.getJSONObject(i);
            if (Objects.isNull(app)) {
                continue;
            }
            listAll.add(app);
            String installTime = app.getString("lastOpenTime");
            if (StringUtils.isBlank(installTime)) {
                continue;
            }
            Date installDate = DateUtil.parseDateWithTimeZone(installTime);
            if (installDate == null) {
                continue;
            }
            if (installDate.getTime() >= nowDate.getTime()) {
                listToday.add(app);
            }
            if (installDate.getTime() >= date1DayAgo.getTime()) {
                listIn1Day.add(app);
            }
            if (installDate.getTime() >= date7DayAgo.getTime()) {
                listIn7Day.add(app);
            }
            if (installDate.getTime() >= date1MonAgo.getTime()) {
                listIn1Mon.add(app);
            }
            if (installDate.getTime() >= date3MonAgo.getTime()) {
                listIn3Mon.add(app);
            }
            if (installDate.getTime() >= date4MonAgo.getTime()) {
                listIn4Mon.add(app);
            }
            if (installDate.getTime() >= date6MonAgo.getTime()) {
                listIn6Mon.add(app);
            }
        }
        Map<String, List<JSONObject>> listMap = new LinkedHashMap<>();
        listMap.put(ALL, listAll);
        listMap.put(TODAY, listToday);
        listMap.put(IN_1_DAYS, listIn1Day);
        listMap.put(IN_7_DAYS, listIn7Day);
        listMap.put(IN_30_DAYS, listIn1Mon);
        listMap.put(IN_90_DAYS, listIn3Mon);
        listMap.put(IN_120_DAYS, listIn4Mon);
        listMap.put(IN_180_DAYS, listIn6Mon);

        // 构造默认值
        Map<String, Object> resultMap = new ConcurrentHashMap<>();
        postfixList.forEach(postfix -> {
            getDefaultMap(resultMap, postfix);
        });
        // 构造特征值
        Stopwatch stopwatch = Stopwatch.createStarted();
        getResultMap(listMap, resultMap);
        LoggerProxy.info("AppLabelServiceAdapterV3", LOGGER, "getResultMap info,  cost:{}, request: {}", stopwatch.stop().elapsed(TimeUnit.MILLISECONDS), JacksonUtil.toJson(request));
        response.setRetCode(RetCodeEnum.SUCCESS.getValue());
        response.setRetMsg(RetCodeEnum.SUCCESS.getRetMsg());
        response.setResult(resultMap);
        response.setData(JSON.toJSONString(resultMap));
        // 回传采集时间
        setTransMap(data, response, DiInfluxType.PLIST);

        return response;
    }

    /**
     * 获取特征map
     *
     * @param appMap
     * @param resultMap
     */
    private void getResultMap(Map<String, List<JSONObject>> appMap, Map<String, Object> resultMap) {
        // 获取app分类库
        if (DcAppStockCacheServiceImpl.appStockMap == null) {
            // 获取app分类库
            Stopwatch stopwatch = Stopwatch.createStarted();
            DcAppStockCacheServiceImpl.appStockMap = dcAppStockMapper.selectAll().parallelStream().collect(Collectors.groupingBy(DcAppStock::getStockType));
            LoggerProxy.info("AppLabelServiceAdapterV3", LOGGER, "get appStock, cost:{}", stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
        }
        Map<Integer, List<DcAppStock>> appStockMap = DcAppStockCacheServiceImpl.appStockMap;
        CountDownLatch countDownLatch = new CountDownLatch(appMap.size());
        appMap.forEach((postfix, appList) -> {
            threadPool.execute(() -> {
                Stopwatch stopwatch = Stopwatch.createStarted();
                Map<String, Set<String>> setMap = new HashMap<>();
                try {
                    if (appList.isEmpty()) {
                        return;
                    }
                    FEATURE_KEY.forEach(key -> {
                        Set<String> set = new HashSet<>();
                        setMap.put(key, set);
                    });
                    appList.forEach(app -> {
                        String appName = app.getString("appName");
                        String packageName = app.getString("packageName");
                        if (StringUtils.isBlank(packageName)) {
                            return;
                        }
                        // 安装黑APP的个数:
                        // 1、package_name命中【APP加黑】
                        // 2、符合上述条件的package_name去重计数
                        if (appStockMap.get(STOCK_TYPE_BLACK).parallelStream().anyMatch(black ->
                                StringUtils.equals(black.getPackageName(), packageName)
                        )) {
                            setMap.get(APP_NUM_BLACK).add(packageName);
                            return;
                        }

                        // 安装灰APP的个数:
                        // 1、package_name命中【APP加灰】
                        // 2、符合上述条件的package_name去重计数
                        if (appStockMap.get(STOCK_TYPE_GREY).parallelStream().anyMatch(black ->
                                StringUtils.equals(black.getPackageName(), packageName)
                        )) {
                            setMap.get(APP_NUM_GREY).add(packageName);
                            return;
                        }

                        // 非银借贷类APP的安装个数:
                        // 1、package_name根据【APP枚举定义】得到app_type
                        // 2、app_type包含：‘非银借贷类’的package_name
                        // 3、去重计数
                        if (appStockMap.get(DEFINE_TYPE).parallelStream().anyMatch(black ->
                                StringUtils.equals(black.getPackageName(), packageName)
                                        && StringUtils.equals("非银借贷类", black.getAppType())
                        )) {
                            setMap.get(APP_NUM_LOAN_NONBANK).add(packageName);
                        }

                        // 等级1和2非银借贷类APP的安装个数:
                        // 1、package_name根据【APP枚举定义】得到app_type
                        // 2、app_type包含：‘非银借贷类’的package_name
                        // 3、去重计数
                        if (appStockMap.get(DEFINE_TYPE).parallelStream().anyMatch(black ->
                                StringUtils.equals(black.getPackageName(), packageName)
                                        && StringUtils.equals("非银借贷类", black.getAppType())
                                        && Lists.newArrayList("1", "2").contains(black.getAppRank())
                        )) {
                            setMap.get(APP_NUM_LOAN_NONBANK_RANK_IN_1_2).add(packageName);
                        }

                        // 等级2和3非银借贷类APP的安装个数:
                        // 1、package_name根据【APP枚举定义】得到app_type
                        // 2、app_type包含：‘非银借贷类’的package_name
                        // 3、去重计数
                        if (appStockMap.get(DEFINE_TYPE).parallelStream().anyMatch(black ->
                                StringUtils.equals(black.getPackageName(), packageName)
                                        && StringUtils.equals("非银借贷类", black.getAppType())
                                        && Lists.newArrayList("2", "3").contains(black.getAppRank())
                        )) {
                            setMap.get(APP_NUM_LOAN_NONBANK_RANK_IN_2_3).add(packageName);
                        }

                        // 等级为1的非银借贷类APP的安装个数:
                        // 1、package_name根据【APP枚举定义】得到app_type和app_rank
                        // 2、取app_type包含‘非银借贷类’ 且 app_rank=1 3、符合上述条件的package_name去重计数
                        if (appStockMap.get(DEFINE_TYPE).parallelStream().anyMatch(black ->
                                StringUtils.equals(black.getPackageName(), packageName)
                                        && StringUtils.equals("非银借贷类", black.getAppType()) && StringUtils.equals("1", black.getAppRank())
                        )) {
                            setMap.get(APP_NUM_LOAN_NONBANK_RANK1).add(packageName);
                            return;
                        }

                        // 等级为2的非银借贷类APP的安装个数:
                        // 1、package_name根据【APP枚举定义】得到app_type和app_rank
                        // 2、取app_type包含‘非银借贷类’ 且 app_rank=2 3、符合上述条件的package_name去重计数
                        if (appStockMap.get(DEFINE_TYPE).parallelStream().anyMatch(black ->
                                StringUtils.equals(black.getPackageName(), packageName)
                                        && StringUtils.equals("2", black.getAppRank())
                                        && StringUtils.equals("非银借贷类", black.getAppType())
                        )) {
                            setMap.get(APP_NUM_LOAN_NONBANK_RANK2).add(packageName);
                            return;
                        }

                        // 等级为3的非银借贷类APP的安装个数:
                        // 1、package_name根据【APP枚举定义】得到app_type和app_rank
                        // 2、取app_type包含‘非银借贷类’ 且 app_rank=3 3、符合上述条件的package_name去重计数
                        if (appStockMap.get(DEFINE_TYPE).parallelStream().anyMatch(black ->
                                StringUtils.equals(black.getPackageName(), packageName)
                                        && StringUtils.equals("3", black.getAppRank())
                                        && StringUtils.equals("非银借贷类", black.getAppType())
                        )) {
                            setMap.get(APP_NUM_LOAN_NONBANK_RANK3).add(packageName);
                            return;
                        }

                        // 高端类APP的安装个数:
                        // 1、package_name根据【APP枚举定义】得到app_type和app_rank
                        // 2、取app_type不包含'非银借贷类' 且 app_rank = 1的package_name 3、去重计数
                        if (appStockMap.get(DEFINE_TYPE).parallelStream().anyMatch(black ->
                                StringUtils.equals(black.getPackageName(), packageName)
                                        && StringUtils.equals("1", black.getAppRank())
                                        && !StringUtils.contains(black.getAppType(), "非银借贷类")
                        )) {
                            setMap.get(APP_NUM_SUPERIOR).add(packageName);
                        }

                        // 家长亲子类
                        if (appStockMap.get(DEFINE_TYPE).parallelStream().anyMatch(black ->
                                StringUtils.equals(black.getPackageName(), packageName)
                                        && StringUtils.contains(black.getAppType(), "家长亲子类")
                        )) {
                            setMap.get(APP_NUM_KID).add(packageName);
                            return;
                        }

                        // 学习提升类
                        if (appStockMap.get(DEFINE_TYPE).parallelStream().anyMatch(black ->
                                StringUtils.equals(black.getPackageName(), packageName)
                                        && StringUtils.contains(black.getAppType(), "学习提升类")
                        )) {
                            setMap.get(APP_NUM_EDU).add(packageName);
                            return;
                        }

                        // 稳定收入类
                        if (appStockMap.get(DEFINE_TYPE).parallelStream().anyMatch(black ->
                                StringUtils.equals(black.getPackageName(), packageName)
                                        && Lists.newArrayList("政务类_公积金", "政务类_个税", "特定行职业_国企职工").contains(black.getAppType())
                        )) {
                            setMap.get(APP_NUM_INC).add(packageName);
                            return;
                        }

                        // 优质行职业类
                        if (appStockMap.get(DEFINE_TYPE).parallelStream().anyMatch(black ->
                                StringUtils.equals(black.getPackageName(), packageName)
                                        && Lists.newArrayList("特定行职业_党员", "特定行职业_公务员", "特定行职业_学校教师").contains(black.getAppType())
                        )) {
                            setMap.get(APP_NUM_PRO).add(packageName);
                            return;
                        }

                        // 车主类APP
                        if (appStockMap.get(DEFINE_TYPE).parallelStream().anyMatch(black ->
                                StringUtils.equals(black.getPackageName(), packageName)
                                        && Lists.newArrayList("稳定资产类_车主").contains(black.getAppType())
                        )) {
                            setMap.get(APP_NUM_CAR).add(packageName);
                            return;
                        }

                        // 房主类APP
                        if (appStockMap.get(DEFINE_TYPE).parallelStream().anyMatch(black ->
                                StringUtils.equals(black.getPackageName(), packageName)
                                        && Lists.newArrayList("稳定资产类_房主").contains(black.getAppType())
                        )) {
                            setMap.get(APP_NUM_HOME).add(packageName);
                            return;
                        }

                        // 若【APP枚举定义】无命中，再用app_name 根据【关键字定义】得到app_type
                        if (appStockMap.get(DEFINE_TYPE).parallelStream().noneMatch(black ->
                                StringUtils.equals(black.getPackageName(), packageName)
                        )) {
                            // 家长亲子类：app_name rlike '少儿|宝宝|儿童|幼儿|孩子|识字|认字|口算|自然拼读|中学|家长|家校|亲子|早教|启蒙|儿歌|童话|绘本' and app_name not rlike '教师端|教师版|老师版|老师端'
                            if (ReUtil.contains("少儿|宝宝|儿童|幼儿|孩子|识字|认字|口算|自然拼读|中学|家长|家校|亲子|早教|启蒙|儿歌|童话|绘本", appName) &&
                                    !ReUtil.contains("教师端|教师版|老师版|老师端", appName)) {
                                setMap.get(APP_NUM_KID).add(packageName);
                                // 学习提升类：app_name rlike '网校|云课堂|资格|执业|考研|法考|医考|教考|公考|中公|考公务员|公务员考试|会计|药师|建造师|经济师|师考|职教' and app_name not rlike "养老|校园|普通话"
                            } else if (ReUtil.contains("网校|云课堂|资格|执业|考研|法考|医考|教考|公考|中公|考公务员|公务员考试|会计|药师|建造师|经济师|师考|职教", appName) &&
                                    !ReUtil.contains("养老|校园|普通话", appName)) {
                                setMap.get(APP_NUM_EDU).add(packageName);
                                // 稳定收入类：app_name rlike '公积金|个税|工会'
                            } else if (ReUtil.contains("公积金|个税|工会", appName)) {
                                setMap.get(APP_NUM_INC).add(packageName);
                                // 优质行职业：app_name rlike '党建|党员|公务'
                            } else if (ReUtil.contains("党建|党员|公务", appName)) {
                                setMap.get(APP_NUM_PRO).add(packageName);
                            }
                        }
                    });
                    LoggerProxy.info("AppLabelServiceAdapterV3", LOGGER, "get data, postfix: {}, cost:{}", postfix, stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
                    FEATURE_KEY.forEach(key -> {
                        String featureKey = String.format("%s%s", key, postfix);
                        resultMap.put(featureKey, setMap.get(key).size());
                    });
                } catch (Exception ex) {
                    LoggerProxy.error("AppLabelServiceAdapterV3", LOGGER, "get data error, postfix: {}", postfix);
                } finally {
                    countDownLatch.countDown();
                }
            });
        });
        try {
            countDownLatch.await();
        } catch (Exception ex) {
            LoggerProxy.error("AppLabelServiceAdapterV3", LOGGER, "await error", ex);
        }
    }

    /**
     * 默认值
     *
     * @param resultMap
     * @param postfix
     */
    private void getDefaultMap(Map<String, Object> resultMap, String postfix) {
        FEATURE_KEY.forEach(key -> {
            String mapKey = String.format("%s%s", key, postfix);
            resultMap.put(mapKey, DEVAULT_0);
        });
    }

    @Override
    protected boolean isInUploadTime(ServiceRequest request, ServiceResponse response) {
        try {
            if (!UploadTimeJudgeUtil.isNeedLatestUploadData(request)) {
                return true;
            }
            Date uploadTime = parsePlus(String.valueOf(request.getParams().get(UPLOAD_TIME)), TIME_MILLIS);
            if (response.getTransMap() != null) {
                final Long httpTimeStamp = (Long) response.getTransMap().get(DiInfluxType.PLIST.getCode());
                if (Objects.isNull(httpTimeStamp)) {
                    return false;
                }
                Date latestUploadTime = new Date(httpTimeStamp);
                return uploadTime.compareTo(latestUploadTime) != 1;
            }
            return false;
        } catch (Exception e) {
            LoggerProxy.error("AppLabelServiceAdapterV3V2#isInUploadTime", LOGGER, "error {}", e);
        }
        return false;
    }

}
