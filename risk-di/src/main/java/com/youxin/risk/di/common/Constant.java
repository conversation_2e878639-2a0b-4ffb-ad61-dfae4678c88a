package com.youxin.risk.di.common;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * @Desc 常量
 * @Auth linchongbin
 * @Date 2022/4/28 18:25
 */
public class Constant {
    public static final String SELECT_6_M_CHARGE_TOTAL_COUNT = "select6mChargeTotalCount";
    public static final String SELECT_6_M_CHARGE_TOTAL_COUNT_T_1 = "select6mChargeTotalCountT1";
    public static final String SELECT_RRD_3_M_CHARGE_FAIL_COUNT = "selectRrd3mChargeFailCount";
    public static final String SELECT_6_M_CHARGE_SUCCESS_COUNT = "select6mChargeSuccessCount";
    public static final String SELECT_6_M_CHARGE_SUCCESS_COUNT_T_1 = "select6mChargeSuccessCountT1";
    public static final String SELECT_3_M_CHARGE_TOTAL_AMOUNT = "select3mChargeTotalAmount";
    public static final String SELECT_3_M_CHARGE_TOTAL_FAIL_AMOUNT = "select3mChargeTotalFailAmount";
    public static final String SELECT_3_M_CHARGE_TOTAL_AMOUNT_T_1 = "select3mChargeTotalAmountT1";
    public static final String SELECT_3_M_CHARGE_TOTAL_FAIL_AMOUNT_T_1 = "select3mChargeTotalFailAmountT1";
    public static final String SELECT_TOTAL_DEDUCTION_TIMES = "selectTotalDeductionTimes";
    public static final String SELECT_SUCCESS_DEDUCTION_TIMES = "selectSuccessDeductionTimes";
    public static final String SELECT_REPAY_AMOUNT = "selectRepayAmount";
    public static final String SELECT_NO_BALANCE_AMOUNT_MAX = "selectNoBalanceAmountMax";
    public static final String SELECT_PAY_LAUNCH_TYPE_ZERO_AMOUNT_SUM = "selectPayLaunchTypeZeroAmountSum";
    public static final String SELECT_PAY_LAUNCH_TYPE_ONE_AMOUNT_MIN = "selectPayLaunchTypeOneAmountMin";
    public static final String SELECT_PAY_AMOUNT_AVG = "selectPayAmountAvg";
    public static final String SELECT_NO_BALANCE_AMOUNT_AVG = "selectNoBalanceAmountAvg";
    public static final String SELECT_NO_BALANCE_AMOUNT_SUM = "selectNoBalanceAmountSum";
    public static final String SELECT_PAY_LAUNCH_TYPE_ONE_SUCCESS_AMOUNT_SUM = "selectPayLaunchTypeOneSuccessAmountSum";
    public static final String SELECT_SUCCESS_NO_SYSTEM_PAYMENT_AMOUNT_SUM = "selectSuccessNoSystemPaymentAmountSum";
    public static final String SELECT_PAY_LAUNCH_TYPE_HOUR_AVG = "selectPayLaunchTypeHourAvg";
    public static final String GET_REPAY_RATE_IN_WEEKEND = "getRepayRateInWeekend";
    public static final String SELECT_SUCCESS_NO_SYSTEM_PAYMENT_COUNT = "selectSuccessNoSystemPaymentCount";
    public static final String SELECT_TOTAL_NO_SYSTEM_PAYMENT_COUNT = "selectTotalNoSystemPaymentCount";
    public static final String SELECT_SUCCESS_NO_SYSTEM_PAYMENT_AMOUNT_SUM_RATE = "selectSuccessNoSystemPaymentAmountSumRate";
    public static final String SELECT_TOTAL_NO_SYSTEM_PAYMENT_AMOUNT_SUM_RATE = "selectTotalNoSystemPaymentAmountSumRate";
    public static final String SELECT_SUCCESS_REPAY_TIMES = "selectSuccessRepayTimes";
    public static final String SELECT_TOTAL_REPAY_TIMES = "selectTotalRepayTimes";
    public static final String SELECT_PAYMENT_CTN = "selectPaymentCtn";
    public static final String GET_USER_PAY = "getUserPay";

    public static final String CONF_PATH = "conf/conf";
    public static final String DC_LOAN_QUERY_PATH = "/account/loan/getLoanHistoryInfo";
    public static final String DC_MOBILE_AREA = "/mobile/batch/query/area";

    public static final String DC_LOAN_RESPONSE_STATUS_SUCCESS = "000000";
    public static final String DC_LOAN_RESPONSE_STATUS_SUCCESS_040001 = "040001";
    public static final Integer BOUND = 100;
    // 手机号正则匹配
    public static final String MOBILE_PATTERN = "^1([38][0-9]|4[579]|5[0-9]|6[6]|7[0135678]|9[89])\\d{8}$";

    // 神策埋点反欺诈
    public static final List<String> SENSOR_ITEMS = Lists.newArrayList("婚姻状况", "月收入", "亲属关系", "最高学历");

    public static final Integer MAX_WIDTH = 300;
}
