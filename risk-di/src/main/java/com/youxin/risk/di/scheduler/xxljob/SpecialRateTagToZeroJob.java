package com.youxin.risk.di.scheduler.xxljob;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.youxin.risk.commons.constants.SpecialRateTagEnum;
import com.youxin.risk.commons.tools.redis.RetryableJedis;
import com.youxin.risk.commons.xxl.job.XxlJobBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2023/7/25 19:28
 * @desc
 */
@Component
public class SpecialRateTagToZeroJob implements XxlJobBase {
    private final Logger logger = LoggerFactory.getLogger(SpecialRateTagToZeroJob.class);

    @Resource(name = "riskDiNoRdbAofRedis")
    private RetryableJedis riskDiNoRdbAofRedis ;

    @Override
    @XxlJob("specialRateTagToZeroJob")
    public ReturnT<String> execJobHandler(String param) {
        logger.info("specialRateTagToZeroJob start");
        for (SpecialRateTagEnum specialRateTagEnum : SpecialRateTagEnum.values()) {
            riskDiNoRdbAofRedis.del(specialRateTagEnum.name());
        }
        logger.info("specialRateTagToZeroJob end");
        return ReturnT.SUCCESS;
    }
}
