/**
 * Copyright(c) 2011-2017 by YouCredit Inc.
 * All Rights Reserved
 */
package com.ucredit.riskanalysis.data;

import com.google.common.collect.Lists;
import com.ucredit.riskanalysis.common.product.RiskProduct;
import com.youxin.risk.commons.dao.datacenter.DcSubmitRegisterMapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 设备号使用人数特征计算
 *
 * <AUTHOR>
 * @version 创建时间：2017年11月23日-下午5:59:26
 */
@Service
public class DeviceIdUserNumberHhCalculator {

    private static final Logger LOG = LoggerFactory.getLogger(DeviceIdUserNumberHhCalculator.class);


    public static final String ANDROID_UNKNOW = "Unknown";

    public static final String IOS_CLOSED = "00000000-0000-0000-0000-000000000000";

    public static final String MEIZU_CLOSED = "00000000000000000000000000000000";

    @Autowired
    private DcSubmitRegisterMapper dcSubmitRegisterMapper;

    public Object getValue(String idfa, String imei) {
        try {

            if (!StringUtils.isBlank(idfa)) {//IOS
                if (Lists.newArrayList(IOS_CLOSED, MEIZU_CLOSED).contains(idfa)) {
                    return null;
                }
                return dcSubmitRegisterMapper.getUserCountByIdfa(idfa, RiskProduct.HAO_HUAN.name());
            } else {//Android
                if (ANDROID_UNKNOW.equals(imei) || StringUtils.isBlank(imei)) {
                    return null;
                }

                return dcSubmitRegisterMapper.getUserCountByImei(imei, RiskProduct.HAO_HUAN.name());
            }

        } catch (Exception e) {
            LOG.error("", e);
        }

        return null;
    }

}
