package com.ucredit.riskanalysis.submit.vo;

import com.youxin.risk.commons.vo.datavo.SubmitCommonVo;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class SubmitRegisterVo extends SubmitCommonVo {
	private String mobile;
	private String tongdunFingerprint;
	private String imei;
	private String idfa;

	public String getImei() {
		return imei;
	}

	public void setImei(String imei) {
		this.imei = imei;
	}

	public String getIdfa() {
		return idfa;
	}

	public void setIdfa(String idfa) {
		this.idfa = idfa;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getTongdunFingerprint() {
		return tongdunFingerprint;
	}

	public void setTongdunFingerprint(String tongdunFingerprint) {
		this.tongdunFingerprint = tongdunFingerprint;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this,
				ToStringStyle.SHORT_PREFIX_STYLE);
	}
}
