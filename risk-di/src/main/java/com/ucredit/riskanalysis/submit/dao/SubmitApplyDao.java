package com.ucredit.riskanalysis.submit.dao;

import com.paydayloan.verify.dao.AbstractBaseDao;
import com.ucredit.riskanalysis.common.product.RiskProduct;
import com.ucredit.riskanalysis.common.submit.ApplyType;
import com.ucredit.riskanalysis.submit.model.SubmitApply;
import org.hibernate.Query;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;

@Repository
public class SubmitApplyDao extends AbstractBaseDao<SubmitApply> {

	@Override
	protected Class<SubmitApply> getEntityClass() {
		return SubmitApply.class;
	}

	@SuppressWarnings("unchecked")
	@Deprecated
	public List<SubmitApply> findLastApplyByUser(String sourceSystem,
                                                 String userKey) {
		String hql = "from SubmitApply t1 where sourceSystem = :sourceSystem and userKey = :userKey and not exists "
				+ "(select id from SubmitApply t2 where t2.sourceSystem = t1.sourceSystem and t2.userKey = t1.userKey "
				+ "and t2.applyType = t1.applyType and t2.id > t1.id)";
		Query query = this.createQuery(hql).setParameter("sourceSystem", sourceSystem)
				.setParameter("userKey", userKey);
		return query.list();
	}

	/**
	 * 查找好还关联申请
	 *
	 * @param deviceId
	 * @param applyType
	 * @return
	 */
	@Deprecated
	public List<SubmitApply> getApplyByDeviceIdAndType(String deviceId,
                                                       ApplyType applyType) {

		String hql = "from SubmitApply where deviceId = :deviceId AND sourceSystem IN (:sourceList)  and applyType = :applyType group by userKey";
		return this.createQuery(hql)
				.setParameter("deviceId", deviceId)
				.setParameter("applyType", applyType.name())
				.setParameterList(
						"sourceList",
						Arrays.asList(RiskProduct.PAY_DAY_LOAN.name(),
								RiskProduct.RONG_360.name(),
								RiskProduct.HAO_HUAN.name())).list();
	}


}
