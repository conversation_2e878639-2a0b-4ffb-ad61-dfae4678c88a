package com.ucredit.riskanalysis.data;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class RepayTimesCalculatorTest {

    @Autowired
    private RepayTimesCalculator repayTimesCalculator;

    @Test
    public void getPaymentCtn(){
        Object paymentCtn = repayTimesCalculator.getPaymentCtn(false, "", 30);
        System.out.println(paymentCtn);
    }

    @Test
    public void repayTimes(){
        String repayTimes = repayTimesCalculator.getRepayTimes(false, "", 30);
        System.out.println(repayTimes);
    }

}
