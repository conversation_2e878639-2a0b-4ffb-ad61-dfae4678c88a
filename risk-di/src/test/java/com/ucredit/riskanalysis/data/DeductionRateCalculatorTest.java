package com.ucredit.riskanalysis.data;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class DeductionRateCalculatorTest {

    @Autowired
    private DeductionRateCalculator deductionRateCalculator;

    @Test
    public void test(){
        String value = deductionRateCalculator.getValue(false, "hh_a43e311e-8d63-4594-8445-c364c77d5eef", 30);
        System.out.println(value);
    }
}
