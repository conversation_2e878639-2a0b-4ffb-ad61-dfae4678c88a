package com.ucredit.riskanalysis.data;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class RepayDateCalculatorTest {

    @Autowired
    private RepayDateCalculator repayDateCalculator;


    /**
     * 近 {days}天 主动还款时间 avg
     */
    @Test
    public void getPayLaunchTypeHourAvg(){
        Object payLaunchTypeHourAvg = repayDateCalculator.getPayLaunchTypeHourAvg(false, "hh_a43e311e-8d63-4594-8445-c364c77d5eef", 30);
        System.out.println(payLaunchTypeHourAvg);
    }
}
