package com.ucredit.riskanalysis.data;

import com.alibaba.fastjson.JSONObject;
import com.ucredit.riskanalysis.data.vo.LendRequestInfo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class RegisterPhoneLendRequestInfoCalculatorTest {

    @Autowired
    private RegisterPhoneLendRequestInfoCalculator calculator;
    @Test
    public void getValue() {
        LendRequestInfo v = calculator.getValue("hh_f98c6aa2-e3e0-497c-b8ae-01ed9bb30a92", "18519203236");
        System.out.println();
        System.out.println("####" + JSONObject.toJSONString(v));
    }
}