package com.youxin.risk.gateway.test;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Map;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class DITest {

    private static final Logger LOGGER = LoggerFactory.getLogger(DITest.class);
    @Resource
    private Map<String, String> sourceSystemMap;
    @Before
    public void setup() {
    }

    @Test
    public void start() {
        try {
//            ApplicationContext ac = new ClassPathXmlApplicationContext("/Users/<USER>/work/renrendai/codestest/risk/risk-di/src/test/resources/spring/spring-config.xml");
//            System.out.println(ac.getBean("dataSourceServiceImpl"));
            String s = sourceSystemMap.get("risk_process_engine_batch");
            System.out.println();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

}

