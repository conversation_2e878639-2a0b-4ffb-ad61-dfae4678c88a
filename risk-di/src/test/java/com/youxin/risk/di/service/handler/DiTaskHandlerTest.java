package com.youxin.risk.di.service.handler;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.constants.DiConstant;
import com.youxin.risk.commons.service.dp.BabelDataService;
import com.youxin.risk.di.model.DpCreditResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class DiTaskHandlerTest {
    @Autowired
    private BabelDataService babelDataService;

    @Test
    public void test() {
        testResultIsBlank();
        testResultIs2();
        testResultIs1();
    }

    public void testResultIsBlank() {
        String taskType = DiConstant.FUNDS_CREDIT_RECORD_TYPE;
        String jobId = "966e1b86-b3d0-47e1-8f59-a782ac411c0f";
        String dataFromDp = babelDataService.getDataFromDpByJobId("HAO_HUAN", taskType,
                jobId);
        // 征信结果判断是否查询成功
        if (DiConstant.FUNDS_CREDIT_RECORD_TYPE.equals(taskType)) {
            DpCreditResult dpCreditResult = JSONObject.parseObject(dataFromDp, DpCreditResult.class);
            if (dpCreditResult == null || dpCreditResult.getData() == null) {
                System.out.println("******************null********************");;
                return;
            }
            //如果查征结果不为1，即为失败
            if (DiConstant.CREDIT_CALLBACK_RESULT_1 != dpCreditResult.getData().getResult()) {
                System.out.println("******************null********************");
                return;
            }
        }
        System.out.println("******************" + dataFromDp);
    }


    public void testResultIs2() {
        String taskType = DiConstant.FUNDS_CREDIT_RECORD_TYPE;
        // 无可用征信机构
        String jobId = "966e1b86-b3d0-47e1-8f59-a782ac411c0e";
        String dataFromDp = babelDataService.getDataFromDpByJobId("HAO_HUAN", taskType,
                jobId);
        // 征信结果判断是否查询成功
        if (DiConstant.FUNDS_CREDIT_RECORD_TYPE.equals(taskType)) {
            DpCreditResult dpCreditResult = JSONObject.parseObject(dataFromDp, DpCreditResult.class);
            if (dpCreditResult == null || dpCreditResult.getData() == null) {
                System.out.println("******************null********************");;
                return;
            }
            //如果查征结果不为1，即为失败
            if (DiConstant.CREDIT_CALLBACK_RESULT_1 != dpCreditResult.getData().getResult()) {
                System.out.println("******************null********************");
                return;
            }
        }
        System.out.println("******************" + dataFromDp);
    }

    public void testResultIs1() {
        String taskType = DiConstant.FUNDS_CREDIT_RECORD_TYPE;
        String jobId = "7e6bd671-3bef-4304-a218-a08879f66dca";
        String dataFromDp = babelDataService.getDataFromDpByJobId("HAO_HUAN", taskType,
                jobId);
        // 征信结果判断是否查询成功
        if (DiConstant.FUNDS_CREDIT_RECORD_TYPE.equals(taskType)) {
            DpCreditResult dpCreditResult = JSONObject.parseObject(dataFromDp, DpCreditResult.class);
            if (dpCreditResult == null || dpCreditResult.getData() == null) {
                System.out.println("******************null********************");;
                return;
            }
            //如果查征结果不为1，即为失败
            if (DiConstant.CREDIT_CALLBACK_RESULT_1 != dpCreditResult.getData().getResult()) {
                System.out.println("******************null********************");
                return;
            }
        }
        System.out.println("******************" + dataFromDp);
    }
}