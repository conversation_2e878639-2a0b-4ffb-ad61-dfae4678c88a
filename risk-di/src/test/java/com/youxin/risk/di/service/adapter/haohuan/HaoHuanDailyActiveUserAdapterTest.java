package com.youxin.risk.di.service.adapter.haohuan;

import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.model.DiService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: 调用业务接口的统一处理类的测试类
 * @author: juxiang
 * @create: 2021-07-07 10:33
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class HaoHuanDailyActiveUserAdapterTest {
    @Test
    public void callService() {
        HaoHuanDailyActiveUserAdapter hhBusinessSyncPostServiceAdapter = new HaoHuanDailyActiveUserAdapter();
        hhBusinessSyncPostServiceAdapter.setDiService(prepareDiservice());
        hhBusinessSyncPostServiceAdapter.callService(prepareServiceRequest());
    }

    private DiService prepareDiservice() {
        DiService diService = new DiService();
        diService.setServiceCode("getUserGpsOrIpInfoServiceAdapter");
        diService.setServiceClass("com.youxin.risk.di.service.adapter.hhbusiness.HhBusinessSyncPostServiceAdapter");
        diService.setServiceName("抽取用户信息");
        diService.setServiceMode("SYNC");
        diService.setServiceUrl("http://hfq-riskcenter.weicai.com.cn/allInterests");
        diService.setServiceTimeout(3000L);
        return diService;
    }

    private ServiceRequest prepareServiceRequest() {
        ServiceRequest serviceRequest = new ServiceRequest();
        serviceRequest.setUserKey("66b0d2a53eae5d6af29b43eae4bfa4711");
        Map map=new HashMap();
        map.put("userKey","66b0d2a53eae5d6af29b43eae4bfa4711");
        serviceRequest.setParams(map);
        return serviceRequest;
    }
}
