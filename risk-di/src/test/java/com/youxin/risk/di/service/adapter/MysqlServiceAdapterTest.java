package com.youxin.risk.di.service.adapter;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.adapter.di.ServiceResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.HashMap;
import java.util.Map;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class MysqlServiceAdapterTest {

    @Test
    public void test() {
        Map<String, Object> action = new HashMap<>();
        action.put("repaykey_b1d_amt", 1);
        action.put("repaykey_b0d_cnt", 1);
        action.put("repaykey_b3d_cnt", 1);
        action.put("repaykey_pay7_cnt", 1);
        action.put("repaykey_0d_cnt", 1);
        action.put("repaykey_weekend_cnt", 1);
        action.put("repaykey_a3s_cnt", 1);
        action.put("repaykey_payed_cnt", 1);
        action.put("repaykey_amt", 1);
        action.put("b_180_have_pay_principal_avg", 1);
        action.put("b_180_prepay_period_cnt_avg", 1);
        action.put("b_90_ealist_pay_day_min_cnt_rate", 1);
        action.put("b_90_have_pay_period_cnt_avg", 1);
        action.put("b_90_have_pay_principal_max", 1);
        action.put("b_90_prepay_principal_sum_avg", 1);
        action.put("b_90_prepay_principal_sum_sum_rate", 1);
        action.put("payoff_time_9_14_cnt_b_180_rate", 1);
        action.put("b_180_account_max_dpd_prepay", 1);
        action.put("payActiveAmtSum3M", 1);
        action.put("payActiveSuccssCnt3M", 1);
        action.put("three_month_success_nosystem_payment_cnt_rate", 1);
        action.put("b_30_account_max_dpd_max", 1);
        action.put("payoff_deal_cnt", 1);
        action.put("payoff_time_17_cnt_b_180_rate", 1);
        action.put("d180_pay_launch_type_1_weekday_cnt_rate", 1);
        action.put("D180_pay_launch_type_hour_avg", 1);
        action.put("d30_no_balance_amount_max", 1);
        action.put("d30_pay_launch_type_0_amount_sum", 1);
        action.put("d30_pay_launch_type_1_amount_min", 1);
        action.put("d60_payment_amount_avg", 1);
        action.put("d90_no_balance_amount_avg", 1);
        action.put("d90_no_balance_amount_sum", 1);
        action.put("six_month_payment_cnt", 1);
        action.put("three_month_pay_launch_type_1_succ_amount_sum", 1);
        action.put("D180_success_nosystem_payment_cnt_rate", 1);
        action.put("D30_success_nosystem_payment_amount_sum", 1);
        action.put("D60_success_nosystem_payment_amount_sum_rate", 1);
        action.put("D90_success_nosystem_payment_amount_sum_rate", 1);
        action.put("manual_input_submit_idcard", 1);
        action.put("is_clear", 1);
        action.put("clear_timestamp", 1);
        action.put("clear2now", 1);
        action.put("ever_max_dpd", 1);
        action.put("ahead_repay_cnt", 1);
        action.put("loan_cnt_inst", 1);
        action.put("loan_cnt_tm1", 1);
        action.put("bill_cnt", 1);
        action.put("begin_clear2now", 1);
        action.put("inlast1m_repay_cnt", 1);
        action.put("inlast3m_due_cnt_inst", 1);
        action.put("inlast3m_due_cnt_tm1", 1);
        action.put("inlast6m_repay_cnt", 1);
        action.put("recent_due2now", 1);
        action.put("begin_loan2now_inst", 1);
        action.put("begin_loan2now_tm1", 1);
        action.put("inlast180d_loan_cnt_inst", 1);
        action.put("inlast180d_loan_cnt_tm1", 1);
        action.put("inlast30d_loan_cnt", 1);
        action.put("inlast90d_loan_cnt", 1);
        action.put("recent_loan2now_inst", 1);
        action.put("recent_loan2now_tm1", 1);
        action.put("ahead_repay_pct_tm1", 1);
        action.put("inlast1m_repay_cnt_tm1", 1);
        action.put("inlast15d_apply_daycnt_inst", 1);
        action.put("inlast15d_apply_daycnt_tm1\n", 1);
        action.put("inlast180d_apply_daycnt_inst", 1);
        action.put("inlast180d_apply_daycnt_tm1", 1);
        action.put("inlast30d_apply_daycnt_inst", 1);
        action.put("inlast30d_apply_daycnt_tm1", 1);
        action.put("inlast90d_apply_daycnt_inst", 1);
        action.put("inlast90d_apply_daycnt_tm1", 1);
        action.put("recent_apply2now_inst", 1);
        action.put("recent_apply2now_tm1", 1);
        action.put("user_pay", 1);
        action.put("repay_plan", 1);
        action.put("loan", 1);
        action.put("loan_audit_strategy_result", 1);
        action.put("inlast30d_loan_cnt_inst", 1);
        action.put("inlast15d_loan_cnt_inst", 1);
        action.put("ahead_repay_pct_inst", 1);
        action.put("inlast6m_due_cnt_inst", 1);
        action.put("inlast180d_avg_ahead_days_inst", 1);



        action.put("mobileUserNumber", new HashMap<String, String>() {
            {
                put("registerPhone", "18800000004");
                put("reservedPhone", "18800000001");
                put("contactList", "[{\"mobile\":\"13800138000\"}, {\"mobile\":\"17700001099\"}]");
            }
        });
        Map<String, Object> params = new HashMap<>();
        params.put("action", action);
        ServiceRequest r = new ServiceRequest();
        r.setParams(params);

        MysqlServiceAdapter adapter = new MysqlServiceAdapter();
        // 使用假的user测试，看看程序会不会出错
        r.setUserKey("xxxxxxxx");
        ServiceResponse res = adapter.callService(r);
        System.out.println("=====================" + JSONObject.toJSONString(res, SerializerFeature.WriteMapNullValue));

        // 使用真的userKey测试
        r.setUserKey("DF7CB7554B8D44A4845EE7B743086B5A");
        res = adapter.callService(r);
        System.out.println("=====================" + JSONObject.toJSONString(res, SerializerFeature.WriteMapNullValue));

        // test for user_pay table
        r.setUserKey("0025af938e44cc3a691d59dfa253739c");
        res = adapter.callService(r);
        System.out.println("=====================" + JSONObject.toJSONString(res, SerializerFeature.WriteMapNullValue));

        // test for haohuan_db daily_active_user talbe
        r.setUserKey("fc7eb83c4714a4b217cf271a9516f64e");
        action.clear();
        action.put("daily_active_user", 1);
        action.put("lst1m_login_daycnt_da_inst", 1);
        action.put("lst3m_login_daycnt_da_inst", 1);
        action.put("lst6m_login_daycnt_da_inst", 1);
        action.put("lst1m_login_daycnt_da_tm1", 1);
        action.put("lst3m_login_daycnt_da_tm1", 1);
        action.put("lst6m_login_daycnt_da_tm1", 1);
        action.put("template_log", 1);
        action.put("user_level", 1);
        action.put("verify_user_line_management", 1);
        action.put("line_data", 1);
        action.put("loan_audit_strategy_result", 1);
        action.put("login_device_count", 1);
        Map<String,String> map = new HashMap<>();
        map.put("retainDay","90");
        action.put("funder_reject_reason", map);
        params.put("action", action);
        res = adapter.callService(r);
        r.setParams(params);
        System.out.println("=====================" + JSONObject.toJSONString(res, SerializerFeature.WriteMapNullValue));

    }

}