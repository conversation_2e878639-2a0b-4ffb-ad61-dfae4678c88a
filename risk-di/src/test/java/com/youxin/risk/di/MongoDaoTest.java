package com.youxin.risk.di;

import com.youxin.risk.commons.mongo.MongoDao;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @create 2022/11/22 12:25
 * @desc
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class MongoDaoTest {
    @Autowired
    private MongoDao mongoDao;

    @Test
    public void test() {
        mongoDao.insert("aa", "wa", null);
    }
}
