<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:context="http://www.springframework.org/schema/context" xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:cache="http://www.springframework.org/schema/cache" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
     http://www.springframework.org/schema/aop
     http://www.springframework.org/schema/aop/spring-aop-4.0.xsd
     http://www.springframework.org/schema/context
     http://www.springframework.org/schema/context/spring-context-4.0.xsd
     http://www.springframework.org/schema/util
     http://www.springframework.org/schema/util/spring-util-4.0.xsd
     http://www.springframework.org/schema/tx
     http://www.springframework.org/schema/tx/spring-tx-4.0.xsd
     http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-4.0.xsd">


    <util:constant id="q_channel_request" static-field="com.youxin.risk.commons.constants.RedisKeyEnum.channelRequestQueueName"/>

    <!--<bean id="runnableRejectedHandler" class="com.youxin.risk.commons.concurrent.RiskCallerRunsPolicyHandler" />-->

    <!--<bean id="channelRequestPool" class="com.youxin.risk.commons.concurrent.RiskThreadPoolTaskExecutor"-->
    <!--p:corePoolSize="20" p:maxPoolSize="200" p:queueCapacity="10000"-->
    <!--p:poolName="channelRequestPool" p:keepAliveSeconds="60"-->
    <!--p:rejectedExecutionHandler-ref="runnableRejectedHandler" />-->

    <bean id="channelRequestQueueConsumer" class="com.youxin.risk.commons.queue.QueueConsumer"
          init-method="init" p:queueName-ref="q_channel_request"
          p:consumerThreadsCount="10" p:queueMessageListener-ref="channelRequestListener"
          p:queueMessageHandler-ref="channelRequestQueueHandler"/>
    <!-- 不适用异步模式 -->
    <!--p:processPool-ref="adaptResPool" />-->

    <bean id="channelRequestListener" class="com.youxin.risk.commons.queue.impl.RedisQueueMessageListener"
          p:retryableJedis-ref="retryableJedis" p:messageConverter-ref="channelRequestModelConverter"/>

    <bean id="channelRequestModelConverter" class="com.youxin.risk.commons.queue.impl.JsonMessageConverter"
          p:valueType="com.youxin.risk.commons.model.ChannelRequestModel"/>

</beans>