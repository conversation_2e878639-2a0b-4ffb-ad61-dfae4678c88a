<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns="http://www.springframework.org/schema/beans" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:context="http://www.springframework.org/schema/context" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:cache="http://www.springframework.org/schema/cache" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
     http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
     http://www.springframework.org/schema/aop
     http://www.springframework.org/schema/aop/spring-aop-4.0.xsd
     http://www.springframework.org/schema/context
     http://www.springframework.org/schema/context/spring-context-4.0.xsd
     http://www.springframework.org/schema/util
     http://www.springframework.org/schema/util/spring-util-4.0.xsd 
     http://www.springframework.org/schema/tx
     http://www.springframework.org/schema/tx/spring-tx-4.0.xsd
     http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-4.0.xsd">

	<bean id="baseDruidDataSource" class="com.alibaba.druid.pool.DruidDataSource">
        <property name="maxActive" value="200" />
        <property name="initialSize" value="2" />
        <property name="minIdle" value="2" />
        <property name="maxWait" value="2000" />
        <property name="testOnBorrow" value="true" />
        <property name="defaultTransactionIsolation" value="4" />
        <property name="timeBetweenEvictionRunsMillis" value="30000" />
        <property name="minEvictableIdleTimeMillis" value="300000" />
        <property name="timeBetweenLogStatsMillis" value="300000" />
        <property name="removeAbandoned" value="false" />
        <property name="removeAbandonedTimeout" value="300" />
        <property name="logAbandoned" value="false" />
        <property name="filters" value="stat,wall"/>
    </bean>
    
    <!-- admin datasource -->
    <bean id="adminDataSource" class="com.alibaba.druid.pool.DruidDataSource" parent="baseDruidDataSource">
        <property name="name" value="adminDataSource" />
        <property name="username" value="root" />
        <property name="password" value="youxin_risk" />
        <property name="url" value="**************************************************************************************************************************************************************************************" />
    </bean>
    <bean id="adminSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="adminDataSource"/>
        <property name="configLocation" value="classpath:mybatis-config.xml"/>
        <property name="mapperLocations">
            <list>
                <value>classpath*:mapper/admin/*.xml</value>
            </list>
        </property>
        <property name="typeAliasesPackage" value="com.youxin.risk.commons.model"/>
    </bean>
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.youxin.risk.commons.dao.admin"/>
        <property name="sqlSessionFactoryBeanName" value="adminSqlSessionFactory"/>
    </bean>


    <!-- channel datasource -->
    <bean id="channelDataSource" class="com.alibaba.druid.pool.DruidDataSource" parent="baseDruidDataSource">
        <property name="name" value="channelDataSource" />
        <property name="username" value="root" />
        <property name="password" value="youxin_risk" />
        <property name="url" value="****************************************************************************************************************************************************************************************" />
    </bean>
    <bean id="channelSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="channelDataSource"/>
        <property name="configLocation" value="classpath:mybatis-config.xml"/>
        <property name="mapperLocations">
            <list>
                <value>classpath*:mapper/channel/*.xml</value>
            </list>
        </property>
        <property name="typeAliasesPackage" value="com.youxin.risk.commons.model"/>
    </bean>
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.youxin.risk.commons.dao.channel"/>
        <property name="sqlSessionFactoryBeanName" value="channelSqlSessionFactory"/>
    </bean>
</beans>
