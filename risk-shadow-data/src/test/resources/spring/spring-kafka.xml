<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
     http://www.springframework.org/schema/tx
     http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
     http://www.springframework.org/schema/jee
     http://www.springframework.org/schema/jee/spring-jee-3.0.xsd
     http://www.springframework.org/schema/context
     http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <!-- 监控检查和上下线通知接口相关 -->
    <bean id="kafkaMessageListener" class="com.youxin.risk.commons.health.impl.KafKaMessageListener"/>

    <!-- spring和kafka集成相关配置 -->
    <!--后续考虑单一msg,配置多groupId监听方案,以实现单消息多种事后分析模式-->
    <bean id="consumerProperties" class="java.util.HashMap">
        <constructor-arg>
            <map>
                <entry key="bootstrap.servers" value="${kafka.dp.hosts}"/>
                <entry key="group.id" value="youxin_risk_calculate_Group_57495827e957c6263"/>
                <entry key="enable.auto.commit" value="false"/>
                <entry key="auto.commit.interval.ms" value="1000"/>
                <entry key="session.timeout.ms" value="30000"/>
                <entry key="key.deserializer" value="org.apache.kafka.common.serialization.StringDeserializer"/>
                <entry key="value.deserializer" value="org.apache.kafka.common.serialization.StringDeserializer"/>
            </map>
        </constructor-arg>
    </bean>

    <bean id="consumerFactory" class="org.springframework.kafka.core.DefaultKafkaConsumerFactory">
        <constructor-arg>
            <ref bean="consumerProperties"/>
        </constructor-arg>
    </bean>

    <bean id="containerProperties" class="org.springframework.kafka.listener.config.ContainerProperties">
        <constructor-arg value="${kafka.dp.topic}"/>
        <property name="messageListener" ref="engineMsgListener"/>
        <property name="ackMode" value="MANUAL_IMMEDIATE"/>
    </bean>

    <bean id="messageListenerContainer" class="org.springframework.kafka.listener.KafkaMessageListenerContainer"
          init-method="doStart">
        <constructor-arg ref="consumerFactory"/>
        <constructor-arg ref="containerProperties"/>
    </bean>

    <!-- risk kafka api -->
    <bean id="engineMsgListener" class="com.youxin.risk.commons.kafkav2.RiskKafkaAcknowledgingMessageListener">
        <property name="filters">
            <list>
                <ref bean="engineMsgPaserFilter"/>
            </list>
        </property>
        <property name="handler" ref="engineMsgHander"/>
    </bean>

    <!-- 消息解析filter，将消息反序列化对对象，默认使用fastjson，可自己实现 -->
    <bean id="engineMsgPaserFilter" class="com.youxin.risk.commons.kafkav2.filter.impl.ParseFilter">
        <property name="serializedBeanClassName" value="com.youxin.risk.commons.vo.DataPlatformMessageVo"/>
    </bean>

    <!-- 消息处理器，继承BaseKafKaMessageHandler，注入RetryableJedis，会进行判重操作，重复的请求不再处理 -->
    <bean id="engineMsgHander" class="com.youxin.risk.shadow.listener.BaseShadowKafkaMsgHandler">
    	<property name="retryableJedis" ref="retryableJedis"/>
    </bean>


</beans>