/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.shadow.service;

import com.youxin.risk.shadow.dao.hfq.HfqPreApprovalVerifyResultMapper;
import com.youxin.risk.shadow.model.PreApprovalVerifyResult;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.retry.annotation.Retryable;

import javax.annotation.Resource;

@EnableRetry
public class HfqPreApprovalVerifyResultService {
    private static final int MAX_ATTEMPTS = 3;
    private static final long DELAY = 100L;
    private static final double MULTI_PLIER = 1;

    @Resource
    private HfqPreApprovalVerifyResultMapper verifyResultMapper;

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public int insert(PreApprovalVerifyResult verifyResult) {
        return verifyResultMapper.savePreApprovalVerifyResult(verifyResult);
    }
}
