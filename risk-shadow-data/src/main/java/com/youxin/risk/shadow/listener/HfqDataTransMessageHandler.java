package com.youxin.risk.shadow.listener;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.google.common.base.Stopwatch;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.exception.RiskRuntimeException;
import com.youxin.risk.commons.kafkav2.KafkaContext;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.utils.LogUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.shadow.config.Apollo;
import com.youxin.risk.shadow.constants.ShadowConstant;
import com.youxin.risk.shadow.service.handler.DataTransHandler;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class HfqDataTransMessageHandler extends BaseShadowKafkaMsgHandler {

    @Resource(name = "dataTransHandlerProxy")
    private DataTransHandler dataTransHandlerProxy;

    @Override
    protected void before(KafkaContext context) {
        // check event is not null
        Event message = context.getMessageObject(Event.class);
		// 绑定logId
		LogUtil.bindLogId(message.getSessionId());
        //filter message
        Map<String, Object> verifyResult = message.getVerifyResult();
        if (verifyResult == null){
            LoggerProxy.error("dealHfqDataTransMsgButVerifyResultIsNull", logger, "message=" + message);
            context.setTerminated(true);
            return;
        }
    }

    @Override
    protected void handler0(KafkaContext context) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        long costTime = 0L;
        try {
			Event message = context.getMessageObject(Event.class);
            JSONObject messageObj = JSONObject.parseObject(context.getMessage());
            message.setXml(messageObj.getString("xml"));
            dealExtraMsg(message, context);
            if (Apollo.logDetail()) {
                LoggerProxy.info("receivedHfqDataTransKafkaMessage", logger, "kafkaOrigMessage={}", context.getMessage());
            }
			// 解析event获取原始数据, 获取转换数据编码, 调用template差异化处理
			// 解析event策略码字段，根据类别对应不同业务实现
			Map<String, Object> verifyResult = message.getVerifyResult();
			// check 数据编码
			if (verifyResult.get(ShadowConstant.HFQ_DATA_TRANS_KEY_WORD) == null ||
					StringUtils.isEmpty((String)verifyResult.get(ShadowConstant.HFQ_DATA_TRANS_KEY_WORD))){
				throw new RiskRuntimeException("dealHfqDataTransMsgButDataCodeIsNull");
			}
			String dataCode = (String) verifyResult.get(ShadowConstant.HFQ_DATA_TRANS_KEY_WORD);
			dataTransHandlerProxy.handle(dataCode, message);
			// 考虑无异常视为成功,埋点放入final
            LoggerProxy.info("dealHfqDataTransMsgSuccess", logger, "");
            context.setRetCode(RetCodeEnum.SUCCESS);
        } catch (Exception e) {
            context.setTerminated(true);
            context.setRetCode(RetCodeEnum.FAILED);
            LoggerProxy.error("dealHfqDataTransMsgError", logger, "", e);
        } finally {
            costTime = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
            if (Apollo.logDetail()) {
                LoggerProxy.info("finishHfqDataTransKafKaMsg", logger, "cost={},request={},result={}",
                        costTime, context.getMessage(), context.getRetCode().name());
            }
            point(costTime, context, ShadowConstant.ENGINE_POLICY_POINT_FLAG);
        }
        return;
    }

    private void dealExtraMsg(Event message, KafkaContext context) {
        try {
            Object userBasicInfo = JSONPath.read(context.getMessage(), "dataVo.userBasicInfo");
            if (userBasicInfo != null) {
                message.set("userBasicInfoTarget", userBasicInfo);
            }
            Object signInformation = JSONPath.read(context.getMessage(), "dataVo.signInformation");
            if (signInformation != null) {
                message.set("signInformationTarget", signInformation);
            }
        } catch (Exception e) {
            LoggerProxy.warn("dealExtraMsgError", logger, "", e);
        }
    }

}