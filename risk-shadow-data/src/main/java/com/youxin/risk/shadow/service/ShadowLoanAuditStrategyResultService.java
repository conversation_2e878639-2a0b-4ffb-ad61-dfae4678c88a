/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.shadow.service;

import javax.annotation.Resource;

import com.youxin.risk.shadow.dao.hfq.ShadowLoanAuditStrategyResultMinimalMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.retry.annotation.Retryable;

import com.youxin.risk.shadow.dao.hfq.ShadowLoanAuditStrategyResultMapper;
import com.youxin.risk.shadow.model.ShadowLoanAuditStrategyResult;

@EnableRetry
public class ShadowLoanAuditStrategyResultService {

    private static final Logger logger = LoggerFactory.getLogger(ShadowLoanAuditStrategyResultService.class);

    private static final int MAX_ATTEMPTS = 3;
    private static final long DELAY = 100L;
    private static final double MULTI_PLIER = 1;

    @Resource
    private ShadowLoanAuditStrategyResultMapper loanAuditStrategyResultMapper;

    @Resource
    private ShadowLoanAuditStrategyResultMinimalMapper loanAuditStrategyResultMinimalMapper;

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public int insert(ShadowLoanAuditStrategyResult model) {
        return loanAuditStrategyResultMapper.insert(model);
    }

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public void insertMinimal(ShadowLoanAuditStrategyResult model) {
        int count = loanAuditStrategyResultMinimalMapper.countByLoanKey(model.getUserKey(), model.getLoanKey());
        if (count <= 0) {
            loanAuditStrategyResultMinimalMapper.insertReplace(model);
        } else {
            logger.error("multi insert loan_audit_strategy_result_minimal failed!{}-{}-{}", model.getUserKey(), model.getLoanKey(), count);
        }
    }

}
