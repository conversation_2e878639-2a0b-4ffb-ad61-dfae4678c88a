<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.shadow.dao.hfq.ShadowUserLineManagementMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.shadow.model.ShadowUserLineManagement">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="user_key" jdbcType="VARCHAR" property="userKey" />
        <result column="source_system" jdbcType="VARCHAR" property="sourceSystem" />
        <result column="credit_line" jdbcType="DOUBLE" property="creditLine" />
        <result column="avail_line" jdbcType="DOUBLE" property="availLine" />
        <result column="util_line" jdbcType="DOUBLE" property="utilLine" />
        <result column="loan_line" jdbcType="DOUBLE" property="loanLine" />
        <result column="loan_avail_line" jdbcType="DOUBLE" property="loanAvailLine" />
        <result column="loan_actual_line" jdbcType="DOUBLE" property="loanActualLine" />
        <result column="loan_util_line" jdbcType="DOUBLE" property="loanUtilLine" />
        <result column="loan_rate" jdbcType="DOUBLE" property="loanRate" />
        <result column="loan_period" jdbcType="INTEGER" property="loanPeriod" />
        <result column="bt_line" jdbcType="DOUBLE" property="btLine" />
        <result column="bt_avail_line" jdbcType="DOUBLE" property="btAvailLine" />
        <result column="bt_actual_line" jdbcType="DOUBLE" property="btActualLine" />
        <result column="bt_util_line" jdbcType="DOUBLE" property="btUtilLine" />
        <result column="bt_rate" jdbcType="DOUBLE" property="btRate" />
        <result column="bt_period" jdbcType="INTEGER" property="btPeriod" />
        <result column="shop_line" jdbcType="DOUBLE" property="shopLine" />
        <result column="shop_avail_line" jdbcType="DOUBLE" property="shopAvailLine" />
        <result column="shop_actual_line" jdbcType="DOUBLE" property="shopActualLine" />
        <result column="shop_util_line" jdbcType="DOUBLE" property="shopUtilLine" />
        <result column="shop_rate" jdbcType="DOUBLE" property="shopRate" />
        <result column="shop_period" jdbcType="INTEGER" property="shopPeriod" />
        <result column="account_status" jdbcType="VARCHAR" property="accountStatus" />
        <result column="is_closed" jdbcType="BIT" property="isClosed" />
        <result column="user_point" jdbcType="DOUBLE" property="userPoint" />
        <result column="user_level" jdbcType="VARCHAR" property="userLevel" />
        <result column="line_assign_time" jdbcType="TIMESTAMP" property="lineAssignTime" />
        <result column="strategy_id" jdbcType="INTEGER" property="strategyId" />
        <result column="strategy_type" jdbcType="VARCHAR" property="strategyType" />
        <result column="is_active" jdbcType="BIT" property="isActive" />
        <result column="last_id" jdbcType="INTEGER" property="lastId" />
        <result column="loan_id" jdbcType="INTEGER" property="loanId" />
        <result column="loan_key" jdbcType="VARCHAR" property="loanKey" />
        <result column="segment_code" jdbcType="VARCHAR" property="segmentCode" />
        <result column="test_code" jdbcType="VARCHAR" property="testCode" />
        <result column="status" jdbcType="BIT" property="status" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="tmp_shop_line" jdbcType="DOUBLE" property="tmpShopLine" />
        <result column="tmp_shop_avail_line" jdbcType="DOUBLE" property="tmpShopAvailLine" />
        <result column="tmp_shop_util_line" jdbcType="DOUBLE" property="tmpShopUtilLine" />
        <result column="tmp_shop_actual_line" jdbcType="DOUBLE" property="tmpShopActualLine" />
        <result column="tmp_loan_line" jdbcType="DOUBLE" property="tmpLoanLine" />
        <result column="tmp_loan_avail_line" jdbcType="DOUBLE" property="tmpLoanAvailLine" />
        <result column="tmp_loan_util_line" jdbcType="DOUBLE" property="tmpLoanUtilLine" />
        <result column="tmp_loan_actual_line" jdbcType="DOUBLE" property="tmpLoanActualLine" />
        <result column="fix_shop_line" jdbcType="DOUBLE" property="fixShopLine" />
        <result column="fix_loan_line" jdbcType="DOUBLE" property="fixLoanLine" />
        <result column="tmp_shop_line_status" jdbcType="VARCHAR" property="tmpShopLineStatus" />
        <result column="tmp_loan_line_status" jdbcType="VARCHAR" property="tmpLoanLineStatus" />
        <result column="tmp_shop_line_end_time" jdbcType="TIMESTAMP" property="tmpShopLineEndTime" />
        <result column="tmp_loan_line_end_time" jdbcType="TIMESTAMP" property="tmpLoanLineEndTime" />
        <result column="tmp_shop_line_end_time_ext1" jdbcType="TIMESTAMP" property="tmpShopLineEndTimeExt1" />
        <result column="tmp_loan_line_end_time_ext1" jdbcType="TIMESTAMP" property="tmpLoanLineEndTimeExt1" />
        <result column="vulm_id" jdbcType="INTEGER" property="vulmId" />
    </resultMap>

    <sql id="table_name">
      verify_user_line_management
    </sql>

    <sql id="save_Base_Column_List">
      user_key, source_system, credit_line, avail_line, util_line, loan_line, loan_avail_line,
      loan_actual_line, loan_util_line, loan_rate, loan_period, bt_line, bt_avail_line,
      bt_actual_line, bt_util_line, bt_rate, bt_period, shop_line, shop_avail_line, shop_actual_line,
      shop_util_line, shop_rate, shop_period, account_status, is_closed, user_point, user_level,
      line_assign_time, strategy_id, strategy_type, is_active, last_id, loan_id, loan_key,
      segment_code, test_code, status, remark, create_time, update_time, version, period_line_rate, reason_code,
      tmp_shop_line, tmp_shop_avail_line, tmp_shop_util_line, tmp_shop_actual_line, tmp_loan_line, tmp_loan_avail_line,
      tmp_loan_util_line, tmp_loan_actual_line, fix_shop_line, fix_loan_line, tmp_shop_line_end_time,
      tmp_loan_line_end_time, tmp_shop_line_status, tmp_loan_line_status, tmp_shop_line_end_time_ext1,
      tmp_loan_line_end_time_ext1, vulm_id
    </sql>

    <sql id="Base_Column_List">
        id, <include refid="save_Base_Column_List"/>
    </sql>

    <insert id="insertReplace" parameterType="com.youxin.risk.shadow.model.ShadowUserLineManagement">
        replace into <include refid="table_name"/> (<include refid="save_Base_Column_List"/>) values
        (#{userKey}, #{sourceSystem}, #{creditLine}, #{availLine}, #{utilLine},
        #{loanLine}, #{loanAvailLine}, #{loanActualLine}, #{loanUtilLine}, #{loanRate}, #{loanPeriod},
        #{btLine}, #{btAvailLine}, #{btActualLine}, #{btUtilLine}, #{btRate}, #{btPeriod},
        #{shopLine}, #{shopAvailLine}, #{shopActualLine}, #{shopUtilLine}, #{shopRate}, #{shopPeriod},
        #{accountStatus}, #{isClosed}, #{userPoint}, #{userLevel}, #{lineAssignTime}, #{strategyId},
        #{strategyType}, #{isActive}, #{lastId}, #{loanId}, #{loanKey}, #{segmentCode},
        #{testCode}, #{status}, #{remark}, now(),now(), #{version},
        #{periodLineRate}, #{reasonCode},#{tmpShopLine} ,#{tmpShopAvailLine} ,#{tmpShopUtilLine} ,
        #{tmpShopActualLine} ,#{tmpLoanLine} ,#{tmpLoanAvailLine} ,#{tmpLoanUtilLine} ,
        #{tmpLoanActualLine} ,#{fixShopLine} ,#{fixLoanLine} ,#{tmpShopLineEndTime} ,#{tmpLoanLineEndTime},
        #{tmpShopLineStatus} ,#{tmpLoanLineStatus},#{tmpShopLineEndTimeExt1} ,#{tmpLoanLineEndTimeExt1},#{vulmId})
    </insert>


    <update id="updateStatusInvalid" parameterType="java.lang.String">
        update <include refid="table_name"/> set is_active=0,update_time = now()
        where user_key=#{userKey} and source_system=#{sourceSystem}
    </update>

    <select id="selectLastValidRec" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from <include refid="table_name"/>
        where user_key=#{userKey} and source_system=#{sourceSystem} and is_active = 1 ORDER BY id DESC
    </select>


</mapper>