<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
     http://www.springframework.org/schema/tx
     http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
     http://www.springframework.org/schema/jee
     http://www.springframework.org/schema/jee/spring-jee-3.0.xsd
     http://www.springframework.org/schema/context
     http://www.springframework.org/schema/context/spring-context-3.0.xsd">


    <!-- spring和kafka集成相关配置 -->
    <!-- risk kafka api -->
    <bean id="engineMsgListener" class="com.youxin.risk.commons.kafkav2.consumer.KafkaAckConsumer">
        <property name="groupId" value="${kafka.shadow.datatrans.topic.group.id}"/>
        <property name="topic" value="${kafka.shadow.datatrans.topic}"/>
        <property name="bootstrapServers" value="${kafka.dp.hosts}"/>
        <property name="filters">
            <list>
                <ref bean="shadowDataTransMsgPaserFilter"/>
            </list>
        </property>
        <property name="messageHandler" ref="engineMsgHander"/>
    </bean>

<!--    <bean id="engineMsgMirrorListener" class="com.youxin.risk.commons.kafkav2.consumer.KafkaAckConsumer">-->
<!--        <property name="groupId" value="${kafka.shadow.datatrans.topic.group.id}"/>-->
<!--        <property name="topic" value="${kafka.shadow.datatrans.topic}"/>-->
<!--        <property name="bootstrapServers" value="${kafka.mirror.dp.hosts}"/>-->
<!--        <property name="filters">-->
<!--            <list>-->
<!--                <ref bean="shadowDataTransMsgPaserFilter"/>-->
<!--            </list>-->
<!--        </property>-->
<!--        <property name="messageHandler" ref="engineMsgHander"/>-->
<!--    </bean>-->

    <!-- 消息解析filter，将消息反序列化对对象，默认使用fastjson，可自己实现 -->
    <bean id="shadowDataTransMsgPaserFilter" class="com.youxin.risk.commons.kafkav2.filter.impl.ParseFilter">
        <property name="serializedBeanClassName" value="com.youxin.risk.commons.model.Event"/>
    </bean>

    <!-- 消息处理器，继承BaseKafKaMessageHandler，注入RetryableJedis，会进行判重操作，重复的请求不再处理 -->
    <bean id="engineMsgHander" class="com.youxin.risk.shadow.listener.HfqDataTransMessageHandler">
        <property name="retryableJedis" ref="retryableJedis"/>
    </bean>

    <bean id="engineMonitorMsgListener" class="com.youxin.risk.commons.kafkav2.consumer.KafkaAckConsumer">
        <property name="groupId" value="${kafka.shadow.monitor.datatrans.topic.group.id}"/>
        <property name="topic" value="${kafka.shadow.monitor.datatrans.topic}"/>
        <property name="bootstrapServers" value="${kafka.dp.hosts}"/>
        <property name="filters">
            <list>
                <ref bean="shadowMonitorDataTransMsgPaserFilter"/>
            </list>
        </property>
        <property name="messageHandler" ref="engineMonitorMsgHander"/>
    </bean>

<!--    <bean id="engineMonitorMsgMirrorListener" class="com.youxin.risk.commons.kafkav2.consumer.KafkaAckConsumer">-->
<!--        <property name="groupId" value="${kafka.shadow.monitor.datatrans.topic.group.id}"/>-->
<!--        <property name="topic" value="${kafka.shadow.monitor.datatrans.topic}"/>-->
<!--        <property name="bootstrapServers" value="${kafka.mirror.dp.hosts}"/>-->
<!--        <property name="filters">-->
<!--            <list>-->
<!--                <ref bean="shadowMonitorDataTransMsgPaserFilter"/>-->
<!--            </list>-->
<!--        </property>-->
<!--        <property name="messageHandler" ref="engineMonitorMsgHander"/>-->
<!--    </bean>-->

    <!-- 消息解析filter，将消息反序列化对对象，默认使用fastjson，可自己实现 -->
    <bean id="shadowMonitorDataTransMsgPaserFilter" class="com.youxin.risk.commons.kafkav2.filter.impl.ParseFilter">
        <property name="serializedBeanClassName" value="com.youxin.risk.shadow.model.MonitorResult"/>
    </bean>

    <!-- 消息处理器，继承BaseKafKaMessageHandler，注入RetryableJedis，会进行判重操作，重复的请求不再处理 -->
    <bean id="engineMonitorMsgHander" class="com.youxin.risk.shadow.listener.LoanAuditResultTransMessageHandler">
        <property name="retryableJedis" ref="retryableJedis"/>
    </bean>
</beans>
