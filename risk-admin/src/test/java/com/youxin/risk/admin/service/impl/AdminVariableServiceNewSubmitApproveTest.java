package com.youxin.risk.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yonxin.risk.approve.client.client.RiskApproveClient;
import com.yonxin.risk.approve.client.model.ApproveSubmitReq;
import com.yonxin.risk.approve.client.model.template.base.Approver;
import com.youxin.risk.admin.dao.admin.AdminVariableConfigMapper;
import com.youxin.risk.admin.model.AdminVariableConfigVo;
import com.youxin.risk.admin.model.approve.VariableApproveTemplate;
import com.youxin.risk.admin.utils.UserInfoUtil;
import com.youxin.risk.commons.exception.CannotGetLockException;
import com.youxin.risk.commons.exception.ValidateException;
import com.youxin.risk.commons.model.VariableConfigDO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * AdminVariableServiceNew.submitApprove方法的单元测试
 * 测试三种场景：
 * 1. 单个变量 启用状态
 * 2. 单个变量 停用状态
 * 3. 多个变量 有启用 有停用
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({RiskApproveClient.class, UserInfoUtil.class})
public class AdminVariableServiceNewSubmitApproveTest {

    @InjectMocks
    private AdminVariableServiceNew adminVariableServiceNew;

    @Mock
    private AdminVariableConfigMapper adminVariableConfigMapper;

    @Mock
    private NodeManagerService nodeManagerService;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(RiskApproveClient.class);
        PowerMockito.mockStatic(UserInfoUtil.class);
        
        // 模拟用户名
        PowerMockito.when(UserInfoUtil.getUsername()).thenReturn("testUser");
        
        // 模拟锁定检查，返回空表示没有审批中的配置
        PowerMockito.when(RiskApproveClient.acquireLastOpUser(anyString())).thenReturn("");
        
        // 模拟审批提交
//        PowerMockito.doNothing().when(RiskApproveClient.class);
        PowerMockito.when(RiskApproveClient.submit(any(ApproveSubmitReq.class))).thenReturn("success");

        
        // 模拟审批人和通知人
        List<Approver> approvers = new ArrayList<>();
        approvers.add(new Approver(Arrays.asList("approver1")));
        when(nodeManagerService.getApprover(anyBoolean())).thenReturn(approvers);
        
        List<String> notifyers = new ArrayList<>();
        notifyers.add("notifyer1");
        when(nodeManagerService.getNotifyer(anyString())).thenReturn(notifyers);
    }

    /**
     * 测试场景1：单个变量 启用状态
     * 预期结果：直接更新变量配置，不走审批流程
     */
    @Test
    public void testSubmitApprove_SingleEnable() {
        // 准备测试数据
        AdminVariableConfigVo adminVariableConfigVo = new AdminVariableConfigVo();
        adminVariableConfigVo.setIds(Lists.newArrayList("1"));
        adminVariableConfigVo.setStrategyType("testStrategy");
        adminVariableConfigVo.setStrategyNodeCode("testNode");
        adminVariableConfigVo.setAirRunningStatus(VariableConfigDO.STATUS_ENABLE);
        
        // 模拟数据库中的变量配置
        VariableConfigDO variableConfigDO = new VariableConfigDO();
        variableConfigDO.setId(1L);
        variableConfigDO.setVariableCode("testVar");
        variableConfigDO.setStrategyType("testStrategy");
        variableConfigDO.setStrategyNodeCode("testNode");
        variableConfigDO.setAirRunningStatus(VariableConfigDO.STATUS_ENABLE); // 启用状态
        Date now = new Date();
        variableConfigDO.setAirRunningEndTime(new Date(now.getTime() + 1000 * 60 * 60 * 24L));
        
        List<VariableConfigDO> variableConfigDOList = Arrays.asList(variableConfigDO);
        when(adminVariableConfigMapper.selectByIds(any())).thenReturn(variableConfigDOList);
        when(adminVariableConfigMapper.update(any(VariableConfigDO.class))).thenReturn(1);
        
        // 执行测试方法
        adminVariableServiceNew.submitApprove(adminVariableConfigVo);
        
        // 验证结果：应该调用updateBatch方法更新配置，不走审批流程
        verify(adminVariableConfigMapper, times(1)).selectByIds(any());
        verify(adminVariableConfigMapper, times(1)).update(any(VariableConfigDO.class));
        PowerMockito.verifyStatic(RiskApproveClient.class, never());
        RiskApproveClient.submit(any(ApproveSubmitReq.class));
    }

    /**
     * 测试场景2：单个变量 停用状态
     * 预期结果：走审批流程
     */
    @Test
    public void testSubmitApprove_SingleDisable() {
        // 准备测试数据
        AdminVariableConfigVo adminVariableConfigVo = new AdminVariableConfigVo();
        adminVariableConfigVo.setIds(Lists.newArrayList("1"));
        adminVariableConfigVo.setStrategyType("testStrategy");
        adminVariableConfigVo.setStrategyNodeCode("testNode");
        adminVariableConfigVo.setAirRunningStatus(VariableConfigDO.STATUS_ENABLE); // 要修改为启用状态
        
        // 模拟数据库中的变量配置
        VariableConfigDO variableConfigDO = new VariableConfigDO();
        variableConfigDO.setId(1L);
        variableConfigDO.setVariableCode("testVar");
        variableConfigDO.setStrategyType("testStrategy");
        variableConfigDO.setStrategyNodeCode("testNode");
        variableConfigDO.setAirRunningStatus(VariableConfigDO.STATUS_DISABLE); // 停用状态
        Date date = new Date();
        variableConfigDO.setAirRunningEndTime(new Date(date.getTime() + 1000 * 60 * 60 * 24));
        
        List<VariableConfigDO> variableConfigDOList = Arrays.asList(variableConfigDO);
        when(adminVariableConfigMapper.selectByIds(any())).thenReturn(variableConfigDOList);
        
        // 执行测试方法
        adminVariableServiceNew.submitApprove(adminVariableConfigVo);
        
        // 验证结果：应该走审批流程
        verify(adminVariableConfigMapper, times(1)).selectByIds(any());
        verify(adminVariableConfigMapper, never()).update(any(VariableConfigDO.class));
        PowerMockito.verifyStatic(RiskApproveClient.class, times(1));
        RiskApproveClient.submit(any(ApproveSubmitReq.class));
    }

    /**
     * 测试场景3：多个变量 有启用 有停用
     * 预期结果：启用状态的变量直接更新，停用状态的变量走审批流程
     */
    @Test
    public void testSubmitApprove_MixedStatus() {
        // 准备测试数据
        AdminVariableConfigVo adminVariableConfigVo = new AdminVariableConfigVo();
        adminVariableConfigVo.setIds(Lists.newArrayList("1", "2"));
        adminVariableConfigVo.setStrategyType("testStrategy");
        adminVariableConfigVo.setStrategyNodeCode("testNode");
        Date date = new Date();
        adminVariableConfigVo.setAirRunningEndTime(new Date(date.getTime() + 1000 * 60 * 60 * 24));

        // 模拟数据库中的变量配置 - 一个启用，一个停用
        VariableConfigDO variableConfigDO1 = new VariableConfigDO();
        variableConfigDO1.setId(1L);
        variableConfigDO1.setVariableCode("testVar1");
        variableConfigDO1.setStrategyType("testStrategy");
        variableConfigDO1.setStrategyNodeCode("testNode");
        variableConfigDO1.setAirRunningStatus(VariableConfigDO.STATUS_ENABLE); // 启用状态
        
        VariableConfigDO variableConfigDO2 = new VariableConfigDO();
        variableConfigDO2.setId(2L);
        variableConfigDO2.setVariableCode("testVar2");
        variableConfigDO2.setStrategyType("testStrategy");
        variableConfigDO2.setStrategyNodeCode("testNode");
        variableConfigDO2.setAirRunningStatus(VariableConfigDO.STATUS_DISABLE); // 停用状态
        
        List<VariableConfigDO> variableConfigDOList = Arrays.asList(variableConfigDO1, variableConfigDO2);
        when(adminVariableConfigMapper.selectByIds(any())).thenReturn(variableConfigDOList);
        when(adminVariableConfigMapper.update(any(VariableConfigDO.class))).thenAnswer(invocation -> {
            VariableConfigDO argument = (VariableConfigDO)invocation.getArgument(0);
            return argument.getId() == 1 ? 1 : 0;
        });
        
        // 执行测试方法
        adminVariableServiceNew.submitApprove(adminVariableConfigVo);
        
        // 验证结果：应该同时调用updateBatch和审批流程
        verify(adminVariableConfigMapper, times(1)).selectByIds(any());
        verify(adminVariableConfigMapper, times(1)).update(any(VariableConfigDO.class));
        PowerMockito.verifyStatic(RiskApproveClient.class, times(1));
        Object submit = RiskApproveClient.submit(any(ApproveSubmitReq.class));
        System.out.println(submit);
    }


    /**
     *
     */
    @Test(expected = ValidateException.class)
    public void testSubmitApprove_MixedStatus_withAirEndTime_withException() {
        // 准备测试数据
        AdminVariableConfigVo adminVariableConfigVo = new AdminVariableConfigVo();
        adminVariableConfigVo.setIds(Lists.newArrayList("1", "2"));
        adminVariableConfigVo.setStrategyType("testStrategy");
        adminVariableConfigVo.setStrategyNodeCode("testNode");
        Date date = new Date();
//        adminVariableConfigVo.setAirRunningEndTime(new Date(date.getTime() - 1000 * 60 * 60 * 24));

        // 模拟数据库中的变量配置 - 一个启用，一个停用
        VariableConfigDO variableConfigDO1 = new VariableConfigDO();
        variableConfigDO1.setId(1L);
        variableConfigDO1.setVariableCode("testVar1");
        variableConfigDO1.setStrategyType("testStrategy");
        variableConfigDO1.setStrategyNodeCode("testNode");
        variableConfigDO1.setAirRunningStatus(VariableConfigDO.STATUS_ENABLE); // 启用状态
        variableConfigDO1.setAirRunningEndTime(new Date(date.getTime() + 1000 * 60 * 60 * 24));

        VariableConfigDO variableConfigDO2 = new VariableConfigDO();
        variableConfigDO2.setId(2L);
        variableConfigDO2.setVariableCode("testVar2");
        variableConfigDO2.setStrategyType("testStrategy");
        variableConfigDO2.setStrategyNodeCode("testNode");
        variableConfigDO2.setAirRunningStatus(VariableConfigDO.STATUS_DISABLE); // 停用状态
//        Date date = new Date();
        variableConfigDO2.setAirRunningEndTime(new Date(date.getTime() - 1000 * 60 * 60 * 24));

        List<VariableConfigDO> variableConfigDOList = Arrays.asList(variableConfigDO1, variableConfigDO2);
        when(adminVariableConfigMapper.selectByIds(any())).thenReturn(variableConfigDOList);
        when(adminVariableConfigMapper.update(any(VariableConfigDO.class))).thenAnswer(invocation -> {
            VariableConfigDO argument = (VariableConfigDO)invocation.getArgument(0);
            return argument.getId() == 1 ? 1 : 0;
        });

        // 执行测试方法
        adminVariableServiceNew.submitApprove(adminVariableConfigVo);

        // 验证结果：应该同时调用updateBatch和审批流程
        verify(adminVariableConfigMapper, times(1)).selectByIds(any());
        verify(adminVariableConfigMapper, times(1)).update(any(VariableConfigDO.class));
        PowerMockito.verifyStatic(RiskApproveClient.class, times(1));
        Object submit = RiskApproveClient.submit(any(ApproveSubmitReq.class));
        System.out.println(submit);
    }

    /**
     *
     */
    @Test
    public void testSubmitApprove_MixedStatus_withAirEndTime() {
        // 准备测试数据
        AdminVariableConfigVo adminVariableConfigVo = new AdminVariableConfigVo();
        adminVariableConfigVo.setIds(Lists.newArrayList("1", "2"));
        adminVariableConfigVo.setStrategyType("testStrategy");
        adminVariableConfigVo.setStrategyNodeCode("testNode");
        Date date = new Date();
//        adminVariableConfigVo.setAirRunningEndTime(new Date(date.getTime() - 1000 * 60 * 60 * 24));

        // 模拟数据库中的变量配置 - 一个启用，一个停用
        VariableConfigDO variableConfigDO1 = new VariableConfigDO();
        variableConfigDO1.setId(1L);
        variableConfigDO1.setVariableCode("testVar1");
        variableConfigDO1.setStrategyType("testStrategy");
        variableConfigDO1.setStrategyNodeCode("testNode");
        variableConfigDO1.setAirRunningStatus(VariableConfigDO.STATUS_ENABLE); // 启用状态
        variableConfigDO1.setAirRunningEndTime(new Date(date.getTime() + 1000 * 60 * 60 * 24));

        VariableConfigDO variableConfigDO2 = new VariableConfigDO();
        variableConfigDO2.setId(2L);
        variableConfigDO2.setVariableCode("testVar2");
        variableConfigDO2.setStrategyType("testStrategy");
        variableConfigDO2.setStrategyNodeCode("testNode");
        variableConfigDO2.setAirRunningStatus(VariableConfigDO.STATUS_DISABLE); // 停用状态
//        Date date = new Date();
        variableConfigDO2.setAirRunningEndTime(new Date(date.getTime() + 1000 * 60 * 60 * 24));

        List<VariableConfigDO> variableConfigDOList = Arrays.asList(variableConfigDO1, variableConfigDO2);
        when(adminVariableConfigMapper.selectByIds(any())).thenReturn(variableConfigDOList);
        when(adminVariableConfigMapper.update(any(VariableConfigDO.class))).thenAnswer(invocation -> {
            VariableConfigDO argument = (VariableConfigDO)invocation.getArgument(0);
            return argument.getId() == 1 ? 1 : 0;
        });

        // 执行测试方法
        adminVariableServiceNew.submitApprove(adminVariableConfigVo);

        // 验证结果：应该同时调用updateBatch和审批流程
        verify(adminVariableConfigMapper, times(1)).selectByIds(any());
        verify(adminVariableConfigMapper, times(1)).update(any(VariableConfigDO.class));
        PowerMockito.verifyStatic(RiskApproveClient.class, times(1));
        Object submit = RiskApproveClient.submit(any(ApproveSubmitReq.class));
        System.out.println(submit);
    }


    /**
     * 测试场景：配置ID为空
     * 预期结果：抛出ValidateException异常
     */
    @Test(expected = ValidateException.class)
    public void testSubmitApprove_EmptyIds() {
        // 准备测试数据 - 空ID列表
        AdminVariableConfigVo adminVariableConfigVo = new AdminVariableConfigVo();
        adminVariableConfigVo.setIds(Lists.newArrayList());
        
        // 执行测试方法，应该抛出异常
        adminVariableServiceNew.submitApprove(adminVariableConfigVo);
    }

    /**
     * 测试场景：有审批中的配置
     * 预期结果：抛出CannotGetLockException异常
     */
    @Test(expected = CannotGetLockException.class)
    public void testSubmitApprove_AlreadyInApproval() {
        // 准备测试数据
        AdminVariableConfigVo adminVariableConfigVo = new AdminVariableConfigVo();
        adminVariableConfigVo.setIds(Lists.newArrayList("1"));
        adminVariableConfigVo.setStrategyType("testStrategy");
        adminVariableConfigVo.setStrategyNodeCode("testNode");
        Date date = new Date();
        adminVariableConfigVo.setAirRunningEndTime(new Date(date.getTime() + 1000 * 60 * 60 * 24));
        
        // 模拟数据库中的变量配置
        VariableConfigDO variableConfigDO = new VariableConfigDO();
        variableConfigDO.setId(1L);
        variableConfigDO.setVariableCode("testVar");
        variableConfigDO.setStrategyType("testStrategy");
        variableConfigDO.setStrategyNodeCode("testNode");
        variableConfigDO.setAirRunningStatus(VariableConfigDO.STATUS_DISABLE); // 停用状态
        
        List<VariableConfigDO> variableConfigDOList = Arrays.asList(variableConfigDO);
        when(adminVariableConfigMapper.selectByIds(any())).thenReturn(variableConfigDOList);
        
        // 模拟有审批中的配置
        PowerMockito.when(RiskApproveClient.acquireLastOpUser(anyString())).thenReturn("otherUser");
        
        // 执行测试方法，应该抛出异常
        adminVariableServiceNew.submitApprove(adminVariableConfigVo);
    }
}