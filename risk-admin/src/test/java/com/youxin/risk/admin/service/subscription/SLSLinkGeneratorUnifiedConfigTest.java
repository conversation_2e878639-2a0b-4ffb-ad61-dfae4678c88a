package com.youxin.risk.admin.service.subscription;

import com.youxin.risk.commons.utils.SLSLinkGenerator;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * SLSLinkGenerator 统一配置测试类
 * 
 * <AUTHOR> Assistant
 */
@RunWith(MockitoJUnitRunner.class)
public class SLSLinkGeneratorUnifiedConfigTest {

    private String mockUnifiedConfig;

    @Before
    public void setUp() {
        // 准备模拟的统一配置数据
        mockUnifiedConfig = "{\n" +
                "  \"apiverify\": {\n" +
                "    \"name\": \"API验证策略\",\n" +
                "    \"frequencyMinutes\": 60,\n" +
                "    \"totalPushes\": 3,\n" +
                "    \"monitors\": [\n" +
                "      {\n" +
                "        \"id\": \"dashboard-1747904265305-468192\",\n" +
                "        \"name\": \"API调用监控\",\n" +
                "        \"eventCode\": \"ApiVerify\",\n" +
                "        \"useDynamicLink\": true,\n" +
                "        \"slsConfig\": {\n" +
                "          \"dashboardName\": \"dashboard-1747904265305-468192\",\n" +
                "          \"token\": [\n" +
                "            {\n" +
                "              \"key\": \"date\",\n" +
                "              \"value\": \"60\"\n" +
                "            }\n" +
                "          ],\n" +
                "          \"extensions\": [\n" +
                "            {\n" +
                "              \"autoFresh\": \"30s\"\n" +
                "            }\n" +
                "          ]\n" +
                "        }\n" +
                "      },\n" +
                "      {\n" +
                "        \"id\": \"dashboard-1747904265305-468195\",\n" +
                "        \"name\": \"API性能监控\",\n" +
                "        \"eventCode\": \"ApiPerformance\",\n" +
                "        \"useDynamicLink\": true,\n" +
                "        \"slsConfig\": {\n" +
                "          \"dashboardName\": \"dashboard-1747904265305-468195\",\n" +
                "          \"token\": [\n" +
                "            {\n" +
                "              \"key\": \"date\",\n" +
                "              \"value\": \"120\"\n" +
                "            },\n" +
                "            {\n" +
                "              \"key\": \"service\",\n" +
                "              \"value\": \"api-gateway\"\n" +
                "            }\n" +
                "          ],\n" +
                "          \"extensions\": [\n" +
                "            {\n" +
                "              \"autoFresh\": \"60s\"\n" +
                "            }\n" +
                "          ]\n" +
                "        }\n" +
                "      }\n" +
                "    ]\n" +
                "  },\n" +
                "  \"strategyTypeB\": {\n" +
                "    \"name\": \"B类风控策略\",\n" +
                "    \"monitors\": [\n" +
                "      {\n" +
                "        \"id\": \"sls-003\",\n" +
                "        \"name\": \"风控效果监控\",\n" +
                "        \"eventCode\": \"RiskMonitor\",\n" +
                "        \"useDynamicLink\": true,\n" +
                "        \"slsConfig\": {\n" +
                "          \"dashboardName\": \"dashboard-1747904265305-468193\",\n" +
                "          \"token\": [\n" +
                "            {\n" +
                "              \"key\": \"date\",\n" +
                "              \"value\": \"120\"\n" +
                "            }\n" +
                "          ],\n" +
                "          \"extensions\": [\n" +
                "            {\n" +
                "              \"autoFresh\": \"60s\"\n" +
                "            }\n" +
                "          ]\n" +
                "        }\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "}";
    }

    @Test
    public void testIsEventCodeConfiguredInUnifiedConfig_Found() {
        // Mock NacosClient
        try (MockedStatic<com.youxin.risk.commons.utils.service.NacosClient> mockedNacos = 
                Mockito.mockStatic(com.youxin.risk.commons.utils.service.NacosClient.class)) {
            
            mockedNacos.when(() -> com.youxin.risk.commons.utils.service.NacosClient.getByNameSpace(
                    anyString(), anyString(), anyString()))
                    .thenReturn(mockUnifiedConfig);

            // 测试存在的事件代码
            boolean result = SLSLinkGenerator.isEventCodeConfiguredInUnifiedConfig("ApiVerify");
            assertTrue("应该找到 ApiVerify 事件代码", result);

            // 测试另一个存在的事件代码
            result = SLSLinkGenerator.isEventCodeConfiguredInUnifiedConfig("ApiPerformance");
            assertTrue("应该找到 ApiPerformance 事件代码", result);

            // 测试第三个存在的事件代码
            result = SLSLinkGenerator.isEventCodeConfiguredInUnifiedConfig("RiskMonitor");
            assertTrue("应该找到 RiskMonitor 事件代码", result);
        }
    }

    @Test
    public void testIsEventCodeConfiguredInUnifiedConfig_NotFound() {
        // Mock NacosClient
        try (MockedStatic<com.youxin.risk.commons.utils.service.NacosClient> mockedNacos = 
                Mockito.mockStatic(com.youxin.risk.commons.utils.service.NacosClient.class)) {
            
            mockedNacos.when(() -> com.youxin.risk.commons.utils.service.NacosClient.getByNameSpace(
                    anyString(), anyString(), anyString()))
                    .thenReturn(mockUnifiedConfig);

            // 测试不存在的事件代码
            boolean result = SLSLinkGenerator.isEventCodeConfiguredInUnifiedConfig("NonExistentEvent");
            assertFalse("不应该找到 NonExistentEvent 事件代码", result);
        }
    }

    @Test
    public void testIsEventCodeConfiguredInUnifiedConfig_EmptyConfig() {
        // Mock NacosClient 返回空配置
        try (MockedStatic<com.youxin.risk.commons.utils.service.NacosClient> mockedNacos = 
                Mockito.mockStatic(com.youxin.risk.commons.utils.service.NacosClient.class)) {
            
            mockedNacos.when(() -> com.youxin.risk.commons.utils.service.NacosClient.getByNameSpace(
                    anyString(), anyString(), anyString()))
                    .thenReturn("");

            // 测试空配置情况
            boolean result = SLSLinkGenerator.isEventCodeConfiguredInUnifiedConfig("ApiVerify");
            assertFalse("空配置时不应该找到任何事件代码", result);
        }
    }

    @Test
    public void testGetShareableLinkByStrategyAndMonitor() {
        // Mock NacosClient 和 SLS 相关方法
        try (MockedStatic<com.youxin.risk.commons.utils.service.NacosClient> mockedNacos = 
                Mockito.mockStatic(com.youxin.risk.commons.utils.service.NacosClient.class);
             MockedStatic<SLSLinkGenerator> mockedSLS = 
                Mockito.mockStatic(SLSLinkGenerator.class, Mockito.CALLS_REAL_METHODS)) {
            
            mockedNacos.when(() -> com.youxin.risk.commons.utils.service.NacosClient.getByNameSpace(
                    anyString(), anyString(), anyString()))
                    .thenReturn(mockUnifiedConfig);

            // Mock SLSLinkGenerator.getShareableLinkByConfig 方法
            String expectedLink = "https://sls.console.aliyun.com/lognext/project/risk-service-logs/dashboard/dashboard-1747904265305-468192?slsRegion=cn-beijing&sls_ticket=test-ticket";
            mockedSLS.when(() -> SLSLinkGenerator.getShareableLinkByConfig(anyString(), anyString(), anyString()))
                    .thenReturn(expectedLink);

            // 测试根据策略类型和监控ID生成链接
            String result = SLSLinkGenerator.getShareableLinkByStrategyAndMonitor("apiverify", "dashboard-1747904265305-468192");
            assertEquals("应该返回预期的链接", expectedLink, result);
        }
    }

    @Test
    public void testGetShareableLinkByEventCode_FromUnifiedConfig() {
        // Mock NacosClient
        try (MockedStatic<com.youxin.risk.commons.utils.service.NacosClient> mockedNacos =
                Mockito.mockStatic(com.youxin.risk.commons.utils.service.NacosClient.class);
             MockedStatic<SLSLinkGenerator> mockedSLS =
                Mockito.mockStatic(SLSLinkGenerator.class, Mockito.CALLS_REAL_METHODS)) {

            // 返回统一配置
            mockedNacos.when(() -> com.youxin.risk.commons.utils.service.NacosClient.getByNameSpace(
                    anyString(), anyString(), anyString()))
                    .thenReturn(mockUnifiedConfig);

            // Mock SLSLinkGenerator.getShareableLinkByConfig 方法
            String expectedLink = "https://sls.console.aliyun.com/test-link";
            mockedSLS.when(() -> SLSLinkGenerator.getShareableLinkByConfig(anyString(), anyList(), anyList()))
                    .thenReturn(expectedLink);

            // 测试通过事件代码生成链接（应该从统一配置中获取）
            String result = SLSLinkGenerator.getShareableLinkByEventCode("ApiVerify");
            assertEquals("应该从统一配置中获取并返回预期的链接", expectedLink, result);
        }
    }

    @Test
    public void testConfigFileNameCorrectness() {
        // 这个测试验证配置文件名是否正确
        try (MockedStatic<com.youxin.risk.commons.utils.service.NacosClient> mockedNacos = 
                Mockito.mockStatic(com.youxin.risk.commons.utils.service.NacosClient.class)) {
            
            mockedNacos.when(() -> com.youxin.risk.commons.utils.service.NacosClient.getByNameSpace(
                    anyString(), anyString(), anyString()))
                    .thenReturn(mockUnifiedConfig);

            // 调用方法触发配置获取
            SLSLinkGenerator.isEventCodeConfiguredInUnifiedConfig("ApiVerify");

            // 验证是否使用了正确的配置文件名
            mockedNacos.verify(() -> com.youxin.risk.commons.utils.service.NacosClient.getByNameSpace(
                    anyString(), 
                    "strategy-monitor-unified-config", // 验证使用了正确的配置文件名
                    anyString()));
        }
    }
}
