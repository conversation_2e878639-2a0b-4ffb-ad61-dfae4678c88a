<?xml version="1.0" encoding="UTF-8" ?>
<configuration>
    <appender name="info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- <File>${app.log.home}/${app.name}-info.eslog</File> -->
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.youxin.risk.commons.logback.RiskLogbackLayout">
                <Pattern>%d{"yyyy-MM-dd HH:mm:ss,SSS"}^|%p^|%t^|%tid^|%X{logid}^|%logger{0}^|%risk_method:%line^|%msg%n</Pattern>
            </layout>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>info</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${app.log.home}/${app.name}-info.eslog.%d{yyyy-MM-dd}</FileNamePattern>
        </rollingPolicy>
    </appender>

    <appender name="secInfo" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- <File>${app.log.home}/${app.name}-info.eslog</File> -->
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>%msg%n</pattern>
        </layout>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>info</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${app.log.home}/sec-operation-log-%d{yyyy-MM-dd}.log</FileNamePattern>
        </rollingPolicy>
    </appender>
    <appender name="async_info" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <appender-ref ref="info"/>
        <includeCallerData>true</includeCallerData>
    </appender>

    <appender name="async_secInfo" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <appender-ref ref="secInfo"/>
        <includeCallerData>true</includeCallerData>
    </appender>
    
    <appender name="warn" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- <File>${app.log.home}/${app.name}-warn.eslog</File> -->
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.youxin.risk.commons.logback.RiskLogbackLayout">
                <Pattern>%d{"yyyy-MM-dd HH:mm:ss,SSS"}^|%p^|%t^|%tid^|%X{logid}^|%logger{0}^|%risk_method:%line^|%msg%n</Pattern>
            </layout>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <OnMismatch>DENY</OnMismatch>
            <OnMatch>ACCEPT</OnMatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${app.log.home}/${app.name}-warn.eslog.%d{yyyy-MM-dd}</FileNamePattern>
        </rollingPolicy>
    </appender>

    <!-- 增加异步日志 -->
    <appender name="async_warn" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <appender-ref ref="warn"/>
        <includeCallerData>true</includeCallerData>
    </appender>
    
    <appender name="error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- <File>${app.log.home}/${app.name}-error.eslog</File> -->
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.youxin.risk.commons.logback.RiskLogbackLayout">
                <Pattern>%d{"yyyy-MM-dd HH:mm:ss,SSS"}^|%p^|%t^|%tid^|%X{logid}^|%logger{0}^|%risk_method:%line^|%msg%n</Pattern>
            </layout>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${app.log.home}/${app.name}-error.eslog.%d{yyyy-MM-dd}</FileNamePattern>
        </rollingPolicy>
    </appender>

    <!-- 增加异步日志 -->
    <appender name="async_error" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <appender-ref ref="error"/>
        <includeCallerData>true</includeCallerData>
    </appender>
    
     <appender name="console" class="ch.qos.logback.core.ConsoleAppender">  
         <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>${console.log.level}</level>
        </filter>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.youxin.risk.commons.logback.RiskLogbackLayout">
                <Pattern>%d{"yyyy-MM-dd HH:mm:ss,SSS"}^|%p^|%t^|%tid^|%X{logid}^|%logger{0}^|%risk_method:%line^|%msg%n</Pattern>
            </layout>
        </encoder>
    </appender>

    <!-- 增加异步日志 -->
    <appender name="async_console" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <appender-ref ref="console"/>
        <includeCallerData>true</includeCallerData>
    </appender>
    
    <logger name="org.apache.axis.ConfigurationException" level="INFO" />
    <logger name ="com.youxin.risk.admin.securitylog" level="INFO">
        <appender-ref ref="async_secInfo" />
    </logger>
    <logger name ="com" level="info">
        <appender-ref ref="async_info" />
        <appender-ref ref="async_warn" />
        <appender-ref ref="async_error" />
        <appender-ref ref="async_console" />
    </logger>

    <logger name ="org" level="info">
        <appender-ref ref="async_info" />
        <appender-ref ref="async_warn" />
        <appender-ref ref="async_error" />
        <appender-ref ref="async_console" />
    </logger>
</configuration>
