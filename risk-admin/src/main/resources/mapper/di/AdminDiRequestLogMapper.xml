<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youxin.risk.admin.dao.di.AdminDiRequestLogMapper">
  <resultMap id="BaseResultMap" type="com.youxin.risk.admin.model.AdminDiRequestLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="source_system" jdbcType="VARCHAR" property="sourceSystem" />
    <result column="request_id" jdbcType="VARCHAR" property="requestId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="is_sync_finished" jdbcType="INTEGER" property="isSyncFinished" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, source_system, request_id, status, is_sync_finished, create_time, update_time
  </sql>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from di_request_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <select id="selectRequestLogTotal" parameterType="com.youxin.risk.admin.vo.AdminDiRequestLogVo" resultType="int">
  	select 
  	count(1)
  	from di_request_log
  	<where>
  	<if test="sourceSystem != null">
  		and source_system = #{sourceSystem}
  	</if>
  	<if test="requestId != null">
  		and request_id = #{requestId}
  	</if>
  	<if test="status != null">
  		and status = #{status}
  	</if>
  	<if test="isSyncFinished != null">
  		and is_sync_finished = #{isSyncFinished}
  	</if>
  	<if test="startTime != null and endTime != null">
  		and create_time &gt; #{startTime} and create_time &lt; #{endTime}
  	</if>
  	</where>
  	order by id desc
  </select>
  <select id="selectAllRequestLog" parameterType="com.youxin.risk.admin.vo.AdminDiRequestLogVo" resultMap="BaseResultMap">
  	select 
  	<include refid="Base_Column_List" />
  	from di_request_log
  	<where>
  	<if test="sourceSystem != null">
  		and source_system = #{sourceSystem}
  	</if>
  	<if test="requestId != null">
  		and request_id = #{requestId}
  	</if>
  	<if test="status != null">
  		and status = #{status}
  	</if>
  	<if test="isSyncFinished != null">
  		and is_sync_finished = #{isSyncFinished}
  	</if>
  	<if test="startTime != null and endTime != null">
  		and create_time &gt; #{startTime} and create_time &lt; #{endTime}
  	</if>
  	</where>
  	order by id desc
  	limit ${startIndex},${pageSize}
  </select>
</mapper>