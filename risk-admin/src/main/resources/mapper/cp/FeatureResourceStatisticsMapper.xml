<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.admin.dao.cp.FeatureResourceStatisticsMapper">
  <resultMap id="BaseResultMap" type="com.youxin.risk.admin.model.cp.FeatureResourceStatistics">
    <id column="id" property="id" jdbcType="BIGINT"/>
    <result column="feature_code" property="featureCode" jdbcType="VARCHAR"/>
    <result column="event_code" property="eventCode" jdbcType="VARCHAR"/>
    <result column="step" property="step" jdbcType="VARCHAR"/>
    <result column="feature_name" property="featureName" jdbcType="VARCHAR"/>
    <result column="tp90_lweek_total_time" property="tp90LweekTotalTime" jdbcType="VARCHAR"/>
    <result column="creator" property="creator" jdbcType="VARCHAR"/>
    <result column="dept" property="dept" jdbcType="VARCHAR"/>
    <result column="yday_cals" property="ydayCals" jdbcType="VARCHAR"/>
    <result column="lweek_cals" property="lweekCals" jdbcType="VARCHAR"/>
    <result column="mean_time" property="meanTime" jdbcType="VARCHAR"/>
    <result column="lweek_total_time" property="lweekTotalTime" jdbcType="VARCHAR"/>
    <result column="max_time" property="maxTime" jdbcType="VARCHAR"/>
    <result column="min_time" property="minTime" jdbcType="VARCHAR"/>
    <result column="tp99" property="tp99" jdbcType="VARCHAR"/>
    <result column="tp90" property="tp90" jdbcType="VARCHAR"/>
    <result column="tp70" property="tp70" jdbcType="VARCHAR"/>
    <result column="datasource_TP90" property="datasourceTP90" jdbcType="VARCHAR"/>
    <result column="datasource_TP99" property="datasourceTP99" jdbcType="VARCHAR"/>
    <result column="datasource_max" property="datasourceMax" jdbcType="VARCHAR"/>
    <result column="last_online_time" property="lastOnlineTime" jdbcType="VARCHAR"/>
    <result column="resource_percent" property="resourcePercent" jdbcType="VARCHAR"/>
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, feature_code, event_code, step,description, feature_name, tp90_lweek_total_time,creator,
    dept,yday_cals,lweek_cals,mean_time,lweek_total_time,max_time,min_time,tp99,tp90,tp70,datasource_TP90,datasource_TP99,datasource_max,last_online_time,
    resource_percent,create_time,update_time
  </sql>

  <insert id="insert" parameterType="com.youxin.risk.admin.model.cp.FeatureResourceStatistics">
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
  insert into feature_resource_statistics(feature_code, event_code, step,description, feature_name, tp90_lweek_total_time,creator,
  dept,yday_cals,lweek_cals,mean_time,lweek_total_time,max_time,min_time,tp99,tp90,tp70,datasource_TP90,datasource_TP99,datasource_max,last_online_time,
  resource_percent,create_time,update_time)
    alues ( #{featureCode,jdbcType=VARCHAR},
    #{eventCode,jdbcType=VARCHAR},
    #{step,jdbcType=INTEGER},
    #{featureName,jdbcType=VARCHAR},
    #{tp90LweekTotalTime,jdbcType=VARCHAR},
    #{creator,jdbcType=VARCHAR},
    #{dept,jdbcType=INTEGER},
    #{ydayCals,jdbcType=VARCHAR},
    #{lweekCals,jdbcType=VARCHAR},
    #{meanTime,jdbcType=VARCHAR},
    #{lweekTotalTime,jdbcType=VARCHAR},
    #{maxTime,jdbcType=VARCHAR},
    #{minTime,jdbcType=VARCHAR},
    #{tp99,jdbcType=VARCHAR},
    #{tp90,jdbcType=VARCHAR},
    #{tp70,jdbcType=VARCHAR},
    #{datasourceTP90,jdbcType=VARCHAR},
    #{datasourceTP99,jdbcType=VARCHAR},
    #{datasourceMax,jdbcType=VARCHAR},
    #{lastOnlineTime,jdbcType=VARCHAR},
    #{resourcePercent,jdbcType=VARCHAR},
    now(),
    now())
  </insert>

</mapper>
