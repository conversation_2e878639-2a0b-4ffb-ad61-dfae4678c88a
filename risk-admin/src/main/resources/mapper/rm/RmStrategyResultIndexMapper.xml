<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.admin.dao.rm.RmStrategyResultIndexMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.admin.dto.rulescore.RmStrategyResultIndexDTO" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="user_key" property="userKey" jdbcType="VARCHAR" />
        <result column="loan_key" property="loanKey" jdbcType="VARCHAR" />
        <result column="source_system" property="sourceSystem" jdbcType="VARCHAR" />
        <result column="step" property="step" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="event_code" property="eventCode" jdbcType="VARCHAR" />
        <result column="session_id" property="sessionId" jdbcType="VARCHAR" />
        <result column="row_key" property="rowKey" jdbcType="VARCHAR" />
        <result column="strategy_code_id" property="strategyCodeId" jdbcType="INTEGER" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <select id="getRowKeysByConditions" resultMap="BaseResultMap">
        select row_key,step from rm_strategy_result_index
        <where>
            <if test="stepList != null and stepList.size() > 0">
                and step in
                <foreach collection="stepList" item="step" index="index" open="(" close=")" separator=",">
                    #{step}
                </foreach>
            </if>
            <if test="loanKey!=null and loanKey!=''">
                AND loan_key = #{loanKey}
            </if>
        </where>
        limit 1
    </select>

    <select id="getRowKeysByType" resultMap="BaseResultMap">
        select row_key,step,loan_key from rm_strategy_result_index
        <where>
            <if test="typeList != null and typeList.size() > 0">
                and type in
                <foreach collection="typeList" item="type" index="index" open="(" close=")" separator=",">
                    #{type}
                </foreach>
            </if>
        </where>
        order by create_time desc
        limit 200
    </select>

    <select id="getRowKeysByNode" resultMap="BaseResultMap">
        select row_key,step,loan_key from rm_strategy_result_index
        <where>
            <if test="nodeList != null and nodeList.size() > 0">
                and step in
                <foreach collection="nodeList" item="step" index="index" open="(" close=")" separator=",">
                    #{step}
                </foreach>
            </if>
        </where>
        order by create_time desc
        limit 200
    </select>

    <select id="getResultByTypeAndCreateTime" parameterType="java.util.HashMap" resultMap="BaseResultMap">
        select row_key,user_key,loan_key,event_code,step from rm_strategy_result_index
        <where>
            <if test="strategyType!=null and strategyType!=''">
                AND `type` = #{strategyType}
            </if>
            <if test="createTimeStart!=null and createTimeStart!=''">
                AND create_time between #{createTimeStart} and #{createTimeEnd}
            </if>
        </where>
        order by id desc
        limit 100
    </select>

    <select id="getResultByConditions" parameterType="java.util.HashMap" resultMap="BaseResultMap">
        select row_key,user_key,loan_key,event_code,step,`type` from rm_strategy_result_index
        <where>
            <if test="loanKey!=null and loanKey!=''">
                AND loan_key = #{loanKey}
            </if>
            <if test="userKey!=null and userKey!=''">
                AND user_key = #{userKey}
            </if>

            <if test="eventCode!=null and eventCode!=''">
                AND event_code = #{eventCode}
            </if>
            <if test="step!=null and step!=''">
                AND step = #{step}
            </if>
        </where>
        order by id desc
        limit #{limit}
    </select>
</mapper>