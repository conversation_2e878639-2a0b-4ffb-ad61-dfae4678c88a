<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018 Baidu, Inc. All Rights Reserved.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youxin.risk.admin.dao.rm.AdminStrategyCandidateMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.admin.model.AdminStrategyCandidate">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="source_system" property="sourceSystem" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="developer" property="developer" jdbcType="VARCHAR"/>
        <result column="strategy_version" property="strategyVersion" />
        <result column="strategy_code_id" property="strategyCodeId" />
        <result column="remark" property="remark" />
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="model_id" property="modelId" jdbcType="VARCHAR"/>
        <result column="risk_approval" property="riskApproval" jdbcType="BIT"/>
        <result column="business_approval" property="businessApproval" jdbcType="BIT"/>
        <result column="manager_approval" property="managerApproval" jdbcType="BIT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="python_version" property="pythonVersion" jdbcType="VARCHAR"/>
        <result column="project_id" property="projectId" jdbcType="BIGINT"/>
        <result column="group_name" property="groupName" jdbcType="VARCHAR"/>
        <result column="engine_version" property="engineVersion" jdbcType="INTEGER"/>
        <result column="strategy_apply_info" jdbcType="VARCHAR" property="strategyApplyInfo" />
    </resultMap>

    <sql id="Base_Column_List">
    id,source_system,type,developer,strategy_version,strategy_code_id,status,create_time,update_time,remark,model_id,risk_approval,business_approval,manager_approval,python_version,project_id,group_name,strategy_apply_info
    </sql>

    <insert id="insert" parameterType="com.youxin.risk.admin.model.AdminStrategyCandidate" useGeneratedKeys="true" keyProperty="id">
    insert into   rm_strategy_candidate (
      source_system,
      type,
      developer,
      strategy_version,
      strategy_code_id,
      status,
      remark,
      model_id,
      risk_approval,
      business_approval,
      manager_approval,
      python_version,
      project_id,
      group_name,
      engine_version
    ) values (
        #{sourceSystem},
        #{type},
        #{developer},
        #{strategyVersion},
        #{strategyCodeId},
        #{status},
        #{remark},
        #{modelId},
        #{riskApproval},
        #{businessApproval},
        #{managerApproval},
        #{pythonVersion},
        #{projectId},
        #{groupName},
        #{engineVersion}
    )
  </insert>

    <update id="update" parameterType="com.youxin.risk.admin.model.AdminStrategyCandidate">
        update   rm_strategy_candidate
        <set>
            <if test="sourceSystem != null">
                source_system = #{sourceSystem},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="developer != null">
                developer = #{developer},
            </if>
            <if test="strategyVersion != null">
                strategy_version = #{strategyVersion},
            </if>
            <if test="strategyCodeId != null">
                strategy_code_id = #{strategyCodeId},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>

            <if test="modelId != null">
                model_id = #{modelId},
            </if>
            <if test="riskApproval != null">
                risk_approval = #{riskApproval},
            </if>
            <if test="businessApproval != null">
                business_approval = #{businessApproval},
            </if>
            <if test="managerApproval != null">
                manager_approval = #{managerApproval},
            </if>
            <if test="strategyApplyInfo != null">
                strategy_apply_info = #{strategyApplyInfo},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="delete" parameterType="java.lang.Long">
        delete from   rm_strategy_candidate
        where id = #{id}
    </delete>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from   rm_strategy_candidate
        where id = #{id}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from   rm_strategy_candidate
        order by id desc
    </select>

    <sql id="common_where_condition">
        <if test="sourceSystem != null">
            and source_system = #{sourceSystem}
        </if>
        <if test="type != null">
            and type LIKE #{typePattern}
        </if>
        <if test="developer != null">
            and developer LIKE CONCAT('%',#{developer},'%')
        </if>
        <if test="strategyVersion != null">
            and strategy_version = #{strategyVersion}
        </if>
        <if test="strategyCodeId != null">
            and strategy_code_id = #{strategyCodeId}
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="remark != null">
            and remark = #{remark}
        </if>
        <if test="modelId != null">
            and model_id = #{modelId}
        </if>
        <if test="riskApproval != null">
            and risk_approval = #{riskApproval}
        </if>
        <if test="businessApproval != null">
            and business_approval = #{businessApproval}
        </if>
        <if test="managerApproval != null">
            and manager_approval = #{managerApproval}
        </if>
    </sql>

    <select id="selectCount" resultType="java.lang.Integer" parameterType="java.util.Map">
        <if test="type != null">
          <bind name="typePattern" value="type+'%'"/>
        </if>
        select
          count(1)
        from   rm_strategy_candidate
        <where>
            <include refid="common_where_condition"/>
        </where>
    </select>

    <select id="selectList" resultMap="BaseResultMap" parameterType="java.util.Map">
        <if test="type != null">
            <bind name="typePattern" value="type+'%'"/>
        </if>
        select
        <include refid="Base_Column_List" />
        from   rm_strategy_candidate
        <where>
            <include refid="common_where_condition"/>
        </where>
        order by id desc
        limit #{start}, #{limit}
    </select>

    <select id="selectTypesByPrefix" resultType="java.lang.String" parameterType="java.util.Map">
        <if test="type != null">
            <bind name="typePattern" value="type+'%'"/>
        </if>
        select type from  rm_strategy_candidate
        <where>
            <if test="type != null">
                type LIKE #{typePattern}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER  BY  type ASC
    </select>

    <select id="getWaitOnlineCountByType" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(*) from  rm_strategy_candidate
        where type like CONCAT(#{type}, '_20', '%')
        and status not in ('ONLINE', 'DROPPED')
    </select>

    <select id="selectByTypeList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rm_strategy_candidate
        where type in
        <foreach collection="typeList" item="type" index="index" open="(" separator="," close=")">
            #{type}
        </foreach>
    </select>
    <select id="selectTypeByIds" resultType="java.lang.String">
        select
        type
        from rm_strategy_candidate
        where id in
        <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectByType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rm_strategy_candidate
        where type = #{type}
    </select>

    <select id="selectByGroup" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM rm_strategy_candidate
        WHERE group_name = #{group}
    </select>

    <sql id="common_page_sql">
        SELECT
        IF( @GROUP_NAME=a.group_name,  @curRank := @curRank +1 ,@curRank := 1  )  AS row_cnt,
        @ID := a.id AS id,
        @SOURCESYSTEM := a.source_system AS source_system,
        @TYPE := a.type AS type,
        @DEVELOPER := a.developer AS developer,
        @STRATEGY_VERSION := a.strategy_version AS strategy_version,
        @STRATEGY_CODE_ID := a.strategy_code_id AS strategy_code_id,
        @REMARK := a.remark AS remark,
        @STATUSS := a.`status` AS `status`,
        @MODEL_ID := a.model_id AS model_id,
        @RISK_APROVAL := a.risk_approval AS risk_approval,
        @BUSINESS_APPROVAL := a.business_approval AS business_approval,
        @MANAGER_APPROVAL := a.manager_approval AS manager_approval,
        @CREATE_TIME := a.create_time AS create_time,
        @UPDATE_TIME := a.update_time AS update_time,
        @PYTHON_VERSION := a.python_version AS python_version,
        @PROJECT_ID := a.project_id AS project_id,
        @GROUP_NAME := a.group_name AS group_name,
		@TEST_ENGINE_TYPE :=a.engine_version as engine_version
        FROM
        (SELECT * FROM rm_strategy_candidate) a,
        (SELECT  @GROUP_NAME := null, @curRank := 0) t_temp
    </sql>

    <!--现在mysql版本为5.6.40 不支持窗口函数 使用下面这种方式代替-->
    <!--https://blog.csdn.net/Janny2015/article/details/118420894?utm_medium=distribute.pc_relevant_t0.none-task-blog-2%7Edefault%7ECTRLIST%7Edefault-1.no_search_link&depth_1-utm_source=distribute.pc_relevant_t0.none-task-blog-2%7Edefault%7ECTRLIST%7Edefault-1.no_search_link-->
    <select id="selectPageList" resultMap="BaseResultMap">
        <if test="type != null">
            <bind name="typePattern" value="type+'%'"/>
        </if>
        SELECT * FROM( <include refid="common_page_sql"/> ) t
        WHERE t.row_cnt = 1 <include refid="common_where_condition"/>
        ORDER BY id desc
        LIMIT #{start}, #{limit}
    </select>

    <select id="selectPageCount" resultType="java.lang.Long">
        <if test="type != null">
            <bind name="typePattern" value="type+'%'"/>
        </if>
        SELECT count(1) FROM( <include refid="common_page_sql"/> ) t
        WHERE t.row_cnt = 1 <include refid="common_where_condition"/>
    </select>

    <select id="selectWaitOnlineRecords" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rm_strategy_candidate
        where status in ('WAIT_ONLINE', 'WAIT_APPROVE', 'IN_APPROVAL')
        AND left(type,length(type)-15) IN
        <foreach collection="types" item="type" index="index" open="(" separator="," close=")">
            #{type}
        </foreach>
    </select>

    <update id="updateStatus" parameterType="com.youxin.risk.admin.model.AdminStrategyCandidate">
        update rm_strategy_candidate
        <set>
            <if test="sourceSystem != null">
                source_system = #{sourceSystem},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="developer != null">
                developer = #{developer},
            </if>
            <if test="strategyVersion != null">
                strategy_version = #{strategyVersion},
            </if>
            <if test="strategyCodeId != null">
                strategy_code_id = #{strategyCodeId},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="modelId != null">
                model_id = #{modelId},
            </if>
            <if test="riskApproval != null">
                risk_approval = #{riskApproval},
            </if>
            <if test="businessApproval != null">
                business_approval = #{businessApproval},
            </if>
            <if test="managerApproval != null">
                manager_approval = #{managerApproval},
            </if>
        </set>
        where id = #{id} and status != 'WAIT_ONLINE'
    </update>
    <select id="selectOnlineByType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from rm_strategy_candidate
        where type like CONCAT(#{type}, '_20', '%')
        and status = 'ONLINE'
        order by id desc limit 1
    </select>
    <update id="updateStatusRoll">
        update rm_strategy_candidate set status = 'DROPPED'
        where id = #{id} and status = 'ONLINE'
    </update>
    <select id="selectByTypeAndStatus" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from rm_strategy_candidate
        where type like CONCAT(#{type}, '_20', '%')
        AND status = #{status}
        order by create_time desc
        limit 2
    </select>
    <select id="selectByStrategyVersion" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from rm_strategy_candidate
        where strategy_version = #{strategyVersion}
        order by id desc
        limit 1
    </select>
</mapper>