<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018 Baidu, Inc. All Rights Reserved.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youxin.risk.admin.dao.admin.AdminStrategyExperimentMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.admin.model.AdminStrategyExperiment">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="source_system" property="sourceSystem" jdbcType="VARCHAR"/>
        <result column="exp_code" property="expCode" jdbcType="VARCHAR"/>
        <result column="exp_name" property="expName" jdbcType="VARCHAR"/>
        <result column="exp_type" property="expType" jdbcType="VARCHAR"/>
        <result column="online_strategy_code" property="onlineStrategyCode" jdbcType="VARCHAR"/>
        <result column="experiment_strategy_code" property="experimentStrategyCode" jdbcType="VARCHAR"/>
        <result column="begin_time" property="beginTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="exp_flow_percent" property="expFlowPercent" jdbcType="INTEGER"/>
        <result column="cmp_field" property="cmpField" jdbcType="VARCHAR"/>
        <result column="cmp_type" property="cmpType" jdbcType="VARCHAR"/>
        <result column="group_name" property="groupName" jdbcType="VARCHAR"/>
        <result column="operator" property="operator" jdbcType="VARCHAR"/>
        <result column="need_removed_features" property="needRemovedFeatures" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="exp_number" property="expNumber" jdbcType="INTEGER"/>
        <result column="stop_type" property="stopType" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
    id,source_system,exp_code,exp_name,exp_type,online_strategy_code,experiment_strategy_code,begin_time,end_time,status,
        exp_flow_percent,cmp_field,cmp_type, group_name,need_removed_features,operator,create_time,update_time,exp_number,stop_type
    </sql>

    <sql id="common_where_condition">
        <if test="onlineStrategyCode != null">
            and online_strategy_code = #{onlineStrategyCode}
        </if>
        <if test="expCode != null">
            and exp_code like CONCAT('%',#{expCode},'%')
        </if>
        <if test="expName != null">
            and exp_name like CONCAT('%',#{expName},'%')
        </if>
        <if test="expType != null">
            and exp_type = #{expType}
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="sourceSystem != null">
            and source_system = #{sourceSystem}
        </if>
        <if test="operator != null">
            and operator like CONCAT('%',#{operator},'%')
        </if>
        <if test="stopType != null">
            and stop_type =#{stopType}
        </if>
    </sql>

    <insert id="insert" parameterType="com.youxin.risk.admin.model.AdminStrategyExperiment" useGeneratedKeys="true"
            keyProperty="id">
    insert into admin_strategy_experiment (
      source_system,
      exp_code,
      exp_name,
      exp_type,
      online_strategy_code,
      experiment_strategy_code,
      begin_time,
      end_time,
      status,
      exp_flow_percent,
      cmp_field,
      cmp_type,
      group_name,
      operator,
      need_removed_features,
      exp_number,
      stop_type
    ) values (
      #{sourceSystem},
      #{expCode},
      #{expName},
      #{expType},
      #{onlineStrategyCode},
      #{experimentStrategyCode},
      #{beginTime},
      #{endTime},
      #{status},
      #{expFlowPercent},
      #{cmpField},
      #{cmpType},
      #{groupName},
      #{operator},
      #{needRemovedFeatures},
      #{expNumber},
      #{stopType}
    )
  </insert>

    <update id="update" parameterType="com.youxin.risk.admin.model.AdminStrategyExperiment">
        update admin_strategy_experiment
        <set>
            <if test="sourceSystem != null">
                source_system = #{sourceSystem},
            </if>
            <if test="expName != null">
                exp_name = #{expName},
            </if>
            <if test="expType != null">
                exp_type = #{expType},
            </if>
            <if test="onlineStrategyCode != null">
                online_strategy_code = #{onlineStrategyCode},
            </if>
            <if test="experimentStrategyCode != null">
                experiment_strategy_code = #{experimentStrategyCode},
            </if>
            <if test="beginTime != null">
                begin_time = #{beginTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="expFlowPercent != null">
                exp_flow_percent = #{expFlowPercent},
            </if>
            <if test="cmpField != null">
                cmp_field = #{cmpField},
            </if>
            <if test="cmpType != null">
                cmp_type = #{cmpType},
            </if>
            <if test="needRemovedFeatures != null">
                need_removed_features = #{needRemovedFeatures},
            </if>
            <if test="expNumber != null">
                exp_number =#{expNumber},
            </if>
            <if test="stopType != null">
                stop_type =#{stopType}
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="delete" parameterType="java.lang.Long">
        delete from admin_strategy_experiment
        where id = #{id}
    </delete>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from admin_strategy_experiment
        where id = #{id}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin_strategy_experiment
        order by id desc
    </select>

    <select id="selectCount" resultType="java.lang.Integer" parameterType="java.util.Map">
        select
          count(1)
        from admin_strategy_experiment
        <where>
            <include refid="common_where_condition"/>
        </where>
    </select>

    <select id="selectList" resultMap="BaseResultMap" parameterType="java.util.Map">
        select
        <include refid="Base_Column_List" />
        from admin_strategy_experiment
        <where>
            <include refid="common_where_condition"/>
        </where>
        order by id desc
        limit #{start}, #{limit}
    </select>

    <select id="selectForEnable" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from admin_strategy_experiment
        where status = "DISABLE"
        and begin_time &lt;= now()
        and end_time &gt; now()
    </select>

    <select id="selectForDisable" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from admin_strategy_experiment
        where status = "ENABLE"
        and end_time &lt;= now()
        and stop_type='TIMER'
    </select>

    <update id="batchUpdateStatus">
        update admin_strategy_experiment
        set status = #{status}
        where id in
        <foreach collection="idList" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <sql id="common_page_sql">
        SELECT
        IF( @GROUP_NAME=a.group_name,  @curRank := @curRank +1 ,@curRank := 1  )  AS row_cnt,
        @ID := a.id AS id,
        @SOURCE_SYSTEM := a.source_system AS source_system,
        @EXP_CODE := a.exp_code AS exp_code,
        @EXP_NAME := a.exp_name AS exp_name,
        @EXP_TYPE := a.exp_type AS exp_type,
        @ONLINE_STRATEGY_CODE := a.online_strategy_code AS online_strategy_code,
        @EXPERIMENT_STRATEGY_CODE := a.experiment_strategy_code AS experiment_strategy_code,
        @BEGIN_TIME := a.begin_time AS begin_time,
        @END_TIME := a.end_time AS end_time,
        @STATUS := a.`status` AS `status`,
        @EXP_FLOW_PERCENT := a.exp_flow_percent AS exp_flow_percent,
        @CMP_FIELD := a.cmp_field AS cmp_field,
        @CMP_TYPE := a.cmp_type AS cmp_type,
        @GROUP_NAME := a.group_name AS group_name,
        @OPERATOR := a.operator AS operator,
        @UPDATE_TIME := a.update_time AS update_time,
        @CREATE_TIME := a.create_time AS create_time,
        @EXP_NUMBER := a.exp_number AS exp_number,
        @STOP_TYPE := a.stop_type AS stop_type
        FROM
        (SELECT * FROM admin_strategy_experiment) a,
        (SELECT  @GROUP_NAME := null, @curRank := 0) t_temp
    </sql>

    <!--现在mysql版本为5.6.40 不支持窗口函数 使用下面这种方式代替-->
    <!--https://blog.csdn.net/Janny2015/article/details/118420894?utm_medium=distribute.pc_relevant_t0.none-task-blog-2%7Edefault%7ECTRLIST%7Edefault-1.no_search_link&depth_1-utm_source=distribute.pc_relevant_t0.none-task-blog-2%7Edefault%7ECTRLIST%7Edefault-1.no_search_link-->
    <select id="selectPageList" resultMap="BaseResultMap">
        SELECT * FROM ( <include refid="common_page_sql"/> ) t
        WHERE t.row_cnt = 1 <include refid="common_where_condition"/>
        ORDER BY id desc
        LIMIT #{start}, #{limit}
    </select>

    <select id="selectPageCount" resultType="java.lang.Long">
        SELECT count(1) FROM( <include refid="common_page_sql"/> ) t
        WHERE t.row_cnt = 1 <include refid="common_where_condition"/>
    </select>

    <select id="selectByGroup" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM admin_strategy_experiment
        WHERE group_name = #{group}
    </select>
    <select id="getAdminStrategyExperiment" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM admin_strategy_experiment
        WHERE experiment_strategy_code = #{experimentStrategyCode}
    </select>
    <select id="getAdminStrategyExpByExpCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM admin_strategy_experiment
        WHERE exp_code = #{expCode}
    </select>
</mapper>