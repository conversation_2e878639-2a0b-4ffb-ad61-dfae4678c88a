<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youxin.risk.admin.dao.admin.AdminProcessDefinitionMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.ProcessDefinition">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="process_def_id" property="processDefId" jdbcType="VARCHAR"/>
        <result column="process_name" property="processName" jdbcType="VARCHAR"/>
        <result column="process_content" property="processContent" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="process_json" property="processJson" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="in_use" property="inUse" jdbcType="TINYINT"/>
        <result column="referrence_id" property="referrenceId" jdbcType="VARCHAR"/>
        <result column="serial_node" property="serialNode" jdbcType="VARCHAR"/>
        <result column="sub_process" property="subProcess" jdbcType="TINYINT"/>
        <result column="strategy_type" property="strategyType" jdbcType="VARCHAR"/>
        <result column="referrence_process_def_ids" property="referrenceProcessDefIds" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , process_def_id, process_name, process_content, status, create_time, update_time,process_json,version
        ,in_use,referrence_id,serial_node,sub_process,strategy_type, referrence_process_def_ids
    </sql>

    <insert id="insert" parameterType="com.youxin.risk.commons.model.ProcessDefinition">
        insert into admin_process_definition (process_def_id,
                                              process_name,
                                              process_content,
                                              status,
                                              process_json,
                                              version,
                                              in_use,
                                              referrence_id,
                                              serial_node,
                                              sub_process,
                                              strategy_type,
                                              referrence_process_def_ids)
        values (#{processDefId},
                #{processName},
                #{processContent},
                #{status},
                #{processJson},
                #{version},
                #{inUse},
                #{referrenceId},
                #{serialNode},
                #{subProcess},
                #{strategyType},
                #{referrenceProcessDefIds});

    </insert>

    <update id="update" parameterType="com.youxin.risk.commons.model.ProcessDefinition">
        update admin_process_definition
        <set>
            <if test="processName != null">
                process_name = #{processName},
            </if>
            <if test="processContent != null">
                process_content = #{processContent},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="processJson != null">
                process_json = #{processJson},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=INTEGER},
            </if>
            <if test="serialNode != null">
                serial_node = #{serialNode},
            </if>
            <if test="referrenceProcessDefIds != null">
                referrence_process_def_ids = #{referrenceProcessDefIds},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="delete" parameterType="java.lang.Long">
        delete
        from admin_process_definition
        where id = #{id}
    </delete>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from admin_process_definition
        where id = #{id}
    </select>

    <select id="getByProcessDefId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from admin_process_definition
        where process_def_id = #{processDefId}
    </select>

    <select id="getMaxVersion" resultType="int" parameterType="java.lang.String">
        select max(version)
        from admin_process_definition
        where referrence_id = #{referrenceId}
    </select>

    <select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.Map">
        select
        <include refid="Base_Column_List"/>
        from admin_process_definition
        <where>
            <if test="processDefId != null">
                and process_def_id = #{processDefId}
            </if>
            <if test="processName != null">
                and process_name = #{processName}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectIds" resultType="java.lang.Integer" parameterType="java.util.Map">
        select
        max(id) as id
        from admin_process_definition
        <where>
            <if test="referrenceId != null">
                and referrence_id like CONCAT('%',#{referrenceId},'%')
            </if>
            <if test="processName != null">
                and process_name like CONCAT('%',#{processName},'%')
            </if>
        </where>
        group by referrence_id order by id desc
    </select>

    <select id="selectListById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin_process_definition
        <where>
            <if test="ids != null">
                and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        order by id desc
        limit #{start}, #{limit}
    </select>

    <select id="selectForEvent" resultMap="BaseResultMap">
        select id,
               process_def_id,
               process_name,
               referrence_id
        from admin_process_definition
        where status = 'ENABLE'
        order by id desc
    </select>

    <select id="selectGraphEvent" resultMap="BaseResultMap">
        select t0.id,
               t1.id as id2,
               t0.process_def_id,
               t0.process_name,
               t0.referrence_id
        from admin_process_definition t0
                 join (
            select max(id) as id,
                   referrence_id
            from admin_process_definition
            where status = 'ENABLE'
              and process_json is not null
            group by referrence_id
        ) t1 on t0.referrence_id = t1.referrence_id and t0.id = t1.id
        order by t0.id desc;
    </select>
    <select id="selectEffectives" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin_process_definition
        where status = 'ENABLE' and in_use = 1
        order by id desc
    </select>


    <select id="selectEventStepList" resultType="java.util.Map">
        SELECT a.event_code,a.event_name, b.process_content, b.process_def_id, a.source_system
        from admin_event_info a
        LEFT JOIN admin_process_definition b
        on a.process_def_id = b.process_def_id
        <where>
            <if test="sourceSystemList != null">
                and a.source_system in
                <foreach collection="sourceSystemList" item="sourcesystem" index="index" open="(" close=")"
                         separator=",">
                    #{sourcesystem}
                </foreach>
            </if>
        </where>
        ORDER BY a.id DESC
    </select>

    <select id="getVersions" resultMap="BaseResultMap" parameterType="java.lang.String">
        select <include refid="Base_Column_List"/>
        from admin_process_definition
        where referrence_id = #{referrenceId}
    </select>

    <select id="getByReferrenceId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin_process_definition
        where referrence_id = #{referrenceId}
        and version = #{version}
    </select>

    <select id="getInUse" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin_process_definition
        where referrence_id = #{referrenceId}
        and in_use = 1
        order by id desc
        limit 1
    </select>

    <update id="updateInUse" parameterType="java.lang.String">
        update admin_process_definition
        <set>
            in_use = 0,
        </set>
        where referrence_id = #{referrenceId}
    </update>

    <update id="updateInUseTrue">
        update admin_process_definition
        <set>
            in_use = 1,
        </set>
        where referrence_id = #{referrenceId}
        and version = #{version}
    </update>

    <update id="updateInUseFalse">
        update admin_process_definition
        <set>
            in_use = 0,
        </set>
        where referrence_id = #{referrenceId}
        and version = #{version}
    </update>

    <update id="updateInUseFalseForProcessDefId">
        update admin_process_definition
        <set>
            in_use = 0,
        </set>
        where process_def_id = #{processDefId}
    </update>

    <update id="updateDisable">
        update admin_process_definition
        <set>
            status = 'DISABLE',
        </set>
        where referrence_id = #{referrenceId} and version = #{version}
    </update>

    <select id="getByReferrenceIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin_process_definition
        <where>
            <if test="referrenceIds != null">
                and referrence_id in
                <foreach collection="referrenceIds" item="referrenceId" index="index" open="(" close=")" separator=",">
                    #{referrenceId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectAllByStrategyTypeIsNotNull" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin_process_definition
        where strategy_type is not null and status != 'DISABLE'
    </select>
    <select id="selectAllByStrategyType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin_process_definition
        where strategy_type = #{strategyType}
    </select>
    <select id="selectProcessDefIdBySubProcess" resultType="String">
        select process_def_id
        from admin_process_definition
        where status = 'ENABLE'
        <if test="isSubProcess!=null">
            and sub_process =#{isSubProcess}
        </if>
        order by id desc
    </select>
    <update id="updateReferrenceProcessDefIds">
        update admin_process_definition
        <set>
            referrence_process_def_ids = #{referrenceProcessDefIds},
        </set>
        where id = #{id}
    </update>

    <select id="getByProcessDefIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin_process_definition
        <where>
            <if test="processDefIds != null">
                and process_def_id in
                <foreach collection="processDefIds" item="processDefId" index="index" open="(" close=")" separator=",">
                    #{processDefId}
                </foreach>
            </if>
        </where>
    </select>
</mapper>