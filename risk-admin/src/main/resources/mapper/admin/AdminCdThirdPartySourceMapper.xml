<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.admin.dao.admin.AdminCdThirdPartySourceMapper" >

    <resultMap id="BaseResultMap" type="com.youxin.risk.admin.model.AdminCdThirdPartySource" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="source_system" property="sourceSystem" jdbcType="VARCHAR" />
        <result column="third_party_service_key" property="thirdPartyServiceKey" jdbcType="VARCHAR" />
        <result column="third_party_service_desc" property="thirdPartyServiceDesc" jdbcType="VARCHAR" />
        <!--<result column="third_party_name" property="thirdPartyName" jdbcType="VARCHAR" />-->
        <!--<result column="third_party_obtain_method" property="thirdPartyObtainMethod" jdbcType="VARCHAR" />-->
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <resultMap id="BaseResultMap2" type="com.youxin.risk.admin.model.AdminThirdPartySourceInterface" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="third_party_service_key" property="thirdPartyServiceKey" jdbcType="VARCHAR" />
        <result column="third_party_name" property="thirdPartyName" jdbcType="VARCHAR" />
        <result column="third_party_obtain_method" property="thirdPartyObtainMethod" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, source_system, third_party_service_key, third_party_service_desc, create_time, update_time
    </sql>

    <sql id="Base_Column_List_interfaces" >
        id, third_party_service_key, third_party_name,third_party_obtain_method, create_time, update_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select 
        <include refid="Base_Column_List" />
        from admin_process_dynamic_datanode
        where id = #{id,jdbcType=INTEGER}
    </select>

    <delete id="delete" parameterType="java.lang.Long" >
        delete from admin_process_dynamic_datanode
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.youxin.risk.admin.model.AdminCdThirdPartySource" >
        insert into admin_process_dynamic_datanode (source_system, third_party_service_key,
            third_party_service_desc,  create_time,
            update_time)
        values ( #{sourceSystem,jdbcType=VARCHAR}, #{thirdPartyServiceKey,jdbcType=VARCHAR},
            #{thirdPartyServiceDesc,jdbcType=VARCHAR},  #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP})
    </insert>



    <update id="update" parameterType="com.youxin.risk.admin.model.AdminCdThirdPartySource" >
        update admin_process_dynamic_datanode
        <set >
            <if test="sourceSystem != null" >
                source_system = #{sourceSystem,jdbcType=VARCHAR},
            </if>
            <if test="thirdPartyServiceKey != null" >
                third_party_service_key = #{thirdPartyServiceKey,jdbcType=VARCHAR},
            </if>
            <if test="thirdPartyServiceDesc != null" >
                third_party_service_desc = #{thirdPartyServiceDesc,jdbcType=VARCHAR},
            </if>

            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.youxin.risk.admin.model.AdminCdThirdPartySource" >
        update admin_process_dynamic_datanode
        set source_system = #{sourceSystem,jdbcType=VARCHAR},
            third_party_service_key = #{thirdPartyServiceKey,jdbcType=VARCHAR},
            third_party_service_desc = #{thirdPartyServiceDesc,jdbcType=VARCHAR},
            third_party_name = #{thirdPartyName,jdbcType=VARCHAR},
            third_party_obtain_method = #{thirdPartyObtainMethod,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>


    <select id="selectCount" resultType="java.lang.Integer" parameterType="java.util.Map">
        select
        count(1)
        from admin_process_dynamic_datanode
        <where>
            <if test="thirdPartyServiceKey != null">
                and third_party_service_key = #{thirdPartyServiceKey}
            </if>
            <if test="sourceSystem != null">
                and source_system = #{sourceSystem}
            </if>
            <if test="startTime != null and endTime != null" >
                and create_time &gt; #{startTime} and create_time &lt; #{endTime}
            </if>
        </where>
    </select>

    <select id="selectList" resultMap="BaseResultMap" parameterType="java.util.Map">
        select
        <include refid="Base_Column_List"/>
        from admin_process_dynamic_datanode
        <where>
            <if test="thirdPartyServiceKey != null">
                and third_party_service_key = #{thirdPartyServiceKey}
            </if>
            <if test="sourceSystem != null">
                and source_system = #{sourceSystem}
            </if>
            <if test="startTime != null and endTime != null" >
                and create_time &gt; #{startTime} and create_time &lt; #{endTime}
            </if>
        </where>
        order by id desc
        limit #{start}, #{limit}
    </select>

    <delete id="deleteChildInterfaces" parameterType="java.lang.String" >
        delete from admin_process_dynamic_datanode_kids
        where third_party_service_key = #{thirdPartyServiceKey,jdbcType=VARCHAR}
    </delete>

    <!--查找该数据源配置的所有三方-->
    <select id="selectChildInterfaces" resultMap="BaseResultMap2" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List_interfaces"/>
        from admin_process_dynamic_datanode_kids
        where third_party_service_key = #{thirdPartyServiceKey,jdbcType=VARCHAR}
        order by id desc
    </select>


    <!--批量插入  useGeneratedKeys="true"-->
    <insert id="SaveChildInterfaces"   parameterType="java.util.List">
        insert into admin_process_dynamic_datanode_kids (third_party_service_key,third_party_name,
        third_party_obtain_method,create_time,
        update_time)
        values
        <!--item就是List里每一项的对象名，要用","分割每一条数据，最后要";"结尾-->
        <foreach collection="list" item="item" index="index" separator="," close=";">
            ( #{item.thirdPartyServiceKey,jdbcType=VARCHAR}, #{item.thirdPartyName,jdbcType=VARCHAR}, #{item.thirdPartyObtainMethod,jdbcType=VARCHAR}
            , #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

</mapper>