<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youxin.risk.admin.dao.admin.AdminProcessExperimentMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.admin.model.AdminProcessExperiment">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="source_system" property="sourceSystem" jdbcType="VARCHAR"/>
        <result column="exp_code" property="expCode" jdbcType="VARCHAR"/>
        <result column="exp_name" property="expName" jdbcType="VARCHAR"/>
        <result column="exp_type" property="expType" jdbcType="VARCHAR"/>
        <result column="begin_time" property="beginTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="exp_flow_percent" property="expFlowPercent" jdbcType="INTEGER"/>
        <result column="process_def_id" property="processDefId" jdbcType="VARCHAR"/>
        <result column="feature_submit_code" property="featureSubmitCode" jdbcType="VARCHAR" javaType="java.util.Set" typeHandler="com.youxin.risk.commons.mybatis.SetTypeHandler"/>
        <result column="strategy_submit_code" property="strategySubmitCode" jdbcType="VARCHAR"/>
        <result column="steps" property="steps" jdbcType="VARCHAR" javaType="java.util.Set" typeHandler="com.youxin.risk.commons.mybatis.SetTypeHandler"/>
        <result column="event_code" property="eventCode" jdbcType="VARCHAR"/>
        <result column="fea_cmp_field" property="feaCmpField" jdbcType="VARCHAR"/>
        <result column="fea_cmp_type" property="feaCmpType" jdbcType="VARCHAR"/>
        <result column="cmp_field" property="cmpField" jdbcType="VARCHAR"/>
        <result column="cmp_type" property="cmpType" jdbcType="VARCHAR"/>
        <result column="old_service_code" property="oldServiceCode" jdbcType="VARCHAR"/>
        <result column="new_service_code" property="newServiceCode" jdbcType="VARCHAR"/>
        <result column="fea_service_code" property="feaServiceCode" jdbcType="VARCHAR"/>
        <result column="online_service_code" property="olineServiceCode" jdbcType="VARCHAR"/>
        <result column="service_cmp_field" property="serviceCmpField" jdbcType="VARCHAR"/>
        <result column="service_cmp_type" property="serviceCmpType" jdbcType="VARCHAR"/>
        <result column="node_mapping" property="nodeMapping" jdbcType="VARCHAR"/>
        <result column="operator" property="operator" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="exp_number" property="expNumber" jdbcType="INTEGER"/>
        <result column="stop_type" property="stopType" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,source_system,exp_code,exp_name,exp_type,begin_time,end_time,status,exp_flow_percent,process_def_id,feature_submit_code,strategy_submit_code,
        steps,event_code,fea_cmp_field,fea_cmp_type,cmp_field,cmp_type,old_service_code,new_service_code,
        fea_service_code,online_service_code,service_cmp_field,service_cmp_type,node_mapping,operator,create_time,update_time
            ,exp_number,stop_type
    </sql>

    <sql id="common_where_condition">
        <if test="expCode != null and expCode != ''">
            and exp_code like CONCAT('%',#{expCode},'%')
        </if>
        <if test="expName != null and expName != ''">
            and exp_name like CONCAT('%',#{expName},'%')
        </if>
        <if test="expType != null and expType != ''">
            and exp_type = #{expType}
        </if>
        <if test="status != null and status != ''">
            and status = #{status}
        </if>
        <if test="sourceSystem != null and sourceSystem != ''">
            and source_system = #{sourceSystem}
        </if>
        <if test="operator != null and operator != ''">
            and operator like CONCAT('%',#{operator},'%')
        </if>
        <if test="stopType != null">
            and stop_type =#{stopType}
        </if>
    </sql>

    <insert id="insert" parameterType="com.youxin.risk.admin.model.AdminProcessExperiment">
        insert into admin_process_experiment (source_system,
                                              exp_code,
                                              exp_name,
                                              exp_type,
                                              begin_time,
                                              end_time,
                                              status,
                                              exp_flow_percent,
                                              process_def_id,
                                              feature_submit_code,
                                              strategy_submit_code,
                                              steps,
                                              event_code,
                                              fea_cmp_field,
                                              fea_cmp_type,
                                              cmp_field,
                                              cmp_type,
                                              old_service_code,
                                              new_service_code,
                                              fea_service_code,
                                              online_service_code,
                                              service_cmp_field,
                                              service_cmp_type,
                                              node_mapping,
                                              operator,
                                              exp_number,
                                              stop_type)
        values (#{sourceSystem},
                #{expCode},
                #{expName},
                #{expType},
                #{beginTime},
                #{endTime},
                #{status},
                #{expFlowPercent},
                #{processDefId},
                #{featureSubmitCode, typeHandler=com.youxin.risk.commons.mybatis.SetTypeHandler},
                #{strategySubmitCode},
                #{steps, typeHandler=com.youxin.risk.commons.mybatis.SetTypeHandler},
                #{eventCode},
                #{feaCmpField},
                #{feaCmpType},
                #{cmpField},
                #{cmpType},
                #{oldServiceCode},
                #{newServiceCode},
                #{feaServiceCode},
                #{olineServiceCode},
                #{serviceCmpField},
                #{serviceCmpType},
                #{nodeMapping},
                #{operator},
                #{expNumber},
                #{stopType})
    </insert>

    <update id="update" parameterType="com.youxin.risk.admin.model.AdminProcessExperiment">
        update admin_process_experiment
        <set>
            <if test="sourceSystem != null and sourceSystem != ''">
                source_system = #{sourceSystem},
            </if>
            <if test="expCode != null and expCode != ''">
                exp_code = #{expCode},
            </if>
            <if test="expName != null and expName != ''">
                exp_name = #{expName},
            </if>
            <if test="expType != null and expType != ''">
                exp_type = #{expType},
            </if>
            <if test="beginTime != null">
                begin_time = #{beginTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="steps != null and steps.size > 0">
                steps = #{steps, typeHandler=com.youxin.risk.commons.mybatis.SetTypeHandler},
            </if>
            <if test="processDefId != null and processDefId != ''" >
                process_def_id = #{processDefId},
            </if>
            <if test="expFlowPercent != null">
                exp_flow_percent = #{expFlowPercent},
            </if>
            <if test="featureSubmitCode != null and featureSubmitCode.size > 0">
                feature_submit_code = #{featureSubmitCode, typeHandler=com.youxin.risk.commons.mybatis.SetTypeHandler},
            </if>
            <if test="strategySubmitCode != null and strategySubmitCode != ''">
                strategy_submit_code = #{strategySubmitCode},
            </if>
            <if test="nodeMapping != null and nodeMapping != ''">
                node_mapping = #{nodeMapping},
            </if>
            <if test="feaCmpField != null and feaCmpField != ''">
                fea_cmp_field = #{feaCmpField},
            </if>
            <if test="feaCmpType != null and feaCmpType != ''">
                fea_cmp_type = #{feaCmpType},
            </if>
            <if test="cmpField != null and cmpField != ''">
                cmp_field = #{cmpField},
            </if>
            <if test="cmpType != null and cmpType != ''">
                cmp_type = #{cmpType},
            </if>
            <if test="oldServiceCode != null and oldServiceCode != ''">
                old_service_code = #{oldServiceCode},
            </if>
            <if test="newServiceCode != null and newServiceCode != ''">
                new_service_code = #{newServiceCode},
            </if>
            <if test="feaServiceCode != null and feaServiceCode != ''">
                fea_service_code = #{feaServiceCode},
            </if>
            <if test="olineServiceCode != null and olineServiceCode != ''">
                online_service_code = #{olineServiceCode},
            </if>
            <if test="serviceCmpField != null and serviceCmpField != ''">
                service_cmp_field = #{serviceCmpField},
            </if>
            <if test="serviceCmpType != null and serviceCmpType != ''">
                service_cmp_type = #{serviceCmpType},
            </if>
            <if test="expNumber != null">
                exp_number =#{expNumber},
            </if>
            <if test="stopType != null">
                stop_type =#{stopType}
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="delete" parameterType="java.lang.Long">
        delete
        from admin_process_experiment
        where id = #{id}
    </delete>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from admin_process_experiment
        where id = #{id}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin_process_experiment
        order by id desc
    </select>

    <select id="selectCount" resultType="java.lang.Integer" parameterType="java.util.Map">
        select
        count(1)
        from admin_process_experiment
        <where>
            <include refid="common_where_condition"/>
        </where>
    </select>

    <select id="selectList" resultMap="BaseResultMap" parameterType="java.util.Map">
        select
        <include refid="Base_Column_List"/>
        from admin_process_experiment
        <where>
            <include refid="common_where_condition"/>
        </where>
        order by id desc
        limit #{start}, #{limit}
    </select>

    <select id="selectForEnable" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin_process_experiment
        where status = "DISABLE"
        and begin_time &lt;= now()
        and end_time &gt; now()
    </select>

    <select id="selectForDisable" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin_process_experiment
        where status = "ENABLE"
        and end_time &lt;= now()
        and stop_type='TIMER'
    </select>

    <update id="batchUpdateStatus">
        update admin_process_experiment
        set status = #{status}
        where id in
        <foreach collection="idList" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>