<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.admin.dao.admin.RuleCandidateScoreMapper">

    <resultMap id="baseResult" type="com.youxin.risk.admin.model.rulescore.RuleCandidateScore">
        <id column="id" property="id" jdbcType="BIGINT"></id>
        <result column="rule_key" property="ruleKey" jdbcType="VARCHAR"></result>
        <result column="rule_name" property="ruleName" jdbcType="VARCHAR"></result>
        <result column="rule_version" property="ruleVersion" jdbcType="INTEGER"></result>
        <result column="rule_status" property="ruleStatus" jdbcType="VARCHAR"></result>
        <result column="rule_run_code" property="ruleRunCode" jdbcType="VARCHAR"></result>
        <result column="code" property="code" jdbcType="VARCHAR"></result>
        <result column="task_id" property="taskId" jdbcType="VARCHAR"></result>
        <result column="task_url" property="taskUrl" jdbcType="VARCHAR"></result>
        <result column="remark" property="remark" jdbcType="VARCHAR"></result>
        <result column="work_start_time" property="workStartTime" jdbcType="VARCHAR"></result>
        <result column="result_table_name" property="resultTableName" jdbcType="VARCHAR"></result>
        <result column="is_var_confirm" property="isVarConfirm" jdbcType="INTEGER"></result>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"></result>
        <result column="modify_user" property="modifyUser" jdbcType="VARCHAR"></result>
        <result column="create_time" property="createTime"></result>
        <result column="update_time" property="updateTime"></result>
    </resultMap>

    <insert id="insert" parameterType="com.youxin.risk.admin.model.rulescore.RuleCandidateScore">
        <selectKey keyColumn="id" keyProperty="id" order="AFTER" resultType="java.lang.Long">
            select LAST_INSERT_ID()
        </selectKey>
        insert into rule_candidate_score (rule_key,rule_name,`rule_version`,`rule_run_code`,`code`,`create_user`,`modify_user`,rule_status,remark,create_time,is_var_confirm,update_time,work_start_time)
        values (#{ruleKey},#{ruleName},#{ruleVersion},#{ruleRunCode},#{code},#{createUser},#{modifyUser},#{ruleStatus},#{remark},#{createTime},#{isVarConfirm},#{updateTime},#{workStartTime})
    </insert>

    <sql id="Base_Column_List">
        id,rule_key,rule_name,rule_version,rule_status,rule_run_code,code,remark,task_id,task_url,result_table_name,create_user,modify_user,create_time,update_time,work_start_time,is_var_confirm
    </sql>


    <select id="findRuleCandidateScoreList" resultMap="baseResult">
        select
        <include refid="Base_Column_List"/>
        from rule_candidate_score where  rule_key = #{ruleKey}
        order by id desc
    </select>

    <select id = "findRuleCandidateScoreRunningTask" resultMap="baseResult">
        select
        <include refid="Base_Column_List"/>
        from rule_candidate_score where rule_status = 1 and task_id is not null
    </select>

    <select id="findRuleCandidateScoreOnlineByRuleKey" resultMap="baseResult">
        select
        <include refid="Base_Column_List"/>
        from rule_candidate_score where  rule_key = #{ruleKey} and rule_status = 6
    </select>

    <select id="findRuleCandidateScoreOnlineByRuleKeyAndVersion" resultMap="baseResult">
        select
        <include refid="Base_Column_List"/>
        from rule_candidate_score where  rule_key = #{ruleKey} and rule_version = #{ruleVersion} and rule_status = 6
    </select>


    <select id="findRuleCandidateScoreByRuleKeyVersion" resultMap="baseResult">
        select
        <include refid="Base_Column_List"/>
        from rule_candidate_score where  rule_key = #{ruleKey} and rule_version = #{ruleVersion}
    </select>

    <select id="findRuleCandidateScoreOnline" resultMap="baseResult">
        select
        <include refid="Base_Column_List"/>
        from rule_candidate_score where rule_status = 6
    </select>

    <update id="updateStatus" >
        update rule_candidate_score
        set rule_status = #{ruleStatus},update_time = #{updateTime},modify_user = #{modifyUser}
        where rule_key = #{ruleKey} and rule_version = #{ruleVersion}
    </update>

    <update id="updateRuleCandidateScoreTask">
        update rule_candidate_score
        set task_id = #{taskId},task_url = #{taskUrl},result_table_name = #{resultTableName},update_time = #{updateTime}
        where rule_key = #{ruleKey} and rule_version = #{ruleVersion}
    </update>

    <update id="updateRuleCandidateScoreIsVarConfirm">
        update rule_candidate_score
        set is_var_confirm = #{isVarConfirm},update_time = #{updateTime}
        where rule_key = #{ruleKey} and rule_version = #{ruleVersion}
    </update>

    <select id="findRuleCandidateScore" resultMap="baseResult">
        SELECT
            id,rule_key,rule_version,rule_status,create_user,modify_user,create_time,update_time,task_url,is_var_confirm
        FROM rule_candidate_score
        <where>
            <if test="pageQueryVo.ruleKey != null and pageQueryVo.ruleKey != ''">
                AND rule_key LIKE CONCAT('%',#{pageQueryVo.ruleKey},'%')
            </if>
            <if test="pageQueryVo.ruleStatus != null and pageQueryVo.ruleStatus != ''">
                AND rule_status LIKE CONCAT('%',#{pageQueryVo.ruleStatus},'%')
            </if>
            <if test="pageQueryVo.modifyUser !=null and pageQueryVo.modifyUser != '' and pageQueryVo.onlyShowMe == 0">
                AND modify_user LIKE CONCAT('%',#{pageQueryVo.modifyUser},'%')
            </if>
            <if test="pageQueryVo.onlyShowMe !=null and pageQueryVo.onlyShowMe == 1">
                AND modify_user LIKE CONCAT('%',#{pageQueryVo.userKey},'%')
            </if>

            <if test="pageQueryVo.createStartTime != null and pageQueryVo.createStartTime != ''">
                AND create_time <![CDATA[ >= ]]> #{pageQueryVo.createStartTime}
            </if>
            <if test="pageQueryVo.createEndTime != null and pageQueryVo.createEndTime != ''">
                AND create_time <![CDATA[ <= ]]> #{pageQueryVo.createEndTime}
            </if>
            <if test="pageQueryVo.updateStartTime != null and pageQueryVo.updateStartTime != ''">
                AND update_time <![CDATA[ >= ]]> #{pageQueryVo.updateStartTime}
            </if>
            <if test="pageQueryVo.updateEndTime != null and pageQueryVo.updateEndTime != ''">
                AND update_time <![CDATA[ <= ]]> #{pageQueryVo.updateEndTime}
            </if>
        </where>
        order by update_time desc
    </select>


    <select id="findRuleCandidateScoreHistory" resultMap="baseResult">
        SELECT
        id,rule_key,rule_version,rule_status,create_user,modify_user,create_time,update_time
        FROM rule_candidate_score
        <where>
            <if test="pageQueryVo.ruleKey != null and pageQueryVo.ruleKey != ''">
                AND rule_key LIKE CONCAT('%',#{pageQueryVo.ruleKey},'%')
            </if>
            <if test="pageQueryVo.ruleStatus != null and pageQueryVo.ruleStatus != ''">
                AND rule_status LIKE CONCAT('%',#{pageQueryVo.ruleStatus},'%')
            </if>
            <if test="pageQueryVo.modifyUser !=null and pageQueryVo.modifyUser != ''">
                AND modify_user LIKE CONCAT('%',#{pageQueryVo.modifyUser},'%')
            </if>
            <if test="pageQueryVo.startTime != null and pageQueryVo.startTime != ''">
                AND update_time <![CDATA[ >= ]]> #{pageQueryVo.startTime}
            </if>
            <if test="pageQueryVo.endTime != null and pageQueryVo.endTime != ''">
                AND update_time <![CDATA[ <= ]]> #{pageQueryVo.endTime}
            </if>

            <if test="pageQueryVo.createStartTime != null and pageQueryVo.createStartTime != ''">
                AND create_time <![CDATA[ >= ]]> #{pageQueryVo.createStartTime}
            </if>
            <if test="pageQueryVo.createEndTime != null and pageQueryVo.createEndTime != ''">
                AND create_time <![CDATA[ <= ]]> #{pageQueryVo.createEndTime}
            </if>
        </where>
        order by rule_version desc
    </select>

    <select id="findBindRuleKeyListByNode" resultMap="baseResult">
        select c.*
        from rule_score a
        join (
            select strategy, node, rule_key, max(rule_version) as online_version
            from rule_score_strategy
            group by strategy, node, rule_key
        ) b on a.rule_key = b.rule_key and b.online_version = a.rule_version
        join rule_candidate_score c
        on a.rule_key = c.rule_key
        and b.online_version = c.rule_version
        where b.strategy = #{strategyType}
          and b.node = #{nodeName}
          and a.rule_version != -1
    </select>

    <select id="findBindOnlineRuleKeyByNode" resultMap="baseResult">
        select c.*
        from rule_score a
        join (
            select strategy, node, rule_key, max(rule_version) as online_version
            from rule_score_strategy
            group by strategy, node, rule_key
        ) b on a.rule_key = b.rule_key and b.online_version = a.rule_version
        join rule_candidate_score c
        on a.rule_key = c.rule_key
        and b.online_version = c.rule_version
        <where> b.strategy = #{strategyType}
            and b.node = #{nodeName}
            and a.rule_version != -1
          <if test="ruleKey != null">
             and ruleKey like CONCAT('%',#{ruleKey},'%')
          </if>
        </where>
    </select>

    <select id="selectOnlineRuleKeyList" resultMap="baseResult">
        select rule_key,rule_name,rule_run_code from rule_candidate_score where rule_status = 6
        and rule_key in
        <foreach collection="ruleKeys" item="ruleKey" open="(" separator="," close=")">
            #{ruleKey}
        </foreach>
    </select>
</mapper>