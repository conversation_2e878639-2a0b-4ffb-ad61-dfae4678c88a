<html>
<head>
    <style>
        .td {
            width: 200px;
        }
    </style>
</head>
<body>
<h3>申请上线${newVo.feature.tag!}</h3>
<h3>申请人：${submitUser!}</h3>

<table cellspacing="0" border="1" style="border-collapse:collapse;">
    <tr align="center">
        <th></th>
        <th>修改前</th>
        <th>修改后</th>
        <th></th>
        <th>修改前</th>
        <th>修改后</th>
    </tr>
    <tr align="center">
        <td class="td">特征名称</td>
        <td class="td">${oldVo.feature.tag!}</td>
        <td class="td">${newVo.feature.tag!}</td>
        <td class="td">中文特征名称</td>
        <td class="td">${oldVo.feature.label!}</td>
        <td class="td">${newVo.feature.label!}</td>
    </tr>
    <tr align="center">
        <td class="td">业务线</td>
        <td class="td">${oldVo.feature.sourceSystem!}</td>
        <td class="td">${newVo.feature.sourceSystem!}</td>
        <td class="td">特征类型</td>
        <td class="td">${oldVo.feature.preType!}</td>
        <td class="td">${newVo.feature.preType!}</td>
    </tr>
    <tr align="center">
        <td class="td">事件步骤</td>
        <td class="td">
            <#if oldVo.feature.eventStep?exists>
                <#list oldVo.feature.eventStep as event>
                    ${event.event!}-
                    <#if event.applyStep?exists>
                        <#list event.applyStep as step>
                            ${step!},
                        </#list>
                    </#if>
                    ,
                </#list>
            </#if>
        </td>
        <td class="td">
            <#if newVo.feature.eventStep?exists>
                <#list newVo.feature.eventStep as event>
                    ${event.event!}-
                    <#if event.applyStep?exists>
                        <#list event.applyStep as step>
                            ${step!},
                        </#list>
                    </#if>
                    ,
                </#list>
            </#if>
        </td>
        <td class="td">依赖特征</td>
        <td class="td">
            <#if oldVo.feature.dataDepends?exists>
                <#list oldVo.feature.dataDepends as value>
                    ${value!},
                </#list>
            </#if>
        </td>
        <td class="td">
            <#if newVo.feature.dataDepends?exists>
                <#list newVo.feature.dataDepends as value>
                    ${value!},
                </#list>
            </#if>
        </td>
    </tr>
    <tr align="center">
        <td class="td">依赖三方数据源</td>
        <td class="td">
            <#if oldVo.feature.thirdPartyDataDepends?exists>
                <#list oldVo.feature.thirdPartyDataDepends as value>
                    ${value!},
                </#list>
            </#if>
        </td>
        <td class="td">
            <#if newVo.feature.thirdPartyDataDepends?exists>
                <#list newVo.feature.thirdPartyDataDepends as value>
                    ${value!},
                </#list>
            </#if>
        </td>
        <td class="td">依赖内部数据源</td>
        <td class="td">
            <#if oldVo.feature.innerDataDepends?exists>
                <#list oldVo.feature.innerDataDepends as value>
                    ${value!},
                </#list>
            </#if>
        </td>
        <td class="td">
            <#if newVo.feature.innerDataDepends?exists>
                <#list newVo.feature.innerDataDepends as value>
                    ${value!},
                </#list>
            </#if>
        </td>
    </tr>
    <tr align="center">
        <td class="td">三方数据路径集合</td>
        <td class="td">${oldVo.feature.thirddataJoinPath!}</td>
        <td class="td">${newVo.feature.thirddataJoinPath!}</td>
        <td class="td">内部数据路径集合</td>
        <td class="td">${oldVo.feature.innerdataJoinPath!}</td>
        <td class="td">${newVo.feature.innerdataJoinPath!}</td>
    </tr>
    <tr align="center">
        <td class="td">模型</td>
        <td class="td">${oldVo.feature.modelListString!}</td>
        <td class="td">${newVo.feature.modelListString!}</td>
        <td class="td">XML路径</td>
        <td class="td">${oldVo.feature.xmlPath!}</td>
        <td class="td">${newVo.feature.xmlPath!}</td>
    </tr>
    <tr align="center">
        <td class="td">自定义函数</td>
        <td class="td">
            <#if oldVo.feature.commonFunList?exists>
                <#list oldVo.feature.commonFunList as value>
                    ${value.funName!},
                </#list>
            </#if>
        </td>
        <td class="td">
            <#if newVo.feature.commonFunList?exists>
                <#list newVo.feature.commonFunList as value>
                    ${value.funName!},
                </#list>
            </#if>
        </td>
        <td class="td">特征项是否一致</td>
        <td class="td">${oldVo.feature.outputInfo!}</td>
        <td class="td">${newVo.feature.outputInfo!}</td>
    </tr>
    <tr align="center">
        <td class="td">特征代码是否一致</td>
        <td class="td">${oldVo.feature.computingCode!}</td>
        <td class="td">${newVo.feature.computingCode!}</td>
    </tr>
</table>
<h4>查看修改前特征详情：${oldVo.featureDetailUrl!}</h4>
<h4>查看修改后特征详情：${newVo.featureDetailUrl!}</h4>

<#if approveStream?exists && approveStream?size gt 0>
    <h3>审批流水</h3>
    <table cellspacing="0" border="1" style="border-collapse:collapse;">
        <tr align="center">
            <td>操作人</td>
            <td>操作时间</td>
            <td>操作内容</td>
        </tr>
        <#list approveStream as value>
            <tr align="center">
                <td>${value.approveUser!}</td>
                <#if value.createTime?exists>
                    <td>${value.createTime?string("yyyy-MM-dd HH:mm:ss")}</td>
                <#else>
                    <td></td>
                </#if>
                <#if value.approveResult == 1>
                    <td>审批通过</td>
                <#elseif value.approveResult == 2>
                    <td>审批拒绝</td>
                <#else>
                    <td>未完成审批</td>
                </#if>
            </tr>
        </#list>
    </table>
</#if>


<a href="${agreeExecuteUrl}">同意</a>
<a href="${refuseExecuteUrl}">拒绝</a>
</body>
</html>