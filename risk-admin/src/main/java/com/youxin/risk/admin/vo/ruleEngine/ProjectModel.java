package com.youxin.risk.admin.vo.ruleEngine;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

public class ProjectModel {

    @NotNull(message = "项目名称不能为空")
    @Pattern(regexp = "^[a-zA-Z][a-zA-Z0-9]*$", message = "项目名称仅能包含数字和英文,且首字母不能为数字")
    private String projectName;

    @NotNull(message = "策略类型不能为空")
    private String strategyType;

    @NotNull(message = "备注不能为空")
    private String remark;

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getStrategyType() {
        return strategyType;
    }

    public void setStrategyType(String strategyType) {
        this.strategyType = strategyType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
