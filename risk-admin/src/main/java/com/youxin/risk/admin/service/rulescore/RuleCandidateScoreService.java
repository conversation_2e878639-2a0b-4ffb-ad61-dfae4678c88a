package com.youxin.risk.admin.service.rulescore;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yonxin.risk.approve.client.client.RiskApproveClient;
import com.yonxin.risk.approve.client.model.ApproveSubmitReq;
import com.youxin.risk.admin.dao.admin.*;
import com.youxin.risk.admin.dto.rulescore.RuleScoreDTO;
import com.youxin.risk.admin.model.rulescore.*;
import com.youxin.risk.admin.service.impl.NodeManagerService;
import com.youxin.risk.admin.service.notifier.DeploySuccessNotifier;
import com.youxin.risk.admin.tools.notification.AlertNotificationHandler;
import com.youxin.risk.admin.utils.RuleSetCodeHandler;
import com.youxin.risk.admin.vo.template.RuleScoreOnlineOpApproveTemplate;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.commons.exception.ValidateException;
import com.youxin.risk.commons.tools.lock.LockResult;
import com.youxin.risk.commons.tools.lock.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.youxin.risk.admin.model.rulescore.RuleCandidateScore.*;

@Service
@Slf4j
public class RuleCandidateScoreService {
    private static final Logger logger = LoggerFactory.getLogger(RuleCandidateScoreService.class);
    @Autowired
    private RuleCandidateScoreMapper ruleCandidateScoreMapper;
    @Autowired
    private RuleScoreStrategyMapper ruleScoreStrategyMapper;
    @Autowired
    private RuleScoreMapper ruleScoreMapper;
    @Autowired
    private TransactionUtil transactionUtil;
    @Resource
    private RuleSetCodeHandler ruleSetCodeHandler;
    @Autowired
    private AdminVariableConfigMapper adminVariableConfigMapper;
    @Resource
    private RedisLock redisLock;
    @Autowired
    private RuleScoreRelationOnlineMapper ruleScoreRelationOnlineMapper;
    @Autowired
    private RuleScoreRelationMapper ruleScoreRelationMapper;
    @Autowired
    private NodeManagerService nodeManagerService;
    @Autowired
    private RuleScoreMirrorMapper ruleScoreMirrorMapper;
    @Resource
    private DeploySuccessNotifier deploySuccessNotifier;

    /** 回调地址 **/
    @Value("${domain.url}")
    private String domainServiceUrl;
    @Resource
    private AlertNotificationHandler alertNotificationHandler;


    public void save(Integer version, String userKey, List<String> variableList, RuleScoreDTO ruleScoreDTO){
        RuleCandidateScore ruleCandidateScore = new RuleCandidateScore();
        ruleCandidateScore.setRuleKey(ruleScoreDTO.getRuleKey());
        ruleCandidateScore.setRuleVersion(version);
        ruleCandidateScore.setCreateUser(userKey);
        ruleCandidateScore.setModifyUser(userKey);
        ruleCandidateScore.setRuleName(ruleScoreDTO.getRuleName());
        ruleCandidateScore.setRuleRunCode(ruleScoreDTO.getRuleRunCode());//编辑的原始python代码
        String code = ruleSetCodeHandler.generate(ruleScoreDTO.getRuleKey(),version,ruleScoreDTO.getRuleRunCode());
        ruleCandidateScore.setCode(code);
        ruleCandidateScore.setRemark(ruleScoreDTO.getRemark());
        ruleCandidateScore.setCreateTime(new Date());
        ruleCandidateScore.setUpdateTime(new Date());
        ruleCandidateScore.setWorkStartTime(new Date());

        boolean isIntegration = false;
        RuleScore ruleScore = ruleScoreMapper.findRuleScore(ruleScoreDTO.getRuleKey());

        /** 查看之前绑定过节点,绑定过节点的需要集成测试 **/
        List<RuleScoreStrategy> ruleScoreStrategyList =  ruleScoreStrategyMapper.findRuleScoreStrategyList(ruleScoreDTO.getRuleKey(),ruleScore.getRuleVersion());
        if (CollectionUtils.isNotEmpty(ruleScoreStrategyList)){
            isIntegration = true;
        }
        if (!isIntegration){
            ruleCandidateScore.setRuleStatus(ruleStatusFail);
        }else  {
            Set<String> ruleSet = this.ruleSetCodeHandler.queryRuleList(ruleScoreDTO.getRuleRunCode());
            log.info("ruleKey = {} version = {} ruleSet ={} ruleScoreRelations = {}",ruleScoreDTO.getRuleKey(),version,JSON.toJSONString(ruleSet),JSON.toJSONString(ruleScoreDTO.getRuleScoreRelations()));
            for (String rule : ruleSet){
                List<String> ruleScoreRelations = ruleScoreDTO.getRuleScoreRelations();
                if (!ruleScoreRelations.contains(rule)){
                    throw new ValidateException("代码中存在的规则分 " + rule + " 不在依赖中");
                }
            }

            /** 发起集成测试任务前校验 **/
            check(ruleScoreStrategyList,variableList,ruleScoreDTO.getRuleScoreRelations(),ruleCandidateScore);
            ruleCandidateScore.setRuleStatus(RuleCandidateScore.ruleStatusRunning);
        }
        ruleCandidateScoreMapper.insert(ruleCandidateScore);

        /** 集成任务异步执行 **/
        if (isIntegration){
            transactionUtil.doRuleCandidateTransAction(version,userKey,ruleScoreDTO,ruleScoreDTO.getRuleRunCode(),ruleScoreStrategyList);
        }
    }


    /**
     * 解析代码中依赖的变量在已绑定的节点上已经导入，若没有绑定在节点上则跳过此校验
     * 解析代码中依赖的规则在已绑定的节点上已经绑定，若没有绑定在节点上则跳过此校验
     * @param variableList
     */
    private void check(List<RuleScoreStrategy> ruleScoreStrategyList,List<String> variableList,List<String> ruleScoreRelations,RuleCandidateScore ruleCandidateScore) {
        /** 查看绑定策略节点 **/
        for (RuleScoreStrategy ruleScoreStrategy : ruleScoreStrategyList){
            JSONObject param = new JSONObject();
            param.put("strategyType", ruleScoreStrategy.getStrategy());
            param.put("strategyNodeCode",ruleScoreStrategy.getNode());
            List<String> allVars = adminVariableConfigMapper.getVariableCodesByParam(param);
            if (!CollectionUtils.isEmpty(variableList) && !allVars.containsAll(variableList)){
                /** 集成测试的时候自动导入变量 **/
                nodeManagerService.fastVarImport(ruleScoreStrategy.getStrategy(), ruleScoreStrategy.getNode(),variableList);
            }

        }

        /** 检查 **/
        for (RuleScoreStrategy ruleScoreStrategy : ruleScoreStrategyList){
            JSONObject param = new JSONObject();
            param.put("strategyType", ruleScoreStrategy.getStrategy());
            param.put("strategyNodeCode",ruleScoreStrategy.getNode());
            if (!CollectionUtils.isEmpty(ruleScoreRelations)){
                for (String relationRuleKey : ruleScoreRelations) {
                    RuleCandidateScore ruleCandidateScoreRelation = ruleCandidateScoreMapper.findRuleCandidateScoreOnlineByRuleKey(relationRuleKey);
                    /** 查看规则分是否绑定在对应的策略和节点**/
                    RuleScoreStrategy ruleScoreStrategyByParam = ruleScoreStrategyMapper.findRuleScoreStrategyByParam(relationRuleKey,ruleCandidateScoreRelation.getRuleVersion(),ruleScoreStrategy.getStrategy(), ruleScoreStrategy.getNode());
                    if (ruleScoreStrategyByParam == null){
                        throw new ValidateException("依赖的上线规则分"+relationRuleKey+"没有绑定: " + "策略 " +  ruleScoreStrategy.getStrategy() + "节点 " + ruleScoreStrategy.getNode());
                    }
                }
            }
        }
    }

    /**
     * 上线
     * 修改3个表内容
     * @param ruleKey
     * @param ruleVersion
     */
    @MoreTransaction(value = {"adminTransactionManager","rmTransactionManager"})
    public void online(String ruleKey,Integer ruleVersion,String modifyUser){
        LockResult result = null;
        List<RuleScoreStrategy> ruleScoreStrategyList = new ArrayList<>();
        String lockKey = "RuleScore.online.key";
        try {
            result = redisLock.tryLock(lockKey, 5);
            if (!result.isSuccess()) {
                return;
            }
            /**
             * 检查规则分依赖关系
             */
            check(ruleKey,ruleVersion);

            /** 如果同样规则key候选表里面的上线状态的,则修改为下线 **/
            RuleCandidateScore ruleCandidateScoreOnline = ruleCandidateScoreMapper.findRuleCandidateScoreOnlineByRuleKey(ruleKey);
            if (ruleCandidateScoreOnline != null) {
                ruleCandidateScoreMapper.updateStatus(ruleKey, ruleCandidateScoreOnline.getRuleVersion(), ruleOffline, new Date(), modifyUser);
            }
            /** 将自己的状态修改为上线 **/
            ruleCandidateScoreMapper.updateStatus(ruleKey,ruleVersion,ruleOnline,new Date(),modifyUser);

            /** 自动绑定一下之前的上线版本的策略和节点，复制一下**/
            if (ruleCandidateScoreOnline != null) {
                /** 之前有绑定的,一定上过线 **/
                ruleScoreStrategyList = ruleScoreStrategyMapper.findRuleScoreStrategyList(ruleKey, ruleCandidateScoreOnline.getRuleVersion());
                for (RuleScoreStrategy ruleScoreStrategy : ruleScoreStrategyList){
                    /** 把之前上线版本的绑定关系复制 **/
                    ruleScoreStrategy.setRuleVersion(ruleVersion);
                    ruleScoreStrategy.setModifyUser(modifyUser);
                    ruleScoreStrategy.setCreateTime(new Date());
                    ruleScoreStrategy.setUpdateTime(new Date());
                    ruleScoreStrategyMapper.insert(ruleScoreStrategy);
                }
            }
            /** 将规则分上线表的上线版本,更新当前版本 **/
            ruleScoreMapper.onlineVersion(ruleKey,ruleVersion,new Date(),modifyUser);

            /** 规则分上线依赖删除 **/
            ruleScoreRelationOnlineMapper.delete(ruleKey);

            /** 上线后将镜像的状态更新停用 **/
            ruleScoreMirrorMapper.updateStatusByRuleScore(ruleKey,ruleVersion,new Date(),modifyUser);

            /** 规则分上线依赖删除 **/
            List<RuleScoreRelation> ruleScoreRelationList = ruleScoreRelationMapper.findRuleScoreRelationList(ruleKey, ruleVersion);
            if (CollectionUtils.isNotEmpty(ruleScoreRelationList)){
                List<RuleScoreRelationOnline> ruleScoreRelationOnlineList = new ArrayList<>();
                for (RuleScoreRelation ruleScoreRelation : ruleScoreRelationList){
                    RuleScoreRelationOnline ruleScoreRelationOnline = new RuleScoreRelationOnline();
                    ruleScoreRelationOnline.setRuleKey(ruleScoreRelation.getRuleKey());
                    ruleScoreRelationOnline.setRelationRuleKey(ruleScoreRelation.getRelationRuleKey());
                    ruleScoreRelationOnline.setCreateTime(new Date());
                    ruleScoreRelationOnline.setUpdateTime(new Date());
                    ruleScoreRelationOnlineList.add(ruleScoreRelationOnline);
                }
                ruleScoreRelationOnlineMapper.insertBatch(ruleScoreRelationOnlineList);
            }
        }catch (Exception e){
            /** 捕获异常要往外面抛出 **/
            log.error("RuleCandidateScoreService error  param ruleKey = {} ruleVersion = {} modifyUser = {}", ruleKey,ruleVersion,modifyUser);
            throw new RuntimeException(e);
        }
        finally {
            if (result != null && result.isSuccess()){
                redisLock.releaseLock(lockKey, result.getLockId());
            }

            alertNotificationHandler.sendRuleOnlineNotification(ruleKey, ruleVersion, modifyUser);

            // 更新节点的相关信息
            updateOnlineStrategyNodeVariableUsage(ruleScoreStrategyList);
            log.info("RuleCandidateScoreService finish  param ruleKey = {} ruleVersion = {} modifyUser = {}", ruleKey,ruleVersion,modifyUser);
        }
    }

    private void updateOnlineStrategyNodeVariableUsage(List<RuleScoreStrategy> ruleScoreStrategyList) {
        for (RuleScoreStrategy ruleScoreStrategy : ruleScoreStrategyList){
            Map<String, String> argMap = new HashMap<>();
            argMap.put("strategyNodeCode", ruleScoreStrategy.getNode());
            argMap.put("strategyType", ruleScoreStrategy.getStrategy());
            deploySuccessNotifier.notifyListeners(argMap);
        }
    }

    private void check(String ruleKey,Integer ruleVersion){
        /** 当前的依赖关系 **/
        List<RuleScoreRelation> ruleScoreRelationList = ruleScoreRelationMapper.findRuleScoreRelationList(ruleKey, ruleVersion);
        List<String> relationRuleKeyList = ruleScoreRelationList.stream().map(ruleScoreRelation -> ruleScoreRelation.getRelationRuleKey()).collect(Collectors.toList());
        /** 查看依赖的规则分是否有其他的依赖 **/
        for (String relationRuleKey : relationRuleKeyList) {
            List<RuleScoreRelationOnline> relationOnlineByRelationRuleKey = ruleScoreRelationOnlineMapper.findRuleScoreRelationOnlineByRuleKey(relationRuleKey);
            if (CollectionUtils.isNotEmpty(relationOnlineByRelationRuleKey)){
                throw new ValidateException("规则分限制最大2层级: 依赖的规则分" + relationRuleKey +"存在依赖");
            }
        }
        if (CollectionUtils.isNotEmpty(relationRuleKeyList)) {
            /** 查看当前的规则分是否被其他所依赖 **/
            List<RuleScoreRelationOnline> ruleScoreRelationOnlineByRelationRuleKey = ruleScoreRelationOnlineMapper.findRuleScoreRelationOnlineByRelationRuleKey(ruleKey);
            if (CollectionUtils.isNotEmpty(ruleScoreRelationOnlineByRelationRuleKey)) {
                List<String> ruleKeys = ruleScoreRelationOnlineByRelationRuleKey.stream()
                        .map(ruleScoreRelationOnline -> ruleScoreRelationOnline.getRuleKey()).distinct().collect(Collectors.toList());
                throw new ValidateException("规则分限制最大2层级: 当前规则分被" + JSON.toJSONString(ruleKeys) + "所依赖");
            }
        }
    }

    /**
     *
     * @param ruleKey
     * @param ruleVersion
     */
    @Transactional(transactionManager = "adminTransactionManager",rollbackFor = Exception.class)
    public void offline(String ruleKey,Integer ruleVersion,String modifyUser){
        /** 将自己的状态修改为下线 **/
        ruleCandidateScoreMapper.updateStatus(ruleKey,ruleVersion,ruleOffline,new Date(),modifyUser);
        /** 将规则分上线表的上线版本,更新当前版本为-1,表示下线 **/
        ruleScoreMapper.offlineVersion(ruleKey,ruleVersion,new Date(),modifyUser);
    }

    @Resource(name = "eventRestTemplate")
    private RestTemplate eventRestTemplate;

    @Value("${approve.url}")
    private String approveUrl;

    /**
     * 审批人
     * @param userKey
     * @param template
     */
    public Boolean setApprover(String userKey,RuleScoreOnlineOpApproveTemplate template){
      //String url = "http://antifraud-risk-approve.test.weicai.com.cn/approve/getMembers?userKey={userKey}";
      Map<String, Object> params = new HashMap<>();
      params.put("userKey", userKey);
      HttpHeaders headers = new HttpHeaders();

      HttpEntity<String> httpEntity = new HttpEntity<>(null,headers);
      log.info("setApprover exchange url = {} params = {}" , approveUrl,JSON.toJSONString(params));

      ResponseEntity<String> response = eventRestTemplate.exchange(approveUrl, HttpMethod.GET, httpEntity, String.class, params);
      log.info("setApprover response = {}" , JSON.toJSONString(response));

      String config = ApolloClientAdapter.getStringConfig(ApolloNamespaceEnum.ADMIN_SPACE, "ruleNew.score.approve", "{}");
      log.info("setApprover config = {}" , config);
      JSONObject approverConfig = JSON.parseObject(config);
      JSONObject productApprovers = approverConfig.getJSONObject("productApprovers");
      log.info("setApprover productApprovers = {}" , productApprovers);


      Boolean isStrategy = false;
      if (response.getStatusCode() == HttpStatus.OK  &&  response.getBody() != null){
         JSONObject body = JSONObject.parseObject(response.getBody());
         JSONObject data = body.getJSONObject("data");
         log.info("setApprover data = {}" , data);

         isStrategy = data.getBoolean("isStrategy");
         if (isStrategy) {
            template.setApprovers(productApprovers.getObject("isStrategy", List.class));
         }else {
            template.setApprovers(productApprovers.getObject("no_isStrategy", List.class));
         }
     }
      return isStrategy;
    }

    /**
     * 通知人
     * @param userKey
     * @param template
     */
    public void setNotify(String userKey,RuleScoreOnlineOpApproveTemplate template,Boolean isStrategy){
        String config = ApolloClientAdapter.getStringConfig(ApolloNamespaceEnum.ADMIN_SPACE, "ruleNew.score.approve", "{}");
        log.info("setNotify users = {} config = {} isStrategy= {}", userKey,config,isStrategy);
        JSONObject approverConfig = JSON.parseObject(config);
        if (isStrategy) {
            template.setNotifyers(approverConfig.getObject("isStrategyNotifiers", List.class));
        }else {
            template.setNotifyers(approverConfig.getObject("isNotStrategyNotifiers", List.class));
        }
        template.getNotifyers().add(userKey);
    }

    /**
     * 提交审批申请
     * @param jsonObject
     * @param userKey
     */
    public void onlineForApprove(JSONObject jsonObject,String userKey){
        String ruleKey = jsonObject.getString("ruleKey");
        Integer ruleVersion = jsonObject.getInteger("ruleVersion");
        RuleScoreOnlineOpApproveTemplate template = new RuleScoreOnlineOpApproveTemplate(userKey);
        template.setSourceSystem("好分期");
        template.setRuleKey(ruleKey);
        template.setRuleVersion(String.valueOf(ruleVersion));

        String applicationContent = jsonObject.getString("applicationContent");
        template.setApplicationContent(applicationContent);
        String applicationReason = jsonObject.getString("applicationReason");
        template.setApplicationReason(applicationReason);

        Boolean isStrategy = setApprover(userKey, template);
        setNotify(userKey,template,isStrategy);

        ApproveSubmitReq approveSubmitReq = new ApproveSubmitReq();
        String onlineCallbackUrl = "/ruleCandidateScore/callback";
        approveSubmitReq.setUserName(userKey);
        approveSubmitReq.setCallbackUrl(domainServiceUrl + onlineCallbackUrl);
        JSONObject callbackParams = new JSONObject();
        callbackParams.put("ruleKey",ruleKey);
        callbackParams.put("ruleVersion",ruleVersion);
        /** 补充回调参数 **/
        callbackParams.put("userKey",userKey);
        approveSubmitReq.setCallbackParams(callbackParams.toJSONString());
        template.buildApplyData();
        approveSubmitReq.setApproveTemplateValue(template.toString());
        RiskApproveClient.submit(approveSubmitReq);
    }
}
