package com.youxin.risk.admin.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.admin.annotation.BusinessIsolation;
import com.youxin.risk.admin.utils.ContextUtils;
import com.youxin.risk.commons.constants.EngineDataVoPathEnum;
import com.youxin.risk.commons.constants.EngineTransformTypeEnum;
import com.youxin.risk.commons.constants.SourceSystemEnum;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 下拉列表接口
 * <AUTHOR>
 * @date 2019-02-27
 */
@Controller
@RequestMapping("/selectList")
public class SelectListController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SelectListController.class);

    /**
     * 业务线下拉列表
     */
    @BusinessIsolation
    @ResponseBody
    @RequestMapping(value = "/businessType")
    public ResponseEntity<?> selectPage() {

        JSONArray response = initSelectList();
        List<String> sourceSystemList = ContextUtils.getSourceSystemList();
        for (SourceSystemEnum sourceSystemEnum : SourceSystemEnum.validSourceSystemList()) {
            if(!sourceSystemEnum.isValid() || !sourceSystemList.contains(sourceSystemEnum.name())) {
                continue;
            }
            JSONObject item = new JSONObject();
            item.put("value", sourceSystemEnum.name());
            item.put("label", sourceSystemEnum.desc());
            response.add(item);
        }
        LoggerProxy.info(LOGGER,"response:{}", response);
        return buildSuccessResponse(response);
    }

    @ResponseBody
    @RequestMapping(value = "/dataVoPath")
    public ResponseEntity<?> dataVoPath() {
        JSONArray response = initSelectList();
        for (EngineDataVoPathEnum enumItem : EngineDataVoPathEnum.values()) {
            JSONObject item = new JSONObject();
            item.put("value", enumItem.name());
            item.put("label", enumItem.getDesc());
            response.add(item);
        }
        return buildSuccessResponse(response);
    }

    @ResponseBody
    @RequestMapping(value = "/transformType")
    public ResponseEntity<?> transformType() {
        JSONArray response = initSelectList();
        for (EngineTransformTypeEnum enumItem : EngineTransformTypeEnum.values()) {
            JSONObject item = new JSONObject();
            item.put("value", enumItem.name());
            item.put("label", enumItem.desc());
            response.add(item);
        }
        return buildSuccessResponse(response);
    }

    private JSONArray initSelectList() {
        JSONArray response = new JSONArray();
        JSONObject item = new JSONObject();
        item.put("value", null);
        item.put("label", "all");
        response.add(item);
        return response;
    }

}
