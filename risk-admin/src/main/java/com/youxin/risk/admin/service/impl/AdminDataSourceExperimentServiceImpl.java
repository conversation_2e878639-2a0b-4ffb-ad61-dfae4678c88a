/*
 * Copyright (C) 2018 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.admin.service.impl;

import com.google.common.collect.Maps;
import com.youxin.risk.admin.dao.admin.AdminDataSourceExperimentMapper;
import com.youxin.risk.admin.model.AdminDataSourceExperiment;
import com.youxin.risk.admin.model.AdminFeatureExperiment;
import com.youxin.risk.admin.model.Page;
import com.youxin.risk.admin.service.AdminDataSourceExperimentService;
import com.youxin.risk.commons.mongo.DataSourceExperimentResultMongoDao;
import com.youxin.risk.commons.vo.DataSourceExperimentResultVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-7-12
 */
@Service
public class AdminDataSourceExperimentServiceImpl extends BaseServiceImpl<AdminDataSourceExperiment> implements AdminDataSourceExperimentService {

    private static final Logger logger = LoggerFactory.getLogger(AdminDataSourceExperimentServiceImpl.class);

    @Resource
    private AdminDataSourceExperimentMapper adminDataSourceExperimentMapper;

    @Resource
    private DataSourceExperimentResultMongoDao dataSourceExperimentResultMongoDao;

    @Override
    protected void init() {
        tableName = "admin_datasource_experiment";
        baseMapper = adminDataSourceExperimentMapper;
    }

    @Override
    public Page<DataSourceExperimentResultVo> selectExperimentResultPage(Map<String, Object> params) {
        int pageNo = params.containsKey("pageNum") ? (Integer) params.get("pageNum") : 1;
        int pageSize = params.containsKey("pageSize") ? (Integer) params.get("pageSize") : 20;
        int start = (pageNo - 1) * pageSize;
        int limit = pageSize;
        params.put("start", start);
        params.put("limit", limit);
        long total = dataSourceExperimentResultMongoDao.selectCount(params);
        List<DataSourceExperimentResultVo> list = dataSourceExperimentResultMongoDao.selectList(params);
        Page<DataSourceExperimentResultVo> page = new Page<>();
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        page.setTotal(total);
        page.setList(list);
        return page;
    }

    @Override
    public DataSourceExperimentResultVo getExperimentResult(String id) {
        //过滤掉不需要立即返回的字段
        String[] noQuerys = {"request","response"};
        return dataSourceExperimentResultMongoDao.getConditionResultById(id,noQuerys,null);
    }

    @Override
    public DataSourceExperimentResultVo getExperimentInfoData(String id,String infoType) {
        //需要立即返回的字段
        String[] querys = new String[]{infoType};
        return dataSourceExperimentResultMongoDao.getConditionResultById(id,null,querys);
    }


    @Override
    public List<AdminDataSourceExperiment> selectForEnable() {
        return adminDataSourceExperimentMapper.selectForEnable();
    }

    @Override
    public List<AdminDataSourceExperiment> selectForDisable() {
        return adminDataSourceExperimentMapper.selectForDisable();
    }

    @Override
    public void updateStatus(List<Long> idList, String status) {
        adminDataSourceExperimentMapper.batchUpdateStatus(idList, status);
        updateSysDbUpdateTime(false);
    }

    @Override
    public void delete(Long id) {
        AdminDataSourceExperiment adminDataSourceExperiment = adminDataSourceExperimentMapper.get(id);
        super.delete(id);
        dataSourceExperimentResultMongoDao.removeByExpCode(adminDataSourceExperiment.getExpCode());
    }

    @Override
    public Boolean checkExists(String expCode) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("expCode", expCode);
        int count = adminDataSourceExperimentMapper.selectCount(params);
        if (count > 0) {
            return true;
        }
        return false;
    }

    @Override
    public void saveOrUpdate(AdminDataSourceExperiment request) {
        if (request.getId() == null) {
            baseMapper.insert(request);
        } else {
            request.setUpdateTime(new Date());
            baseMapper.update(request);
        }
        updateSysDbUpdateTime(false);
    }

    @Override
    public List<AdminDataSourceExperiment> selectList(Map<String, Object> params) {
        return adminDataSourceExperimentMapper.selectList(params);
    }
}