/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.admin.model;

import org.apache.commons.lang3.builder.ToStringBuilder;

import com.youxin.risk.commons.model.NodeData;

public class AdminNodeGroupData extends AdminNodeData {
    private static final long serialVersionUID = -8093149392733576738L;

    private String groupCode;
    private String groupName;
    private String groupType;
    private String datasourceGroupCode;
    private String datasourceGroupName;

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getDatasourceGroupName() {
        return datasourceGroupName;
    }

    public void setDatasourceGroupName(String datasourceGroupName) {
        this.datasourceGroupName = datasourceGroupName;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getGroupType() {
        return groupType;
    }

    public void setGroupType(String groupType) {
        this.groupType = groupType;
    }

    public String getDatasourceGroupCode() {
        return datasourceGroupCode;
    }

    public void setDatasourceGroupCode(String datasourceGroupCode) {
        this.datasourceGroupCode = datasourceGroupCode;
    }

}
