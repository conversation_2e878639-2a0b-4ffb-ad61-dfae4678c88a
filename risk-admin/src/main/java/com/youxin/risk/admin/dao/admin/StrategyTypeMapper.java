package com.youxin.risk.admin.dao.admin;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.StrategyTypeDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface StrategyTypeMapper  {

    StrategyTypeDO selectById(Long id);

    int insert(StrategyTypeDO strategyTypeDO);

    List<StrategyTypeDO> listByStrategyTypeCode(@Param("strategyTypeCode") String strategyTypeCode);

    /**
     * 查询所有的策略类型
     * @return
     */
    List<String> selectAllStrategyTypeCode();

    List<String> selectStrategyNodes(@Param("strategyTypeCode") String strategyTypeCode);

    String selectStrategyTypeByNode(@Param("strategyNode") String strategyNode);

    long countByParams(JSONObject params);

    long countByStrategyNode(String strategyNodeCode);

    List<StrategyTypeDO> listByParams(JSONObject params);

    int deleteById(Long id);
}
