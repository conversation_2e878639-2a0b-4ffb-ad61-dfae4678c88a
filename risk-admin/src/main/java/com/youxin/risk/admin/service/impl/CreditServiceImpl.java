package com.youxin.risk.admin.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.youxin.apollo.client.NacosClient;
import com.youxin.risk.admin.constants.ApolloLocalNamespace;
import com.youxin.risk.admin.dao.admin.AdminNodeDataParamMapper;
import com.youxin.risk.admin.service.CreditService;
import com.youxin.risk.commons.service.WeChatService;
import com.youxin.risk.admin.utils.StringUtils;
import com.youxin.risk.commons.tools.redis.RetryableJedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/15 11:33
 * @desc
 */
@Component
public class CreditServiceImpl implements CreditService {

    private final Logger LOGGER = LoggerFactory.getLogger(CreditServiceImpl.class);

    // B
    public static final String HAO_HUAN_VERIFY_B = "hhDataStepBV3";
    // IRR_B
    public static final String HAO_HUAN_VERIFY_IRR_B = "IRR_hhDataStepB";
    // IRR_B_FL
    public static final String HAO_HUAN_VERIFY_IRR_B_FL = "FL_IRR_hhDataStepB";
    // B_FL
    public static final String HAO_HUAN_VERIFY_B_FL = "hhDataStepB_FL";
    // IRR24_A
    public static final String HAO_HUAN_VERIFY_IRR24_A = "IRR24_hhDataStepA";

    // RETRIAL_AUDIT_C
    public static final String HH_AMOUNT_RA_RETRIAL_AUDIT_C = "hhRetrialAuditDataStepC";
    // AMOUNT_REAUDIT_IRR_C
    public static final String HH_AMOUNT_RA_AMOUNT_REAUDIT_IRR_C = "IRR_hhAmountRetrialAuditDataStepC";
    // AMOUNT_REAUDIT_C
    public static final String HH_AMOUNT_RA_AMOUNT_REAUDIT_C = "hhAmountRetrialAuditDataStepC";

    // 放款审核相关步骤node_code
    public List<String> haoHuanVerifyNodeCodeList = Lists.newArrayList(HAO_HUAN_VERIFY_B,
            HAO_HUAN_VERIFY_IRR_B, HAO_HUAN_VERIFY_IRR_B_FL, HAO_HUAN_VERIFY_B_FL, HAO_HUAN_VERIFY_IRR24_A);

    // 额度重审相关步骤node_code
    public List<String> hhAmountRaNodeCodeList = Lists.newArrayList(HH_AMOUNT_RA_RETRIAL_AUDIT_C,
            HH_AMOUNT_RA_AMOUNT_REAUDIT_IRR_C, HH_AMOUNT_RA_AMOUNT_REAUDIT_C);

    public static final String HH_CREDIT_DATA_CODE = "hhCreditData";
    public static final String WHITE_BOX_VERSION = "whiteboxVersion";
    public static final String WHITE_BOX_VERSION_HEIKA = "\"HEIKA\"";
    public static final String WHITE_BOX_VERSION_V2 = "\"V2\"";
    public static final String WHITE_BOX_VERSION_V3 = "\"V3\"";

    public static final String REDIS_CREDIT_HEIKA_FLAG = "credit.heika.flag";
    public static final String REDIS_CREDIT_HEIKA_FLAG_VALUE_HEIKA = "heika";
    public static final String REDIS_CREDIT_HEIKA_FLAG_VALUE_NOHEIKA = "noheika";

    @Resource
    private AdminNodeDataParamMapper adminNodeDataParamMapper;

    @Autowired
    private RetryableJedis retryableJedis;

    @Resource
    private WeChatService weChatService;

    @Value("${wechat.credit.warn.url}")
    private String wechatWarnUrl;

    @Override
    public void updateToHeiKa() {
        String nodeCodeConfigStr = NacosClient.getByNameSpace(ApolloLocalNamespace.localNamespace, "getCredit.nodeCodes",
                "{\"haoHuanVerifyNodeCodes\":\"\",\"hhAmountRetrialAuditNodeCodes\":\"\",\"haoHuanVerifyDataCode\":\"\"}");
        LOGGER.info("updateToHeiKa config: {}", nodeCodeConfigStr);
        // 获取进件审核相关步骤的节点编码
        JSONObject nodeCodeConfig = JSONObject.parseObject(nodeCodeConfigStr);
        final String haoHuanVerifyNodeCodes = nodeCodeConfig.getString("haoHuanVerifyNodeCodes");
        if (StringUtils.isNotBlank(haoHuanVerifyNodeCodes)) {
            haoHuanVerifyNodeCodeList = Lists.newArrayList(StringUtils.split(haoHuanVerifyNodeCodes, ","));
        }
        // 获取数据源编码
        String haoHuanVerifyDataCode = nodeCodeConfig.getString("haoHuanVerifyDataCode");
        if (StringUtils.isBlank(haoHuanVerifyDataCode)) {
            haoHuanVerifyDataCode = HH_CREDIT_DATA_CODE;
        }
        // 1.进件审核（B、IRR_B、IRR_B_FL、B_FL、HH_API_C、IRR24_A）查征请求whiteboxversion由'V2'恢复为'HEIKA
        int verifyAffected = adminNodeDataParamMapper.updateByNodeCodeAndDataCodeAndParamName(haoHuanVerifyNodeCodeList,
                haoHuanVerifyDataCode,
                WHITE_BOX_VERSION,
                WHITE_BOX_VERSION_V2,
                WHITE_BOX_VERSION_HEIKA);
        if (verifyAffected > 0) {
            retryableJedis.set(REDIS_CREDIT_HEIKA_FLAG, REDIS_CREDIT_HEIKA_FLAG_VALUE_HEIKA);
            // 推送操作成功消息
            sendMsg("updateToHeiKa", "黑卡恢复成功，请关注！");
            return;
        }
        LOGGER.error("updateToHeiKa error, verifyAffected: {}", verifyAffected);
        throw new RuntimeException("恢复异常");
    }

    @Override
    public void updateNoHeiKa() {
        String nodeCodeConfigStr = NacosClient.getByNameSpace(ApolloLocalNamespace.localNamespace, "getCredit.nodeCodes",
                "{\"haoHuanVerifyNodeCodes\":\"\",\"hhAmountRetrialAuditNodeCodes\":\"\",\"haoHuanVerifyDataCode\":\"\"}}");
        LOGGER.info("updateNoHeiKa config: {}", nodeCodeConfigStr);
        // 获取进件审核相关步骤的节点编码
        JSONObject nodeCodeConfig = JSONObject.parseObject(nodeCodeConfigStr);
        final String haoHuanVerifyNodeCodes = nodeCodeConfig.getString("haoHuanVerifyNodeCodes");
        if (StringUtils.isNotBlank(haoHuanVerifyNodeCodes)) {
            haoHuanVerifyNodeCodeList = Lists.newArrayList(StringUtils.split(haoHuanVerifyNodeCodes, ","));
        }
        // 获取数据源编码
        String haoHuanVerifyDataCode = nodeCodeConfig.getString("haoHuanVerifyDataCode");
        if (StringUtils.isBlank(haoHuanVerifyDataCode)) {
            haoHuanVerifyDataCode = HH_CREDIT_DATA_CODE;
        }
        // 1.进件审核（B、IRR_B、IRR_B_FL、B_FL、HH_API_C、IRR24_A）查征请求whiteboxversion由'HEIKA'切换为'V2'
        int verifyAffected = adminNodeDataParamMapper.updateByNodeCodeAndDataCodeAndParamName(haoHuanVerifyNodeCodeList,
                haoHuanVerifyDataCode,
                WHITE_BOX_VERSION,
                WHITE_BOX_VERSION_HEIKA,
                WHITE_BOX_VERSION_V2);
        if (verifyAffected > 0) {
            retryableJedis.set(REDIS_CREDIT_HEIKA_FLAG, REDIS_CREDIT_HEIKA_FLAG_VALUE_NOHEIKA);
            // 推送操作成功消息
            sendMsg("updateNoHeiKa", "黑卡降级成功，请关注！");
            return;
        }
        LOGGER.error("updateNoHeiKa error, verifyAffected: {}", verifyAffected);
        throw new RuntimeException("降级异常");
    }

    @Override
    public String getCreditFlag() {
        return retryableJedis.get(REDIS_CREDIT_HEIKA_FLAG);
    }

    @Override
    public void setCreditFlag(String value) {
        retryableJedis.set(REDIS_CREDIT_HEIKA_FLAG, value);
    }

    /**
     * 发送操作消息
     *
     * @param methodName
     * @param msgContent
     */
    private void sendMsg(String methodName, String msgContent) {
        try {
            weChatService.sendTextMsgWithWarnAll(msgContent,"c2bafb36-a735-4eab-bc46-fe4e2d2fc563");
        } catch (Exception ex) {
            LOGGER.error("{} sendMsgerror", methodName, ex);
        }
    }
}
