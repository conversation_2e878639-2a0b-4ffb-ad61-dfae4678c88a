package com.youxin.risk.admin.utils;


import com.youxin.risk.admin.constants.AdminConstants;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
public class FileDownLoadUtils {

    /**
     * 通用下载请求
     *
     * @param fileName 文件名称
     * @param delete 是否删除
     */
    public static void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request)
    {
        fileDownload(AdminConstants.DOWNLOAD_PATH,fileName,1,delete,response,request);
    }

    public static void fileDownload(String path,String fileName, int fileNameType, Boolean delete, HttpServletResponse response, HttpServletRequest request)
    {
        try
        {
            if (!FileUtils.isValidFilename(fileName))
            {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }
            String realFileName = null;
            if(fileNameType == 1){
                realFileName = System.currentTimeMillis() + "_" + fileName.substring(fileName.indexOf("_") + 1);
            }else if(fileNameType == 2){
                realFileName = fileName;
            }

            String filePath = path + fileName;

            response.setCharacterEncoding("utf-8");
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + FileUtils.setFileDownloadHeader(request, realFileName));
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete)
            {
                FileUtils.deleteFile(filePath);
            }
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }
}
