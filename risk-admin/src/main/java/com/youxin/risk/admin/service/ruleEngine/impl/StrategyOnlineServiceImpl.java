package com.youxin.risk.admin.service.ruleEngine.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yonxin.risk.approve.client.client.RiskApproveClient;
import com.yonxin.risk.approve.client.model.ApproveSubmitReq;
import com.yonxin.risk.approve.client.model.enums.ApproveStatus;
import com.yonxin.risk.approve.client.model.template.base.Approver;
import com.youxin.risk.admin.constants.CandidateStrategyOpeEnum;
import com.youxin.risk.admin.constants.CandidateStrategyStatusEnum;
import com.youxin.risk.admin.constants.StrategyExpStatusEnum;
import com.youxin.risk.admin.dao.admin.AdminStrategyExpMapper;
import com.youxin.risk.admin.dao.cp.UserMapper;
import com.youxin.risk.admin.dao.rm.AdminStrategyCodeMapper;
import com.youxin.risk.admin.domain.ruleengine.AdminCandidateStrategy;
import com.youxin.risk.admin.model.*;
import com.youxin.risk.admin.model.cp.User;
import com.youxin.risk.admin.service.notifier.DeploySuccessNotifier;
import com.youxin.risk.admin.service.ruleEngine.AdminCandidateStrategyService;
import com.youxin.risk.admin.service.ruleEngine.AdminStrategyExpService;
import com.youxin.risk.admin.service.ruleEngine.StrategyOnlineService;
import com.youxin.risk.admin.service.ruleEngine.UserOperateStrategyLogService;
import com.youxin.risk.admin.service.rulescore.MoreTransaction;
import com.youxin.risk.admin.tools.notification.AlertNotificationHandler;
import com.youxin.risk.admin.tools.ruleEngine.StrategyCodeHandler;
import com.youxin.risk.admin.tools.ruleScore.RuleScoreHandler;
import com.youxin.risk.admin.utils.ApprovalWorkflowUtil;
import com.youxin.risk.admin.utils.UserInfoUtil;
import com.youxin.risk.admin.vo.template.StrategyOnlineOpApproveTemplate;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.commons.dao.admin.EventStrategyRelationMapper;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

@Service
public class StrategyOnlineServiceImpl implements StrategyOnlineService {

    private static final Logger logger = LoggerFactory.getLogger(StrategyOnlineServiceImpl.class);
    private static final String STRATEGY_ONLINE_OPERATION_APPROVER = "strategyOnline.operation.approver";
    private static final String PRODUCT_APPROVERS = "productApprovers";

    @Autowired
    private UserOperateStrategyLogService userOperateStrategyLogService;
    @Resource
    private StrategyCodeHandler strategyCodeHandler;
    @Resource
    private RuleScoreHandler ruleScoreHandler;
    @Resource
    private UserMapper userMapper;
    @Value("${domain.url}")
    private String domainServiceUrl;
    @Value("${spring.profiles.active:default}")
    private String activeProfile;
    @Autowired
    private AdminStrategyCodeMapper adminStrategyCodeMapper;
    @Resource
    private EventStrategyRelationMapper eventStrategyRelationMapper;
    @Resource
    private AdminStrategyExpMapper adminStrategyExpMapper;
    @Resource
    private AdminCandidateStrategyService adminCandidateStrategyService;
    @Resource
    private AdminStrategyExpService adminStrategyExpService;
    @Resource
    private RuleProjectServiceImpl ruleProjectService;
    @Resource
    private DeploySuccessNotifier deploySuccessNotifier;
    @Resource
    private AlertNotificationHandler alertNotificationHandler;

    @Override
    public void onlineForApprove(AdminStrategyApply adminStrategyApply) {
        AdminCandidateStrategy adminStrategyCandidate = adminCandidateStrategyService.selectById(adminStrategyApply.getId());
        if(adminStrategyCandidate == null) {
            throw new RuntimeException("策略候选表记录不存在！");
        }
        preCheckApprove(adminStrategyCandidate);

        ApproveSubmitReq approveSubmitReq = createApproveSubmitReq(adminStrategyApply, adminStrategyCandidate);
        try {
            // 提交审批请求
            logger.info("提交策略上线审批请求，策略类型：{}，策略版本：{}",
                    adminStrategyApply.getStrategyType(), adminStrategyApply.getStrategyVersion());
            RiskApproveClient.submit(approveSubmitReq);
            logger.info("策略上线审批请求提交成功，策略ID：{}", adminStrategyApply.getId());
        } catch (Exception e) {
            logger.error("onlineForApproveError", e);
            throw new RuntimeException("提交策略审批异常！请联系管理员");
        }

        updateAdminCandidateStrategy(adminStrategyCandidate, adminStrategyApply);
        userOperateStrategyLogService.logUserOperation(adminStrategyApply.getId(), CandidateStrategyOpeEnum.APPROVE_ONLINE);
    }

    /**
     * 构建带有策略信息的审批模板
     */
    private ApproveSubmitReq createApproveSubmitReq(AdminStrategyApply adminStrategyApply, AdminCandidateStrategy adminStrategyCandidate) {
        String userName = UserInfoUtil.getUsername();
        Integer strategyCandidateId = adminStrategyApply.getId();

        // 创建审批请求
        ApproveSubmitReq approveSubmitReq = new ApproveSubmitReq();
        approveSubmitReq.setUserName(userName);
        approveSubmitReq.setCallbackUrl(domainServiceUrl + "/strategy_candidate/v1/online/callback");
        approveSubmitReq.setCallbackParams(JSON.toJSONString(strategyCandidateId));

        // 构建审批模板
        StrategyOnlineOpApproveTemplate template = new StrategyOnlineOpApproveTemplate(userName);
        BeanUtils.copyProperties(adminStrategyApply, template);
        template.setApplicant(userName);
        template.addSummaryInfo(String.format("策略类型：%s", adminStrategyCandidate.getStrategyType()));
        template.addSummaryInfo(String.format("策略版本：%s", strategyCandidateId));

        // 配置通知人
        JSONObject approverConfig = getApproverConfig();
        List<String> notifiers = approverConfig.getObject("notifiers", List.class);
        if (notifiers == null) {
            notifiers = new ArrayList<>();
        }
        notifiers.add(userName);
        template.setNotifyers(notifiers);

        // 配置审批人
        configureApprovers(template, userName);

        // 完成构建
        template.buildApplyData();
        approveSubmitReq.setApproveTemplateValue(template.toString());

        Map<String, Object> extendParams = new HashMap<>();
        extendParams.put("lockKey", adminStrategyApply.getStrategyType() + "_" + activeProfile);
        approveSubmitReq.setExtendParams(extendParams);

        return approveSubmitReq;
    }

    /**
     * 从Apollo获取审批人配置
     */
    private JSONObject getApproverConfig() {
        return JSON.parseObject(ApolloClientAdapter.getStringConfig(
                ApolloNamespaceEnum.ADMIN_SPACE,
                STRATEGY_ONLINE_OPERATION_APPROVER,
                "{}"));
    }

    /**
     * 配置审批人
     */
    private void configureApprovers(StrategyOnlineOpApproveTemplate template, String userName) {
        JSONObject approverConfig = getApproverConfig();
        JSONObject approvers = approverConfig.getJSONObject("approvers");
        JSONObject submitUserAndApproverMap = approverConfig.getJSONObject("submitUserAndApproverMap");

        if (approvers == null || submitUserAndApproverMap == null) {
            throw new IllegalStateException("审批人配置无效");
        }

        User user = getUserByName(userName);
        if (user == null) {
            throw new IllegalStateException("未找到用户：" + userName);
        }

        // 查找审批人列表
        List<Approver> approversList = findApproversByUser(approvers, submitUserAndApproverMap, user);

        // 添加用户作为最终审批人
        approversList.add(new Approver(1, Collections.singletonList(user.getUsername())));

        template.setApprovers(approversList);
    }

    /**
     * 根据用户查找审批人
     */
    private List<Approver> findApproversByUser(JSONObject approvers, JSONObject submitUserAndApproverMap, User user) {
        // 尝试通过上级查找
        if (submitUserAndApproverMap.containsKey(user.getParent())) {
            String approverKey = submitUserAndApproverMap.getString(user.getParent());
            return getApproversFromConfig(approvers, approverKey);
        }

        // 尝试通过用户名查找
        if (submitUserAndApproverMap.containsKey(user.getUsername())) {
            String approverKey = submitUserAndApproverMap.getString(user.getUsername());
            return getApproversFromConfig(approvers, approverKey);
        }

        // 使用默认产品审批人
        return getApproversFromConfig(approvers, PRODUCT_APPROVERS);
    }

    /**
     * 从配置中获取审批人列表
     */
    private List<Approver> getApproversFromConfig(JSONObject approvers, String key) {
        List<Object> rawApprovers = approvers.getObject(key, List.class);
        if (rawApprovers == null) {
            return new ArrayList<>();
        }

        List<Approver> result = new ArrayList<>();
        for (Object raw : rawApprovers) {
            if (raw instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> approverMap = (Map<String, Object>) raw;
                @SuppressWarnings("unchecked")
                List<String> userIds = (List<String>) approverMap.get("userid");
                result.add(new Approver((Integer) approverMap.get("attr"), userIds));
            }
        }

        return result;
    }

    /**
     * 获取用户信息
     */
    private User getUserByName(String userName) {
        List<User> users = userMapper.selectByUserName(userName);
        return org.apache.commons.collections.CollectionUtils.isNotEmpty(users) ? users.get(0) : null;
    }

    private void updateAdminCandidateStrategy(AdminCandidateStrategy adminStrategyCandidate, AdminStrategyApply adminStrategyApply) {
        adminStrategyCandidate.setStatus(CandidateStrategyStatusEnum.IN_APPROVAL.getCode());
        adminStrategyCandidate.setStrategyApplyInfo(JSON.toJSONString(adminStrategyApply));
        adminCandidateStrategyService.update(adminStrategyCandidate);
    }

    @Override
    public void onlineForCallback(String params, ApproveStatus approveResult, String approvalDetailInfo) {
        AdminCandidateStrategy candidate = adminCandidateStrategyService.selectById(Integer.parseInt(params));
        if(candidate == null) {
            throw new RuntimeException("策略候选表记录不存在！");
        }
        // 校验当前状态，如果已经是终态则忽略此次回调
        if (isTerminalStatus(candidate.getStatus())) {
            logger.warn("Current status is terminal, ignore callback. candidateId:{}, status:{}"
                    , candidate.getId(), candidate.getStatus());
            return;
        }

        // 判断是否为两阶段审批流程
        boolean isTwoStageApproval = false;
        boolean isFirstStageApproved = false;
        boolean isSecondStageApproved = false;

        if (StringUtils.isNotBlank(approvalDetailInfo)) {
            try {
                isTwoStageApproval = ApprovalWorkflowUtil.isTwoStageApprovalFlow(approvalDetailInfo);
                if (isTwoStageApproval) {
                    isFirstStageApproved = ApprovalWorkflowUtil.isFirstStageApproved(approvalDetailInfo);
                    isSecondStageApproved = ApprovalWorkflowUtil.isSecondStageApproved(approvalDetailInfo);
                    logger.info("Two-stage approval: firstStageApproved={}, secondStageApproved={}",
                            isFirstStageApproved, isSecondStageApproved);
                }
            } catch (Exception e) {
                logger.error("Parse approval detail info error", e);
                // 解析审批详情失败，视为非两阶段审批
                isTwoStageApproval = false;
            }
        }

        // 处理状态更新
        if (isTwoStageApproval) {
            handleTwoStageApproval(candidate, approveResult, isFirstStageApproved, isSecondStageApproved, approvalDetailInfo);
        } else {
            handleNormalApproval(candidate, approveResult, approvalDetailInfo);
        }
        adminCandidateStrategyService.update(candidate);
    }

    /**
     * 判断状态是否为终态
     */
    private boolean isTerminalStatus(String status) {
        return CandidateStrategyStatusEnum.ONLINE.getCode().equals(status) ||
                CandidateStrategyStatusEnum.OFFLINE.getCode().equals(status) ||
                CandidateStrategyStatusEnum.DROPPED.getCode().equals(status);
    }

    /**
     * 处理两阶段审批逻辑
     */
    private void handleTwoStageApproval(AdminCandidateStrategy candidate,
                                        ApproveStatus approveResult,
                                        boolean isFirstStageApproved,
                                        boolean isSecondStageApproved,
                                        String approvalDetailInfo) {
        String userName = ApprovalWorkflowUtil.getMostRecentApprover(approvalDetailInfo);
        // 首先根据总体审批状态处理
        switch (approveResult) {
            case PASSED:
                userOperateStrategyLogService.logUserOperation(candidate.getId(),
                        CandidateStrategyOpeEnum.APPROVE_SUCCESS, userName);
                // 如果两阶段状态都是通过，则直接更新为上线状态
                this.toOnline(candidate, userName, CandidateStrategyOpeEnum.APPROVE_TO_ONLINE, getRollBackFlag(candidate)); // 触发上线动作
                break;
            case IN_APPROVAL: // 上线情况、回滚情况都一致逻辑判断
                // 审批中状态 - 需要根据阶段状态细分
                if (isFirstStageApproved && !isSecondStageApproved) {
                    // 第一阶段已通过，第二阶段审批中
                    candidate.setStatus(CandidateStrategyStatusEnum.WAIT_ONLINE.getCode());
                }
                // 都是审批中的用户为null
                if(StringUtils.isNotEmpty(userName)){
                    userOperateStrategyLogService.logUserOperation(candidate.getId(),
                            CandidateStrategyOpeEnum.APPROVE_SUCCESS, userName);
                }
                break;
            case REJECTED:
                // 第二阶段还未开始
                if(!isSecondStageApproved){
                    if (getRollBackFlag(candidate)) {
                        candidate.setStatus(CandidateStrategyStatusEnum.OFFLINE.getCode());
                    } else {
                        candidate.setStatus(CandidateStrategyStatusEnum.WAIT_APPROVE.getCode());
                    }
                }
                userOperateStrategyLogService.logUserOperation(candidate.getId(),
                        CandidateStrategyOpeEnum.APPROVE_REJECT, userName);
                break;
            case REVOKED:
                // 第二阶段还未开始
                if(!isSecondStageApproved){
                    if (getRollBackFlag(candidate)) {
                        candidate.setStatus(CandidateStrategyStatusEnum.OFFLINE.getCode());
                    } else {
                        candidate.setStatus(CandidateStrategyStatusEnum.WAIT_APPROVE.getCode());
                    }
                }
                userOperateStrategyLogService.logUserOperation(candidate.getId(),
                        CandidateStrategyOpeEnum.APPROVE_REVOKE, ApprovalWorkflowUtil.getApplyer(approvalDetailInfo));
                break;
            default:
                throw new RuntimeException("审批结果异常");
        }
    }

    /**
     * 处理普通审批逻辑
     */
    private void handleNormalApproval(AdminCandidateStrategy candidate, ApproveStatus approveResult, String approvalDetailInfo) {
        String userName = ApprovalWorkflowUtil.getMostRecentApprover(approvalDetailInfo);
        switch (approveResult) {
            case PASSED:
                candidate.setStatus(CandidateStrategyStatusEnum.WAIT_ONLINE.getCode());
                userOperateStrategyLogService.logUserOperation(candidate.getId(),
                        CandidateStrategyOpeEnum.APPROVE_SUCCESS, userName);
                break;
            case IN_APPROVAL:
                break;
            case REJECTED:
                if (getRollBackFlag(candidate)) {
                    candidate.setStatus(CandidateStrategyStatusEnum.OFFLINE.getCode());
                } else {
                    candidate.setStatus(CandidateStrategyStatusEnum.WAIT_APPROVE.getCode());
                }
                userOperateStrategyLogService.logUserOperation(candidate.getId(),
                        CandidateStrategyOpeEnum.APPROVE_REJECT, userName);
                break;
            case REVOKED:
                if (getRollBackFlag(candidate)) {
                    candidate.setStatus(CandidateStrategyStatusEnum.OFFLINE.getCode());
                } else {
                    candidate.setStatus(CandidateStrategyStatusEnum.WAIT_APPROVE.getCode());
                }
                userOperateStrategyLogService.logUserOperation(candidate.getId(),
                        CandidateStrategyOpeEnum.APPROVE_REVOKE, userName);
                break;
            default:
                throw new RuntimeException("审批结果异常");
        }
    }

    /**
     * 查看是否是回滚审批
     * @param candidate
     * @return
     */
    private Boolean getRollBackFlag(AdminCandidateStrategy candidate) {
        String strategyApplyInfo = candidate.getStrategyApplyInfo();
        AdminStrategyApply adminStrategyApply = JSON.parseObject(strategyApplyInfo, AdminStrategyApply.class);
        return adminStrategyApply.getRollbackFlag();
    }

    @Override
    @MoreTransaction(value = {"adminTransactionManager","rmTransactionManager"})
    public void online(Integer id) {
        AdminCandidateStrategy adminCandidateStrategy = adminCandidateStrategyService.selectById(id);
        if(adminCandidateStrategy == null) {
            throw new RuntimeException("策略候选表记录不存在！");
        }

        if (CandidateStrategyStatusEnum.ONLINE.name().equals(adminCandidateStrategy.getStatus())) {
            throw new RuntimeException("该策略已上线，请刷新页面！");
        }

        if (!CandidateStrategyStatusEnum.WAIT_ONLINE.name().equals(adminCandidateStrategy.getStatus())) {
            throw new RuntimeException("策略候选表记录状态不是待上线状态，异常！");
        }


        toOnline(adminCandidateStrategy, UserInfoUtil.getUsername(), CandidateStrategyOpeEnum.TO_ONLINE, false);
    }

    private void toOnline(AdminCandidateStrategy adminCandidateStrategy, String userName, CandidateStrategyOpeEnum operation, boolean isRollFlog) {
        disablePreviousExperiments(adminCandidateStrategy);
        updateStrategyStatus(adminCandidateStrategy);

        userOperateStrategyLogService.logUserOperation(adminCandidateStrategy.getId(),
                operation, userName);

        if(!isRollFlog){ // 回滚操作不需要更新，用户点击详情会自动拉取
            // 当前用户上线的策略，那么相当于当前用户已经拉取过线上代码
            ruleProjectService.updateUserHistoryForOnlineId(adminCandidateStrategy.getProjectId(), userName);
        }

        // 通知上线成功
        notifyDeploySuccess(adminCandidateStrategy);

        // 发送企业微信通知
        alertNotificationHandler.sendStrategyOnlineToAlert(adminCandidateStrategy.getStrategyType()
                , getEventCodesByStrategyType(adminCandidateStrategy.getStrategyType())
                , userName);
    }

    /**
     * 根据策略类型获取关联的事件编码列表
     * @param strategyType 策略类型
     * @return 关联的事件编码列表
     */
    private String getEventCodesByStrategyType(String strategyType) {
        List<String> eventCodesByStrategyType = eventStrategyRelationMapper.selectEventCodesByStrategyType(strategyType);
        if (!CollectionUtils.isEmpty(eventCodesByStrategyType)) {
            return eventCodesByStrategyType.get(0);
        }
        return null;
    }

    private void notifyDeploySuccess(AdminCandidateStrategy adminCandidateStrategy) {
        Map<String, Integer> nodeCodeIdMap = JSON.parseObject(adminCandidateStrategy.getCodeIds(), Map.class);
        nodeCodeIdMap.forEach((nodeName, codeId) -> {
            Map<String, String> argMap = new HashMap<>();
            argMap.put("strategyNodeCode", nodeName);
            argMap.put("strategyType", adminCandidateStrategy.getStrategyType());
            argMap.put("id", String.valueOf(adminCandidateStrategy.getId()));
            deploySuccessNotifier.notifyListeners(argMap);
        });
    }

    private void disablePreviousExperiments(AdminCandidateStrategy adminCandidateStrategy) {
        List<AdminStrategyExp> adminStrategyExperimentList = adminStrategyExpMapper
                .getAdminStrategyExp(adminCandidateStrategy.getStrategyType());
        if (!CollectionUtils.isEmpty(adminStrategyExperimentList)) {
            for (AdminStrategyExp adminStrategyExp : adminStrategyExperimentList) {
                if (StrategyExpStatusEnum.ENABLE.name().equals(adminStrategyExp.getStatus())) {
                    AdminStrategyExp strategyExperiment = new AdminStrategyExp();
                    strategyExperiment.setId(adminStrategyExp.getId());
                    strategyExperiment.setStatus(StrategyExpStatusEnum.DISABLE.name());
                    adminStrategyExpMapper.update(strategyExperiment);
                    new Thread(() -> adminStrategyExpService.updateStrategyExperimentStatus(adminStrategyExp)).start();
                }
            }
        }
    }

    private void updateStrategyStatus(AdminCandidateStrategy adminCandidateStrategy) {
        Date now = new Date();
        AdminCandidateStrategy adminCandidateStrategyOldOnline = adminCandidateStrategyService.selectDeploySuccess(adminCandidateStrategy.getStrategyType());
        adminCandidateStrategy.setStatus(CandidateStrategyStatusEnum.ONLINE.getCode());
        adminCandidateStrategy.setUpdateTime(now);
        adminCandidateStrategyService.update(adminCandidateStrategy);

        if (adminCandidateStrategyOldOnline != null) {
            adminCandidateStrategyOldOnline.setStatus(CandidateStrategyStatusEnum.OFFLINE.getCode());
            adminCandidateStrategyOldOnline.setUpdateTime(now);
            adminCandidateStrategyService.update(adminCandidateStrategyOldOnline);
        }
    }

    @Override
    public void rollback(Integer id) {
        AdminCandidateStrategy adminCandidateStrategy = adminCandidateStrategyService.selectById(id);
        if (adminCandidateStrategy == null) {
            LoggerProxy.error("rollbackError", logger, "strategy candidate is null, id={}", id);
            throw new RuntimeException("策略候选表记录不存在！");
        }
        // 查询是否有不是终态的策略候选表记录
        adminCandidateStrategyService.selectAllNotFinal(adminCandidateStrategy.getStrategyType())
                .forEach(strategy -> {
                    throw new RuntimeException("存在未终态的策略候选表记录，请先废弃！id:" + strategy.getId());
                });
        AdminStrategyApply adminStrategyApply = JSON.parseObject(adminCandidateStrategy.getStrategyApplyInfo(), AdminStrategyApply.class);
        adminStrategyApply.setRollbackFlag(true); // 代表申请是回滚操作
        onlineForApprove(adminStrategyApply);
    }

    public void preCheckApprove(AdminCandidateStrategy adminCandidateStrategy) {
        Map<String, List<String>> unBindVarsForNodeName = new HashMap<>();
        JSONObject codeIds = JSON.parseObject(adminCandidateStrategy.getCodeIds());
        for (String nodeName : codeIds.keySet()) {
            AdminStrategyCode adminStrategyCode = adminStrategyCodeMapper.selectStrategyCode(Long.valueOf(codeIds.getInteger(nodeName)));
            Set<String> usedVars = strategyCodeHandler.queryVariableList(adminStrategyCode.getStrategyCode());
            Set<String> ruleScoreUsedVars = ruleScoreHandler.queryUsedVarsForRules(adminCandidateStrategy.getStrategyType(), nodeName);
            usedVars.addAll(ruleScoreUsedVars);
            List<String> bindVars = strategyCodeHandler.queryBindVars(adminCandidateStrategy.getStrategyType(), nodeName);
            List<String> unBindVars = new ArrayList<>();
            for (String usedVar : usedVars) {
                if (!bindVars.contains(usedVar)) {
                    unBindVars.add(usedVar);
                }
            }
            if (!CollectionUtils.isEmpty(unBindVars)) {
                unBindVarsForNodeName.put(nodeName, unBindVars);
            }
        }
        if (!CollectionUtils.isEmpty(unBindVarsForNodeName)) {
            throw new RuntimeException("策略变量未绑定，请绑定后再申请上线: " + JSON.toJSONString(unBindVarsForNodeName));
        }
    }
}