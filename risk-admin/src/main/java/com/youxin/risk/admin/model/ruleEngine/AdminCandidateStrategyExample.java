package com.youxin.risk.admin.model.ruleEngine;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AdminCandidateStrategyExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    private Boolean forUpdate;

    public AdminCandidateStrategyExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    public void setForUpdate(Boolean forUpdate) {
        this.forUpdate = forUpdate;
    }

    public Boolean getForUpdate() {
        return forUpdate;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGroupNameIsNull() {
            addCriterion("group_name is null");
            return (Criteria) this;
        }

        public Criteria andGroupNameIsNotNull() {
            addCriterion("group_name is not null");
            return (Criteria) this;
        }

        public Criteria andGroupNameEqualTo(String value) {
            addCriterion("group_name =", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameNotEqualTo(String value) {
            addCriterion("group_name <>", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameGreaterThan(String value) {
            addCriterion("group_name >", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameGreaterThanOrEqualTo(String value) {
            addCriterion("group_name >=", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameLessThan(String value) {
            addCriterion("group_name <", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameLessThanOrEqualTo(String value) {
            addCriterion("group_name <=", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameLike(String value) {
            addCriterion("group_name like", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameNotLike(String value) {
            addCriterion("group_name not like", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameIn(List<String> values) {
            addCriterion("group_name in", values, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameNotIn(List<String> values) {
            addCriterion("group_name not in", values, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameBetween(String value1, String value2) {
            addCriterion("group_name between", value1, value2, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameNotBetween(String value1, String value2) {
            addCriterion("group_name not between", value1, value2, "groupName");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Integer value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Integer value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Integer value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Integer value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Integer value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Integer> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Integer> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Integer value1, Integer value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Integer value1, Integer value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andStrategyTypeIsNull() {
            addCriterion("strategy_type is null");
            return (Criteria) this;
        }

        public Criteria andStrategyTypeIsNotNull() {
            addCriterion("strategy_type is not null");
            return (Criteria) this;
        }

        public Criteria andStrategyTypeEqualTo(String value) {
            addCriterion("strategy_type =", value, "strategyType");
            return (Criteria) this;
        }

        public Criteria andStrategyTypeNotEqualTo(String value) {
            addCriterion("strategy_type <>", value, "strategyType");
            return (Criteria) this;
        }

        public Criteria andStrategyTypeGreaterThan(String value) {
            addCriterion("strategy_type >", value, "strategyType");
            return (Criteria) this;
        }

        public Criteria andStrategyTypeGreaterThanOrEqualTo(String value) {
            addCriterion("strategy_type >=", value, "strategyType");
            return (Criteria) this;
        }

        public Criteria andStrategyTypeLessThan(String value) {
            addCriterion("strategy_type <", value, "strategyType");
            return (Criteria) this;
        }

        public Criteria andStrategyTypeLessThanOrEqualTo(String value) {
            addCriterion("strategy_type <=", value, "strategyType");
            return (Criteria) this;
        }

        public Criteria andStrategyTypeLike(String value) {
            addCriterion("strategy_type like", value, "strategyType");
            return (Criteria) this;
        }

        public Criteria andStrategyTypeNotLike(String value) {
            addCriterion("strategy_type not like", value, "strategyType");
            return (Criteria) this;
        }

        public Criteria andStrategyTypeIn(List<String> values) {
            addCriterion("strategy_type in", values, "strategyType");
            return (Criteria) this;
        }

        public Criteria andStrategyTypeNotIn(List<String> values) {
            addCriterion("strategy_type not in", values, "strategyType");
            return (Criteria) this;
        }

        public Criteria andStrategyTypeBetween(String value1, String value2) {
            addCriterion("strategy_type between", value1, value2, "strategyType");
            return (Criteria) this;
        }

        public Criteria andStrategyTypeNotBetween(String value1, String value2) {
            addCriterion("strategy_type not between", value1, value2, "strategyType");
            return (Criteria) this;
        }

        public Criteria andStrategyNameIsNull() {
            addCriterion("strategy_name is null");
            return (Criteria) this;
        }

        public Criteria andStrategyNameIsNotNull() {
            addCriterion("strategy_name is not null");
            return (Criteria) this;
        }

        public Criteria andStrategyNameEqualTo(String value) {
            addCriterion("strategy_name =", value, "strategyName");
            return (Criteria) this;
        }

        public Criteria andStrategyNameNotEqualTo(String value) {
            addCriterion("strategy_name <>", value, "strategyName");
            return (Criteria) this;
        }

        public Criteria andStrategyNameGreaterThan(String value) {
            addCriterion("strategy_name >", value, "strategyName");
            return (Criteria) this;
        }

        public Criteria andStrategyNameGreaterThanOrEqualTo(String value) {
            addCriterion("strategy_name >=", value, "strategyName");
            return (Criteria) this;
        }

        public Criteria andStrategyNameLessThan(String value) {
            addCriterion("strategy_name <", value, "strategyName");
            return (Criteria) this;
        }

        public Criteria andStrategyNameLessThanOrEqualTo(String value) {
            addCriterion("strategy_name <=", value, "strategyName");
            return (Criteria) this;
        }

        public Criteria andStrategyNameLike(String value) {
            addCriterion("strategy_name like", value, "strategyName");
            return (Criteria) this;
        }

        public Criteria andStrategyNameNotLike(String value) {
            addCriterion("strategy_name not like", value, "strategyName");
            return (Criteria) this;
        }

        public Criteria andStrategyNameIn(List<String> values) {
            addCriterion("strategy_name in", values, "strategyName");
            return (Criteria) this;
        }

        public Criteria andStrategyNameNotIn(List<String> values) {
            addCriterion("strategy_name not in", values, "strategyName");
            return (Criteria) this;
        }

        public Criteria andStrategyNameBetween(String value1, String value2) {
            addCriterion("strategy_name between", value1, value2, "strategyName");
            return (Criteria) this;
        }

        public Criteria andStrategyNameNotBetween(String value1, String value2) {
            addCriterion("strategy_name not between", value1, value2, "strategyName");
            return (Criteria) this;
        }

        public Criteria andCodeIdsIsNull() {
            addCriterion("code_ids is null");
            return (Criteria) this;
        }

        public Criteria andCodeIdsIsNotNull() {
            addCriterion("code_ids is not null");
            return (Criteria) this;
        }

        public Criteria andCodeIdsEqualTo(String value) {
            addCriterion("code_ids =", value, "codeIds");
            return (Criteria) this;
        }

        public Criteria andCodeIdsNotEqualTo(String value) {
            addCriterion("code_ids <>", value, "codeIds");
            return (Criteria) this;
        }

        public Criteria andCodeIdsGreaterThan(String value) {
            addCriterion("code_ids >", value, "codeIds");
            return (Criteria) this;
        }

        public Criteria andCodeIdsGreaterThanOrEqualTo(String value) {
            addCriterion("code_ids >=", value, "codeIds");
            return (Criteria) this;
        }

        public Criteria andCodeIdsLessThan(String value) {
            addCriterion("code_ids <", value, "codeIds");
            return (Criteria) this;
        }

        public Criteria andCodeIdsLessThanOrEqualTo(String value) {
            addCriterion("code_ids <=", value, "codeIds");
            return (Criteria) this;
        }

        public Criteria andCodeIdsLike(String value) {
            addCriterion("code_ids like", value, "codeIds");
            return (Criteria) this;
        }

        public Criteria andCodeIdsNotLike(String value) {
            addCriterion("code_ids not like", value, "codeIds");
            return (Criteria) this;
        }

        public Criteria andCodeIdsIn(List<String> values) {
            addCriterion("code_ids in", values, "codeIds");
            return (Criteria) this;
        }

        public Criteria andCodeIdsNotIn(List<String> values) {
            addCriterion("code_ids not in", values, "codeIds");
            return (Criteria) this;
        }

        public Criteria andCodeIdsBetween(String value1, String value2) {
            addCriterion("code_ids between", value1, value2, "codeIds");
            return (Criteria) this;
        }

        public Criteria andCodeIdsNotBetween(String value1, String value2) {
            addCriterion("code_ids not between", value1, value2, "codeIds");
            return (Criteria) this;
        }

        public Criteria andCommitIdsIsNull() {
            addCriterion("commit_ids is null");
            return (Criteria) this;
        }

        public Criteria andCommitIdsIsNotNull() {
            addCriterion("commit_ids is not null");
            return (Criteria) this;
        }

        public Criteria andCommitIdsEqualTo(String value) {
            addCriterion("commit_ids =", value, "commitIds");
            return (Criteria) this;
        }

        public Criteria andCommitIdsNotEqualTo(String value) {
            addCriterion("commit_ids <>", value, "commitIds");
            return (Criteria) this;
        }

        public Criteria andCommitIdsGreaterThan(String value) {
            addCriterion("commit_ids >", value, "commitIds");
            return (Criteria) this;
        }

        public Criteria andCommitIdsGreaterThanOrEqualTo(String value) {
            addCriterion("commit_ids >=", value, "commitIds");
            return (Criteria) this;
        }

        public Criteria andCommitIdsLessThan(String value) {
            addCriterion("commit_ids <", value, "commitIds");
            return (Criteria) this;
        }

        public Criteria andCommitIdsLessThanOrEqualTo(String value) {
            addCriterion("commit_ids <=", value, "commitIds");
            return (Criteria) this;
        }

        public Criteria andCommitIdsLike(String value) {
            addCriterion("commit_ids like", value, "commitIds");
            return (Criteria) this;
        }

        public Criteria andCommitIdsNotLike(String value) {
            addCriterion("commit_ids not like", value, "commitIds");
            return (Criteria) this;
        }

        public Criteria andCommitIdsIn(List<String> values) {
            addCriterion("commit_ids in", values, "commitIds");
            return (Criteria) this;
        }

        public Criteria andCommitIdsNotIn(List<String> values) {
            addCriterion("commit_ids not in", values, "commitIds");
            return (Criteria) this;
        }

        public Criteria andCommitIdsBetween(String value1, String value2) {
            addCriterion("commit_ids between", value1, value2, "commitIds");
            return (Criteria) this;
        }

        public Criteria andCommitIdsNotBetween(String value1, String value2) {
            addCriterion("commit_ids not between", value1, value2, "commitIds");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("`status` like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("`status` not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andUserNameIsNull() {
            addCriterion("user_name is null");
            return (Criteria) this;
        }

        public Criteria andUserNameIsNotNull() {
            addCriterion("user_name is not null");
            return (Criteria) this;
        }

        public Criteria andUserNameEqualTo(String value) {
            addCriterion("user_name =", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotEqualTo(String value) {
            addCriterion("user_name <>", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameGreaterThan(String value) {
            addCriterion("user_name >", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("user_name >=", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLessThan(String value) {
            addCriterion("user_name <", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLessThanOrEqualTo(String value) {
            addCriterion("user_name <=", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLike(String value) {
            addCriterion("user_name like", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotLike(String value) {
            addCriterion("user_name not like", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameIn(List<String> values) {
            addCriterion("user_name in", values, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotIn(List<String> values) {
            addCriterion("user_name not in", values, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameBetween(String value1, String value2) {
            addCriterion("user_name between", value1, value2, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotBetween(String value1, String value2) {
            addCriterion("user_name not between", value1, value2, "userName");
            return (Criteria) this;
        }

        public Criteria andStrategyApplyInfoIsNull() {
            addCriterion("strategy_apply_info is null");
            return (Criteria) this;
        }

        public Criteria andStrategyApplyInfoIsNotNull() {
            addCriterion("strategy_apply_info is not null");
            return (Criteria) this;
        }

        public Criteria andStrategyApplyInfoEqualTo(String value) {
            addCriterion("strategy_apply_info =", value, "strategyApplyInfo");
            return (Criteria) this;
        }

        public Criteria andStrategyApplyInfoNotEqualTo(String value) {
            addCriterion("strategy_apply_info <>", value, "strategyApplyInfo");
            return (Criteria) this;
        }

        public Criteria andStrategyApplyInfoGreaterThan(String value) {
            addCriterion("strategy_apply_info >", value, "strategyApplyInfo");
            return (Criteria) this;
        }

        public Criteria andStrategyApplyInfoGreaterThanOrEqualTo(String value) {
            addCriterion("strategy_apply_info >=", value, "strategyApplyInfo");
            return (Criteria) this;
        }

        public Criteria andStrategyApplyInfoLessThan(String value) {
            addCriterion("strategy_apply_info <", value, "strategyApplyInfo");
            return (Criteria) this;
        }

        public Criteria andStrategyApplyInfoLessThanOrEqualTo(String value) {
            addCriterion("strategy_apply_info <=", value, "strategyApplyInfo");
            return (Criteria) this;
        }

        public Criteria andStrategyApplyInfoLike(String value) {
            addCriterion("strategy_apply_info like", value, "strategyApplyInfo");
            return (Criteria) this;
        }

        public Criteria andStrategyApplyInfoNotLike(String value) {
            addCriterion("strategy_apply_info not like", value, "strategyApplyInfo");
            return (Criteria) this;
        }

        public Criteria andStrategyApplyInfoIn(List<String> values) {
            addCriterion("strategy_apply_info in", values, "strategyApplyInfo");
            return (Criteria) this;
        }

        public Criteria andStrategyApplyInfoNotIn(List<String> values) {
            addCriterion("strategy_apply_info not in", values, "strategyApplyInfo");
            return (Criteria) this;
        }

        public Criteria andStrategyApplyInfoBetween(String value1, String value2) {
            addCriterion("strategy_apply_info between", value1, value2, "strategyApplyInfo");
            return (Criteria) this;
        }

        public Criteria andStrategyApplyInfoNotBetween(String value1, String value2) {
            addCriterion("strategy_apply_info not between", value1, value2, "strategyApplyInfo");
            return (Criteria) this;
        }

        public Criteria andTestNumberIsNull() {
            addCriterion("test_number is null");
            return (Criteria) this;
        }

        public Criteria andTestNumberIsNotNull() {
            addCriterion("test_number is not null");
            return (Criteria) this;
        }

        public Criteria andTestNumberEqualTo(Integer value) {
            addCriterion("test_number =", value, "testNumber");
            return (Criteria) this;
        }

        public Criteria andTestNumberNotEqualTo(Integer value) {
            addCriterion("test_number <>", value, "testNumber");
            return (Criteria) this;
        }

        public Criteria andTestNumberGreaterThan(Integer value) {
            addCriterion("test_number >", value, "testNumber");
            return (Criteria) this;
        }

        public Criteria andTestNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("test_number >=", value, "testNumber");
            return (Criteria) this;
        }

        public Criteria andTestNumberLessThan(Integer value) {
            addCriterion("test_number <", value, "testNumber");
            return (Criteria) this;
        }

        public Criteria andTestNumberLessThanOrEqualTo(Integer value) {
            addCriterion("test_number <=", value, "testNumber");
            return (Criteria) this;
        }

        public Criteria andTestNumberIn(List<Integer> values) {
            addCriterion("test_number in", values, "testNumber");
            return (Criteria) this;
        }

        public Criteria andTestNumberNotIn(List<Integer> values) {
            addCriterion("test_number not in", values, "testNumber");
            return (Criteria) this;
        }

        public Criteria andTestNumberBetween(Integer value1, Integer value2) {
            addCriterion("test_number between", value1, value2, "testNumber");
            return (Criteria) this;
        }

        public Criteria andTestNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("test_number not between", value1, value2, "testNumber");
            return (Criteria) this;
        }

        public Criteria andSampleStartTimeIsNull() {
            addCriterion("sample_start_time is null");
            return (Criteria) this;
        }

        public Criteria andSampleStartTimeIsNotNull() {
            addCriterion("sample_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andSampleStartTimeEqualTo(Date value) {
            addCriterion("sample_start_time =", value, "sampleStartTime");
            return (Criteria) this;
        }

        public Criteria andSampleStartTimeNotEqualTo(Date value) {
            addCriterion("sample_start_time <>", value, "sampleStartTime");
            return (Criteria) this;
        }

        public Criteria andSampleStartTimeGreaterThan(Date value) {
            addCriterion("sample_start_time >", value, "sampleStartTime");
            return (Criteria) this;
        }

        public Criteria andSampleStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("sample_start_time >=", value, "sampleStartTime");
            return (Criteria) this;
        }

        public Criteria andSampleStartTimeLessThan(Date value) {
            addCriterion("sample_start_time <", value, "sampleStartTime");
            return (Criteria) this;
        }

        public Criteria andSampleStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("sample_start_time <=", value, "sampleStartTime");
            return (Criteria) this;
        }

        public Criteria andSampleStartTimeIn(List<Date> values) {
            addCriterion("sample_start_time in", values, "sampleStartTime");
            return (Criteria) this;
        }

        public Criteria andSampleStartTimeNotIn(List<Date> values) {
            addCriterion("sample_start_time not in", values, "sampleStartTime");
            return (Criteria) this;
        }

        public Criteria andSampleStartTimeBetween(Date value1, Date value2) {
            addCriterion("sample_start_time between", value1, value2, "sampleStartTime");
            return (Criteria) this;
        }

        public Criteria andSampleStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("sample_start_time not between", value1, value2, "sampleStartTime");
            return (Criteria) this;
        }

        public Criteria andSampleEndTimeIsNull() {
            addCriterion("sample_end_time is null");
            return (Criteria) this;
        }

        public Criteria andSampleEndTimeIsNotNull() {
            addCriterion("sample_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andSampleEndTimeEqualTo(Date value) {
            addCriterion("sample_end_time =", value, "sampleEndTime");
            return (Criteria) this;
        }

        public Criteria andSampleEndTimeNotEqualTo(Date value) {
            addCriterion("sample_end_time <>", value, "sampleEndTime");
            return (Criteria) this;
        }

        public Criteria andSampleEndTimeGreaterThan(Date value) {
            addCriterion("sample_end_time >", value, "sampleEndTime");
            return (Criteria) this;
        }

        public Criteria andSampleEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("sample_end_time >=", value, "sampleEndTime");
            return (Criteria) this;
        }

        public Criteria andSampleEndTimeLessThan(Date value) {
            addCriterion("sample_end_time <", value, "sampleEndTime");
            return (Criteria) this;
        }

        public Criteria andSampleEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("sample_end_time <=", value, "sampleEndTime");
            return (Criteria) this;
        }

        public Criteria andSampleEndTimeIn(List<Date> values) {
            addCriterion("sample_end_time in", values, "sampleEndTime");
            return (Criteria) this;
        }

        public Criteria andSampleEndTimeNotIn(List<Date> values) {
            addCriterion("sample_end_time not in", values, "sampleEndTime");
            return (Criteria) this;
        }

        public Criteria andSampleEndTimeBetween(Date value1, Date value2) {
            addCriterion("sample_end_time between", value1, value2, "sampleEndTime");
            return (Criteria) this;
        }

        public Criteria andSampleEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("sample_end_time not between", value1, value2, "sampleEndTime");
            return (Criteria) this;
        }

        public Criteria andSampleStepsIsNull() {
            addCriterion("sample_steps is null");
            return (Criteria) this;
        }

        public Criteria andSampleStepsIsNotNull() {
            addCriterion("sample_steps is not null");
            return (Criteria) this;
        }

        public Criteria andSampleStepsEqualTo(String value) {
            addCriterion("sample_steps =", value, "sampleSteps");
            return (Criteria) this;
        }

        public Criteria andSampleStepsNotEqualTo(String value) {
            addCriterion("sample_steps <>", value, "sampleSteps");
            return (Criteria) this;
        }

        public Criteria andSampleStepsGreaterThan(String value) {
            addCriterion("sample_steps >", value, "sampleSteps");
            return (Criteria) this;
        }

        public Criteria andSampleStepsGreaterThanOrEqualTo(String value) {
            addCriterion("sample_steps >=", value, "sampleSteps");
            return (Criteria) this;
        }

        public Criteria andSampleStepsLessThan(String value) {
            addCriterion("sample_steps <", value, "sampleSteps");
            return (Criteria) this;
        }

        public Criteria andSampleStepsLessThanOrEqualTo(String value) {
            addCriterion("sample_steps <=", value, "sampleSteps");
            return (Criteria) this;
        }

        public Criteria andSampleStepsLike(String value) {
            addCriterion("sample_steps like", value, "sampleSteps");
            return (Criteria) this;
        }

        public Criteria andSampleStepsNotLike(String value) {
            addCriterion("sample_steps not like", value, "sampleSteps");
            return (Criteria) this;
        }

        public Criteria andSampleStepsIn(List<String> values) {
            addCriterion("sample_steps in", values, "sampleSteps");
            return (Criteria) this;
        }

        public Criteria andSampleStepsNotIn(List<String> values) {
            addCriterion("sample_steps not in", values, "sampleSteps");
            return (Criteria) this;
        }

        public Criteria andSampleStepsBetween(String value1, String value2) {
            addCriterion("sample_steps between", value1, value2, "sampleSteps");
            return (Criteria) this;
        }

        public Criteria andSampleStepsNotBetween(String value1, String value2) {
            addCriterion("sample_steps not between", value1, value2, "sampleSteps");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNull() {
            addCriterion("task_id is null");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNotNull() {
            addCriterion("task_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaskIdEqualTo(String value) {
            addCriterion("task_id =", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotEqualTo(String value) {
            addCriterion("task_id <>", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThan(String value) {
            addCriterion("task_id >", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThanOrEqualTo(String value) {
            addCriterion("task_id >=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThan(String value) {
            addCriterion("task_id <", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThanOrEqualTo(String value) {
            addCriterion("task_id <=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLike(String value) {
            addCriterion("task_id like", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotLike(String value) {
            addCriterion("task_id not like", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIn(List<String> values) {
            addCriterion("task_id in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotIn(List<String> values) {
            addCriterion("task_id not in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdBetween(String value1, String value2) {
            addCriterion("task_id between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotBetween(String value1, String value2) {
            addCriterion("task_id not between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andDevelopmentCenterUrlIsNull() {
            addCriterion("development_center_url is null");
            return (Criteria) this;
        }

        public Criteria andDevelopmentCenterUrlIsNotNull() {
            addCriterion("development_center_url is not null");
            return (Criteria) this;
        }

        public Criteria andDevelopmentCenterUrlEqualTo(String value) {
            addCriterion("development_center_url =", value, "developmentCenterUrl");
            return (Criteria) this;
        }

        public Criteria andDevelopmentCenterUrlNotEqualTo(String value) {
            addCriterion("development_center_url <>", value, "developmentCenterUrl");
            return (Criteria) this;
        }

        public Criteria andDevelopmentCenterUrlGreaterThan(String value) {
            addCriterion("development_center_url >", value, "developmentCenterUrl");
            return (Criteria) this;
        }

        public Criteria andDevelopmentCenterUrlGreaterThanOrEqualTo(String value) {
            addCriterion("development_center_url >=", value, "developmentCenterUrl");
            return (Criteria) this;
        }

        public Criteria andDevelopmentCenterUrlLessThan(String value) {
            addCriterion("development_center_url <", value, "developmentCenterUrl");
            return (Criteria) this;
        }

        public Criteria andDevelopmentCenterUrlLessThanOrEqualTo(String value) {
            addCriterion("development_center_url <=", value, "developmentCenterUrl");
            return (Criteria) this;
        }

        public Criteria andDevelopmentCenterUrlLike(String value) {
            addCriterion("development_center_url like", value, "developmentCenterUrl");
            return (Criteria) this;
        }

        public Criteria andDevelopmentCenterUrlNotLike(String value) {
            addCriterion("development_center_url not like", value, "developmentCenterUrl");
            return (Criteria) this;
        }

        public Criteria andDevelopmentCenterUrlIn(List<String> values) {
            addCriterion("development_center_url in", values, "developmentCenterUrl");
            return (Criteria) this;
        }

        public Criteria andDevelopmentCenterUrlNotIn(List<String> values) {
            addCriterion("development_center_url not in", values, "developmentCenterUrl");
            return (Criteria) this;
        }

        public Criteria andDevelopmentCenterUrlBetween(String value1, String value2) {
            addCriterion("development_center_url between", value1, value2, "developmentCenterUrl");
            return (Criteria) this;
        }

        public Criteria andDevelopmentCenterUrlNotBetween(String value1, String value2) {
            addCriterion("development_center_url not between", value1, value2, "developmentCenterUrl");
            return (Criteria) this;
        }

        public Criteria andResultTableNameIsNull() {
            addCriterion("result_table_name is null");
            return (Criteria) this;
        }

        public Criteria andResultTableNameIsNotNull() {
            addCriterion("result_table_name is not null");
            return (Criteria) this;
        }

        public Criteria andResultTableNameEqualTo(String value) {
            addCriterion("result_table_name =", value, "resultTableName");
            return (Criteria) this;
        }

        public Criteria andResultTableNameNotEqualTo(String value) {
            addCriterion("result_table_name <>", value, "resultTableName");
            return (Criteria) this;
        }

        public Criteria andResultTableNameGreaterThan(String value) {
            addCriterion("result_table_name >", value, "resultTableName");
            return (Criteria) this;
        }

        public Criteria andResultTableNameGreaterThanOrEqualTo(String value) {
            addCriterion("result_table_name >=", value, "resultTableName");
            return (Criteria) this;
        }

        public Criteria andResultTableNameLessThan(String value) {
            addCriterion("result_table_name <", value, "resultTableName");
            return (Criteria) this;
        }

        public Criteria andResultTableNameLessThanOrEqualTo(String value) {
            addCriterion("result_table_name <=", value, "resultTableName");
            return (Criteria) this;
        }

        public Criteria andResultTableNameLike(String value) {
            addCriterion("result_table_name like", value, "resultTableName");
            return (Criteria) this;
        }

        public Criteria andResultTableNameNotLike(String value) {
            addCriterion("result_table_name not like", value, "resultTableName");
            return (Criteria) this;
        }

        public Criteria andResultTableNameIn(List<String> values) {
            addCriterion("result_table_name in", values, "resultTableName");
            return (Criteria) this;
        }

        public Criteria andResultTableNameNotIn(List<String> values) {
            addCriterion("result_table_name not in", values, "resultTableName");
            return (Criteria) this;
        }

        public Criteria andResultTableNameBetween(String value1, String value2) {
            addCriterion("result_table_name between", value1, value2, "resultTableName");
            return (Criteria) this;
        }

        public Criteria andResultTableNameNotBetween(String value1, String value2) {
            addCriterion("result_table_name not between", value1, value2, "resultTableName");
            return (Criteria) this;
        }

        public Criteria andVariableConfirmFlagIsNull() {
            addCriterion("variable_confirm_flag is null");
            return (Criteria) this;
        }

        public Criteria andVariableConfirmFlagIsNotNull() {
            addCriterion("variable_confirm_flag is not null");
            return (Criteria) this;
        }

        public Criteria andVariableConfirmFlagEqualTo(Byte value) {
            addCriterion("variable_confirm_flag =", value, "variableConfirmFlag");
            return (Criteria) this;
        }

        public Criteria andVariableConfirmFlagNotEqualTo(Byte value) {
            addCriterion("variable_confirm_flag <>", value, "variableConfirmFlag");
            return (Criteria) this;
        }

        public Criteria andVariableConfirmFlagGreaterThan(Byte value) {
            addCriterion("variable_confirm_flag >", value, "variableConfirmFlag");
            return (Criteria) this;
        }

        public Criteria andVariableConfirmFlagGreaterThanOrEqualTo(Byte value) {
            addCriterion("variable_confirm_flag >=", value, "variableConfirmFlag");
            return (Criteria) this;
        }

        public Criteria andVariableConfirmFlagLessThan(Byte value) {
            addCriterion("variable_confirm_flag <", value, "variableConfirmFlag");
            return (Criteria) this;
        }

        public Criteria andVariableConfirmFlagLessThanOrEqualTo(Byte value) {
            addCriterion("variable_confirm_flag <=", value, "variableConfirmFlag");
            return (Criteria) this;
        }

        public Criteria andVariableConfirmFlagIn(List<Byte> values) {
            addCriterion("variable_confirm_flag in", values, "variableConfirmFlag");
            return (Criteria) this;
        }

        public Criteria andVariableConfirmFlagNotIn(List<Byte> values) {
            addCriterion("variable_confirm_flag not in", values, "variableConfirmFlag");
            return (Criteria) this;
        }

        public Criteria andVariableConfirmFlagBetween(Byte value1, Byte value2) {
            addCriterion("variable_confirm_flag between", value1, value2, "variableConfirmFlag");
            return (Criteria) this;
        }

        public Criteria andVariableConfirmFlagNotBetween(Byte value1, Byte value2) {
            addCriterion("variable_confirm_flag not between", value1, value2, "variableConfirmFlag");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}