package com.youxin.risk.admin.vo.ruleEngine;

import com.youxin.risk.admin.domain.ruleengine.AdminCandidateStrategy;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AdminCandidateStrategyDetailVo extends AdminCandidateStrategy {

    private List<AdminUserOperateStrategyLog> operationList;
}
