package com.youxin.risk.admin.controller;

import com.youxin.risk.admin.annotation.BusinessIsolation;
import com.youxin.risk.admin.service.AdminEventInfoService;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

import static com.youxin.risk.admin.controller.BaseController.buildSuccessResponse;

/**
 * @description: 为spider 提供配置服务类
 * @author: zhanhuadong
 * @create: 2022-11-07 11:06
 **/
@RestController
@RequestMapping("/forSpider")
public class AdminForSpiderController extends BaseController{

    /**
     * 读取事件限流配置
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getLimitMap", method = RequestMethod.GET)
    public ResponseEntity<?> getLimitMap() {
        Map<String, Integer> mapConfig = ApolloClientAdapter.getMapConfig(ApolloNamespaceEnum.GW_SPACE, "event.limit.map", Integer.class);
        return buildSuccessResponse(mapConfig);
    }

    /**
     * 读取跑批事件限流配置
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getBatchLimitMap", method = RequestMethod.GET)
    public ResponseEntity<?> getBatchLimitMap() {
        Map<String, Integer> mapConfig = ApolloClientAdapter.getMapConfig(ApolloNamespaceEnum.GW_SPACE, "batch.event.limit.map", Integer.class);
        return buildSuccessResponse(mapConfig);
    }

}
