package com.youxin.risk.admin.service.ruleEngine.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.admin.constants.*;
import com.youxin.risk.admin.service.AdminStrategyTypeService;
import com.youxin.risk.admin.service.ruleEngine.StrategyService;
import com.youxin.risk.commons.constants.StrategyDiffTypeEnums;
import com.youxin.risk.commons.constants.StrategyExpDiffTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 策略相关服务实现类
 */
@Service
public class StrategyServiceImpl implements StrategyService {

    private static final Logger logger = LoggerFactory.getLogger(StrategyServiceImpl.class);

    @Resource
    private AdminStrategyTypeService adminStrategyTypeService;

    @Override
    public JSONObject allTip(JSONObject jsonObject) {
        JSONArray typeArray = jsonObject.getJSONArray("type");
        JSONObject result = new JSONObject();
        for (int j = 0; j < typeArray.size(); j++) {
            String type = typeArray.getString(j);
            switch (type.toUpperCase()) {
                case "CANDIDATE_STRATEGY_STATUS":
                    result.put("CANDIDATE_STRATEGY_STATUS", getCandidateStrategyStatus());
                    break;
                case "STRATEGY_TYPE":
                    result.put("STRATEGY_TYPE", getStrategyTypes());
                    break;
                case "CMP_INTEGRATION_STATUS":
                    result.put("CMP_INTEGRATION_STATUS", getCmpIntegrationStatus());
                    break;
                case "STRATEGY_EXP_STATUS":
                    result.put("STRATEGY_EXP_STATUS", getStrategyExpStatusEnum());
                    break;
                case "STRATEGY_EXP_STOP_TYPE":
                    result.put("STRATEGY_EXP_STOP_TYPE", getStopTypeEnum());
                    break;
                case "STRATEGY_COMPARE_DIFF_TYPE":
                    result.put("STRATEGY_COMPARE_DIFF_TYPE", getStrategyCompareDiffTypeEnums());
                    break;
                case "STRATEGY_EXP_DIFF_TYPE":
                    result.put("STRATEGY_EXP_DIFF_TYPE", getStrategyExpDiffTypeEnum());
                    break;
                case "STRATEGY_DIFF_TYPE":
                    result.put("STRATEGY_DIFF_TYPE", getStrategyDiffTypeEnums());
                    break;
                default:
                    logger.warn("未知的策略类型: {}", type);
            }
        }
        return result;
    }

    private List<JSONObject> getCandidateStrategyStatus() {
        logger.info("获取候选策略状态");
        List<JSONObject> integrationStatusList = new ArrayList<>();
        for (CandidateStrategyStatusEnum status : CandidateStrategyStatusEnum.values()) {
            JSONObject m = new JSONObject();
            m.put("value", status.getCode());
            m.put("label", status.getDesc());
            integrationStatusList.add(m);
        }
        return integrationStatusList;
    }

    private List<JSONObject> getStrategyTypes() {
        logger.info("获取策略类型");
        List<JSONObject> strategyTypeList = new ArrayList<>();
        adminStrategyTypeService.selectAllStrategyTypeCode().forEach(strategyType -> {
            JSONObject m = new JSONObject();
            m.put("value", strategyType);
            m.put("label", strategyType);
            strategyTypeList.add(m);
        });
        return strategyTypeList;
    }

    private List<JSONObject> getStrategyExpStatusEnum() {
        logger.info("获取EXP状态");
        List<JSONObject> strategyExpStatusList = new ArrayList<>();
        for (StrategyExpStatusEnum status : StrategyExpStatusEnum.values()) {
            JSONObject m = new JSONObject();
            m.put("value", status.getCode());
            m.put("label", status.getDesc());
            strategyExpStatusList.add(m);
        }
        return strategyExpStatusList;
    }

    private List<JSONObject> getStopTypeEnum() {
        logger.info("获取stopType状态");
        List<JSONObject> strategyExpStatusList = new ArrayList<>();
        for (StrategyExpStopTypeEnum status : StrategyExpStopTypeEnum.values()) {
            JSONObject m = new JSONObject();
            m.put("value", status.getCode());
            m.put("label", status.getDesc());
            strategyExpStatusList.add(m);
        }
        return strategyExpStatusList;
    }

    private List<JSONObject> getStrategyExpDiffTypeEnum() {
        logger.info("获取diffType状态");
        List<JSONObject> strategyExpStatusList = new ArrayList<>();
        for (StrategyExpDiffTypeEnum status : StrategyExpDiffTypeEnum.values()) {
            JSONObject m = new JSONObject();
            m.put("value", status.getCode());
            m.put("label", status.getDesc());
            strategyExpStatusList.add(m);
        }
        return strategyExpStatusList;
    }

    private List<JSONObject> getStrategyCompareDiffTypeEnums() {
        logger.info("获取diffType状态");
        List<JSONObject> strategyExpStatusList = new ArrayList<>();
        for (StrategyCompareDiffTypeEnums status : StrategyCompareDiffTypeEnums.values()) {
            JSONObject m = new JSONObject();
            m.put("value", status.getCode());
            m.put("label", status.getDesc());
            strategyExpStatusList.add(m);
        }
        return strategyExpStatusList;
    }

    private List<JSONObject> getStrategyDiffTypeEnums() {
        logger.info("获取diffType状态");
        List<JSONObject> strategyExpStatusList = new ArrayList<>();
        for (StrategyDiffTypeEnums status : StrategyDiffTypeEnums.values()) {
            JSONObject m = new JSONObject();
            m.put("value", status.getCode());
            m.put("label", status.getDesc());
            strategyExpStatusList.add(m);
        }
        return strategyExpStatusList;
    }

    private List<JSONObject> getCmpIntegrationStatus() {
        logger.info("获取CMP集成状态");
        List<JSONObject> cmpIntegrationStatusList = new ArrayList<>();
        for (CmpIntegrationStatusEnum status : CmpIntegrationStatusEnum.values()) {
            JSONObject m = new JSONObject();
            m.put("value", status.getCode());
            m.put("label", status.getDesc());
            cmpIntegrationStatusList.add(m);
        }
        return cmpIntegrationStatusList;
    }

    @Override
    public List<String> queryNode(JSONObject jsonObject) {
        logger.info("查询策略节点");
        String strategyType = jsonObject.getString("strategyType");
        if (strategyType != null) {
            return adminStrategyTypeService.selectStrategyNodes(strategyType);
        }
        logger.warn("策略类型为空");
        return Collections.emptyList();
    }
}