package com.youxin.risk.admin.service.impl;

import com.youxin.risk.admin.dao.admin.AlertAdminCollectConfMapper;
import com.youxin.risk.admin.model.alert.AlertAdminCollectConf;
import com.youxin.risk.admin.service.AlertCollectConfService;
import com.youxin.risk.commons.utils.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date
 */
@Service
public class AlertCollectConfServiceImpl extends BaseServiceImpl<AlertAdminCollectConf> implements AlertCollectConfService {
    @Resource
    private AlertAdminCollectConfMapper alertAdminCollectConfMapper;

    @Override
    protected void init() {
        tableName = "alert_admin_collect_conf";
        baseMapper = alertAdminCollectConfMapper;
    }


    @Override
    public AlertAdminCollectConf getDetail(Long id) {
        AlertAdminCollectConf alertCollectConf = super.get(id);
        if(alertCollectConf != null && !StringUtils.isEmpty(alertCollectConf.getCollectCode())){
            alertCollectConf.setAlertAdminCollectConditions(alertAdminCollectConfMapper.selectCondition(alertCollectConf.getCollectCode()));
        }
        return alertCollectConf;
    }

    @Override
    public List<String> getAllCollectCodes() {
        return alertAdminCollectConfMapper.getAllCollectCodes();
    }

    @Override
    public void save(AlertAdminCollectConf alertAdminCollectConf){
        super.save(alertAdminCollectConf);
        //先删除字表所有condition在批量插入所有的conditions
        alertAdminCollectConfMapper.deleteChildConditions(alertAdminCollectConf.getCollectCode());
        if(!org.apache.commons.collections.CollectionUtils.isEmpty(alertAdminCollectConf.getAlertAdminCollectConditions())){
            alertAdminCollectConfMapper.SaveChildConditions(alertAdminCollectConf.getAlertAdminCollectConditions());
        }

    }

    @Override
    public void delete(Long id){
        AlertAdminCollectConf alertCollectConf = super.get(id);
        alertAdminCollectConfMapper.deleteChildConditions(alertCollectConf.getCollectCode());
        super.delete(id);

    }

}
