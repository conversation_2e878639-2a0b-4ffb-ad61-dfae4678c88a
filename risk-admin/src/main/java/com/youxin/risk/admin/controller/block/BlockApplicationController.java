package com.youxin.risk.admin.controller.block;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.youxin.apollo.client.NacosClient;
import com.youxin.risk.admin.controller.BaseController;
import com.youxin.risk.admin.controller.block.request.EliminateAlarmRequest;
import com.youxin.risk.admin.controller.block.request.EngineEventRequest;
import com.youxin.risk.admin.controller.block.request.EngineForwardRequest;
import com.youxin.risk.admin.controller.block.request.EngineRetryRequest;
import com.youxin.risk.admin.controller.block.request.HHForwardRequest;
import com.youxin.risk.admin.controller.block.request.HHRetryRequest;
import com.youxin.risk.admin.controller.block.request.RRDForwardRequest;
import com.youxin.risk.admin.controller.block.request.TriggerDpCallbackRequest;
import com.youxin.risk.admin.controller.block.response.EngineBlockedData;
import com.youxin.risk.admin.controller.block.response.EngineEventBlockedData;
import com.youxin.risk.admin.controller.block.response.HHBlockedDataTask;
import com.youxin.risk.admin.controller.block.response.RRDBlockDataRecord;
import com.youxin.risk.admin.dao.admin.AdminDiServiceMapper;
import com.youxin.risk.admin.dao.admin.AdminEventNodeRelationMapper;
import com.youxin.risk.admin.dao.di.AdminDiTaskMapper;
import com.youxin.risk.admin.dao.engine.EngineAsyncRequestLogMapper;
import com.youxin.risk.admin.interceptor.SystemLog;
import com.youxin.risk.admin.model.AdminDiService;
import com.youxin.risk.admin.model.AdminDiTask;
import com.youxin.risk.admin.model.AdminEventNodeRelation;
import com.youxin.risk.admin.model.BlockedEngineEvent;
import com.youxin.risk.admin.model.EngineEvent;
import com.youxin.risk.admin.model.GwRequest;
import com.youxin.risk.admin.model.GwRetryRequestBody;
import com.youxin.risk.admin.model.Page;
import com.youxin.risk.admin.model.vo.BlockEventResultVo;
import com.youxin.risk.admin.model.vo.GwRequestVo;
import com.youxin.risk.admin.service.EngineEventService;
import com.youxin.risk.admin.service.GwRequestService;
import com.youxin.risk.admin.service.block.qw.BlockHandlerServiceHelp;
import com.youxin.risk.admin.service.impl.RedisDelTmpCacheService;
import com.youxin.risk.admin.service.wechat.WeChatHandlerServiceHelp;
import com.youxin.risk.admin.vo.EventCodeVo;
import com.youxin.risk.commons.cache.CacheApi;
import com.youxin.risk.commons.model.EngineAsyncRequestLog;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.JsonUtils;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static com.youxin.risk.commons.constants.ApolloNamespace.commonSpace;

/**
 * 处理卡单接口
 *
 * <AUTHOR>
 * @date 2019-06-04
 */
@Controller
@RequestMapping("/block")
@Api(value = "/block", description = "卡单相关操作")
public class BlockApplicationController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(BlockApplicationController.class);

    private final static int HTTP_REQUEST_TIME_OUT = 30000;
    private final static String UNKNOWN_RECORD_TYPE = "unknown";

    @Value("${dp.trigger.callback.url}")
    private String dpCallbackUrl;

    @Value("${ra.base.url}")
    private String raBaseUrl;

    @Value("${rrd.service.url}")
    private String rrdServiceUrl;

    @Value("${gw.service.url}")
    private String gwServiceUrl;

    @Value("${gateway.url}")
    private String gatewayUrl;

    @Resource
    private GwRequestService gwRequestService;

    @Value("${channel.forward.url}")
    private String channelForwardUrl;

    private Executor singlePool = Executors.newSingleThreadExecutor();

    @Autowired
    private AdminEventNodeRelationMapper adminEventNodeRelationMapper;

    @Resource
    private EngineEventService engineEventService;

    @Resource
    private EngineAsyncRequestLogMapper engineAsyncRequestLogMapper;

    @Autowired
    private AdminDiServiceMapper adminDiServiceMapper;

    @Autowired
    private AdminDiTaskMapper adminDiTaskMapper;

    @Value("${risk.verify.url}")
    private String riskVerifyUrl;

    private String verifyRemoveAlert = "/switch/removeSubmitAlert/";

    private String pressTestPre = "pressTest";

    @Autowired
    private RedisDelTmpCacheService redisDelTmpCacheService;

    @RequestMapping(value = "/dp/triggerCallback", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "触发数据平台回调", notes = "触发数据平台回调", response = ResponseEntity.class)
    @SystemLog
    public ResponseEntity<?> triggerCallback(@RequestBody List<TriggerDpCallbackRequest> dpCallbackRequests) {
        List<BlockEventResultVo> resultVos = engineEventService.triggerCallback(dpCallbackRequests, dpCallbackUrl, HTTP_REQUEST_TIME_OUT);
        if (resultVos == null) {
            return buildSuccessResponse("当前有任务还没处理完，请稍候");
        }
        if (CollectionUtils.isNotEmpty(resultVos)) {
            return buildSuccessResponse(resultVos.toString());
        }
        return buildSuccessResponse("触发数据平台回调成功");
    }


    @RequestMapping(value = "/hh/list", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "好分期卡单数据列表", notes = "好分期卡单数据列表", response = HHBlockedDataTask.class, responseContainer = "list")
    public ResponseEntity<?> hhBlockList() {
        String response = SyncHTTPRemoteAPI.get(raBaseUrl + "/block/list", HTTP_REQUEST_TIME_OUT);
        LoggerProxy.info(LOGGER, "response={}", response);
        JSONObject responseObj = JSONObject.parseObject(response);
        if (responseObj.getIntValue("status") != 0) {
            return buildErrorResponse(response);
        }

        List<HHBlockedDataTask> result = Lists.newArrayList();
        JSONArray blockedList = responseObj.getJSONObject("data").getJSONArray("blockedList");
        for (Object o : blockedList) {
            HHBlockedDataTask hhBlockedDataTask = JsonUtils.toObject(JsonUtils.toJson(o), HHBlockedDataTask.class);
            hhBlockedDataTask.setRecordType(hhBlockedDataTask.getRecordType());
            result.add(hhBlockedDataTask);
        }
        return buildSuccessResponse(result);
    }

    @RequestMapping(value = "/hh/retry", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "触发好分期重试", notes = "触发好分期重试", response = ResponseEntity.class)
    @SystemLog
    public ResponseEntity<?> hhRetry(@RequestBody HHRetryRequest request) {
        LoggerProxy.info(LOGGER, "request={}", request);
        Preconditions.checkArgument(StringUtils.isNotBlank(request.getThirdPartyType()));
        Preconditions.checkArgument(StringUtils.isNotBlank(request.getUserKey()));
        Map<String, String> params = Maps.newHashMap();
        params.put("thirdPartyType", request.getThirdPartyType());
        params.put("userKey", request.getUserKey());
        String response = SyncHTTPRemoteAPI.postJson(raBaseUrl + "/block/retry", JsonUtils.toJson(params), HTTP_REQUEST_TIME_OUT);
        LoggerProxy.info(LOGGER, "response={}", response);
        JSONObject responseObj = JSONObject.parseObject(response);
        if (responseObj.getIntValue("status") != 0) {
            return buildErrorResponse(responseObj.getString("message"));
        }
        return buildSuccessResponse("重试成功");
    }

    @RequestMapping(value = "/hh/forward", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "修改好分期数据为已获取状态", notes = "修改好分期数据为已获取状态", response = ResponseEntity.class)
    @SystemLog
    public ResponseEntity<?> hhForward(@RequestBody HHForwardRequest request) {
        LoggerProxy.info(LOGGER, "request={}", request);
        Preconditions.checkArgument(StringUtils.isNotBlank(request.getUserKey()));
        Preconditions.checkArgument(StringUtils.isNotBlank(request.getLoanKey()));
        Map<String, String> params = Maps.newHashMap();
        params.put("userKey", request.getUserKey());
        params.put("loanKey", request.getLoanKey());
        String response = SyncHTTPRemoteAPI.postJson(raBaseUrl + "/block/forward", JsonUtils.toJson(params), HTTP_REQUEST_TIME_OUT);
        LoggerProxy.info(LOGGER, "response={}", response);
        JSONObject responseObj = JSONObject.parseObject(response);
        if (responseObj.getIntValue("status") != 0) {
            return buildErrorResponse(responseObj.getString("message"));
        }
        return buildSuccessResponse("成功");
    }

    @RequestMapping(value = "/rrd/list", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "人人贷卡单数据列表", notes = "人人贷卡单数据列表", response = RRDBlockDataRecord.class, responseContainer = "list")
    public ResponseEntity<?> rrdBlockList() {
        String response = SyncHTTPRemoteAPI.get(rrdServiceUrl + "/block/list", HTTP_REQUEST_TIME_OUT);
        LoggerProxy.info(LOGGER, "response={}", response);
        JSONObject responseObj = JSONObject.parseObject(response);
        if (responseObj.getIntValue("status") != 0) {
            return buildErrorResponse(response);
        }

        List<RRDBlockDataRecord> result = Lists.newArrayList();
        JSONArray blockedList = responseObj.getJSONArray("data");
        for (Object o : blockedList) {
            RRDBlockDataRecord blockedDataTask = JsonUtils.toObject(JsonUtils.toJson(o), RRDBlockDataRecord.class);
            String recordType = recordType(blockedDataTask.getServiceCode());
            blockedDataTask.setRecordType(UNKNOWN_RECORD_TYPE.equals(recordType) ? blockedDataTask.getThirdPartyType() : recordType);
            result.add(blockedDataTask);
        }
        return buildSuccessResponse(result);
    }

    @RequestMapping(value = "/rrd/forward", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "修改人人贷数据为已获取状态", notes = "修改人人贷数据为已获取状态", response = ResponseEntity.class)
    @SystemLog
    public ResponseEntity<?> rrdForward(@RequestBody RRDForwardRequest request) {

        LoggerProxy.info(LOGGER, "request={}", request);
        Preconditions.checkArgument(StringUtils.isNotBlank(request.getApplicationId()));
        Map<String, String> params = Maps.newHashMap();
        params.put("applicationId", request.getApplicationId());
        String response = SyncHTTPRemoteAPI.postJson(rrdServiceUrl + "/block/forward", JsonUtils.toJson(params), HTTP_REQUEST_TIME_OUT);
        LoggerProxy.info(LOGGER, "response={}", response);
        JSONObject responseObj = JSONObject.parseObject(response);
        if (responseObj.getIntValue("status") != 0) {
            return buildErrorResponse(responseObj.getString("message"));
        }
        return buildSuccessResponse("forward success");
    }

    @RequestMapping(value = "/engine/list", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "引擎卡单数据列表", notes = "引擎卡单数据列表", response = EngineBlockedData.class, responseContainer = "list")
    public ResponseEntity<?> engineBlockList() {
        List<EngineEvent> engineEventList = engineEventService.getBlockedList();
        if (CollectionUtils.isEmpty(engineEventList)) {
            return buildSuccessResponse(Collections.EMPTY_LIST);
        }
        List<String> sessionIdList = engineEventList.stream().map(EngineEvent::getSessionId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<EngineAsyncRequestLog> blockedList = engineAsyncRequestLogMapper.getBlockedList(sessionIdList);
        if (CollectionUtils.isEmpty(blockedList)) {
            return buildSuccessResponse(Collections.EMPTY_LIST);
        }
        List<String> requestIdList = blockedList.stream().map(EngineAsyncRequestLog::getAsyncRequestId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<AdminDiTask> adminDiTaskList = adminDiTaskMapper.selectByRequestIdList(requestIdList);

        Map<String, AdminDiTask> adminDiTaskMap = adminDiTaskList.stream().collect(Collectors.toMap(AdminDiTask::getRequestId, adminDiTask -> adminDiTask));
        Map<String, EngineEvent> engineEventMap = engineEventList.stream().collect(Collectors.toMap(EngineEvent::getSessionId, engineEvent -> engineEvent));

        List<EngineBlockedData> result = Lists.newArrayList();
        for (EngineAsyncRequestLog log : blockedList) {
            EngineEvent engineEvent = engineEventMap.get(log.getSessionId());
            EngineBlockedData engineBlockedData = new EngineBlockedData();
            engineBlockedData.setEngineRequestLogId(log.getAsyncRequestId());
            engineBlockedData.setSessionId(log.getSessionId());
            engineBlockedData.setEventCode(engineEvent.getEventCode());
            engineBlockedData.setInsId(engineEvent.getProcessInstanceId());
            engineBlockedData.setThirdPartyType(log.getDataCode());
            String recordType = UNKNOWN_RECORD_TYPE;
            AdminDiTask adminDiTask = adminDiTaskMap.get(log.getAsyncRequestId());
            if (!Objects.isNull(adminDiTask)) {
                recordType = adminDiTaskMap.get(log.getAsyncRequestId()).getTaskType();
            }
            engineBlockedData.setRecordType(recordType);
            engineBlockedData.setJobId(log.getJobId());
            engineBlockedData.setUserKey(log.getUserKey());
            engineBlockedData.setStatus(log.getStatus());
            engineBlockedData.setCreateTime(log.getCreateTime());
            engineBlockedData.setUpdateTime(log.getUpdateTime());
            result.add(engineBlockedData);
        }
        return buildSuccessResponse(result);
    }

    @RequestMapping(value = "/engine/retry", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "数据源重新调用", notes = "数据源重新调用", response = ResponseEntity.class)
    @SystemLog
    public ResponseEntity<?> engineRetry(@RequestBody List<EngineRetryRequest> request) {
        LoggerProxy.info(LOGGER, "retryDataRequest, request:{}", request);
        List<String> requestIdList = Lists.newArrayList();
        request.forEach(retryRequest -> {
            requestIdList.add(retryRequest.getRequestId());
        });
        String result = engineEventService.engineRetry(requestIdList);
        return result.startsWith("error") ? buildErrorResponse(result) : buildSuccessResponse(result);
    }

    @RequestMapping(value = "/engine/forward", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "强制前进", notes = "强制前进", response = ResponseEntity.class)
    @SystemLog
    public ResponseEntity<?> engineForward(@RequestBody List<EngineForwardRequest> request) {
        LoggerProxy.info(LOGGER, "request={}", request);
        List<String> requestLogIdList = Lists.newArrayList();
        List<String> sessionIdList = Lists.newArrayList();
        request.forEach(re -> {
            requestLogIdList.add(re.getEngineRequestLogId());
            sessionIdList.add(re.getSessionId());
        });
        if (CollectionUtils.isEmpty(requestLogIdList) || CollectionUtils.isEmpty(sessionIdList)) {
            return buildSuccessResponse("请求参数缺少");
        }
        String result = engineEventService.engineForward(requestLogIdList, sessionIdList, channelForwardUrl, HTTP_REQUEST_TIME_OUT);
        return result.startsWith("error") ? buildErrorResponse(result) : buildSuccessResponse(result);
    }

    private String recordType(String serviceCode) {
        AdminDiService adminDiService = adminDiServiceMapper.selectEntityByServiceCode(serviceCode);
        if (Objects.isNull(adminDiService)) {
            return UNKNOWN_RECORD_TYPE;
        }
        return adminDiService.getTaskType();
    }

    @RequestMapping(value = "/engine/event/list", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "引擎卡单流程列表", notes = "引擎卡单流程列表", response = EngineEventBlockedData.class, responseContainer = "list")
    public ResponseEntity<?> engineEventBlockList() throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        List<BlockedEngineEvent> blockedEngineEventList = engineEventService.getBlockedEventList();
        if (CollectionUtils.isEmpty(blockedEngineEventList)) {
            return buildSuccessResponse(Collections.EMPTY_LIST);
        }
        List<EngineEventBlockedData> result = Lists.newArrayList();
        for (BlockedEngineEvent blockedEngineEvent : blockedEngineEventList) {
            EngineEventBlockedData engineEventBlockedData = new EngineEventBlockedData();
            engineEventBlockedData.setId(blockedEngineEvent.getId());
            engineEventBlockedData.setCreateTime(blockedEngineEvent.getCreateTime());
            engineEventBlockedData.setUpdateTime(blockedEngineEvent.getUpdateTime());
            engineEventBlockedData.setCurrentNodeId(blockedEngineEvent.getCurrentNodeId());
            engineEventBlockedData.setEventCode(blockedEngineEvent.getEventCode());
            engineEventBlockedData.setLoanKey(blockedEngineEvent.getLoanKey());
            engineEventBlockedData.setProcessInstanceId(blockedEngineEvent.getProcessInstanceId());
            engineEventBlockedData.setProcessDefId(blockedEngineEvent.getProcessDefId());
            engineEventBlockedData.setUserKey(blockedEngineEvent.getUserKey());
            engineEventBlockedData.setStatus(blockedEngineEvent.getStatus());
            engineEventBlockedData.setSessionId(blockedEngineEvent.getSessionId());
            result.add(engineEventBlockedData);
        }
        return buildSuccessResponse(result);
    }

    @RequestMapping(value = "/engine/event/processRetry", method = RequestMethod.POST)
    @ResponseBody
    @SystemLog
    @ApiOperation(value = "重新发起流程", notes = "重新发起流程", response = String.class)
    public ResponseEntity<?> processRetry(@RequestBody List<EngineEventRequest> engineEventRequests) {
        List<String> sessionIdList = Lists.newArrayList();
        engineEventRequests.forEach(request -> {
            sessionIdList.add(request.getSessionId());
        });
        String result = engineEventService.setFailed(sessionIdList);
        return result.startsWith("error") ? buildErrorResponse(result) : buildSuccessResponse(result);
    }

    @RequestMapping(value = "/engine/event/processAndThirdpartyRetry", method = RequestMethod.POST)
    @ResponseBody
    @SystemLog
    @ApiOperation(value = "重新发起流程并且获取三方", notes = "重新发起流程并且获取三方", response = String.class)
    public ResponseEntity<?> processAndThirdpartyRetry(@RequestBody List<EngineEventRequest> engineEventRequests) {
        List<String> sessionIdList = Lists.newArrayList();
        engineEventRequests.forEach(request -> {
            sessionIdList.add(request.getSessionId());
        });
        String result = engineEventService.setFailedAndDeleteThirdparty(sessionIdList);
        return result.startsWith("error") ? buildErrorResponse(result) : buildSuccessResponse(result);
    }

    @RequestMapping(value = "/engine/event/updateCurrentTime", method = RequestMethod.POST)
    @ResponseBody
    @SystemLog
    @ApiOperation(value = "流程事件更新到当前时间", notes = "流程事件更新到当前时间", response = String.class)
    public ResponseEntity<?> updateCurrentTime(@RequestBody List<EngineEventRequest> engineEventRequests) {
        List<String> sessionIdList = Lists.newArrayList();
        engineEventRequests.forEach(request -> {
            sessionIdList.add(request.getSessionId());
        });
        String result = engineEventService.updateCreateTimeBySessionIds(sessionIdList);
        return result.startsWith("error") ? buildErrorResponse(result) : buildSuccessResponse(result);
    }

    /*-----------------------------------修改为分页 update by fanlingyin*/
    @RequestMapping(value = "/engine/event/page", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "引擎卡单流程分页列表", notes = "引擎卡单分页列表", response = EngineEventBlockedData.class, responseContainer = "page")
    public ResponseEntity<?> engineEventBlockPageList(@RequestBody JSONObject params) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        LOGGER.info("/engine/event/page params = {}", params.toJSONString());
        Page<EngineEventBlockedData> pageResult = engineEventService.getBlockedEventPageList(params);
        return buildSuccessResponse(pageResult);
    }

    @RequestMapping(value = "/engine/page", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "引擎卡单数据分页列表", notes = "引擎卡单数据分页列表", response = EngineBlockedData.class, responseContainer = "page")
    public ResponseEntity<?> engineBlockPageList(@RequestBody JSONObject params) {
        Page<EngineBlockedData> pageResult = engineEventService.getBlockedPageList(params);
        return buildSuccessResponse(pageResult);

    }

    @RequestMapping(value = "/hh/page", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "好分期卡单数据分页列表", notes = "好分期卡单数据分页列表", response = HHBlockedDataTask.class, responseContainer = "page")
    public ResponseEntity<?> hhBlockLPageist(@RequestBody JSONObject params) {
        String response = SyncHTTPRemoteAPI.get(raBaseUrl + "/block/list", HTTP_REQUEST_TIME_OUT);
        LoggerProxy.info(LOGGER, "response={}", response);
        JSONObject responseObj = JSONObject.parseObject(response);
        if (responseObj.getIntValue("status") != 0) {
            return buildErrorResponse(response);
        }

        List<HHBlockedDataTask> result = Lists.newArrayList();
        JSONArray blockedList = responseObj.getJSONObject("data").getJSONArray("blockedList");
        for (Object o : blockedList) {
            HHBlockedDataTask hhBlockedDataTask = JsonUtils.toObject(JsonUtils.toJson(o), HHBlockedDataTask.class);
            hhBlockedDataTask.setRecordType(hhBlockedDataTask.getRecordType());
            result.add(hhBlockedDataTask);
        }
        int pageNo = params.containsKey("pageNum") ? (Integer) params.get("pageNum") : 1;
        int pageSize = params.containsKey("pageSize") ? (Integer) params.get("pageSize") : 20;
        PageHelper.startPage(pageNo, pageSize);
        PageInfo pageResult = new PageInfo(result);
        return buildSuccessResponse(pageResult);
    }

    @RequestMapping(value = "/rrd/page", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "人人贷卡单数据分页列表", notes = "人人贷卡单数据分页列表", response = RRDBlockDataRecord.class, responseContainer = "page")
    public ResponseEntity<?> rrdBlockPageList(@RequestBody JSONObject params) {
        String response = SyncHTTPRemoteAPI.get(rrdServiceUrl + "/block/list", HTTP_REQUEST_TIME_OUT);
        LoggerProxy.info(LOGGER, "response={}", response);
        JSONObject responseObj = JSONObject.parseObject(response);
        if (responseObj.getIntValue("status") != 0) {
            return buildErrorResponse(response);
        }

        List<RRDBlockDataRecord> result = Lists.newArrayList();
        JSONArray blockedList = responseObj.getJSONArray("data");
        for (Object o : blockedList) {
            RRDBlockDataRecord blockedDataTask = JsonUtils.toObject(JsonUtils.toJson(o), RRDBlockDataRecord.class);
            String recordType = recordType(blockedDataTask.getServiceCode());
            blockedDataTask.setRecordType(UNKNOWN_RECORD_TYPE.equals(recordType) ? blockedDataTask.getThirdPartyType() : recordType);
            result.add(blockedDataTask);
        }
        int pageNo = params.containsKey("pageNum") ? (Integer) params.get("pageNum") : 1;
        int pageSize = params.containsKey("pageSize") ? (Integer) params.get("pageSize") : 20;
        PageHelper.startPage(pageNo, pageSize);
        PageInfo pageResult = new PageInfo(result);
        return buildSuccessResponse(pageResult);
    }

    @RequestMapping(value = "/engine/event/gatewayRetry", method = RequestMethod.POST)
    @ResponseBody
    @SystemLog
    @ApiOperation(value = "重新进件", notes = "重新进件", response = ResponseEntity.class)
    public ResponseEntity<?> gatewayRetry(@RequestBody List<EngineEventRequest> engineEventRequests) {
        String result = gwRequestService.gatewayRetry(engineEventRequests, gwServiceUrl, gatewayUrl, HTTP_REQUEST_TIME_OUT);
        return result.startsWith("error") ? buildErrorResponse(result) : buildSuccessResponse(result);
    }

    @RequestMapping(value = "/gw/page", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "Gateway卡单流程分页列表", notes = "Gateway卡单流程分页列表", response = GwRequest.class, responseContainer = "page")
    public ResponseEntity<?> gwBlockPageList(@RequestBody JSONObject params) {
        Page<GwRequestVo> pageResult = gwRequestService.getBlockedGwRequestPageList(params);
        return buildSuccessResponse(pageResult);
    }

    @RequestMapping(value = "/event/code/all", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> getEventCodeList() {
        List<AdminEventNodeRelation> adminEventNodeRelationList = adminEventNodeRelationMapper.groupByEventCodeAndStep();
        Set<String> codeSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(adminEventNodeRelationList)) {
            adminEventNodeRelationList.forEach(re -> {
                codeSet.add(re.getEventCode());
            });
        }
        List<EventCodeVo> eventCodeList = new ArrayList<>();
        codeSet.forEach(re -> {
            EventCodeVo eventCodeVo = new EventCodeVo();
            eventCodeVo.setEventCode(re);
            eventCodeVo.setEventCodeLabel(re);
            eventCodeList.add(eventCodeVo);
        });
        return buildSuccessResponse(eventCodeList);
    }

    @RequestMapping(value = "/event/codeAndDesc/all", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> getEventCodeAndDescList() {
        List<AdminEventNodeRelation> adminEventNodeRelationList = adminEventNodeRelationMapper.groupByEventCodeAndStep();
        Set<String> codeSet = new TreeSet<>();
        if (CollectionUtils.isNotEmpty(adminEventNodeRelationList)) {
            adminEventNodeRelationList.forEach(re -> {
                codeSet.add(re.getEventCode()+"_"+re.getEventName());
            });
        }
        List<EventCodeVo> eventCodeList = new ArrayList<>();
        codeSet.forEach(re -> {
            EventCodeVo eventCodeVo = new EventCodeVo();
            eventCodeVo.setEventCode(re.split("_")[0]);
            eventCodeVo.setEventCodeLabel(re);
            eventCodeList.add(eventCodeVo);
        });
        return buildSuccessResponse(eventCodeList);
    }

    //消除报警
    @RequestMapping(value = "/event/eliminate/alarm", method = RequestMethod.POST)
    @ResponseBody
    @SystemLog
    public ResponseEntity<?> eliminateAlarm(@RequestBody List<EliminateAlarmRequest> eliminateAlarmRequests) {
        List<String> sessionIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(eliminateAlarmRequests)) {
            eliminateAlarmRequests.forEach(eliminateAlarmRequest -> {
                gwRequestService.deleteMiniBySessionId(Collections.singletonList(eliminateAlarmRequest.getSessionId()));
                if ("haoHuanVerify".equals(eliminateAlarmRequest.getEventCode())) {
                    sessionIds.add(eliminateAlarmRequest.getSessionId());
                }
            });
            if (CollectionUtils.isNotEmpty(sessionIds)) {
                List<GwRetryRequestBody> gwRetryRequestBodyList = gwRequestService.getRequestBodyBySessionIds(sessionIds);
                gwRetryRequestBodyList.forEach(gwRetryRequestBody -> {
                    Integer loanId = JSONObject.parseObject(gwRetryRequestBody.getRequestBody())
                            .getJSONObject("data").getJSONObject("message").getInteger("loanId");
                    String url = riskVerifyUrl + verifyRemoveAlert + loanId;
                    LoggerProxy.info("BlockApplicationController.eliminateAlarm", LOGGER, "verifyRemoveAlertUrl={},sessionId={}", url, gwRetryRequestBody.getSessionId());
                    String response = SyncHTTPRemoteAPI.get(url, HTTP_REQUEST_TIME_OUT);
                    LoggerProxy.info("BlockApplicationController.eliminateAlarm", LOGGER, "sessionId={},response={}", gwRetryRequestBody.getSessionId(), response);
                    if (StringUtils.isNotBlank(response)) {
                        JSONObject responseObject = JSONObject.parseObject(response);
                        Integer retCode = responseObject.getInteger("status");
                        if (0 != retCode) {
                            LoggerProxy.error("BlockApplicationController.eliminateAlarm", LOGGER, "sessionId={},response={}", gwRetryRequestBody.getSessionId(), response);
                        }
                    }
                });
            }
        } else {
            return buildErrorResponse("param is empty!");
        }
        return buildSuccessResponse("succeed to eliminate alarm!");
    }

    @RequestMapping(value = "/delete/pressTest", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "Gateway卡单流程分页列表", notes = "Gateway卡单流程分页列表", response = GwRequest.class, responseContainer = "page")
    public ResponseEntity<?> deletePressTest(@RequestParam String eventCode) {
        if (StringUtils.isNotBlank(eventCode) && eventCode.startsWith(pressTestPre)) {
            singlePool.execute(() -> {
                gwRequestService.deletePressTest(eventCode);
            });
        }
        return buildSuccessResponse("deletePressTest data started!");
    }

    @RequestMapping(value = "/risk/apollo/value", method = {RequestMethod.GET})
    @ResponseBody
    public String getApolloValue(String key, String nameSpace) {
        try {
            if (StringUtils.isBlank(nameSpace)) {
                nameSpace = commonSpace;
            }
            return NacosClient.getByNameSpace(nameSpace, key, "未获取到apollo配置");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return e.getMessage();
        }
    }

    @RequestMapping(value = "/risk/sysconfig/value", method = {RequestMethod.GET})
    @ResponseBody
    public String getSysconfigValue(String key) {
        try {
            return CacheApi.getDictSysConfig(key);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return e.getMessage();
        }
    }

    //强制终止流程
    @RequestMapping(value = "/event/force/terminate", method = RequestMethod.POST)
    @ResponseBody
    @SystemLog
    public ResponseEntity<?> forceTerminateProcess(@RequestBody List<EngineEventRequest> engineEventRequests) {
        List<String> sessionIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(engineEventRequests)) {
            engineEventRequests.forEach(engineEventRequest -> {
                sessionIds.add(engineEventRequest.getSessionId());
            });
        }
        String result = gwRequestService.forceCallbacked(sessionIds);
        return result.startsWith("error") ? buildErrorResponse(result) : buildSuccessResponse(result);
    }


    @RequestMapping(value = "/callback/page", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "Gateway已回调流程分页列表", notes = "Gateway已回调流程分页列表", response = GwRequest.class, responseContainer = "page")
    public ResponseEntity<?> gwCallbackPageList(@RequestBody JSONObject params) {
        Page<GwRequestVo> pageResult = gwRequestService.getCallbackedGwRequestPageList(params);
        return buildSuccessResponse(pageResult);
    }


    @RequestMapping(value = "/engine/event/callbackUpStreamRetry", method = RequestMethod.POST)
    @ResponseBody
    @SystemLog
    @ApiOperation(value = "重新回调上游", notes = "重新回调上游", response = ResponseEntity.class)
    public ResponseEntity<?> callbackRetry(@RequestBody List<EngineEventRequest> engineEventRequests) {
        if (CollectionUtils.isEmpty(engineEventRequests)) {
            return buildSuccessResponse("success gatewayRetry");
        }
        List<String> sessionIdList =
                engineEventRequests.stream().map(EngineEventRequest::getSessionId).collect(Collectors.toList());
        gwRequestService.retryCallbackGw(sessionIdList);
        return buildSuccessResponse("success gatewayRetry");
    }

    @RequestMapping(value = "/engine/event/forceRejectRetry", method = RequestMethod.POST)
    @ResponseBody
    @SystemLog
    @ApiOperation(value = "重新强制拒绝", notes = "重新强制拒绝", response = ResponseEntity.class)
    public ResponseEntity<?> forceRejectRetry(@RequestBody List<EngineEventRequest> engineEventRequests) {

        if (CollectionUtils.isNotEmpty(engineEventRequests)) {
            List<String> sessionIdList = new ArrayList<>();
            engineEventRequests.forEach(re -> {
                sessionIdList.add(re.getSessionId());
            });
            String result = gwRequestService.forceRejectRetry(sessionIdList);
            return result.startsWith("error") ? buildErrorResponse(result) : buildSuccessResponse(result);
        }
        return buildSuccessResponse("success force reject!");
    }

    @RequestMapping(value = "/delete/cache", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "Gateway卡单流程分页列表", notes = "Gateway卡单流程分页列表", response = GwRequest.class, responseContainer = "page")
    public ResponseEntity<?> deleteCache(@RequestParam long startTime, @RequestParam long endTime) {
        singlePool.execute(() -> {
            redisDelTmpCacheService.delTmpCache(startTime, endTime);
        });
        return buildSuccessResponse("deleteCache data started!");
    }

    //强制拒绝
    @RequestMapping(value = "/event/force/reject", method = RequestMethod.POST)
    @ResponseBody
    @SystemLog
    public ResponseEntity<?> forceRejectProcess(@RequestBody List<EngineEventRequest> engineEventRequests) {
        List<String> sessionIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(engineEventRequests)) {
            engineEventRequests.forEach(engineEventRequest -> {
                sessionIds.add(engineEventRequest.getSessionId());
            });
        }
        String result = gwRequestService.forceReject(sessionIds);

        // 异步调用forceRejectSync 同步各环境
        singlePool.execute(() -> {
            LoggerProxy.info(LOGGER, "forceRejectSync, request:{}", JSON.toJSONString(engineEventRequests));
            gwRequestService.forceRejectSync(engineEventRequests);
        });

        return result.startsWith("error") ? buildErrorResponse(result) : buildSuccessResponse(result);
    }

    //获取可以强制拒绝的事件
    @RequestMapping(value = "/canForceRejectEvents", method = RequestMethod.GET)
    @ResponseBody
    @SystemLog
    public ResponseEntity<?> canForceRejectEvents() {
        List<String> list=gwRequestService.getCanForceRejectEvents();
        return buildSuccessResponse(list);
    }


    @Autowired
    private BlockHandlerServiceHelp blockHandlerServiceHelp;

    //卡单企微操作
    @RequestMapping(value = "/qw", method = RequestMethod.GET)
    @ResponseBody
    @SystemLog
    public String qwHandler(String type, String... params) {
        try {
            blockHandlerServiceHelp.getBlockHandlerServiceByType(type).handler(params);
        } catch (Exception e) {
            LoggerProxy.error("卡单企微操作异常", LOGGER, "操作类型={}, params={}, 异常信息=",
                    type, params, e);
            return "failed,err msg= " + StringUtils.printStackTraceToString(e);
        }

        return "success";
    }

    @Autowired
    private WeChatHandlerServiceHelp weChatHandlerServiceHelp;

    /**
     * 处理企业微信消息
     * @param type 消息类型
     * @param params 参数
     * @return 处理结果
     */
    @RequestMapping(value = "/qw_ext", method = RequestMethod.GET)
    @ResponseBody
    public String handleQwExt(@RequestParam String type, @RequestParam(required = false) String params) {
        try {
            // 将 params 转换为 Map<String, String>
            Map<String, String> paramsMap = StringUtils.isNotBlank(params)
                    ? JSONObject.parseObject(params, Map.class)
                    : Collections.emptyMap();

            String requestUrl = String.format("%s?type=%s&params=%s", gatewayUrl, type, params);
            LoggerProxy.info("企业微信消息处理", LOGGER, "请求链接={}", requestUrl);

            weChatHandlerServiceHelp.getHandlerServiceByType(type).handler(paramsMap);
        } catch (Exception e) {
            LoggerProxy.error("企业微信消息处理异常", LOGGER, "消息类型={}, params={}, 异常信息=",
                    type, params, e);
            return "failed,err msg= " + StringUtils.printStackTraceToString(e);
        }

        return "success";
    }

    //卡单企微操作
    @RequestMapping(value = "/test", method = RequestMethod.GET)
    @ResponseBody
    @SystemLog
    public String test(@RequestParam(required = true) String type,
                            @RequestParam(required = false) String... params ) {
        LOGGER.info("入参={}", params);

        return "success";
    }
}


