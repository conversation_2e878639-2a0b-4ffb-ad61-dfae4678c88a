package com.youxin.risk.admin.model.rulescore;

import com.youxin.risk.admin.dto.rulescore.RuleScoreStrategyDTO;
import com.youxin.risk.commons.model.BaseModel;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class RuleScoreMirror extends BaseModel {
    private String ruleKey;
    private Integer ruleVersion;
    private String ruleMirrorCode;
    /** 策略和节点, 可能有多条记录 **/
    private String strategyNode;
    /** 枚举：启用、停用 **/
    private Integer ruleMirrorStatus;
    private Integer ruleMirrorRate;
    private Integer ruleMirrorCount;
    private Date startTime;
    private Date endTime;

    private Integer ruleMirrorStopType;
    private Integer ruleCompareType;
    /** 逗号分开 **/
    private String containField;
    private String excludeField; // 排除字段的优先级高于包含字段
    private String modifyUser;
    private String createUser;

    private List<RuleScoreStrategyDTO> ruleScoreStrategyDTOS;

    public static final int START = 1;
    public static final int STOP =  2;

    public static final int BY_COUNT = 1;
    public static final int BY_TIME = 2;

    public static final int BY_ALL = 1;//默认
    public static final int BY_DEF = 2;//自定义
}
