package com.youxin.risk.admin.model;

import com.youxin.risk.commons.model.BaseModel;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 手动批量跑策略三方数据源接口配置表
 * 
 * <AUTHOR> 
 * @date 2019-01-16
 */
@Setter
@Getter
public class AdminThirdPartySourceInterface extends BaseModel {

    /**
     * 数据源编码
     */
    private String thirdPartyServiceKey;

    private String thirdPartyName;

    private String thirdPartyObtainMethod;


}