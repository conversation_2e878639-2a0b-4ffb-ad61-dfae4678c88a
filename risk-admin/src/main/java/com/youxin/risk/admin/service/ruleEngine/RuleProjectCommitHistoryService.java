package com.youxin.risk.admin.service.ruleEngine;


import com.youxin.risk.admin.model.ruleEngine.RuleProjectCommitHistory;

import java.util.List;

/**
 * 新决策引擎项目提交历史表(RuleProjectCommitHistory)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-19 15:26:23
 */
public interface RuleProjectCommitHistoryService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    RuleProjectCommitHistory queryById(Integer id);

    /**
     * 新增数据
     *
     * @param ruleProjectCommitHistory 实例对象
     * @return 实例对象
     */
    int insert(RuleProjectCommitHistory ruleProjectCommitHistory);

    /**
     * 修改数据
     *
     * @param ruleProjectCommitHistory 实例对象
     * @return 实例对象
     */
    RuleProjectCommitHistory update(RuleProjectCommitHistory ruleProjectCommitHistory);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Long id);

    RuleProjectCommitHistory getLatestCommitHistoryByProjectId(Integer projectId);

    /**
     * 根据项目id和节点名称查询最新的提交记录
     */
    RuleProjectCommitHistory queryLatestByProjectIdAndNodeName(Integer projectId, String nodeName);

    /**
     * 根据项目id和节点名称查询最早的创建记录
     */
    RuleProjectCommitHistory queryEarliestByProjectId(Integer projectId);

    /**
     * 根据项目id、文件名称查询提交历史
     */
    List<RuleProjectCommitHistory> queryByProjectIdAndFileName(Integer projectId, String fileName);
}
