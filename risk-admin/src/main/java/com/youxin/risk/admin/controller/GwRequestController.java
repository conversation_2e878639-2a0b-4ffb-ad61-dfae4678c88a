package com.youxin.risk.admin.controller;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.admin.model.AdminEventInfo;
import com.youxin.risk.admin.model.GwRequest;
import com.youxin.risk.admin.model.Page;
import com.youxin.risk.admin.service.AdminEventInfoService;
import com.youxin.risk.admin.service.GwRequestService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2018/11/13 13:45
 */
@Controller
@RequestMapping("/gwRequest")
public class GwRequestController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(GwRequestController.class);

    @Resource
    private GwRequestService gwRequestService;

    @ResponseBody
    @RequestMapping(value = "/selectPage", method = RequestMethod.POST)
    public ResponseEntity<?> selectPage(@RequestBody JSONObject params) {
        params.put("pageNo", params.get("pageNum"));
        Page<GwRequest> page = gwRequestService.selectPage(params);
        return buildSuccessResponse(page);
    }

    @ResponseBody
    @RequestMapping(value = "/get", method = RequestMethod.POST)
    public ResponseEntity<?> get(@RequestBody JSONObject request) {
        Long id = request.getLong("id");
        GwRequest gwRequest = gwRequestService.get(id);
        return buildSuccessResponse(gwRequest);
    }

}
