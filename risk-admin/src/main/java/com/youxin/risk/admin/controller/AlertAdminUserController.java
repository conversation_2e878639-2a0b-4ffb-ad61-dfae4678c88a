package com.youxin.risk.admin.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.youxin.risk.admin.interceptor.SystemLog;
import com.youxin.risk.admin.model.AdminDiVariable;
import com.youxin.risk.admin.model.PageResult;
import com.youxin.risk.admin.model.alert.AlertAdminUser;
import com.youxin.risk.admin.service.AdminDiVariableService;
import com.youxin.risk.admin.service.AlertAdminUserService;
import com.youxin.risk.admin.vo.AdminDiVariableVo;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/alertUser")
public class AlertAdminUserController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(AlertAdminUserController.class);

    @Autowired
    private AlertAdminUserService alertAdminUserService;

    @RequestMapping(value = "/getAllAlertUser", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<?> getAllAlertUser(@RequestBody AlertAdminUser alertAdminUser) {
        ResponseEntity<?> result = null;
        try {
            LoggerProxy.info(logger, "getAlertUserByPage request param:{}", alertAdminUser);
            List<AlertAdminUser> list = null;
            if (alertAdminUser != null & alertAdminUser.getPageSize() != null)
                PageHelper.startPage(alertAdminUser.getPageNum(), alertAdminUser.getPageSize());
            list = alertAdminUserService.getList(alertAdminUser);
            PageInfo pageResult = new PageInfo(list);
            result = buildSuccessResponse(pageResult);
        } catch (Exception e) {
            LoggerProxy.error(logger, "getAllAlertUser request error", e);
        }
        return result;
    }

    /**
     * 获取所有未分配到其他组的用户
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/getAlertUsersNotInOtherGroup", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<?> getAlertUsersInNoGroup(@RequestBody AlertAdminUser req) {
        ResponseEntity<?> result = null;
        try {
            LoggerProxy.info(logger, "getAlertUsersInNoGroup request param:{}");
            List<AlertAdminUser> list = null;
            list = alertAdminUserService.getAlertUsersNotInOtherGroup(req.getGroupName());
            result = buildSuccessResponse(list);
        } catch (Exception e) {
            LoggerProxy.error(logger, "getAlertUsersInNoGroup request error", e);
        }
        return result;
    }


    @RequestMapping(value = "/addAlertUser", method = RequestMethod.POST)
    @ResponseBody
    @SystemLog
    public ResponseEntity<?> addItem(@RequestBody AlertAdminUser alertAdminUser) {
        ResponseEntity<?> result = null;
        try {
            LoggerProxy.info(logger, "addAlertUser request param:{}", alertAdminUser);
            int data = alertAdminUserService.addItem(alertAdminUser);
            if (data > 0) {
                LoggerProxy.info(logger, "addAlertUser success change count:{}", data);
                result = buildSuccessResponse("addAlertUser successed");
            } else {
                result = buildErrorResponse("Duplicate admin userName");
            }
        } catch (Exception e) {
            LoggerProxy.error(logger, "addAlertUser request error, param:{}", alertAdminUser, e);
            result = buildErrorResponse("addAlertUser failed");
        }
        return result;
    }

    @RequestMapping(value = "/removeAlertUser", method = RequestMethod.POST)
    @ResponseBody
    @SystemLog
    public ResponseEntity<?> removeItem(@RequestBody AlertAdminUser alertAdminUser) {
        ResponseEntity<?> result = null;
        try {
            LoggerProxy.info(logger, "removeAlertUser request:{}", alertAdminUser);
            int data = alertAdminUserService.removeItem(alertAdminUser.getId());
            if (data > 0) {
                LoggerProxy.info(logger, "removeAlertUser success change count:{}", data);
                result = buildSuccessResponse(data);
            } else {
                result = buildErrorResponse("removeAlertUser failed");
            }
        } catch (Exception e) {
            LoggerProxy.error(logger, "removeAlertUser request error, param:{}", alertAdminUser, e);
            result = buildErrorResponse("removeAlertUser failed");
        }
        return result;
    }

    @RequestMapping(value = "/modifyAlertUser", method = RequestMethod.POST)
    @ResponseBody
    @SystemLog
    public ResponseEntity<?> modifyItem(@RequestBody AlertAdminUser request) {
        ResponseEntity<?> result = null;
        try {
            LoggerProxy.info(logger, "modifyAlertUser request param:{}", request);
            int data = alertAdminUserService.modifyItem(request);
            if (data > 0) {
                LoggerProxy.info(logger, "modifyAlertUser success change count:{}", data);
                result = buildSuccessResponse(data);
            } else {
                LoggerProxy.error(logger, "modifyDiVariable request error");
                result = buildErrorResponse("modifyDiVariable failed");
            }
        } catch (Exception e) {
            LoggerProxy.error(logger, "modifyDiVariable request error, param:{}", request, e);
            result = buildErrorResponse("modifyDiVariable failed");
        }
        return result;
    }

    @RequestMapping(value = "/getAlertUserDetail", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<?> getItemDetail(@RequestBody AlertAdminUser request) {
        ResponseEntity<?> result = null;
        try {
            LoggerProxy.info(logger, "getItemDetail request param:{}", request);
            AlertAdminUser data = alertAdminUserService.queryItemDetail(request);
            result = buildSuccessResponse(data);
        } catch (Exception e) {
            LoggerProxy.error(logger, "getItemDetail request error, param:{}", request, e);
        }
        return result;
    }

}
