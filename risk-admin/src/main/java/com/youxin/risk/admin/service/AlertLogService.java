package com.youxin.risk.admin.service;

import com.youxin.risk.admin.model.alert.AlertLog;
import com.youxin.risk.admin.vo.AlertLogVo;
import com.youxin.risk.admin.vo.AlertMutipHandlerLogVo;

import java.util.List;

/**
 * <AUTHOR>

public interface AlertLogService extends BaseAlertService<AlertLog> {
    List<AlertLogVo> getListWithGroup(AlertLog alertLog);

    void modifyMutipLog(AlertMutipHandlerLogVo alertMutipHandlerLogVo);

    AlertLog selectByExample(AlertLog alertLog);
}
