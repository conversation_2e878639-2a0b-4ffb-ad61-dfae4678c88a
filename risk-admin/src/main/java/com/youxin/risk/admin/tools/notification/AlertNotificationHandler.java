package com.youxin.risk.admin.tools.notification;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.admin.utils.DateUtils;
import com.youxin.risk.admin.utils.UserInfoUtil;
import com.youxin.risk.commons.constants.AlertExtendOperationTypeEnum;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.SLSLinkGenerator;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AlertNotificationHandler {

    private static final Logger logger = LoggerFactory.getLogger(AlertNotificationHandler.class);

    @Value("${alert.base.url}")
    private String alertUrl;
    private static final String ALERT_MSG_API_URL = "/alert/api/event/riskAlert/v1";

    // 规则集上线通知
    public void sendRuleOnlineNotification(String ruleKey, Integer version, String modifyUser) {
        JSONObject msg = new JSONObject(true);
        msg.put("规则集名称", ruleKey);
        msg.put("规则集版本", version);
        msg.put("上线人", modifyUser);
        msg.put("上线时间", DateUtils.getTime());

        sendNotification("sendOnlineNotification", msg, ALERT_MSG_API_URL);
    }

    // 策略上线通知
    public void sendStrategyOnlineToAlert(String strategyType, String eventCode, String userName) {
        JSONObject msg = new JSONObject(true);
        msg.put("策略类型", strategyType);
        msg.put("上线人员", userName);
        msg.put("上线时间", DateUtils.getTime());
        if(StringUtils.isNotEmpty(eventCode)
                && SLSLinkGenerator.isEventCodeConfigured(eventCode)){
            JSONObject extendOp = new JSONObject();
            extendOp.put("type", AlertExtendOperationTypeEnum.SLS_DASHBOARD_LINK.getCode());

            JSONObject params = new JSONObject();
            params.put("eventCode", eventCode);
            extendOp.put("params", params);

            msg.put("扩展操作", extendOp);
        }

        sendNotification("sendOnlineToAlert", msg, ALERT_MSG_API_URL);
    }

    // 规则集解绑通知
    public void sendRuleUnbindingNotification(String ruleKey, Integer version, String node) {
        JSONObject msg = new JSONObject(true);
        msg.put("规则集名称", ruleKey);
        msg.put("规则集版本", version);
        msg.put("解绑节点", node);
        msg.put("解绑人", UserInfoUtil.getUsername());
        msg.put("解绑时间", DateUtils.getTime());

        sendNotification("sendUnbindingNotification", msg, ALERT_MSG_API_URL);
    }

    // 规则集绑定通知
    public void sendRuleBindingNotification(String ruleKey, Integer version, List<String> bindNode) {
        JSONObject msg = new JSONObject(true);
        msg.put("规则集名称", ruleKey);
        msg.put("规则集版本", version);
        msg.put("绑定节点", bindNode);
        msg.put("绑定人", UserInfoUtil.getUsername());
        msg.put("绑定时间", DateUtils.getTime());

        sendNotification("sendBindingNotification", msg, ALERT_MSG_API_URL);
    }

    private void sendNotification(String notificationType, JSONObject msg, String apiUrlSuffix) {
        String logMessage = notificationType + ", msg=" + JSON.toJSONString(msg);
        LoggerProxy.info("sendNotification", logger, logMessage);

        String url = alertUrl;
        if (apiUrlSuffix != null && !apiUrlSuffix.isEmpty()) {
            url += apiUrlSuffix;
        }

        try {
            SyncHTTPRemoteAPI.postJson(url, JSON.toJSONString(msg), 10000);
            LoggerProxy.info("sendNotificationSuccess", logger, logMessage);
        } catch (Exception e) {
            LoggerProxy.error(logger, notificationType + "Error", e);
        }
    }

}