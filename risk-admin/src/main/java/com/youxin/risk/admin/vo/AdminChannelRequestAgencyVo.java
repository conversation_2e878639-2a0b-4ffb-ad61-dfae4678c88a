package com.youxin.risk.admin.vo;

import java.util.Date;

import com.youxin.risk.commons.model.BaseModel;

public class AdminChannelRequestAgencyVo extends BaseModel {

    /**
     * 
     */
    private static final long serialVersionUID = 1L;

    private String requestId; // 请求id

    private String requestAgencyId;

    private String outRequestId;

    private String agencyCode;

    private String dpJodid;

    private String requestAgencyMessage;

    private Date startTime;

    private Date endTime;

    private Integer pageNum;

    private Integer pageSize;

    private Integer startIndex;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public String getRequestAgencyId() {
        return requestAgencyId;
    }

    public void setRequestAgencyId(String requestAgencyId) {
        this.requestAgencyId = requestAgencyId;
    }

    public String getOutRequestId() {
        return outRequestId;
    }

    public void setOutRequestId(String outRequestId) {
        this.outRequestId = outRequestId;
    }

    public String getAgencyCode() {
        return agencyCode;
    }

    public void setAgencyCode(String agencyCode) {
        this.agencyCode = agencyCode;
    }

    public String getDpJodid() {
        return dpJodid;
    }

    public void setDpJodid(String dpJodid) {
        this.dpJodid = dpJodid;
    }

    public String getRequestAgencyMessage() {
        return requestAgencyMessage;
    }

    public void setRequestAgencyMessage(String requestAgencyMessage) {
        this.requestAgencyMessage = requestAgencyMessage;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getStartIndex() {
        return startIndex;
    }

    public void setStartIndex(Integer startIndex) {
        this.startIndex = startIndex;
    }

}