package com.youxin.risk.admin.dao.admin;

import com.youxin.risk.admin.dao.BaseMapper;
import com.youxin.risk.admin.model.AdminFeatureSplitFlow;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/24 16:36
 */
public interface AdminFeatureSplitFlowMapper extends BaseMapper<AdminFeatureSplitFlow> {
    List<AdminFeatureSplitFlow> selectForEnable();

    List<AdminFeatureSplitFlow> selectForDisable();

    void batchUpdateStatus(@Param("idList") List<Long> idList, @Param("status") String status);
}
