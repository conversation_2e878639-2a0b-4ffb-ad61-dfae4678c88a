package com.youxin.risk.admin.service.ruleEngine.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.youxin.risk.admin.constants.CandidateStrategyStatusEnum;
import com.youxin.risk.admin.dao.admin.RuleProjectMapper;
import com.youxin.risk.admin.domain.ruleengine.AdminCandidateStrategy;
import com.youxin.risk.admin.model.ruleEngine.RuleProject;
import com.youxin.risk.admin.service.ruleEngine.AdminCandidateStrategyService;
import com.youxin.risk.admin.service.ruleEngine.StrategyNodeService;
import com.youxin.risk.admin.service.ruleEngine.StrategyResultService;
import com.youxin.risk.admin.service.ruleEngine.handler.*;
import com.youxin.risk.admin.service.ruleEngine.ExecuteStrategy;
import com.youxin.risk.admin.service.rulescore.MoreTransaction;
import com.youxin.risk.admin.vo.ruleEngine.StrategyIntegrationTestModel;
import com.youxin.risk.commons.tools.lock.LockResult;
import com.youxin.risk.commons.tools.lock.RedisLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class IntegrationExecuteStrategy implements ExecuteStrategy<StrategyIntegrationTestModel, String>, InitializingBean {
    private static final Logger logger = LoggerFactory.getLogger(IntegrationExecuteStrategy.class);

    @Resource
    private RedisLock redisLock;
    @Autowired
    private SetDefaultSampleTimesHandler setDefaultSampleTimesHandler;
    @Autowired
    private ExtractAndImportVariablesHandler extractAndImportVariablesHandler;
    @Autowired
    private SubmitIntegrationTestHandler submitIntegrationTestHandler;
    @Autowired
    private CreateIntegrationRecordsHandler createIntegrationRecordsHandler;
    @Resource
    private RuleProjectMapper ruleProjectMapper;
    @Resource
    private StrategyResultService strategyResultService;
    @Resource
    private StrategyNodeService strategyNodeService;
    @Resource
    private ProjectPullServiceImpl projectPullServiceImpl;
    @Resource
    private AdminCandidateStrategyService adminCandidateStrategyService;

    private IntegrationHandler handlerChain;

    @Override
    public void afterPropertiesSet() throws Exception {
        setDefaultSampleTimesHandler.setNext(extractAndImportVariablesHandler);
        extractAndImportVariablesHandler.setNext(submitIntegrationTestHandler);
        submitIntegrationTestHandler.setNext(createIntegrationRecordsHandler);

        this.handlerChain = setDefaultSampleTimesHandler;
    }

    @Override
    @MoreTransaction(value = {"adminTransactionManager", "rmTransactionManager"})
    public String execute(StrategyIntegrationTestModel strategyIntegrationTestModel) {
        logger.info("createIntegrationNew data {}", JSON.toJSONString(strategyIntegrationTestModel));
        preCheckIntegration(strategyIntegrationTestModel);

        LockResult result = null;
        AdminCandidateStrategy adminCandidateStrategy = AdminCandidateStrategy
                .build(strategyIntegrationTestModel.getProjectId());
        try {
            result = redisLock.tryLock("createIntegrationNew", 5);
            if (!result.isSuccess()) {
                logger.error("createIntegrationNew get lock failed");
                throw new RuntimeException("刚刚多人同时操作，请重新点击集成测试按钮!");
            }
            handlerChain.handle(strategyIntegrationTestModel, adminCandidateStrategy);
        } catch (Exception e) {
            logger.error("createIntegrationNew error", e);
            throw new RuntimeException(e.getMessage());
        }finally {
            if (result != null && result.isSuccess()){
                redisLock.releaseLock("createIntegrationNew", result.getLockId());
            }
        }
        return "success";
    }

    private void preCheckIntegration(StrategyIntegrationTestModel strategyIntegrationTestModel) {
        // 校验是否已经拉取线上代码
        List<String> preMergeOnlineList = projectPullServiceImpl.getUnPulledNodes(strategyIntegrationTestModel.getProjectId());
        if (!CollectionUtils.isEmpty(preMergeOnlineList)) {
            throw new RuntimeException("请先拉取线上代码!");
        }
        RuleProject ruleProject = this.ruleProjectMapper.queryById(strategyIntegrationTestModel.getProjectId());
        Assert.notNull(ruleProject, "项目不存在");

        JSONArray outputStructure = strategyResultService.queryByProjectId(strategyIntegrationTestModel.getProjectId());
        if (outputStructure == null) {
            throw new RuntimeException("请先设置返回变量结构");
        }
        Map<String, String> allNodeCode = strategyNodeService.queryAllCode(strategyIntegrationTestModel.getProjectId());
        if (allNodeCode.isEmpty()) {
            throw new RuntimeException("未初始化策略代码, 不能进行集成测试!");
        }
        List<String> nodeNames = strategyNodeService.queryNodeList(strategyIntegrationTestModel.getProjectId());
        if (!CollectionUtils.isEmpty(nodeNames)) {
            List<String> unContains = nodeNames.stream().filter(nodeName -> !allNodeCode.containsKey(nodeName))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(unContains)) {
                throw new RuntimeException("节点: " + unContains + " 未提交代码");
            }
        }

        // 查询是否有不是终态的策略候选表记录
        adminCandidateStrategyService.selectAllNotFinal(ruleProject.getStrategyType())
                .forEach(strategy -> {
                    throw new RuntimeException("存在未终态的策略候选表记录，请先废弃！id:" + strategy.getId());
                });
    }
}