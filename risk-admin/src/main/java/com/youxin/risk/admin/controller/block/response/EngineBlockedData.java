package com.youxin.risk.admin.controller.block.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-06-10
 */
@Data
@ApiModel
public class EngineBlockedData {

    private String engineRequestLogId;

    private String sessionId;

    private String eventCode;

    private String insId;

    private String thirdPartyType;

    private String recordType;

    private String jobId;

    private String nodeId;

    private String userKey;

    private String status;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss.SSSZ")
    private Date createTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss.SSSZ")
    private Date updateTime;

}
