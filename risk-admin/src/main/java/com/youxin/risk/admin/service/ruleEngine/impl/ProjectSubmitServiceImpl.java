package com.youxin.risk.admin.service.ruleEngine.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.admin.domain.ruleengine.AdminCandidateStrategy;
import com.youxin.risk.admin.model.ruleEngine.RuleProjectCommitHistory;
import com.youxin.risk.admin.service.ruleEngine.AdminCandidateStrategyService;
import com.youxin.risk.admin.service.ruleEngine.ProjectSubmitService;
import com.youxin.risk.admin.service.ruleEngine.RuleProjectCommitHistoryService;
import com.youxin.risk.admin.vo.ruleEngine.CommitHistoryModel;
import com.youxin.risk.admin.vo.ruleEngine.ProjectSubmitModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProjectSubmitServiceImpl implements ProjectSubmitService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectSubmitServiceImpl.class);

    @Resource
    private AdminCandidateStrategyService adminCandidateStrategyService;
    @Resource
    private RuleProjectCommitHistoryService ruleProjectCommitHistoryService;

    @Override
    public List<ProjectSubmitModel> list(Map<String, Object> map) {
        Integer projectId = (Integer) map.get("projectId");
        List<AdminCandidateStrategy> adminCandidateStrategies = adminCandidateStrategyService.listByProjectId(projectId);

        return adminCandidateStrategies.stream()
                .filter(strategy -> filterByModifier(strategy, map))
                .filter(strategy -> filterByRemark(strategy, map))
                .map(this::convertToProjectSubmitModel)
                .collect(Collectors.toList());
    }

    private boolean filterByModifier(AdminCandidateStrategy strategy, Map<String, Object> map) {
        String username = strategy.getUserName();
        return map.get("modifier") == null || username.contains((String) map.get("modifier"));
    }

    private boolean filterByRemark(AdminCandidateStrategy strategy, Map<String, Object> map) {
        String comment = Optional.ofNullable(strategy.getRemark()).orElse("");
        return map.get("remark") == null || comment.contains((String) map.get("remark"));
    }

    private ProjectSubmitModel convertToProjectSubmitModel(AdminCandidateStrategy strategy) {
        ProjectSubmitModel model = new ProjectSubmitModel();
        model.setId(strategy.getId());
        model.setRemark(Optional.ofNullable(strategy.getRemark()).orElse(""));
        model.setDeployStatus(strategy.getStatus());
        model.setUpdateTime(strategy.getUpdateTime());
        model.setModifier(strategy.getUserName());
        return model;
    }

    @Override
    public List<CommitHistoryModel> queryById(Integer projectId, Integer id) {
        AdminCandidateStrategy strategyIntegration = adminCandidateStrategyService.selectById(id);
        if(strategyIntegration == null) {
            throw new RuntimeException("策略候选表记录不存在！");
        }
        JSONObject commitIdObject = JSON.parseObject(strategyIntegration.getCommitIds());

        return commitIdObject.entrySet().stream()
                .map(entry -> ruleProjectCommitHistoryService.queryById((Integer) entry.getValue()))
                .map(this::convertToCommitHistoryModel)
                .collect(Collectors.toList());
    }

    private CommitHistoryModel convertToCommitHistoryModel(RuleProjectCommitHistory history) {
        CommitHistoryModel model = new CommitHistoryModel();
        model.setRemark(history.getComment());
        model.setUpdateTime(history.getUpdateTime());
        model.setModifier(history.getUsername());
        model.setNodeName(history.getFileName());
        model.setCommitId(history.getId());
        return model;
    }
}