package com.youxin.risk.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.youxin.apollo.client.NacosClient;
import com.youxin.risk.admin.constants.ApolloLocalNamespace;
import com.youxin.risk.admin.constants.DataSourceTypeEnum;
import com.youxin.risk.admin.constants.TimeGroupEnum;
import com.youxin.risk.admin.influxdb.InfluxDBApi;
import com.youxin.risk.admin.model.AdminCollectionMonitorParam;
import com.youxin.risk.admin.service.AdminCollectionMonitorService;
import com.youxin.risk.commons.utils.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class AdminCollectionMonitorServiceImpl implements AdminCollectionMonitorService {
	protected Logger logger = LoggerFactory.getLogger(getClass());

	@Autowired
	InfluxDBApi influxDBApi;

	private final String SERVICE_TABLE = "risk_di_irr24_call_service_costTime";


	@Override
	public Map<String,List<String>> query(AdminCollectionMonitorParam param) {
		Map<String,List<String>> result = new HashMap<>();
		//数据节点耗时
		String sumCommand = formatQueryCommand(SERVICE_TABLE, param, true);
		// 大于指定差值
		String diffCommand = formatQueryCommand(SERVICE_TABLE, param, false);
		List<Map<String, Object>> sumResult = influxDBApi.query(sumCommand, InfluxDBApi.DBEnum.RISK_DI);
		List<Map<String, Object>> diffResult = influxDBApi.query(diffCommand, InfluxDBApi.DBEnum.RISK_DI);
		logger.info("CollectionMonitor sumCommand={}, result={}",sumCommand, JSONObject.toJSONString(sumResult));
		logger.info("CollectionMonitor diffCommand={}, result={}",diffCommand, JSONObject.toJSONString(diffResult));

		Map<String, String> resultMap = this.getMapFromResult(sumResult, diffResult);

		Set<String> timeSet = new HashSet<>();
		timeSet.addAll(resultMap.keySet());

		List<String> timeList = new ArrayList<>(timeSet);
		List<String> collectionList = new ArrayList<>();

		Collections.sort(timeList);

		timeList.forEach(time -> collectionList.add(resultMap.containsKey(time) ? resultMap.get(time) : "0"));

		result.put("timeList",timeList);
		result.put("collectionList",collectionList);
		return result;
	}

	@Override
	public List<String> queryDataSource(String type) {
		DataSourceTypeEnum dataSourceTypeEnum = DataSourceTypeEnum.getByCode(type);
		if (dataSourceTypeEnum == null) {
			return Collections.EMPTY_LIST;
		}

		String dataSourceInfo = NacosClient.getByNameSpace(ApolloLocalNamespace.localNamespace,"collection.datasource.info","{}");
		Map<String,Object> map = JSONObject.parseObject(dataSourceInfo, Map.class);
		if(map.containsKey(dataSourceTypeEnum.getName())){
			return JSONObject.parseObject(JSON.toJSONString(map.get(dataSourceTypeEnum.getName())), List.class);
		}
		return Collections.EMPTY_LIST;
	}

	private String formatQueryCommand(String table, AdminCollectionMonitorParam param, boolean sumFlag){
		//计算时间偏移量
		long l;
		TimeGroupEnum timeGroupEnum = TimeGroupEnum.getByCode(param.getTimeGroup());
		switch (timeGroupEnum){
			case M_1:
				l = 1000 * 60 * 1;
				break;
			case H_1:
				l = 1000 * 60 * 60;
				break;
			case H_6:
				l = 1000 * 60 * 60 * 6;
				break;
			case D_1:
				l = 1000 * 60 * 60 * 24;
				break;
			case D_7:
				l = 1000 * 60 * 60 * 24 * 7;
				break;
			default:
				l = 0;
		}
		long mod = DateUtil.parse(param.getStartTime(),DateUtil.LONG_WEB_FORMAT).getTime() % l;
		long offset = mod + (1000 * 60 * 60 * 8);
		DataSourceTypeEnum dataSourceTypeEnum = DataSourceTypeEnum.getByCode(param.getType());

		StringBuilder command = new StringBuilder("SELECT count(*) FROM \"").append(table).append("\" WHERE ");
		command.append(" \"type\" = '").append(dataSourceTypeEnum.getName()).append("'");
		command.append(" AND (");
		for(String step: param.getDataSource()){
			command.append(" \"service\"='").append(step).append("' OR");
		}
		command.delete(command.lastIndexOf(" OR"), command.length()).append(")");
		if (!sumFlag) {
			command.append(" AND \"value\" <= ").append(param.getTimeDiff()* 1000L);
			command.append(" AND \"value\" != -999 ");
		}
		command.append(" AND time >= ").append(DateUtil.parse(param.getStartTime(),DateUtil.LONG_WEB_FORMAT).getTime())
				.append("ms and time <= ").append(DateUtil.parse(param.getEndTime(),DateUtil.LONG_WEB_FORMAT).getTime())
				.append("ms GROUP BY time(").append(param.getTimeGroup()).append("," + offset + "ms) fill(0) TZ('Asia/Shanghai')");
		return command.toString();
	}

	public static void main(String[] args) {
		AdminCollectionMonitorParam p = new AdminCollectionMonitorParam();
		p.setDataSource(Lists.newArrayList("deviceFeatureService", "graphFlinkIRR24Service"));
		p.setTimeDiff(1000);
		p.setType("GPS");
		p.setStartTime("2022-05-13 00:00:00");
		p.setEndTime("2022-06-13 00:00:00");
		p.setTimeGroup("7d");
		System.out.println(new AdminCollectionMonitorServiceImpl().formatQueryCommand("risk_di_irr24_call_service_costTime",p, false));
	}

	private Map<String, String> getMapFromResult(List<Map<String, Object>> sumResult, List<Map<String, Object>> diffResult) {
		Map<String, String> diffMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(diffResult)) {
			diffResult.forEach(m -> {
				String time = ((String) m.get("time")).replace("T", " ").replace("+08:00", "");
				String count = m.getOrDefault("count_value", "0").toString();
				diffMap.put(time, count);
			});
		}


		Map<String, String> result = new HashMap<>();
		sumResult.forEach(m -> {
			String time = ((String) m.get("time")).replace("T", " ").replace("+08:00", "");

			if (diffMap.get(time) == null ) {
				result.put(time, "0");
				return;
			}

			String diffCount = diffMap.get(time);
			String sumCount = m.get("count_value").toString();
			BigDecimal diff = new BigDecimal(diffCount);
			BigDecimal sum = new BigDecimal(sumCount);

			if(BigDecimal.ZERO.compareTo(diff) == 0 || BigDecimal.ZERO.compareTo(sum) == 0 ) {
				result.put(time, "0");
				return;
			}
			
			result.put(time, diff.divide(sum,4,  BigDecimal.ROUND_HALF_UP).toString());
		});

		return result;
	}
}
