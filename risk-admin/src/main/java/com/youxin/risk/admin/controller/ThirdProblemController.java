package com.youxin.risk.admin.controller;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.admin.model.Page;
import com.youxin.risk.admin.model.third.ThirdProblem;
import com.youxin.risk.admin.service.ThirdProblemService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/thirdProblem")
public class ThirdProblemController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(
            ThirdProblemController.class);

    @Autowired
    private ThirdProblemService thirdProblemService;

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    private ResponseEntity<?> save(@RequestBody ThirdProblem thirdProblem){
        long endTime = thirdProblem.getEndTime().getTime();
        long startTime = thirdProblem.getStartTime().getTime();
        if(startTime < endTime){
            thirdProblemService.save(thirdProblem);
            return buildSuccessResponse(1);
        }
        return buildErrorResponse("结束时间不能早于开始时间");
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    private ResponseEntity<?> update(@RequestBody ThirdProblem thirdProblem){
        long endTime = thirdProblem.getEndTime().getTime();
        long startTime = thirdProblem.getStartTime().getTime();
        if(startTime < endTime){
            thirdProblemService.update(thirdProblem);
            return buildSuccessResponse(1);
        }
        return buildErrorResponse("结束时间不能早于开始时间");
    }

    @RequestMapping(value = "/get", method = RequestMethod.POST)
    private ResponseEntity<?> save(@RequestBody JSONObject request){
        Long id = request.getLong("id");
        ThirdProblem thirdProblem = thirdProblemService.get(id);
        return buildSuccessResponse(thirdProblem);
    }

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    private ResponseEntity<?> list(@RequestBody JSONObject request){
        Page<ThirdProblem> page = thirdProblemService.list(request);
        return buildSuccessResponse(page);
    }

    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    private ResponseEntity<?> delete(@RequestBody JSONObject request){
        Long id = request.getLong("id");
        thirdProblemService.delete(id);
        return buildSuccessResponse(1);
    }
}
