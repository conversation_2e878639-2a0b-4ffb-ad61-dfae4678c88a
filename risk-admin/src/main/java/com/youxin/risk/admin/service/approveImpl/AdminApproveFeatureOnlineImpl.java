package com.youxin.risk.admin.service.approveImpl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.admin.constants.ApproveTypeEnum;
import com.youxin.risk.admin.dao.admin.AdminApproveSubmitMapper;
import com.youxin.risk.admin.dao.rm.AdminFeatureMapper;
import com.youxin.risk.admin.model.AdminFeature;
import com.youxin.risk.admin.model.AdminFeatureSubmit;
import com.youxin.risk.admin.model.AdminFeatureSubmitDetail;
import com.youxin.risk.admin.model.approve.AdminApproveSubmit;
import com.youxin.risk.admin.service.AdminApproveService;
import com.youxin.risk.admin.service.AdminFeatureSubmitService;
import com.youxin.risk.commons.constants.FeatureSubmitStatusEnum;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.JsonUtils;
import com.youxin.risk.commons.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class AdminApproveFeatureOnlineImpl extends AdminApproveService {

    private final Logger logger = LoggerFactory.getLogger(AdminApproveFeatureOnlineImpl.class);

    @Autowired
    private AdminFeatureSubmitService adminFeatureSubmitService;
    @Autowired
    private AdminApproveSubmitMapper adminApproveSubmitMapper;
    @Autowired
    private AdminFeatureMapper adminFeatureMapper;

    @Value("${cp_auth_host}")
    private String cpAuthHost;
    @Value("${cp_host}")
    private String cpHost;

    /**
     * 提交审批
     * @param submitUser 提交人
     * @param newObj 参数Vo
     */
    @Override
    public void submitApprove(ApproveTypeEnum approveType, String submitUser, Object newObj){
        List<Integer> ids = (List<Integer>) newObj;
        for (Integer id : ids) {
            AdminFeatureSubmit adminFeatureSubmit = adminFeatureSubmitService.get(id.longValue());
            AdminFeatureSubmitDetail adminFeatureSubmitDetail = adminFeatureSubmit.getDetails().get(0);
            String featureName = adminFeatureSubmitDetail.getFeatureName();//特征名
            //待上线特征版本
            String newVersion = adminFeatureSubmitDetail.getFeatureVer();
            //当前特征版本
            AdminFeature adminFeature = adminFeatureMapper.getOldAdminFeature(adminFeatureSubmit.getSourceSystem(),adminFeatureSubmit.getApplyStep(),featureName);
            String oldVersion = adminFeature != null ? adminFeature.getFeatureVer() : null;
            //特征id
            JSONObject groupDetail = getFeatureGroupDetail(adminFeatureSubmit.getGroupVer());
            String featureId = groupDetail.getJSONArray("featureSelectedList").getJSONObject(0).getString("featureId");
            //FeatureDetail
            JSONObject newFeature = getFeatureDetail(featureId,newVersion);
            JSONObject oldFeature = StringUtils.isEmpty(oldVersion) ? new JSONObject() : getFeatureDetail(featureId,oldVersion);
            //判断特征代码是否一致
            String newComputingCode = newFeature.getString("computingCode");
            String oldComputingCode = oldFeature.getString("computingCode");
            if(StringUtils.isEmpty(newComputingCode) && StringUtils.isEmpty(oldComputingCode)){
                newFeature.put("computingCode","一致");
                oldFeature.put("computingCode","一致");
            }else if(StringUtils.isEmpty(newComputingCode)){
                newFeature.put("computingCode","不一致");
                oldFeature.put("computingCode","不一致");
            }else if(newComputingCode.equals(oldComputingCode)){
                newFeature.put("computingCode","一致");
                oldFeature.put("computingCode","一致");
            }else{
                newFeature.put("computingCode","不一致");
                oldFeature.put("computingCode","不一致");
            }
            //判断特征项是否一致
            String newOutputInfo = newFeature.getString("outputInfo");
            String oldOutputInfo = oldFeature.getString("outputInfo");
            if(StringUtils.isEmpty(newOutputInfo) && StringUtils.isEmpty(oldOutputInfo)){
                newFeature.put("outputInfo","一致");
                oldFeature.put("outputInfo","一致");
            }else if(StringUtils.isEmpty(newOutputInfo)){
                newFeature.put("outputInfo","不一致");
                oldFeature.put("outputInfo","不一致");
            }else if(newOutputInfo.equals(oldOutputInfo)){
                newFeature.put("outputInfo","一致");
                oldFeature.put("outputInfo","一致");
            }else{
                newFeature.put("outputInfo","不一致");
                oldFeature.put("outputInfo","不一致");
            }
            //插入请求记录
            Map<String,Object> newApproveRequest = new HashMap<>();
            newApproveRequest.put("feature",newFeature);
            newApproveRequest.put("submitParams",id);
            Map<String,Object> oldApproveRequest = new HashMap<>();
            oldApproveRequest.put("feature",oldFeature);
            oldApproveRequest.put("submitParams",null);
            AdminApproveSubmit approveSubmit = new AdminApproveSubmit(approveType.type(), submitUser, JsonUtils.toJson(oldApproveRequest), JsonUtils.toJson(newApproveRequest));
            logger.info("insert approveSubmit param={}",JsonUtils.toJson(approveSubmit));
            adminApproveSubmitMapper.insert(approveSubmit);
            int submitId =  approveSubmit.getId().intValue();
            //特征详情页url
            String newFeatureDetailUrl = cpHost + "/feature/index?id="+featureId+"&version="+newVersion;
            String oldFeatureDetailUrl = cpHost + "/feature/index?id="+featureId+"&version="+oldVersion;
            newApproveRequest.put("featureDetailUrl",newFeatureDetailUrl);
            oldApproveRequest.put("featureDetailUrl",oldFeatureDetailUrl);
            //拼接邮件文案
            String templateName = approveType.emailTemplate();
            Map<String, Object> map = new HashMap<>();
            map.put("newVo",newApproveRequest);
            map.put("oldVo", oldApproveRequest);
            map.put("submitUser", submitUser);//申请人
            List<List<String>> approveUsers = approveUser(approveType, JSONObject.parseObject(approveSubmit.getNewRequest()), submitUser);
            String content = buildMailContent(templateName,approveType.type(), submitId, map, approveUsers);
            //获取收件人
            Set<String> users = getAllApproveUsers(approveType, submitId);
            if(CollectionUtils.isEmpty(users)){//不需要审批则直接处理
                handleRequest(approveSubmit);
            }else{
                //发送审批邮件
                String emailTitle = approveType.desc();
                sendEmail("【提交审批】" + emailTitle, content, users);
            }
        }
    }

    @Override
    protected String handleRequest(AdminApproveSubmit submit){
        try{
            JSONObject newVo = JSONObject.parseObject(submit.getNewRequest());
            Long id = newVo.getLong("submitParams");
            AdminFeatureSubmit adminFeatureSubmit = adminFeatureSubmitService.get(id);
            if (adminFeatureSubmit == null) {
                throw new Exception("记录不存在");
            }
            if (!FeatureSubmitStatusEnum.WAIT_ONLINE.name().equals(adminFeatureSubmit.getStatus())) {
                throw new Exception("记录状态非法");
            }
            adminFeatureSubmitService.online(id);
            return HANDLE_SUCCESS;
        }catch (Exception e){
            return HANDLE_FAILED + e.getMessage();
        }
    }


    private JSONObject getFeatureGroupDetail(String groupId){
        String url = cpAuthHost + "/features/group/detail";
            Map<String,Object> par = new HashMap<>();
        par.put("groupId",Integer.valueOf(groupId));
        par.put("groupVersion",1);
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type","application/json");
        String response = SyncHTTPRemoteAPI.postJson(url, JSON.toJSONString(par), header,10000);
        logger.info("getFeatureGroupDetail param={}, response={}", JSON.toJSONString(par), response);
        return JSONObject.parseObject(response).getJSONObject("data").getJSONObject("detail");
    }

    private JSONObject getFeatureDetail(String featureId, String featureVersion){
        String url = cpAuthHost + "/features/detail";
        Map<String,Object> par = new HashMap<>();
        par.put("featureId",Integer.valueOf(featureId));
        par.put("featureVersion",Integer.valueOf(featureVersion));
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type","application/json");
        String response = SyncHTTPRemoteAPI.postJson(url, JSON.toJSONString(par), header,10000);
        logger.info("getFeatureDetail param={}, response={}", JSON.toJSONString(par), response);
        return JSONObject.parseObject(response).getJSONObject("data").getJSONObject("detail");
    }

}
