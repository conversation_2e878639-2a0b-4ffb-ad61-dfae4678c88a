package com.youxin.risk.admin.dao.rm;

import com.alibaba.fastjson.JSONArray;
import com.youxin.risk.admin.dao.BaseMapper;
import com.youxin.risk.admin.model.AdminFeatureCode;
import com.youxin.risk.admin.model.AdminFeatureSubmit;
import com.youxin.risk.admin.model.AdminFeatureSubmitDetail;
import com.youxin.risk.admin.model.AdminFeatureSubmitGroup;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/1/24 11:04
 */
public interface AdminFeatureSubmitMapper extends BaseMapper<AdminFeatureSubmit> {
    int insertDetail(AdminFeatureSubmitDetail detail);

    int insertCode(AdminFeatureCode code);

    int deleteCode(Integer id);

    List<AdminFeatureSubmitGroup> selectGroupByPage(Map<String, Object> params);

    int selectGroupTotal(Map<String, Object> params);

    List<AdminFeatureSubmit> selectListByGroup(@Param("sourceSystem")String sourceSystem, @Param("developer")String developer
            , @Param("featureName") String featureName, @Param("createTime") Date createTime);

    List<AdminFeatureSubmitDetail> selectDetails(String featureSubmitCode);

    int deleteDetails(String featureSubmitCode);

    List<AdminFeatureSubmit> selectWaitOnlineRecords(@Param("steps") JSONArray steps);

    List<String> selectCodeByIds(@Param("ids") List<Integer> ids);

    AdminFeatureSubmit getBySubmitCode(String submitCode);

    List<AdminFeatureSubmit> selectBySubmitCodeList(@Param("submitCodeList") List<String> submitCodeList);

	List<AdminFeatureSubmitDetail> queryFeatureOnlineSubmit();

    int updateStepLevelById(@Param("stepLevel")int stepLevel, @Param("id")Long id);

    /**
     * 根据提交id查询层深
     * @param submitDetailId
     * @return
     */
    AdminFeatureSubmitDetail getDetailById(@Param("submitDetailId") Long submitDetailId);

    /**
     * 根据特征的提交code 更新是否通过
     * @param featureSubmitCode
     * @param isPassed
     * @return
     */
    int updateFeatureSubmitBySubmitCode(@Param("featureSubmitCode") String featureSubmitCode, @Param("isPassed") String isPassed);

    int getWaitOnlineCountByTag(@Param("tag") String tag);

    List<AdminFeatureSubmitDetail> getFeatureSubmitDetailByIdList(@Param("idList") List<Long> submitDetailIdList);
}
