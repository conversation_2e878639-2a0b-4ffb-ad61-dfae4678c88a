package com.youxin.risk.admin.client;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Component
public class VariableClient {
    private static final Logger logger = LoggerFactory.getLogger(VariableClient.class);

    @Resource
    private VariableCenterClient variableCenterClient;

    public Map<String, Object> getDefaultValues(Set<String> vars) {
        Map<String, Object> newVars = new HashMap<>();
        if (vars.isEmpty()) {
            return newVars;
        }
        JSONArray variableCodes = variableCenterClient.queryByVariableCodes(new ArrayList<>(vars));
        for (int i = 0; i < variableCodes.size(); i++) {
            JSONObject variableCode = variableCodes.getJSONObject(i);
            String varCode = variableCode.getString("code");
            String varType = variableCode.getString("valueType");
            String varDefaultValue = variableCode.getString("defaultValue");
            newVars.put(varCode, parseDefaultValue(varType, varDefaultValue));
        }
        logger.info("获取单笔测试变量默认值成功, vars: {}, newVars: {}", vars.size(), newVars.size());
        return newVars;
    }

    public Map<String, Object> getDefaultIntegrationValues(Set<String> vars) {
        Map<String, Object> newVars = new HashMap<>();
        if (vars.isEmpty()) {
            return newVars;
        }
        JSONArray variableCodes = variableCenterClient.queryByVariableCodes(new ArrayList<>(vars));
        for (int i = 0; i < variableCodes.size(); i++) {
            JSONObject variableCode = variableCodes.getJSONObject(i);
            String varCode = variableCode.getString("code");
            String varType = variableCode.getString("valueType");
            String varDefaultValue = variableCode.getString("defaultValue");
            newVars.put(varCode, buildVarInfo(varType, varDefaultValue));
        }
        logger.info("获取集成测试变量默认值成功, vars: {}, newVars: {}", vars.size(), newVars.size());
        return newVars;
    }

    private Map<String, String> buildVarInfo(String varType, String varDefaultValue) {
        Map<String, String> varInfo = new HashMap<>();
        varInfo.put("varType", varType);
        varInfo.put("varDefaultValue", varDefaultValue);
        return varInfo;
    }

    private Object parseDefaultValue(String varType, String varDefaultValue) {
        switch (varType) {
            case "NUMBER":
                // 兼容小数
                try{
                    return Integer.parseInt(varDefaultValue);
                }catch (Exception e){
                    return Double.parseDouble(varDefaultValue);
                }
            case "STRING":
                return varDefaultValue;
            case "BOOL":
                return Boolean.parseBoolean(varDefaultValue);
            default:
                return varDefaultValue;
        }
    }
}
