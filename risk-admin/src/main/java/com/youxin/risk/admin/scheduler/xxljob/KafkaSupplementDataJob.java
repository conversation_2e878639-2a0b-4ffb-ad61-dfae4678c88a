package com.youxin.risk.admin.scheduler.xxljob;

import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.youxin.risk.admin.service.KafkaService;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.xxl.job.XxlJobBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * kafka topic补充数据任务，从最早的偏移量开始消费，消息重复无影响
 * <AUTHOR>
 */
@Component
public class KafkaSupplementDataJob implements XxlJobBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(KafkaSupplementDataJob.class);

    @Autowired
    private KafkaService kafkaService;

    @Override
    @XxlJob(value = "kafkaSupplementDataJob")
    public ReturnT<String> execJobHandler(String param) {
        LoggerProxy.info("kafkaSupplementDataJob", LOGGER, "param={}", param);
        try {
            kafkaService.supplementData(JSON.parseObject(param));
            return ReturnT.SUCCESS;
        }catch (Exception e){
            LoggerProxy.error("KafkaSupplementDataJobError",LOGGER, e.getMessage(), e);
        }
        LoggerProxy.info("kafkaSupplementDataJob", LOGGER, "end");
        return ReturnT.FAIL;
    }
}
