package com.youxin.risk.admin.model;

import com.youxin.risk.commons.model.BaseModel;

public class AdminDiServiceOutput extends BaseModel {
    /**
     * 
     */
    private static final long serialVersionUID = 3898140947253601744L;

    private String serviceCode;

    private String outputCode;

    private String valueType;

    private Boolean isTrans;

    private String transCode;
    
    public Boolean getIsTrans() {
        return isTrans;
    }

    public void setIsTrans(Boolean isTrans) {
        this.isTrans = isTrans;
    }

    public String getValueType() {
        return valueType;
    }

    public void setValueType(String valueType) {
        this.valueType = valueType;
    }

    public String getServiceCode() {
        return serviceCode;
    }

    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode == null ? null : serviceCode.trim();
    }

    public String getOutputCode() {
        return outputCode;
    }

    public void setOutputCode(String outputCode) {
        this.outputCode = outputCode == null ? null : outputCode.trim();
    }

    public String getTransCode() {
        return transCode;
    }

    public void setTransCode(String transCode) {
        this.transCode = transCode == null ? null : transCode.trim();
    }
}