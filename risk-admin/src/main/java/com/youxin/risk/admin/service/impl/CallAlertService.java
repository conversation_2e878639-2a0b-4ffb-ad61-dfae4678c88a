package com.youxin.risk.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

@Service("callAlertService")
public class CallAlertService {

    @Value("${alert.collect.config.url}")
    private  String alertCollectUrl ;

    public  String getAlertData(String commond,String postData,String requestId){
        JSONObject jsonParam = JSON.parseObject(postData);
        jsonParam.put("requestId", requestId);
        postData = JSON.toJSONString(jsonParam);
        String retData = "";
        switch (commond){
            case "getDatasourceNames":
                retData = SyncHTTPRemoteAPI.postJson(alertCollectUrl + "getDatasourceNames", postData, 30000);
                break;
            case "getTableNames":
                retData = SyncHTTPRemoteAPI.postJson(alertCollectUrl + "getTableNames", postData, 30000);
                break;
            case "getFunctionFields":
                retData = SyncHTTPRemoteAPI.postJson(alertCollectUrl + "getFunctionFields", postData, 30000);
                break;
            case "getTagKeys":
                retData = SyncHTTPRemoteAPI.postJson(alertCollectUrl + "getTagKeys", postData, 30000);
                break;
            case "getTagValues":
                retData = SyncHTTPRemoteAPI.postJson(alertCollectUrl + "getTagValues", postData, 30000);
                break;
        }
        return retData;

    }
    public static void main(String[] args){
        String pd = "{datasourceName: \"renrendai_antifraud_ra_verify\", tableName: \"verify_review_python\"}";
        JSONObject jsonParam = JSON.parseObject(pd);
        System.out.println("jsonParam: "  + jsonParam);
    }

}
