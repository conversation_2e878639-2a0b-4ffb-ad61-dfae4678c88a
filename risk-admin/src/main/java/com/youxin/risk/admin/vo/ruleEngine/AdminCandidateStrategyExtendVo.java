package com.youxin.risk.admin.vo.ruleEngine;

import com.youxin.risk.admin.domain.ruleengine.AdminCandidateStrategy;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AdminCandidateStrategyExtendVo extends AdminCandidateStrategy {
    /**
     * 变量是否自动导入
     */
    private Boolean autoImportFlag;
    /**
     * 是否展示回滚按钮
     */
    private Boolean showRollback;
}
