package com.youxin.risk.admin.model.ruleEngine;

import java.io.Serializable;
import java.util.Date;

/**
 * 新决策引擎项目提交历史表(RuleProjectCommitHistory)实体类
 */
public class RuleProjectCommitHistory implements Serializable {
    private static final long serialVersionUID = 235680750447613820L;
    /**
     * 主键
     */
    private Integer id;
    /**
     * (项目id)
     */
    private Integer projectId;
    /**
     * (项目名称)
     */
    private String projectName;
    /**
     * (文件名称)
     */
    private String fileName;
    /**
     * (文件内容)
     */
    private String code;
    /**
     * （该版本文件编写人）
     */
    private String username;
    /**
     * (文件备注)
     */
    private String comment;
    /**
     * (是否删除，0正常，1删除)
     */
    private Integer isDel;
    /**
     * (该版本是否已经共享，共享后的版本可查阅 0表示未共享，1表示共享)
     */
    private Integer isShare;
    /**
     * (共享id)
     */
    private String shareId;
    /**
     * （更新时间）
     */
    private Date updateTime;
    /**
     * （创建时间）
     */
    private Date createTime;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getProjectId() {
        return projectId;
    }

    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    public Integer getIsShare() {
        return isShare;
    }

    public void setIsShare(Integer isShare) {
        this.isShare = isShare;
    }

    public String getShareId() {
        return shareId;
    }

    public void setShareId(String shareId) {
        this.shareId = shareId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

}

