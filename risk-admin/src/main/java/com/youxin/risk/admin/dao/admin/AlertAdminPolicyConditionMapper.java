package com.youxin.risk.admin.dao.admin;

import com.youxin.risk.admin.model.alert.AlertAdminPolicyCondition;

import java.util.List;

public interface AlertAdminPolicyConditionMapper extends BaseAlertAdminMapper<AlertAdminPolicyCondition> {
    AlertAdminPolicyCondition selectExisted(AlertAdminPolicyCondition alertAdminPolicyCondition);

    int deleteBatchByPolicyName(String policyName);

    void insertBatch(List<AlertAdminPolicyCondition> list);
}