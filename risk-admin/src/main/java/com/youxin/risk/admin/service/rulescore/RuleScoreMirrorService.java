package com.youxin.risk.admin.service.rulescore;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.admin.dao.admin.RuleScoreMirrorMapper;
import com.youxin.risk.admin.model.rulescore.RuleScoreMirror;
import com.youxin.risk.commons.constants.CompareDiffTypeEnums;
import com.youxin.risk.commons.constants.CompareTypeEnum;
import com.youxin.risk.commons.mongo.RuleScoreResultMongoDao;
import com.youxin.risk.commons.utils.JsonDiffComparatorNew;
import com.youxin.risk.commons.vo.ExperimentCompareResultVo;
import com.youxin.risk.commons.vo.RuleScoreMirrorResultVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class RuleScoreMirrorService {
    private static final Logger logger = LoggerFactory.getLogger(RuleScoreMirrorService.class);

    @Autowired
    private RuleScoreMirrorMapper ruleScoreMirrorMirrorMapper;

    @Autowired
    private RuleScoreResultMongoDao ruleScoreResultMongoDao;

    /**
     * 规则集成比对结果到处到响应流中
     * @param params
     */
    public String exportExperimentResult(JSONObject params) throws IOException {
        List<RuleScoreMirrorResultVo> allList = getRuleScoreMirrorResultVos(params);
        logger.info("exportExperimentResult, allList.size={}", allList.size());
        return simExportToExcel(allList);
    }

    /**
     * 规则比对结果到处到响应流中
     * @param params
     */
    public String exportExperimentResultNew(JSONObject params) throws IOException {
        String ruleMirrorCode = params.getString("ruleMirrorCode");
        RuleScoreMirror ruleScoreMirror = ruleScoreMirrorMirrorMapper.findRuleScoreMirror(ruleMirrorCode);
        List<RuleScoreMirrorResultVo> allList = getRuleScoreMirrorResultVos(params);
        logger.info("exportExperimentResultNew, allList.size={}", allList.size());
        Set<String> cmpKeys = new HashSet<>();
        Set<String> containFieldSet = new HashSet<>(JsonDiffComparatorNew.getCmpFieldSet(ruleScoreMirror.getContainField()));
        Set<String> excludeFieldSet = new HashSet<>(JsonDiffComparatorNew.getCmpFieldSet(ruleScoreMirror.getExcludeField()));
        if (ruleScoreMirror.getRuleCompareType().equals(CompareTypeEnum.BY_ALL.getCode())
                || CollectionUtils.isEmpty(containFieldSet)) {
            if (CollectionUtils.isNotEmpty(allList)) {
                RuleScoreMirrorResultVo ruleScoreMirrorResultVo = allList.get(0);
                List<ExperimentCompareResultVo> diffResultList = ruleScoreMirrorResultVo.getDiffResultList();
                if (CollectionUtils.isNotEmpty(diffResultList)) {
                    for (ExperimentCompareResultVo diffResult : diffResultList) {
                        containFieldSet.add(diffResult.getDiffKey());
                    }
                }
            }
        }

        if(CollectionUtils.isNotEmpty(containFieldSet)){
            for (String excludeField : excludeFieldSet) {
                containFieldSet.removeIf(containField -> containField.startsWith(excludeField));
            }
            cmpKeys.addAll(containFieldSet);
        }

        return exportToExcel(allList, cmpKeys);
    }

    private @NotNull List<RuleScoreMirrorResultVo> getRuleScoreMirrorResultVos(JSONObject params) {
        List<RuleScoreMirrorResultVo> allList = new ArrayList<>();
        int pageNo = 1;
        int pageSize = 1000;
        while (true) {
            params.put("pageNum", pageNo);
            params.put("pageSize", pageSize);
            params.put("expCode", params.get("ruleMirrorCode"));
            List<RuleScoreMirrorResultVo> data = ruleScoreResultMongoDao.selectListForExportNew(params);
            if (CollectionUtils.isEmpty(data)) {
                break;
            }
            logger.info("exportExperimentResultNew, pageNo={}, data.size={}, params={}"
                    , pageNo, data.size(), JSON.toJSONString(params));
            allList.addAll(data);
            pageNo++;

            // 如果startTime为空，那么设置为3天前
            if (!params.containsKey("startTime")) {
                Instant instant1 = Instant.now().minusSeconds(3 * 24 * 60 * 60);
                long timestamp1 = instant1.toEpochMilli();
                params.put("startTime", timestamp1);
            }

            // 最多查询10页
            if (pageNo > 10) {
                break;
            }
        }
        return allList;
    }


    /**
     * 结果导出到响应流中
     * @param allList
     */
    public String simExportToExcel(List<RuleScoreMirrorResultVo> allList) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Results");
        try {
            // Write the header
            Row headerRow = sheet.createRow(0);
            String[] headers = {"user_key", "loan_key", "策略类型", "节点名称", "差异key"
                    , "差异类型", "线上数据", "线上类型", "镜像数据", "镜像类型"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }

            // Write the data
            int rowNum = 1;
            for (RuleScoreMirrorResultVo item : allList) {
                List<ExperimentCompareResultVo> diffResultList = item.getDiffResultList();
                if (CollectionUtils.isNotEmpty(diffResultList)) {
                    for (ExperimentCompareResultVo diffResult : diffResultList) {
                        Row row = sheet.createRow(rowNum++);
                        row.createCell(0).setCellValue(item.getUserKey());
                        row.createCell(1).setCellValue(item.getLoanKey());
                        row.createCell(2).setCellValue(item.getStrategyCodeType());
                        row.createCell(3).setCellValue(item.getNode());
                        row.createCell(4).setCellValue(diffResult.getDiffKey());
                        row.createCell(5).setCellValue(diffResult.getDiffType());
                        row.createCell(6).setCellValue(diffResult.getResponse());
                        row.createCell(7).setCellValue(diffResult.getOnlineFieldType());
                        row.createCell(8).setCellValue(diffResult.getMirrorResponse());
                        row.createCell(9).setCellValue(diffResult.getMirrorFieldType());
                    }
                } else {
                    Row row = sheet.createRow(rowNum++);
                    row.createCell(0).setCellValue(item.getUserKey());
                    row.createCell(1).setCellValue(item.getLoanKey());
                    row.createCell(2).setCellValue(item.getStrategyCodeType());
                    row.createCell(3).setCellValue(item.getNode());
                    for (int i = 4; i < headers.length; i++) {
                        row.createCell(i).setCellValue("");
                    }
                }
            }

            workbook.write(baos);
        } finally {
            workbook.close();
        }

        return Base64.getEncoder().encodeToString(baos.toByteArray());
    }

    /**
     * 结果导出到响应流中
     * @param allList
     */
    public String exportToExcel(List<RuleScoreMirrorResultVo> allList, Set<String> cmpKeys) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Results");

        try {
            Row headerRow = sheet.createRow(0);
            String[] baseHeaders = {"user_key", "loan_key", "策略类型", "节点名称"};
            int colIndex = 0;
            for (String header : baseHeaders) {
                Cell cell = headerRow.createCell(colIndex++);
                cell.setCellValue(header);
            }
            for (String cmp : cmpKeys) {
                String[] cmpHeaders = {cmp + "_线上", cmp + "_线上类型", cmp + "_镜像", cmp + "_镜像类型", cmp + "_是否一致"};
                for (String header : cmpHeaders) {
                    Cell cell = headerRow.createCell(colIndex++);
                    cell.setCellValue(header);
                }
            }

            int rowNum = 1;
            for (RuleScoreMirrorResultVo item : allList) {
                Row row = sheet.createRow(rowNum++);
                colIndex = 0;
                row.createCell(colIndex++).setCellValue(item.getUserKey());
                row.createCell(colIndex++).setCellValue(item.getLoanKey());
                row.createCell(colIndex++).setCellValue(item.getStrategyCodeType());
                row.createCell(colIndex++).setCellValue(item.getNode());

                List<ExperimentCompareResultVo> diffResultList = item.getDiffResultList();
                Map<String, ExperimentCompareResultVo> diffResultMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(diffResultList)) {
                    for (ExperimentCompareResultVo diffResult : diffResultList) {
                        diffResultMap.put(diffResult.getDiffKey(), diffResult);
                    }
                }

                for (String cmp : cmpKeys) {
                    ExperimentCompareResultVo diffResult = diffResultMap.get(cmp);
                    if (diffResult != null) {
                        row.createCell(colIndex++).setCellValue(diffResult.getResponse());
                        row.createCell(colIndex++).setCellValue(diffResult.getOnlineFieldType());
                        row.createCell(colIndex++).setCellValue(diffResult.getMirrorResponse());
                        row.createCell(colIndex++).setCellValue(diffResult.getMirrorFieldType());
                        row.createCell(colIndex++).setCellValue(CompareDiffTypeEnums.NO_DIFF.name().equals(diffResult.getDiffType())
                                ? 1 : 0);
                    } else {
                        for (int i = 0; i < 5; i++) {
                            row.createCell(colIndex++).setCellValue("");
                        }
                    }
                }
            }

            workbook.write(baos);
        } finally {
            workbook.close();
        }

        return Base64.getEncoder().encodeToString(baos.toByteArray());
    }
}
