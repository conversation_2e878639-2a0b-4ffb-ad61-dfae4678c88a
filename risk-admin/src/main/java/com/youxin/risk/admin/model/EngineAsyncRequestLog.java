package com.youxin.risk.admin.model;

import com.youxin.risk.commons.model.BaseModel;

import java.util.Date;

/**
 * 异步请求表
 * 
 * <AUTHOR>
 * 
 * @date 2018-11-19
 */
public class EngineAsyncRequestLog extends BaseModel {

    /**
     * 异步请求流水ID
     */
    private String asyncRequestId;

    /**
     * 事件ID
     */
    private String sessionId;

    /**
     * 来源系统
     */
    private String sourceSystem;

    /**
     * 用户编码
     */
    private String userKey;

    /**
     * 贷款编码
     */
    private String loanKey;

    public String getDataCode() {
        return dataCode;
    }

    public void setDataCode(String dataCode) {
        this.dataCode = dataCode;
    }

    /**
     * 服务编码
     */
    private String dataCode;

    /**
     * 数据任务ID
     */
    private String jobId;

    /**
     * 节点ID
     */
    private String nodeId;

    /**
     * 类型：DI-服务请求，VARIABLE-变量请求，CHANNEL-渠道请求
     */
    private String type;

    /**
     * 状态：INIT-新建，SUBMITTED-已提交，FETCHED-已获取
     */
    private String status;


    public String getAsyncRequestId() {
        return asyncRequestId;
    }

    public void setAsyncRequestId(String asyncRequestId) {
        this.asyncRequestId = asyncRequestId == null ? null : asyncRequestId.trim();
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId == null ? null : sessionId.trim();
    }

    public String getSourceSystem() {
        return sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem == null ? null : sourceSystem.trim();
    }

    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey == null ? null : userKey.trim();
    }

    public String getLoanKey() {
        return loanKey;
    }

    public void setLoanKey(String loanKey) {
        this.loanKey = loanKey == null ? null : loanKey.trim();
    }


    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId == null ? null : jobId.trim();
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId == null ? null : nodeId.trim();
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

}