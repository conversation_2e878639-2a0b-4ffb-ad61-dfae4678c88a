package com.youxin.risk.admin.constants;

/**
 * @description: 卡单监控的聚合方式
 * @author: juxiang
 * @create: 2021-10-25 14:09
 **/
public enum AggregationType {
    TEN_MINUTES("10m","10分钟"),
    SIX_HOURS("6h","6小时"),
    ONE_HOURS("1h","1小时"),
    TWELVE_HOURS("12h","12小时")
    ;

    private String type;
    private String desc;

    AggregationType(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }


    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
