package com.youxin.risk.admin.model;

import com.youxin.risk.commons.model.BaseModel;
import org.springframework.data.annotation.Transient;

import java.util.Date;

/**
 * @ClassName FeatureDistinctDr
 * @Description 依赖特征表: 再研究平台维护后,同步过来(定义表)
 * <AUTHOR>
 * @Date 2021/10/25 5:30 下午
 **/
public class FeatureDistinctDr extends BaseModel {
    /** 从研究平台传过来对应的主键id **/
    /**
     * 事件
     */
    private String event;
    /**
     * 步骤
     */
    private String step;
    /**
     * 特征名
     */
    private String tag;
    /**
     * 特征输出项
     */
    private String outputInfo;
    /**
     * 生效时间
     */
    private Date validTime;
    /**
     * 失效时间
     */
    private Date inValidTime;
    /**
     * 状态
     */
    private Integer status;

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public String getStep() {
        return step;
    }

    public void setStep(String step) {
        this.step = step;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getOutputInfo() {
        return outputInfo;
    }

    public void setOutputInfo(String outputInfo) {
        this.outputInfo = outputInfo;
    }

    public Date getValidTime() {
        return validTime;
    }

    public void setValidTime(Date validTime) {
        this.validTime = validTime;
    }

    public Date getInValidTime() {
        return inValidTime;
    }

    public void setInValidTime(Date inValidTime) {
        this.inValidTime = inValidTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
