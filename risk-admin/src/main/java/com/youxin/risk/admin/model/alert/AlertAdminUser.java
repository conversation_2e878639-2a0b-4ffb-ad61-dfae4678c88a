package com.youxin.risk.admin.model.alert;

import java.util.Date;

/**
 * <AUTHOR> @date 2018-08-07
 */
public class AlertAdminUser extends BaseAlertPojo {

    /**
     * 用户名
     */
    private String userName;

    /**
     * 用户所属组
     */
    private String groupName;

    private String email;

    private String phoneNumber;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName == null ? null : groupName.trim();
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber == null ? null : phoneNumber.trim();
    }
}