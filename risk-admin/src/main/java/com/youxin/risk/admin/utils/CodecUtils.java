package com.youxin.risk.admin.utils;

import com.weicai.caesar.CaesarUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static java.util.regex.Pattern.MULTILINE;

/**
 * 加解密工具类
 *    NOTE:该类为临时过渡类  等所有三方数据全部加密完成后 可移除
 *
 * <AUTHOR>
 */
@Slf4j
public final class CodecUtils {
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("([1-6][1-9]|50)\\d{4}(18|19|20)\\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]", MULTILINE);
    public static final Pattern MOBILE_PATTERN = Pattern.compile("[mobile|phone]([^\\d]{1,5})(\\d{5,11})([^\\d]{1})", MULTILINE);
    private final static ExecutorService pool = new ThreadPoolExecutor(30, 100, 1, TimeUnit.SECONDS, new ArrayBlockingQueue<>(10));


    private static List<String> splitString(String inputString, int length) {
        int size = inputString.length() / length;
        if (inputString.length() % length != 0) {
            size += 1;
        }
        return getStrList(inputString, length, size);
    }

    public static List<String> getStrList(String inputString, int length,
                                          int size) {
        List<String> list = new ArrayList<>();
        for (int index = 0; index < size; index++) {
            String childStr = substring(inputString, index * length,
                    (index + 1) * length);
            list.add(childStr);
        }
        return list;
    }

    public static String substring(String str, int f, int t) {
        if (f > str.length()) {
            return null;
        }
        if (t > str.length()) {
            return str.substring(f);
        } else {
            return str.substring(f, t);
        }
    }

    public static String encodeText(String plaintText) {
        List<Future<String>> futureList = new ArrayList<>();
        try {
            List<String> contents = splitString(plaintText, 80000);
            for (String content : contents) {
                Future<String> future = pool.submit(() -> {
                    String replacedIdCard = idCardEncode(content);
                    return mobileEncode(replacedIdCard);
                });
                futureList.add(future);
            }

            StringBuilder builder = new StringBuilder();
            for (Future<String> stringFuture : futureList) {
                builder.append(stringFuture.get());
            }
            return builder.toString();
        } catch (Exception e) {
            log.error("encodeTextError", e);
        }
        return plaintText;
    }

    private static String mobileEncode(String plaintText) {
        Matcher matcher = MOBILE_PATTERN.matcher(plaintText);
        StringBuilder builder;
        List<String> replacedMobiles = new ArrayList<>();
        while (matcher.find()) {
            String mobile = matcher.group(2);
            String matchedData = matcher.group();
            builder = new StringBuilder(plaintText);
            int start = matcher.start() + (matchedData.length() - mobile.length());
            String encodeMobile = encode(mobile);
            plaintText = builder.replace(start, matcher.end()-1, encode(mobile)).toString();
            matcher = MOBILE_PATTERN.matcher(plaintText);
            replacedMobiles.add(mobile + "-->" + encodeMobile);
        }
        log.info("replacedMobile, list={}", String.join(",", replacedMobiles));
        return plaintText;
    }

    private static String idCardEncode(String plaintText) {
        Matcher matcher = ID_CARD_PATTERN.matcher(plaintText);
        Set<String> handleIdCard = new HashSet<>();
        while (matcher.find()) {
            String idCard = matcher.group();
            String encodeIdCard = encode(idCard);
            plaintText = plaintText.replaceAll(idCard, encodeIdCard);
            handleIdCard.add(idCard + "-->" + encodeIdCard);
        }
        log.info("replacedIdCard, list={}", String.join(",", handleIdCard));
        return plaintText;
    }

    private static String encode(String origin) {
        return CaesarUtil.encode(origin);
    }


    public static void main(String[] args) throws IOException {
        InputStream resourceAsStream = CodecUtils.class.getResourceAsStream("/content.text");
        String s = IOUtils.toString(resourceAsStream, StandardCharsets.UTF_8.name());
        String result = CodecUtils.encodeText(s);
        System.out.println(result);
    }
}
