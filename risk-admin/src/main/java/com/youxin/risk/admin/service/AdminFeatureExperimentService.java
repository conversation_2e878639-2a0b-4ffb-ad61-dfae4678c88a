package com.youxin.risk.admin.service;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.admin.model.AdminFeatureExperiment;
import com.youxin.risk.admin.model.FeatureExperimentCompareVo;
import com.youxin.risk.admin.model.Page;
import com.youxin.risk.commons.vo.FeatureExpResultMetrics;
import com.youxin.risk.commons.vo.FeatureExperimentResultVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/1/24 16:38
 */
public interface AdminFeatureExperimentService extends BaseService<AdminFeatureExperiment> {
    Boolean checkExists(String expCode);

    List<AdminFeatureExperiment> selectForEnable();

    List<AdminFeatureExperiment> selectForDisable();

    void updateStatus(List<Long> idList, String status);

    Page<FeatureExperimentResultVo> selectExperimentResultPage(Map<String, Object> params);

    FeatureExperimentResultVo getExperimentResult(String id, String expType);

    FeatureExperimentResultVo getExperimentInfoData(String id, String infoType, String expType);

    void updateFeatureExperimentStatus(AdminFeatureExperiment item);

    void saveOrUpdate(AdminFeatureExperiment request);

    void batchSaveOrUpdate(Map<String, Object> request) throws Exception;

    /**
     * 根据提交编码获取指标
     * @param submitCode
     * @return
     */
    String getExperimentExpCode(String submitCode);

    /**
     * 计算特征镜像指标
     * @param params
     * @return
     */
    FeatureExpResultMetrics calFeatureExpMetrics(Map<String, Object> params);

    /**
     * 获取特征镜像指标
     * @param expCode 实验编码
     * @return 指标信息
     */
    FeatureExpResultMetrics getFeatureExpMetrics(String expCode);

    /**
     * 根据特征提交编码获取
     * @param params
     * @return
     */
    List<FeatureExperimentCompareVo> exportExperimentResult(JSONObject params);

    /**
     * 列表查询
     * @param params
     * @return
     */
    List<AdminFeatureExperiment> selectList(Map<String, Object> params);
}
