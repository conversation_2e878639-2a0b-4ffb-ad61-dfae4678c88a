package com.youxin.risk.admin.controller;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.admin.interceptor.SystemLog;
import com.youxin.risk.admin.model.AdminProcessNode;
import com.youxin.risk.admin.model.Page;
import com.youxin.risk.admin.service.AdminStrategyTypeService;
import com.youxin.risk.commons.model.StrategyTypeDO;
import com.youxin.risk.commons.model.StrategyTypeVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/10/9 10:28
 * @desc
 */
@Controller
@RequestMapping("/strategyType")
public class AdminStrategyTypeController extends BaseController  {
    private static final Logger LOGGER = LoggerFactory.getLogger(AdminStrategyTypeController.class);

    @Autowired
    private AdminStrategyTypeService adminStrategyTypeService;

    //新增接口
    @ResponseBody
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public ResponseEntity<?> add(@RequestBody StrategyTypeVO request) {
        LOGGER.info("策略类型新增：请求入参={}", JSON.toJSONString(request));
        adminStrategyTypeService.add(request);
        return buildSuccessResponse(1);
    }


    //查询接口
    @ResponseBody
    @RequestMapping(value = "/selectPage", method = RequestMethod.POST)
    public ResponseEntity<?> selectPage(@RequestBody JSONObject params) {
        LOGGER.info("策略类型查询：请求入参={}", JSON.toJSONString(params));
        Page<StrategyTypeVO> page = adminStrategyTypeService.selectPage(params);
        return buildSuccessResponse(page);
    }

    //删除接口

    /**
     * 查询所有策略类型
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/selectAllStrategyType", method = RequestMethod.GET)
    public ResponseEntity<?> selectAllStrategyType() {
        List<String> list = adminStrategyTypeService.selectAllStrategyTypeCode();
        LOGGER.info("查询所有策略类型：结果={}", JSON.toJSONString(list));
        return buildSuccessResponse(list);
    }

    /**
     * 查询所有策略节点
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/selectStrategyNodes", method = RequestMethod.GET)
    public ResponseEntity<?> selectStrategyNodes(@RequestParam(value = "strategyType", required = false) String strategyType) {
        List<String> list = adminStrategyTypeService.selectStrategyNodes(strategyType);
        LOGGER.info("查询策略节点：strategyType={}，结果={}", strategyType, JSON.toJSONString(list));
        return buildSuccessResponse(list);
    }

    /**
     * 查询所有策略节点
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResponseEntity<?> delete(@RequestBody JSONObject jsonObject) {
        Long id = jsonObject.getLong("id");
        Assert.notNull(id, "id不能为空！");
        adminStrategyTypeService.delete(id);
        return buildSuccessResponse(1);
    }
}
