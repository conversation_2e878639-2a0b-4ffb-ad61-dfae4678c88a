package com.youxin.risk.admin.model.vo;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 特征耗时详情
 * <AUTHOR>
 */
@Data
@Builder
public class EventCostTimeDetailVO implements Serializable {

    private static final long serialVersionUID = 772907491620701768L;

    /** 事件名*/
    private String eventCode;
    /** 昨日tp90*/
    private String yesTp90;
    /** 昨日总数*/
    private String yesCount;
    /** 前天tp90*/
    private String ldYesTp90;
    /** 前天总数*/
    private String ldYesCount;
    /** 近七天tp90*/
    private String last7Tp90;
    /** 近七天总数*/
    private String last7Count;
    /** 近14天tp90*/
    private String last14Tp90;
    /** 近14天总数*/
    private String last14Count;
}
