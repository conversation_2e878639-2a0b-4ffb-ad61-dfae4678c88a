package com.youxin.risk.admin.controller;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.admin.model.Page;
import com.youxin.risk.admin.model.third.ThirdNotify;
import com.youxin.risk.admin.service.ThirdNotifyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@RestController
@RequestMapping("/thirdNotify")
public class ThirdNotifyController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(
            ThirdNotifyController.class);

    @Autowired
    private ThirdNotifyService thirdNotifyService;

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    private ResponseEntity<?> save(@RequestBody ThirdNotify thirdNotify){
        if(thirdNotify.getNoticeTime().getTime() < new Date().getTime()){
            return buildErrorResponse("自定义的通知时间不能早于当前时间");
        }else if (thirdNotify.getNoticeTime().getTime() > thirdNotify.getTriggerTime().getTime()){
            return buildErrorResponse("自定义的通知时间不能晚于通知触发时间");
        }
        thirdNotifyService.save(thirdNotify);
        return buildSuccessResponse(1);
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    private ResponseEntity<?> update(@RequestBody ThirdNotify thirdNotify){
        if(thirdNotify.getNoticeTime().getTime() < new Date().getTime()){
            return buildErrorResponse("自定义的通知时间不能早于当前时间");
        }else if (thirdNotify.getNoticeTime().getTime() > thirdNotify.getTriggerTime().getTime()){
            return buildErrorResponse("自定义的通知时间不能晚于通知触发时间");
        }else if(thirdNotify.getIsExpire() == 1){
            return buildErrorResponse("过期通知不能修改。");
        }
        thirdNotifyService.update(thirdNotify);
        return buildSuccessResponse(1);
    }

    @RequestMapping(value = "/get", method = RequestMethod.POST)
    private ResponseEntity<?> save(@RequestBody JSONObject request){
        Long id = request.getLong("id");
        ThirdNotify thirdNotify = thirdNotifyService.get(id);
        return buildSuccessResponse(thirdNotify);
    }

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    private ResponseEntity<?> list(@RequestBody JSONObject request){
        Page<ThirdNotify> page = thirdNotifyService.list(request);
        return buildSuccessResponse(page);
    }

    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    private ResponseEntity<?> delete(@RequestBody JSONObject request){
        Long id = request.getLong("id");
        thirdNotifyService.delete(id);
        return buildSuccessResponse(1);
    }

}
