package com.youxin.risk.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.youxin.risk.admin.dao.admin.AdminNodeDataMapper;
import com.youxin.risk.admin.dao.admin.AdminNodeDataParamMapper;
import com.youxin.risk.admin.model.AdminNodeData;
import com.youxin.risk.admin.model.AdminNodeDataParam;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/28 14:33
 * @desc
 */
@Component
public class AdminNodeDataServiceImpl implements AdminNodeDataService {

    private final Logger logger = LoggerFactory.getLogger(AdminNodeDataServiceImpl.class);

    @Resource
    private AdminNodeDataMapper adminNodeDataMapper;
    @Resource
    private AdminNodeDataParamMapper adminNodeDataParamMapper;


    @Override
    public void insertList(List<AdminNodeData> adminNodeDataList) {
        if (CollectionUtils.isEmpty(adminNodeDataList)) {
            return;
        }
        adminNodeDataMapper.insertList(adminNodeDataList);
        insertDataParams(adminNodeDataList);
    }

    private void insertDataParams(List<AdminNodeData> adminNodeDataList) {
        List<AdminNodeDataParam> nodeDataParams = Lists.newArrayList();
        adminNodeDataList.forEach(nodeData -> nodeDataParams.addAll(nodeData.getDataParams()));
        if (CollectionUtils.isEmpty(nodeDataParams)) {
            return;
        }
        adminNodeDataParamMapper.insertList(nodeDataParams);
    }

    @Override
    public void updateList(List<AdminNodeData> adminNodeDataList) {
        for (AdminNodeData adminNodeData : adminNodeDataList) {
            adminNodeDataMapper.update(adminNodeData);

            List<AdminNodeDataParam> dataParamRecords =
                    adminNodeDataParamMapper.selectListByNodeCodeAndDataCode(adminNodeData.getNodeCode(),
                            adminNodeData.getDataCode());
            if (CollectionUtils.isNotEmpty(dataParamRecords)) {
                Set<Long> oldIdList =
                        dataParamRecords.stream().map(AdminNodeDataParam::getId).collect(Collectors.toSet());
                logger.info("数据节点更新：删除入参表达式，数据库中已有集合{}，新表达式集合{}", JSON.toJSONString(dataParamRecords),
                        JSON.toJSONString(adminNodeData.getDataParams()));
                if (CollectionUtils.isNotEmpty(adminNodeData.getDataParams())) {
                    Set<Long> newIdList =
                            adminNodeData.getDataParams().stream().filter(e -> e.getId() != null).map(AdminNodeDataParam::getId).collect(Collectors.toSet());
                    oldIdList.removeAll(newIdList);
                }
                logger.info("数据节点更新：删除入参表达式，待删除集合{}", JSON.toJSONString(oldIdList));
                if (CollectionUtils.isNotEmpty(oldIdList)) {
                    adminNodeDataParamMapper.deleteList(oldIdList);
                }
            }
            if (CollectionUtils.isNotEmpty(adminNodeData.getDataParams())) {
                for (AdminNodeDataParam dataParam : adminNodeData.getDataParams()) {
                    if (dataParam.getId() == null) {
                        adminNodeDataParamMapper.insertList(Collections.singleton(dataParam));
                    }else{
                        adminNodeDataParamMapper.update(dataParam);
                    }
                }
            }
        }
    }

    @Override
    public void deleteList(List<AdminNodeData> adminNodeDataList) {
        if (CollectionUtils.isEmpty(adminNodeDataList)) {
            return;
        }
        Set<Long> adminNodeDataIds = adminNodeDataList.stream().map(AdminNodeData::getId).collect(Collectors.toSet());
        adminNodeDataMapper.deleteList(adminNodeDataIds);

        Set<Long> dataParamIds =
                adminNodeDataList.stream().flatMap(e -> e.getDataParams().stream()).map(AdminNodeDataParam::getId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(dataParamIds)) {
            return;
        }
        adminNodeDataParamMapper.deleteList(dataParamIds);
    }
}
