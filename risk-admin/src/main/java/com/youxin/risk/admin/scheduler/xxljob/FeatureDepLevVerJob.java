package com.youxin.risk.admin.scheduler.xxljob;

import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.youxin.risk.admin.dao.rm.AdminFeatureDependPropertyMapper;
import com.youxin.risk.admin.dao.rm.AdminFeatureMapper;
import com.youxin.risk.admin.dao.rm.AdminFeatureSubmitMapper;
import com.youxin.risk.admin.model.AdminFeatureSubmitDetail;
import com.youxin.risk.commons.service.WeChatService;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.tools.redis.RetryableJedis;
import com.youxin.risk.commons.xxl.job.XxlJobBase;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.youxin.risk.commons.apollo.ApolloNamespaceEnum.ADMIN_SPACE;

/**
 * @description: 特征依赖层级校验定时任务
 * @author: juxiang
 * @create: 2023-03-01 16:26
 **/
@Component
public class FeatureDepLevVerJob implements XxlJobBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(CardMonitorJob.class);

    @Value("${cp_auth_host}")
    private String cpHost;

    private static final String ERROR_FEATURE_DESC_TITLE="特征依赖层级校验告警:\n";
    private static final String ERROR_FEATURE_DESC="当前特征-层级:%s-%s，依赖特征-层级:%s-%s,步骤:%s;\n";
    private static final String NORMAL_FEATURE_DESC="特征依赖层级校验告警(心跳):\n当前所有特征层级无异常";

    private static final String REDIS_KEY="risk_admin_fea_lev_job";

    private static final String ROBOT_KEY="75a45db4-5bc7-4770-8e43-96f693235150";

    @Resource
    private AdminFeatureMapper featureMapper;

    @Resource
    private AdminFeatureSubmitMapper featureSubmitMapper;

    @Resource
    private AdminFeatureDependPropertyMapper adminFeatureDependPropertyMapper;
    @Resource
    private WeChatService weChatService;
    private static final String SKIP_CHECK_STEP="featureDepSkipCheckStep";

    private static final String UNDERLINE="_";

    @Autowired
    private RetryableJedis retryableJedis;

    private static final int QIYWWEXIN_MAXLENGTH = 4500;


    @Override
    @XxlJob(value = "featureDepLevVerJob")
    public ReturnT<String> execJobHandler(String param) {
        LOGGER.info("featureDepLevVerJob start!");
        try {
            List<StepInfo> stepInfos=this.getAllStep();
            StringBuilder stringBuilder=new StringBuilder();
            Set<String> checkedFeature=new HashSet<>();
            List<String> listConfig = ApolloClientAdapter.getListConfig(ADMIN_SPACE, SKIP_CHECK_STEP, String.class);
            stepInfos.forEach(stepInfo -> {
                if(listConfig.contains(stepInfo.getStep())){
                    return;
                };
                List<Long> featureByStep = featureMapper.getFeatureByStep(stepInfo.getStep());
                if(featureByStep.isEmpty()){
                    return;
                }
                List<AdminFeatureSubmitDetail> stepLevelZeroFeatureList = featureSubmitMapper.getFeatureSubmitDetailByIdList(featureByStep).stream().filter(ele-> Objects.nonNull(ele.getStepLevel()) && ele.getStepLevel()==1).collect(Collectors.toList());
                stepLevelZeroFeatureList.forEach(ele->{
                    this.judgeFeatureLevel(ele,stringBuilder,stepInfo.getStep(),checkedFeature,listConfig);
                });
            });
            if(stringBuilder.length()>0){
                List<String> list = this.splitMessage(stringBuilder.toString().split(";"), ERROR_FEATURE_DESC_TITLE);
                list.forEach(message-> weChatService.sendTextMsg(message,ROBOT_KEY));
                //发送企业微信告警
            }else{
                this.sendHeartBeat(param);
            }
        }catch (Exception e){
            LOGGER.error("featureDepLevVerJob,exception",e);
            return ReturnT.SUCCESS;
        }
        return ReturnT.SUCCESS;
    }

    private void sendHeartBeat(String param) {
        int heartBeatWindow=Integer.parseInt(param);
        int currentHour=new Date().getHours();
        if(currentHour%heartBeatWindow==0){
            weChatService.sendTextMsg(NORMAL_FEATURE_DESC,ROBOT_KEY);
        }
    }

    private void judgeFeatureLevel(AdminFeatureSubmitDetail currentFeature, StringBuilder errorDesc, String step, Set<String> checkedFeature,List<String> listConfig){
        String key=currentFeature.getFeatureName()+UNDERLINE+step;
        if(checkedFeature.contains(key)){
            return;
        }
        checkedFeature.add(key);
        List<Long> featureList = adminFeatureDependPropertyMapper.getDrDependFeatureList(Arrays.asList(currentFeature.getId()), "HAO_HUAN", step);
        if(featureList.isEmpty()){
            return;
        }
        List<AdminFeatureSubmitDetail> dependCurrentFeature=featureSubmitMapper.getFeatureSubmitDetailByIdList(featureList);
        for(int i=0;i<dependCurrentFeature.size();i++){
            AdminFeatureSubmitDetail ele=dependCurrentFeature.get(i);
            if(ele.getStepLevel()<=currentFeature.getStepLevel()  && !listConfig.contains(ele.getFeatureName()+UNDERLINE+step)){
                errorDesc.append(String.format(ERROR_FEATURE_DESC, currentFeature.getFeatureName(),currentFeature.getStepLevel(),ele.getFeatureName(),ele.getStepLevel(),step));
            }
            this.judgeFeatureLevel(ele,errorDesc,step, checkedFeature,listConfig);
        }
    }

    private List<StepInfo> getAllStep() {
        JSONObject result=JSONObject.parseObject(SyncHTTPRemoteAPI.get(cpHost+"/features/step/selectAllStep",30000));
        return JSONObject.parseArray(result.getJSONObject("data").getString("list"),StepInfo.class);
    }


    @Getter
    @Setter
    static class StepInfo {
        private Integer id;

        private Integer eventId;

        private String step;

        private Date createTime;

        private Date updateTime;
    }

    private List<String> splitMessage(String[] message, String title) {
        int length = 0;
        StringBuilder stringBuilder = new StringBuilder(title);
        List<String> list = new ArrayList<>();
        for (String mess : message) {
            length += mess.getBytes(Charset.forName("utf-8")).length;
            if (length > QIYWWEXIN_MAXLENGTH) {
                length = 0;
                list.add(stringBuilder.toString());
                stringBuilder = new StringBuilder(title);
            }
            stringBuilder.append(mess);
        }
        list.add(stringBuilder.toString());
        return list;
    }
    
}
