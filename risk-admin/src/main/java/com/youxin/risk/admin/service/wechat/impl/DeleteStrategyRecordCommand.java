package com.youxin.risk.admin.service.wechat.impl;

import com.youxin.risk.admin.dao.admin.StrategyTypeMapper;
import com.youxin.risk.admin.dao.rm.AdminStrategyCandidateMapper;
import com.youxin.risk.admin.dao.rm.AdminStrategyMapper;
import com.youxin.risk.admin.model.AdminStrategy;
import com.youxin.risk.admin.model.AdminStrategyCandidate;
import com.youxin.risk.admin.service.wechat.CommandInterface;
import com.youxin.risk.commons.model.StrategyTypeDO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.List;

import static com.youxin.risk.admin.qw.enums.CommandEnum.DELETE_STRATEGY_RECORD;

/**
 * @description: 删除策略记录
 */
@Component
public class DeleteStrategyRecordCommand implements CommandInterface {
    @Resource
    private AdminStrategyMapper adminStrategyMapper;
    @Autowired
    private StrategyTypeMapper strategyTypeMapper;
    @Resource
    private AdminStrategyCandidateMapper adminStrategyCandidateMapper;

    @Override
    public String execute(String content) {
        String[] parts = content.split(",");
        if (parts.length != 2) {
            return "无效的输入格式，应为: strategyType,id";
        }

        String strategyType = parts[0].trim();
        Long id;
        try {
            id = Long.parseLong(parts[1].trim());
        } catch (NumberFormatException e) {
            return "无效的策略记录id";
        }

        // 判断新老策略
        List<StrategyTypeDO> strategyTypeDOS = strategyTypeMapper.listByStrategyTypeCode(content);
        if (CollectionUtils.isEmpty(strategyTypeDOS)) {
            return oldStrategyRoll(id, strategyType);
        }else {
            return newStrategyRoll(id, strategyType);
        }
    }

    /**
     * 回滚新策略
     * @param id
     * @param strategyType
     * @return
     */
    private String newStrategyRoll(Long id, String strategyType) {
        AdminStrategyCandidate adminStrategyCandidate = adminStrategyCandidateMapper.get(id);
        if (adminStrategyCandidate == null) {
            return "未找到策略记录";
        }
        AdminStrategyCandidate adminOnlineStrategyCandidate = adminStrategyCandidateMapper.selectOnlineByType(strategyType);
        if (adminOnlineStrategyCandidate == null) {
            return "未找到最新上线的策略记录";
        }
        // type=API_LEND_SD_20240617203402
        String type = adminOnlineStrategyCandidate.getType();
        String[] split = type.split("_20");
        type = split[0];
        if (!type.equals(strategyType)) {
            return "策略类型不匹配";
        }
        if (id > adminOnlineStrategyCandidate.getId()) {
            return "您传的id大于最新上线策略记录id，推测您已经回滚了最新记录，请仔细核对！";
        }
        if (id < adminOnlineStrategyCandidate.getId()) {
            return "您传的id小于最新上线策略记录id，无法回滚!";
        }
        int i = adminStrategyCandidateMapper.updateStatusRoll(id);
        if (i > 0) {
            return String.format("策略记录回滚成功，id:%s", id);
        } else {
            return String.format("策略记录回滚失败，id:%s", id);
        }
    }

    /**
     * 回滚老策略
     * @param id
     * @param strategyType
     * @return
     */
    private String oldStrategyRoll(Long id, String strategyType) {
        AdminStrategy curStrategy = adminStrategyMapper.get(id);
        if (curStrategy == null) {
            return "未找到策略记录";
        }

        AdminStrategy latestStrategy = adminStrategyMapper.selectLatestStrategy(strategyType);
        if (latestStrategy == null) {
            return "未找到最新策略记录";
        }

        if (!curStrategy.getType().equals(strategyType)) {
            return "策略类型不匹配";
        }

        if (id > latestStrategy.getId()) {
            return "您传的id大于最新策略记录id，推测您已经删除了最新记录，请仔细核对！";
        }
        if (id < latestStrategy.getId()) {
            return "您传的id小于最新策略记录id，无法删除!";
        }

        int result = adminStrategyMapper.deleteByType(strategyType, id);
        if (result > 0) {
            return String.format("策略记录删除成功，id:%s", id);
        } else {
            return String.format("策略记录删除失败，id:%s", id);
        }
    }

    @Override
    public String supportCommand() {
        return DELETE_STRATEGY_RECORD.getCommand();
    }
}