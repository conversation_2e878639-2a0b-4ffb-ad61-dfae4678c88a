package com.youxin.risk.admin.model;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @description: 网关请求表的复制表，该表只包含GwRequest部分字段，用作监控用
 * @author: juxiang
 * @create: 2021-09-01 17:19
 **/
@Getter
@Setter
public class GwMiniRequest {
    /**
     * 主键自增id
     */
    private Long id;
    /**
     * 查询状态，未被检索过1,检索过2
     */
    private Integer queryStatus;

    private Date createTime;

    private String eventCode;

    private String sessionId;

}
