package com.youxin.risk.admin.dto.rulescore;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class RuleScoreDTO {
    private Long id;
    private String ruleKey;
    private String ruleName;
    private String ruleRunCode;
    /** 当时线上规则版本 **/
    private Integer onlineRuleVersion;
    private String remark;
    /**
     * 依赖的规则分: 每次保存都会全量传过来
     */
    private List<String> ruleScoreRelations = new ArrayList<>();

    public static void main(String[] args) {
        RuleScoreDTO ruleScoreDTO = new RuleScoreDTO();
        ruleScoreDTO.setRuleKey("ruleKey1");
        ruleScoreDTO.setRuleName("ruleName1");
        ruleScoreDTO.setRemark("remark");
        ruleScoreDTO.setRuleRunCode("result={}\n" +
                "\ttry:\n" +
                "\t    result['dq_cus36_period_proba'] = x.get('mx_dq_cus36_period_proba')\n" +
                "\t    result['dq_cus36_period_scorecut'] = x.get('mx_dq_cus36_period_scorecut')\n" +
                "\texcept:\n" +
                "\t    pass");
        ruleScoreDTO.setRuleScoreRelations(new ArrayList<>());
        System.out.println(JSON.toJSONString(ruleScoreDTO));
    }
}

