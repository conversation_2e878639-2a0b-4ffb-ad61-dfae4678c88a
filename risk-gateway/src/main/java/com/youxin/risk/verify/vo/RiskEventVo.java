/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.verify.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * RiskEventVo
 */
public class RiskEventVo implements Serializable, Cloneable{

	private static final long serialVersionUID = 7698961859494635329L;

	// 系统字段
	private Map<String, Object> params = new ConcurrentHashMap<>(); //参数

	private Map<String, Object> dataVo = new HashMap<>(); //原始数据集合

	private String xml = ""; //feature

	private Map<String, Object> verifyResult; //策略结果

	public Map<String, Object> getParams() {
		return params;
	}

	public void setParams(Map<String, Object> params) {
		this.params = params;
	}

	public Map<String, Object> getDataVo() {
		return dataVo;
	}

	public void setDataVo(Map<String, Object> dataVo) {
		this.dataVo = dataVo;
	}

	public String getXml() {
		return xml;
	}

	public void setXml(String xml) {
		this.xml = xml;
	}

	public Map<String, Object> getVerifyResult() {
		return verifyResult;
	}

	public void setVerifyResult(Map<String, Object> verifyResult) {
		this.verifyResult = verifyResult;
	}
}
