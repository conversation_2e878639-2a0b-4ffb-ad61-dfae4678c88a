/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.verify.vo;

import java.util.Map;

/**
 * hamal sunxin
 * <AUTHOR>
 */
public class VerifyLibraryVo {

    private String command; //请求类别，in、out

    private Map<String, Object> params; //参数

    private String policyCode; //策略编码,用于匹配名单

	public String getCommand() {
		return command;
	}

	public void setCommand(String command) {
		this.command = command;
	}

	public Map<String, Object> getParams() {
		return params;
	}

	public void setParams(Map<String, Object> params) {
		this.params = params;
	}

	public String getPolicyCode() {
		return policyCode;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}
}
