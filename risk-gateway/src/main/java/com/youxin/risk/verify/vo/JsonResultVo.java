package com.youxin.risk.verify.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class JsonResultVo implements Serializable {

    private static final long serialVersionUID = -3682737772692845415L;

    /** 成功状态码 */
    public static final int SUCCESS = 0;
    /** 失败状态码 */
    public static final int ERROR = -1;

    /** 状态码 */
    private int status = SUCCESS;
    /** 消息 */
    private String message;
    /** 数据集 */
    private Map<String, Object> data = new HashMap<String, Object>();

    /**
     * 成功结果
     * 
     * @param message
     * @return
     */
    public static JsonResultVo success(String message) {
        JsonResultVo vo = new JsonResultVo();
        vo.setStatus(SUCCESS);
        vo.setMessage(message);
        return vo;
    }

    /**
     * 成功结果
     * 
     * @return
     */
    public static JsonResultVo success() {
        return success("");
    }

    /**
     * 失败结果
     * 
     * @return
     */
    public static JsonResultVo error() {
        JsonResultVo vo = new JsonResultVo();
        vo.setStatus(-1);
        vo.setMessage("服务器异常");
        return vo;
    }

    /**
     * 失败结果
     * 
     * @param status
     * @param message
     * @return
     */
    public static JsonResultVo error(int status, String message) {
        JsonResultVo vo = new JsonResultVo();
        vo.setStatus(status);
        vo.setMessage(message);
        return vo;
    }

    /**
     * 失败结果
     * 
     * @param detailCodeVo
     * @return
     */
    /*
     * private static JsonResultVo error(DetailCodeVo detailCodeVo) {
     * if (detailCodeVo == null) {
     * return JsonResultVo.error(ERROR, "此返回信息未配置编码，请配置");
     * }
     * JsonResultVo json = new JsonResultVo();
     * json.setMessage(detailCodeVo.getDescript());
     * try {
     * json.setStatus(Integer.parseInt(detailCodeVo.getCode()));
     * } catch (Exception e) {
     * return JsonResultVo.error("错误码转换异常");
     * }
     * return json;
     * }
     */

    /**
     * 失败结果
     * 
     * @param code
     * @param paras
     * @return
     */
    /*
     * public static JsonResultVo error(String code, String... paras) {
     * return error(CodeLocalCache.getByCode(code, paras));
     * }
     */

    /**
     * 增加一个值
     * 
     * @param key
     * @param value
     * @return
     */
    public JsonResultVo addData(String key, Object value) {
        data.put(key, value);
        return this;
    }

    /**
     * 增加一个对象
     * 
     * @param key
     * @param name
     * @param version
     * @param value
     * @return
     */
    /*
     * public JsonResultVo addObjectData(String key, String name, String
     * version, Object value) {
     * data.put(key, MappingConverter.convertObject(name, version, value));
     * return this;
     * }
     */

    /**
     * 增加一个列表
     * 
     * @param key
     * @param name
     * @param version
     * @param list
     * @return
     */
    /*
     * public JsonResultVo addListData(String key, String name, String version,
     * List<?> list) {
     * data.put(key, MappingConverter.convertList(name, version, list));
     * return this;
     * }
     */

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }

}