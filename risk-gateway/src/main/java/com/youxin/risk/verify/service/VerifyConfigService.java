package com.youxin.risk.verify.service;

import com.youxin.risk.commons.model.verify.VerifyConfig;

import java.util.List;
import java.util.Map;

public interface VerifyConfigService {

    /**
     * 持久化
     * 
     * @param entity
     */
    public void persist(VerifyConfig entity);

    /**
     * 修改
     * 
     * @param key
     * @param value
     * @param operator
     * @return
     */
    public VerifyConfig update(String key, String value, Integer operator);

    /**
     * 按键查找配置信息
     * 
     * @param key
     * @return
     */
    public VerifyConfig getByKey(String key);

    /**
     * 按键查找配置信息值
     * 
     * @param key
     * @return
     */
    public String getValueByKey(String key);

    /**
     * 查找全部列表
     * 
     * @return
     */
    public List<VerifyConfig> getAll();

    /**
     * 查找全部配置映射
     * 
     * @return
     */
    public Map<String, String> getAllMapping();
}
