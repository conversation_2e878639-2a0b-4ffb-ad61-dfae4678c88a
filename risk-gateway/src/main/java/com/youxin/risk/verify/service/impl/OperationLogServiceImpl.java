package com.youxin.risk.verify.service.impl;


import com.youxin.risk.commons.dao.datacenter.DcOperationLogMapper;
import com.youxin.risk.commons.model.datacenter.DcOperationLog;
import com.youxin.risk.commons.model.datacenter.common.VerifyCommonData;
import com.youxin.risk.commons.utils.ObjectTransferUtils;
import com.youxin.risk.verify.enums.OperationType;
import com.youxin.risk.verify.mapper.OperationLogMapper;
import com.youxin.risk.verify.model.OperationLog;
import com.youxin.risk.verify.service.OperationLogService;
import com.youxin.risk.verify.vo.OperationLogVo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.URLDecoder;
import java.util.List;

@Service
@Transactional
public class OperationLogServiceImpl implements OperationLogService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OperationLogServiceImpl.class);

    @Autowired
    private OperationLogMapper operationLogMapper;


    @Autowired
    private DcOperationLogMapper dcOperationLogMapper;

    @Override
    public Integer saveOperationLog(OperationLogVo opLogVo) {
        OperationLog opLog;
        try {
            opLog = opLogVo.toOperationLog();
            opLog.setVersion(0);
            this.operationLogMapper.persist(opLog);
            return opLog.getId();
        } catch (InstantiationException | IllegalAccessException
                | SecurityException e) {
            return null;
        }

    }

    @Override
    public OperationLogVo getOpLogVoFromInput(VerifyCommonData commonVo,
                                              OperationType opType) {
        OperationLogVo opLogVo = new OperationLogVo();
        opLogVo.setAppVersion(commonVo.getAppVersion());
        opLogVo.setChannelCode(commonVo.getChannelCode());
        opLogVo.setDeviceId(commonVo.getDeviceId());
        opLogVo.setIp(commonVo.getIp());
        opLogVo.setOperationType(opType.name());
        opLogVo.setMobileModel(handleMobileModel(commonVo));
        opLogVo.setOsVersion(commonVo.getOsVersion());
        opLogVo.setPlatform(commonVo.getPlatform());
        opLogVo.setSourceSystem(commonVo.getSourceSystem());
        opLogVo.setUserKey(commonVo.getUserKey());
        opLogVo.setChannel(commonVo.getChannel());
        opLogVo.setDevice(commonVo.getDevice());
        opLogVo.setJailBroken(commonVo.getJailBroken());
        opLogVo.setIsCopyPackage(commonVo.getIsCopyPackage());
        return opLogVo;
    }


    /**
     * 超过32位截取
     *
     * @param commonVo
     * @return
     */
    private String handleMobileModel(VerifyCommonData commonVo) {
        String mobileModel = commonVo.getMobileModel();
        if (StringUtils.isBlank(mobileModel) || mobileModel.length() <= 32) {
            return mobileModel;
        }
        try {
            //通常URLencode造成的超长，尝试decode
            mobileModel = URLDecoder.decode(mobileModel, "UTF-8");
        } catch (Exception e) {
            LOGGER.warn("decode error for mobileModel={},userKey={}", mobileModel, commonVo.getUserKey(), e);
        }

        if (mobileModel.length() > 32) {
            mobileModel = mobileModel.substring(0, 32);
        }
        commonVo.setMobileModel(mobileModel);

        return mobileModel;
    }

    @Override
    public OperationLogVo getLastOperationLogByUserKeyAndType(String userKey, String sourceSystem, OperationType type)
            throws InstantiationException, IllegalAccessException,
            SecurityException {
        OperationLog log = this.operationLogMapper
                .findLastOperationLogByUserAndOperationType(userKey, sourceSystem,
                        type.name());
        OperationLogVo logVo = ObjectTransferUtils.transferObject(log,
                OperationLogVo.class);
        return logVo;
    }

    @Override
    public OperationLogVo getLastOperationLogByUserKeyAndTypeOld(String userKey, String sourceSystem, OperationType type)
            throws InstantiationException, IllegalAccessException,
            SecurityException {
        OperationLog log = this.operationLogMapper
                .findLastOperationLogByUserAndOperationTypeOld(userKey, sourceSystem,
                        type.name());
        OperationLogVo logVo = ObjectTransferUtils.transferObject(log,
                OperationLogVo.class);
        return logVo;
    }


    @Override
    public List<OperationLog> getOperationLogListAfterId(Integer logId,
                                                         String userKey) {
        return this.operationLogMapper.findOperationLogListAfterId(logId, userKey);
    }

    @Override
    public DcOperationLog findLastOperationLogByUserAndOperationType(String userKey, String sourceSystem, String operationType) {
        try {
            return dcOperationLogMapper.findLastOperationLogByUserAndOperationType(userKey, sourceSystem, operationType);
        } catch (Exception ex) {
            LOGGER.error("获取dcOperationLog异常, userKey: {}, sourceSystem: {}, type: {}", userKey, sourceSystem, operationType, ex);
            return null;
        }
    }

}