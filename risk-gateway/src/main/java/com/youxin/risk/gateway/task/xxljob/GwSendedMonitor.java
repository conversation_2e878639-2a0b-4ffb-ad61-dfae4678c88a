package com.youxin.risk.gateway.task.xxljob;

import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.youxin.risk.commons.constants.PointConstant;
import com.youxin.risk.commons.xxl.job.XxlJobBase;
import com.youxin.risk.gateway.contants.GwRequestStatus;
import com.youxin.risk.gateway.service.GwRequestModelService;
import com.youxin.risk.metrics.MetricsAPI;
import com.youxin.risk.metrics.enums.MetricsOpType;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/8/12 10:33
 * @desc
 */
@Component
public class GwSendedMonitor implements XxlJobBase {
    private final static Logger LOGGER = LoggerFactory.getLogger(GwSendedMonitor.class);

    @Autowired
    private GwRequestModelService gwRequestModelService;

    @XxlJob(value = "gatewaySendedJob")
    @Override
    public ReturnT<String> execJobHandler(String param) {
        LOGGER.info("gatewaySendedJob start,param={}", param);
        Map<String, Long> eventCodeAndCountMap =
                gwRequestModelService.countGroupByEventCode(GwRequestStatus.SENDED.name());
        LOGGER.info("gatewaySendedJob eventCodeAndCountMap={}", JSON.toJSONString(eventCodeAndCountMap));
        if (MapUtils.isEmpty(eventCodeAndCountMap)) {
            return ReturnT.SUCCESS;
        }

        //打点
        for (Map.Entry<String, Long> entry : eventCodeAndCountMap.entrySet()) {
            Map<String, String> tags = new HashMap<>(2);
            tags.put("eventCode", entry.getKey());
            Map<String, Number> values = new HashMap<>(2);
            values.put("count", entry.getValue());
            MetricsAPI.point(PointConstant.GW_POINT_SENDED_COUNT, tags, values, false, MetricsOpType.count);
        }

        LOGGER.info("gatewaySendedJob end");
        return ReturnT.SUCCESS;
    }
}
