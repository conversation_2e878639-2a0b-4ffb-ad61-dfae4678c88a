package com.youxin.risk.gateway.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.exception.RiskRuntimeException;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.JsonKeyFormatUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Service
@RequiredArgsConstructor
public class EventProcessorService {
    private static final Logger LOGGER = LoggerFactory.getLogger(EventProcessorService.class);

    private static final int STATUS_SUCCESS = 0;
    private static final int TIMEOUT_MILLI_SECOND = 30 * 60 * 1000;

    @Value("${risk.quota.url}")
    private String riskQuotaUrl;

    @Value("${risk.service.url.engine}")
    private String engineServiceUrl;

    /**
     * 处理事件并根据需要更新策略结果信息
     * todo 所有额度事件都处理，不区分卡单是否是因为额度超过20w；
     * @param event
     * @throws RiskRuntimeException 如果流程处理失败
     */
    public void processEvent(Event event) {
        if (!isValidEvent(event)) {
            return;
        }

        if (!isQuotaEvent(event)) {
            LoggerProxy.warn("processEvent", LOGGER,
                    "不是额度事件，不需要处理, loanKey={}, userKey={}, eventCode={}",
                    event.getLoanKey(), event.getUserKey(), event.getEventCode());
            return;
        }

        try {
            JSONObject quotaLineResult = fetchUserQuota(event.getUserKey());
            JSONObject newVerifyResult = buildNewLineResult(event.getVerifyResult(), quotaLineResult);

            // 添加下划线key
            newVerifyResult = JsonKeyFormatUtil.mergeResults(newVerifyResult
                    , JsonKeyFormatUtil.convertToCamelCase(JSON.parseObject(JSON.toJSONString(newVerifyResult))));
            event.setVerifyResult(newVerifyResult);
            updateEvent(event);
            LoggerProxy.info("processEvent", LOGGER,
                    "userKey={}, loanKey={}, eventCode={}, processEvent success",
                    event.getUserKey(), event.getLoanKey(), event.getEventCode());
        } catch (Exception e) {
            LoggerProxy.error("processEvent", LOGGER,
                    "userKey={}, loanKey={}, eventCode={}, processEvent failed:{}",
                    event.getUserKey(), event.getLoanKey(), event.getEventCode(), e.getMessage());
            throw new RiskRuntimeException(RetCodeEnum.FAILED, "终止卡单处理：获取当前用户最新额度数据失败！");
        }
    }

    private boolean isValidEvent(Event event) {
        return event != null && event.getVerifyResult() != null;
    }

    private boolean isQuotaEvent(Event event) {
        if (!event.getVerifyResult().containsKey("reasonCode")) {
            return false;
        }

        Object reasonCode = event.getVerifyResult().get("reasonCode");
        JSONObject reasonCodeJson = JSON.parseObject(JSONObject.toJSONString(reasonCode));
        return (reasonCodeJson.containsKey("quotaInfo")
                        || reasonCodeJson.containsKey("quota_info"));
    }

    private JSONObject fetchUserQuota(String userKey) {
        Map<String, String> params = new HashMap<>();
        params.put("userKey", userKey);
        String response = sendHttpRequest(riskQuotaUrl + "/quota/api/quota/queryCallBackQuotaMessage", params);

        JSONObject responseJson = JSONObject.parseObject(response);
        validateResponse(responseJson);

        return responseJson.getJSONObject("data");
    }

    private void validateResponse(JSONObject response) {
        if (response.getInteger("status") != STATUS_SUCCESS) {
            throw new RiskRuntimeException(RetCodeEnum.FAILED, response.getString("message"));
        }
    }

    public void updateEvent(Event event) {
        sendHttpRequest(engineServiceUrl + "/updateEvent", event);
    }

    public void sendKafkaEvent(Event event) {
        sendHttpRequest(engineServiceUrl + "/engine_helper/sendKafkaEvent", event);
    }

    public String sendHttpRequest(String url, Object requestObject) {
        return SyncHTTPRemoteAPI.postJson(url, JSONObject.toJSONString(requestObject), TIMEOUT_MILLI_SECOND);
    }

    @SuppressWarnings("unchecked")
    public JSONObject buildNewLineResult(Object originalLineResult, JSONObject processedLineResult) {
        Map<String, Object> originalMap = JSON.parseObject(
                JSON.toJSONString(originalLineResult),
                new TypeReference<Map<String, Object>>() {}
        );
        Map<String, Object> processedMap = JSON.parseObject(
                JSON.toJSONString(processedLineResult),
                new TypeReference<Map<String, Object>>() {}
        );

        Map<String, Object> newLineResultMap = new HashMap<>(originalMap);
        updateBasicQuotaInfo(newLineResultMap, processedMap);
        updateReasonCode(newLineResultMap, processedMap);
        updatePeriodLineRates(newLineResultMap, processedMap);


        return new JSONObject(newLineResultMap);
    }

    private void updateBasicQuotaInfo(Map<String, Object> newLineResultMap, Map<String, Object> processedMap) {
        newLineResultMap.put("creditLine", processedMap.get("totalAmount"));
        newLineResultMap.put("availLine", processedMap.get("availAmount"));
        newLineResultMap.put("utilLine", processedMap.get("usedAmount"));
        newLineResultMap.put("loanLine", processedMap.get("totalAmount"));
        newLineResultMap.put("loanAvailLine", processedMap.get("availAmount"));
        newLineResultMap.put("loanActualLine", processedMap.get("actualAmount"));
        newLineResultMap.put("loanUtilLine", processedMap.get("usedAmount"));
        newLineResultMap.put("loanRate", processedMap.get("loanRate"));
        newLineResultMap.put("accountStatus", processedMap.get("accountStatus"));
        newLineResultMap.put("lineAssignTime", processedMap.get("lineAssignTime"));
        newLineResultMap.put("ext1", JSON.toJSONString(processedMap.get("ext1")));
        newLineResultMap.put("loanPeriod", processedMap.get("loanPeriod"));
    }

    @SuppressWarnings("unchecked")
    private void updateReasonCode(Map<String, Object> newLineResultMap, Map<String, Object> processedMap) {
        Map<String, Object> reasonCode = (Map<String, Object>) newLineResultMap.get("reasonCode");
        Map<String, Object> touchCode = (Map<String, Object>) processedMap.get("touchCode");

        Map<String, Object> newReasonCode = new HashMap<>();
        if(null != touchCode){
            for (Map.Entry<String, Object> entry : touchCode.entrySet()) {
                Map<String, Object> newEntry = new HashMap<>();
                newEntry.put("tag", entry.getValue());
                newReasonCode.put(entry.getKey(), newEntry);
            }
        }
        reasonCode.put("events", newReasonCode);
    }

    @SuppressWarnings("unchecked")
    private void updatePeriodLineRates(Map<String, Object> newLineResultMap, Map<String, Object> processedMap) {
        List<Map<String, Object>> periodLineRates = (List<Map<String, Object>>) processedMap.get("periodLineRates");
        if (CollectionUtils.isEmpty(periodLineRates)) {
            newLineResultMap.put("periodLineRate", "{}");
            return;
        }

        List<Map<String, Object>> newPeriodLineRates = new ArrayList<>();
        for (Map<String, Object> rate : periodLineRates) {
            Map<String, Object> newRate = createPeriodLineRate(rate);
            newPeriodLineRates.add(newRate);
        }
        newLineResultMap.put("periodLineRate", JSON.toJSONString(newPeriodLineRates));

        // apiPeriodLineRate返回给业务方字段需要转换成字符串，后续使用额度中心返回的apiPeriodLineRate
        Object apiPeriodLineRateObj = newLineResultMap.get("apiPeriodLineRate");
        if(null != apiPeriodLineRateObj){
            if (apiPeriodLineRateObj instanceof JSONArray) {
                JSONArray apiPeriodLineRate = (JSONArray) apiPeriodLineRateObj;
                newLineResultMap.put("apiPeriodLineRate", JSON.toJSONString(apiPeriodLineRate));
            }
        }
    }

    private Map<String, Object> createPeriodLineRate(Map<String, Object> rate) {
        Map<String, Object> newRate = new HashMap<>();
        newRate.put("min", rate.get("min"));
        newRate.put("max", rate.get("max"));
        newRate.put("period", rate.get("period"));
        newRate.put("rate", rate.get("rate"));
        newRate.put("type", "loan");

        Optional.ofNullable(rate.get("tmp_rate_end_time"))
                .ifPresent(endTime -> newRate.put("tmp_rate_end_time", endTime));
        Optional.ofNullable(rate.get("tmp_rate"))
                .ifPresent(tmpRate -> newRate.put("tmp_rate", tmpRate));

        return newRate;
    }
}