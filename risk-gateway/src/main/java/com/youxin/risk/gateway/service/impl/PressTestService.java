package com.youxin.risk.gateway.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.dao.gw.GwRequestMapper;
import com.youxin.risk.commons.model.GwRequest;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.NacosClientAdapter;
import com.youxin.risk.commons.utils.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> little blacksmith
 * @Description TODO
 * @Date 2021/5/7 15:30
 * @Version 1.0
 */
@Service
public class PressTestService {

    private Logger logger = LoggerFactory.getLogger(PressTestService.class);

    @Autowired
    private GwRequestMapper gwRequestMapper;

    protected BlockingQueue<String> pressBlockingQueue = new LinkedBlockingQueue<>(2000);

    protected BlockingQueue<String> queryBlockingQueue = new LinkedBlockingQueue<>(5000);

    @Value("${gw.service.url}")
    private String gwServiceUrl;

    private Executor pressThreadpool = new ThreadPoolExecutor(3, 8,
            3000L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<Runnable>(5000),
            Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static Map<String,String> header = Maps.newHashMap();

    static {
        header.put("Content-Type","application/json");
    }

    public void pressTest(String eventCode){
        Thread queryThread = new Thread(){
            @Override
            public void run() {
                queryData();
            }
        };
        queryThread.start();
        Thread exchangeThread = new Thread(){
            @Override
            public void run() {
                exchange(eventCode);
            }
        };
        exchangeThread.start();
        doPress(eventCode);
    }

    private void exchange(String eventCode){
        logger.info("pressTestExchangeStart eventCode={}",eventCode);
        for(;;){
            try{
                boolean pressTestSwitch = NacosClientAdapter.getBooleanConfig("pressTestSwitch",false);
                if(!pressTestSwitch){
                    logger.info("pressTestExchange break eventCode={}",eventCode);
                    break;
                }
                String userKey = queryBlockingQueue.take();
                Thread.sleep(getDelay(eventCode));
                pressBlockingQueue.put(userKey);
            }catch (Exception e){
                logger.error(e.getMessage(),e);
            }
        }
    }

    private void doPress(String eventCode){
        logger.info("pressTestSendRequestStart eventCode={}",eventCode);
        for(;;){
            try {
                boolean pressTestSwitch =  NacosClientAdapter.getBooleanConfig("pressTestSwitch",false);
                if(!pressTestSwitch){
                    logger.info("doPress break eventCode={}",eventCode);
                    break;
                }
                String userKey = pressBlockingQueue.take();
                pressThreadpool.execute(new Runnable() {
                    @Override
                    public void run() {
                        sendRequest(userKey,eventCode);
                    }
                });
            } catch (Exception e) {
                logger.error(e.getMessage(),e);
            }
        }
    }

    private void queryData(){
        logger.info("pressTestQueryDataStart...");
        long startId = 0;
        for(;;){
            boolean pressTestSwitch = NacosClientAdapter.getBooleanConfig("pressTestSwitch",false);
            if(!pressTestSwitch){
                logger.info("queryData break");
                break;
            }
            List<GwRequest> requestList = gwRequestMapper.selectAfterId(startId, 10000);
            if(CollectionUtils.isEmpty(requestList)){
                continue;
            }
            logger.info("query test record size={}",requestList.size());
            for (GwRequest gwRequest : requestList) {
                try {
                    String userKey = analysisUserKey(gwRequest.getRequestMessage());
                    queryBlockingQueue.put(userKey);
                } catch (Exception e) {
                    logger.error(e.getMessage(),e);
                    continue;
                }
            }
            startId= requestList.get(requestList.size()-1).getId();
        }
    }

    private static String analysisUserKey(String request){
        JSONObject requestObject = JSONObject.parseObject(request);
        return  requestObject.getJSONObject("message").getString("userKey");
    }

    private static String buildPreLoanAuditRequestBody(String userKey,String eventCode){
        JSONObject request = new JSONObject();
        request.put("icode","0100010105");
        JSONObject data = new JSONObject();
        String requestId = UUID.randomUUID().toString();
        data.put("requestId",requestId);
        data.put("type","async");
        JSONObject message = new JSONObject();
        message.put("eventCode",eventCode);
        message.put("sourceSystem","HAO_HUAN");
        message.put("transId",requestId);
        message.put("userKey",userKey);
        data.put("message",message);
        request.put("data",data);
        return request.toJSONString();
    }

    private void sendRequest(String userKey,String eventCode){
        logger.info("sendPressTestRequest userKey={},eventCode={}", userKey,eventCode);
        String requestBody = buildPreLoanAuditRequestBody(userKey,eventCode);
        String response = SyncHTTPRemoteAPI.postJson(gwServiceUrl, requestBody, header, 2000);
        logger.info("press response={}", response);
    }

    private Long getDelay(String eventCode) {
        Long delay = 50L;
        try {
            Map<String,String> delayTimeMap = NacosClientAdapter.getMapConfig("pressTestEventDelay", String.class);
            String delayTime = delayTimeMap.get(eventCode);
            if (StringUtils.isNotEmpty(delayTime)) {
                delay = Long.valueOf(delayTime);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
        }
        return delay;
    }
}
