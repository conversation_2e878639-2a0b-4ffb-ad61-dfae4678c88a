package com.youxin.risk.gateway.service;

import com.youxin.risk.commons.model.GwRequest;
import com.youxin.risk.gateway.vo.GatewayVo;

import java.util.List;
import java.util.Map;

public interface GwRequestModelService {

    int insert(GatewayVo gatewayVo);

    List<GwRequest> selectLockTimeout(int size);

    List<GwRequest> selectWaitCallback(int size);

    int lockWaitCallback(long id);

    GwRequest select(GwRequest model);

    List<GwRequest> selectDelete(int retainDay,int limit);

    int deleteByIds(List<GwRequest> items);

    int deleteRequestMessageBySessionIdList(List<String> sessionIdList);

    int updateCallbackMsg(String callbackMsg, String sessionId);

    int updateCallbacked(String sessionId);

    int unlock(long id);

    List<GwRequest> selectNotCallBackBySids(List<String> items);

    List<GwRequest> selectShortIntervalCallback(int size);

    List<GwRequest> selectMiddleIntervalCallback(int size);

    List<GwRequest> selectLongIntervalCallback(int size);

    List<GwRequest> selectLongCostInFinishCallback(int size);

    int updateSended(String sessionId);

    int queryCountByEventAndStatus(String eventCode,String status,int limit);

    /**
     * 根据eventCode分组计算数量
     * @return
     */
    Map<String, Long> countGroupByEventCode(String status);

    boolean exitingByLoanKey(String loanKey);

    boolean eventStatusIsFinal(String loanKey);

}
