package com.youxin.risk.gateway.test;

import com.youxin.risk.verify.enums.SubmitDataType;
import com.youxin.risk.verify.service.SubmitService;
import com.youxin.risk.verify.vo.VerifySubmitVo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class SubmitServiceTest {

    @Autowired
    private SubmitService submitService;

    @Test
    public void submitInformationToDc(){
        VerifySubmitVo verifySubmitVo = new VerifySubmitVo();
        verifySubmitVo.setDeviceName("deviceName");
        submitService.submitInformationToDc(verifySubmitVo, SubmitDataType.VERIFY_SUBMIT, "sliuchag87ad098adga");
    }
}
