#关闭metrics
metrics.stop=${metrics.stop}
# 命名空间(对应数据库)，长度不能大于32，只支持数字、字母、下划线，必输项
metrics.namespace=antifraud_risk_dc
# redis,kafka，必输项
metrics.remote.queue=redis
metrics.remote.redis.is.cluster=1
#127.0.0.1:8379，必输项
metrics.remote.queue.server=${metrics.remote.queue.server}
metrics.remote.queue.redis.password=${metrics.remote.queue.redis.password}

metrics.point.kafka.hosts=${metrics.point.kafka.hosts}
metrics.point.kafka.topic=${metrics.point.kafka.topic}
metrics.point.kafka.group.id=${metrics.point.kafka.group.id}
metrics.point.mirror.kafka.hosts=${metrics.point.mirror.kafka.hosts}

metric.point.sender.type=com.youxin.risk.metrics.sender.impl.KafkaSender