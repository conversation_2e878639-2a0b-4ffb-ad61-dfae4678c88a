/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.driver.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.youxin.risk.commons.model.creditDriver.CdJobRecord;
import com.youxin.risk.commons.mongo.OfflineBatchMongoDao;
import com.youxin.risk.driver.common.CdConstant;
import com.youxin.risk.driver.interfaces.DataLoader;
import com.youxin.risk.driver.model.JobContext;

/**
 * 查mongo获取手动额度的userkey
 */
@Service()
public class HfqManualAmountDataLoader implements DataLoader {

    @Autowired
    private OfflineBatchMongoDao offlineBatchMongoDao;

    protected Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public JobContext loadData(Object param) {
        JobContext jobContext = new JobContext();
        CdJobRecord cdJobRecord = (CdJobRecord) param;
        jobContext.setCdJobRecordParam(cdJobRecord);
        jobContext.setLoadedData(this.offlineBatchMongoDao.getUndealBatch(CdConstant.OFF_LINE_PROCESSID_MANUAL,null));

        return jobContext;
    }
}
