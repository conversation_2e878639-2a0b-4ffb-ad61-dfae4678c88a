package com.youxin.risk.driver.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.youxin.risk.commons.cache.CacheApi;
import com.youxin.risk.commons.tools.redis.RetryableJedis;
import com.youxin.risk.commons.utils.ContextUtil;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.driver.service.concurrent.BatchDataProcesser;
import com.youxin.risk.driver.service.impl.BatchJobServiceImpl;
import com.youxin.risk.driver.service.impl.StatServiceInvokeDataLoader;
import com.youxin.risk.driver.task.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.creditDriver.CdConfig;
import com.youxin.risk.commons.model.creditDriver.CdJobRecord;
import com.youxin.risk.commons.mongo.OfflineBatchRecordMongoDao;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.driver.interfaces.DataProcesser;
import com.youxin.risk.driver.service.CdConfigService;
import com.youxin.risk.driver.service.CdJobRecordService;

@Controller
public class ApiController {

    @Resource(name = "rrdCreditingJob")
    BaseCreditJob baseCreditJob;

    @Resource(name = "rrdCriditMidDataProcesser")
    DataProcesser rrdCriditMidDataProcesser;
    @Resource(name = "hfqLineMidPayOffDataProcesser")
    DataProcesser hfqLineMidPayOffDataProcesser;
    @Resource(name = "hfqTmpLineInvalidDataProcesser")
    DataProcesser hfqTmpLineInvalidDataProcesser;
    @Resource(name = "statServiceInvokeDataProcesser")
    DataProcesser statstatServiceInvokeDataProcesser;

    @Autowired
    CdJobRecordService cdJobRecordService;
    @Autowired
    CdConfigService cdConfigService;

    @Autowired
    HfqManualAmountJob hfqManualAmountJob;
    @Autowired
    HfqLineMiddleJob hfqLineMiddleJob;
    @Autowired
    HfqLineMiddleMangeJob hfqLineMiddleMangeJob;

    @Autowired
    private OfflineBatchRecordMongoDao offlineBatchRecordMongoDao;

    @Autowired
    HfqManualAmountAJob hfqManualAmountAJob;

    @Autowired
    private RetryableJedis retryableJedis;

    @Autowired
    private BatchJobServiceImpl batchJobService;

    protected final Logger logger = LoggerFactory.getLogger(getClass());


    @RequestMapping(value = "/startRrdCreditingJob")
    @ResponseBody
    public String startJob(@RequestBody JSONObject para) throws Exception {
        try {

            if (para.get("jobInstance") == null) {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        baseCreditJob.doJob();
                    }
                }).start();
            } else {//此处不判断重复手动触发任务，存在同一个任务实例多个线程同时跑的问题。此api只会在上线任务中断需要手动触发继续执行时调用
                CdJobRecord cdJobRecord = new CdJobRecord();
                cdJobRecord.setJobInstance(para.getString("jobInstance"));
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        baseCreditJob.excute(cdJobRecord);
                    }
                }).start();
            }
        } catch (Exception e) {
            LoggerProxy.error("manualStartJobError", logger, "request=" + para.toJSONString(), e);
        }
        return "jobStarted";
    }

    @RequestMapping(value = "/startManualAmountJob")
    @ResponseBody
    public String startManualAmountJob() throws Exception {
        try {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    hfqManualAmountJob.doJob();
                }
            }).start();
        } catch (Exception e) {
            LoggerProxy.error("startManualAmountJobError", logger, "startManualAmountJobError", e);
        }
        return "jobStarted";
    }

    @RequestMapping(value = "/startMiddleAmountJob")
    @ResponseBody
    public String startMiddleAmountJob() throws Exception {
        try {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    hfqLineMiddleJob.doJob();
                }
            }).start();
        } catch (Exception e) {
            LoggerProxy.error("startMiddleAmountJobError", logger, "startMiddleAmountJobError", e);
        }
        return "jobStarted";
    }

    @RequestMapping(value = "/risk/callback")
    @ResponseBody
    public String gatewayCallback() {
        LoggerProxy.info(logger, "gatewayCallbacked");
        return "{\"data\":{\"retCode\":\"S0000\"}}";
    }

    @RequestMapping(value = "/manul/runCreditingUser")
    @ResponseBody
    public String gatewayCallback(@RequestBody JSONObject params) {
        try {
            JSONArray uids = params.getJSONArray("uids");
            ArrayList<String> uidList = new ArrayList<>();
            if (uids.size() > 0) {
                for (Object item : uids) {
                    uidList.add((String) item);
                }
            }
            new Thread(new Runnable() {
                @Override
                public void run() {
                    rrdCriditMidDataProcesser.processData(uidList);
                }
            }).start();
            return "{\"data\":{\"retCode\":\"success\"}}";
        } catch (Exception e) {
            logger.error("manulRunCreditingUserError,params= " + params.toJSONString(), e);
            return "{\"data\":{\"retCode\":\"failed\"}}";
        }

    }

    @RequestMapping(value = "/manul/runHfqLineMidPayoffUser")
    @ResponseBody
    public String runHfqLineMidPayoffUser(@RequestBody JSONObject params) {
        try {
            JSONArray users = params.getJSONArray("users");
            List<Map> userList = new ArrayList<>();
            if (users.size() > 0) {
                for (Object item : users) {
                    userList.add((Map) item);
                }
            }
            new Thread(new Runnable() {
                @Override
                public void run() {
                    hfqLineMidPayOffDataProcesser.processData(userList);
                }
            }).start();
            return "{\"data\":{\"retCode\":\"success\"}}";
        } catch (Exception e) {
            logger.error("manulRunHfqLineMidPayoffUserError,params= " + params.toJSONString(), e);
            return "{\"data\":{\"retCode\":\"failed\"}}";
        }
    }

    @RequestMapping(value = "/manul/runHfqTmpLineInvalidUser")
    @ResponseBody
    public String runHfqTmpLineInvalidUser(@RequestBody JSONObject params) {
        try {
            JSONArray users = params.getJSONArray("users");
            List<Map> userList = new ArrayList<>();
            if (users.size() > 0) {
                for (Object item : users) {
                    userList.add((Map) item);
                }
            }
            new Thread(new Runnable() {
                @Override
                public void run() {
                    hfqTmpLineInvalidDataProcesser.processData(userList);
                }
            }).start();
            return "{\"data\":{\"retCode\":\"success\"}}";
        } catch (Exception e) {
            logger.error("manulRunHfqLineMidPayoffUserError,params= " + params.toJSONString(), e);
            return "{\"data\":{\"retCode\":\"failed\"}}";
        }
    }

    @RequestMapping(value = "/manul/insertOrUpdateJob")
    @ResponseBody
    public String modifyJob(@RequestBody String param) {
        try {
            CdJobRecord cdJobRecord = JSON.parseObject(param, CdJobRecord.class);
            int ret = cdJobRecordService.insertOrUpdateOnJobinstance(cdJobRecord);
            return "{\"data\":{\"retCode\":\"success\",\"ret\":" + ret + "}}";
        } catch (Exception e) {
            logger.error("manulModifyJobError,params= " + param, e);
            return "{\"data\":{\"retCode\":\"failed\"}}";
        }

    }

    @RequestMapping(value = "/manul/config")
    @ResponseBody
    public String configJob(@RequestBody String param) {
        try {
            CdConfig cdConfig = JSON.parseObject(param, CdConfig.class);
            cdConfigService.updateByPrimaryKeySelective(cdConfig);
            return "{\"data\":{\"retCode\":\"success\"}}";
        } catch (Exception e) {
            logger.error("manulModifyJobError,params= " + param, e);
            return "{\"data\":{\"retCode\":\"failed\"}}";
        }

    }

    @RequestMapping(value = "/startManualAmountAJob")
    @ResponseBody
    public String startManualAmountAJob() throws Exception {
        try {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    hfqManualAmountAJob.doJob();
                }
            }).start();
        } catch (Exception e) {
            LoggerProxy.error("startManualAmountAJobError", logger, "startManualAmountAJobError", e);
        }
        return "jobStarted";
    }

    @RequestMapping(value = "/manual/runStatServiceInvokeJob/{statDate}")
    @ResponseBody
    public String startStatServiceInvokeJob(@PathVariable String statDate) {
        try {
            if (StringUtils.isNotBlank(statDate)) {
                if (!StatServiceInvokeDataLoader.isCorrectFormat(statDate)) {
                    return "statDate format error";
                }
            } else {//get default value if statDate is blank
                statDate = StatServiceInvokeDataLoader.getStatDate();
            }
            final String calculateDate = statDate;
            new Thread(new Runnable() {
                @Override
                public void run() {
                    statstatServiceInvokeDataProcesser.processData(calculateDate);
                }
            }).start();
        } catch (Exception e) {
            LoggerProxy.error("startStatServiceInvokeJobError", logger, "startStatServiceInvokeJobError", e);
        }
        return "jobStarted";
    }

    @RequestMapping(value = "/startCDJob/{jobName}")
    @ResponseBody
    public String startTargetJob(@PathVariable String jobName) {
        try {
            if (StringUtils.isEmpty(jobName)) {
                return "jobNameNvl";
            }
            BaseCreditJob targetJob = (BaseCreditJob) ContextUtil.getBean(jobName);
            if (targetJob == null) {
                return "jobInstanceNvl";
            }
            new Thread(new Runnable() {
                @Override
                public void run() {
                    targetJob.doJob();
                }
            }).start();
        } catch (Exception e) {
            LoggerProxy.error("startManualAmountAJobError", logger, "startManualAmountAJobError", e);
        }
        return "jobStarted";
    }

    /**
     * 重试 失败的记录
     * @param jobName
     * @return
     */
    @RequestMapping(value = "/retryCDJob")
    @ResponseBody
    public String retryTargetJob(@RequestParam String jobName, @RequestParam Integer batchId) {
        try {
            if (StringUtils.isEmpty(jobName) || batchId == null) {
                return "jobName或batchId为空！";
            }
            BaseCreditJob targetJob = (BaseCreditJob) ContextUtil.getBean(jobName);
            if (targetJob == null) {
                return "根据jobName没有找到相应实例，请检查该jobName是否已配置！";
            }

            targetJob.getDataProcesser().retryHandleErrorRecords(batchId);
        } catch (Exception e) {
            LoggerProxy.error("重试异常", logger, "异常信息=", e);
        }
        return "重试结束";
    }

    @RequestMapping(value = "/getEventCodeByJobName")
    @ResponseBody
    public String getEventCodeByJobName(@RequestParam("jobName") String jobName) {
        String eventCode = "";
        try {
            LoggerProxy.info("getEventCodeByJobName", logger, "jobName={}", jobName);
            if (StringUtils.isEmpty(jobName)) {
                return eventCode;
            }
            BaseCreditJob targetJob = (BaseCreditJob) ContextUtil.getBean(jobName);
            if (targetJob == null) {
                LoggerProxy.info("getEventCodeByJobName", logger, "targetJob is null...");
                return eventCode;
            }
            eventCode = ((BatchDataProcesser) targetJob.getDataProcesser()).getEventCode();
        } catch (Exception e) {
            LoggerProxy.error("getEventCodeByJobName", logger, "getEventCodeByJobNameError=", e);
        }
        return eventCode;
    }

    @RequestMapping(value = "/stopCDJob/{processId}")
    @ResponseBody
    public String stopCDJob(@PathVariable String processId) {
        try {
            if (StringUtils.isEmpty(processId)) {
                return "processIdNvl";
            }
            batchJobService.stopJob(processId);
        } catch (Exception e) {
            LoggerProxy.error("stoptBatchJobError", logger, "stopBatchJobError", e);
        }
        return "jobStoped";
    }

    @RequestMapping(value = "/stopByBatchId/{batchId}")
    @ResponseBody
    public String stopByBatchId(@PathVariable String batchId) {
        try {
            if (StringUtils.isEmpty(batchId)) {
                return "batchId is null";
            }
            int batchIdInt = Integer.parseInt(batchId);
            batchJobService.stopJobByBatchId(batchIdInt);
        } catch (Exception e) {
            LoggerProxy.error("stopByBatchIdError", logger, "error msg=", e);
        }
        return "success";
    }

    @RequestMapping(value = "/startProcessCDJob/{processId}")
    @ResponseBody
    public String startProcessCDJob(@PathVariable String processId) {
        try {
            if (StringUtils.isEmpty(processId)) {
                return "processIdNvl";
            }
            batchJobService.startJob(processId);
        } catch (Exception e) {
            LoggerProxy.error("startProcessBatchJobError", logger, "startProcessBatchJobError", e);
        }
        return "jobStarted";
    }


    @RequestMapping(value = "/release/{lockKey}")
    @ResponseBody
    public String releaseLocak(@PathVariable String lockKey) {
        retryableJedis.del(lockKey);
        return "release success " + lockKey;
    }

    @RequestMapping(value = "/risk/sysconfig/value", method = {RequestMethod.GET})
    @ResponseBody
    public String getSysconfigValue(String key) {
        try {
            return CacheApi.getDictSysConfig(key);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return e.getMessage();
        }
    }
}
