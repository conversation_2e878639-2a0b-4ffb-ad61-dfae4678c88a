package com.youxin.risk.driver.task;

import com.youxin.risk.driver.model.CdConfigKeyEnum;
import com.youxin.risk.driver.model.JobNamespaceEnum;

/**
 * 三方接口信息同步统计job
 */
public class StatServiceSyncJob extends BaseCreditJob {


    @Override
    public String controlJobKey() {
        return CdConfigKeyEnum.StatServiceSyncJob_SwitchKey.name();
    }

    @Override
    public String getLockName() {
        return getJobNamespace() + controlJobKey();
    }

    @Override
    protected String getJobNamespace() {
        return JobNamespaceEnum.STAT_SERVICE_SYNC_LIST.name();
    }
}
