package com.youxin.risk.driver.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.youxin.risk.driver.interfaces.DataActioner;

@Service("hfqLineMiddleDataActioner")
public class HfqLineMiddleDataActioner implements DataActioner {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public boolean handData(Object data, Object jobRecord) {
    	//好分期贷中已结清暂无需分页,处理完成返回为true,结束job
        return true;
    }
}
