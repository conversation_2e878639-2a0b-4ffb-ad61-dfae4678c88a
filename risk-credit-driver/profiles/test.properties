mode.name=test
app.name=risk-credit-driver

home.base=/home/<USER>

app.home=${home.base}/risk-control/${app.name}
app.log.home=${catalina.base}/logs

tomcat.home=${home.base}/products/tomcat/tomcat_risk_credit_driver
tomcat.port=8101
tomcat.shutdown.port=8102
tomcat.connection.timeout=5000
tomcat.doc.base=${app.home}
tomcat.allow.ips=172.*.*.*||127.0.0.1||10.*.*.*

java.opts=-Xmx2000m -Xms2000m -Xmn1000m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=128m \
		-verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xloggc:$CATALINA_HOME/logs/gc.log \
		-XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$CATALINA_HOME/logs/oom.log \
		-Djava.nio.channels.spi.SelectorProvider=sun.nio.ch.EPollSelectorProvider \
        -Dfile.encoding=UTF8  -Duser.timezone=GMT+08

console.log.level=DEBUG

datasource.maxActive=200
datasource.initialSize=2
datasource.minIdle=2
datasource.maxWait=2000
datasource.testOnBorrow=true
datasource.defaultTransactionIsolation=4
datasource.timeBetweenEvictionRunsMillis=30000
datasource.minEvictableIdleTimeMillis=300000
datasource.timeBetweenLogStatsMillis=300000
datasource.druid.remove.abandoned=false
datasource.druid.remove.abandoned.timeout=300
datasource.druid.log.abandoned=false
datasource.connectProperties.connectTimeout=1000
datasource.connectProperties.socketTimeout=5000
datasource.logAbandoned=false
datasource.removeAbandoned=true
datasource.removeAbandonedTimeout=120
datasource.poolPreparedStatements=false
#datasource.filters=stat,wall,log4j
datasource.filters=stat,wall

datasource.url.params=characterEncoding=utf8&amp;autoReconnect=true&amp;zeroDateTimeBehavior=convertToNull&amp;useUnicode=true&amp;useOldAliasMetadataBehavior=true

admin.datasource.url=*****************************************?${datasource.url.params}
admin.datasource.username=test
admin.datasource.pwd=cfx4TxzSdTXOXuyUOcL1

cd.datasource.url=*************************************************?${datasource.url.params}
cd.datasource.username=test
cd.datasource.pwd=cfx4TxzSdTXOXuyUOcL1

stat.datasource.url=******************************************?${datasource.url.params}
stat.datasource.username=test
stat.datasource.pwd=cfx4TxzSdTXOXuyUOcL1

haohuan.datasource.url=*********************************************?${datasource.url.params}
haohuan.datasource.username=admin
haohuan.datasource.pwd=<EMAIL>

datasource.admin.maxActive=50
datasource.admin.initialSize=2
datasource.admin.minIdle=2

redis.maxTotal=8
redis.maxIdle=8
redis.minIdle=4
redis.maxWaitMillis=5000
redis.testOnBorrow=true
redis.cluster.connectionTimeout=3000
redis.cluster.soTimeout=3000
redis.cluster.maxAttempts=1
redis.cluster.password=passwd456
redis.cluster.nodes=************:7000,************:7001,************:7002,\
  ************:7100,************:7101,************:7102


metrics.remote.queue.server=${redis.cluster.nodes}
metrics.remote.queue.redis.password=${redis.cluster.password}
metrics.stop=false

di.url=http://antifraud-risk-di.test.rrdbg.com/handler

rrd.getUserBaseInfo.url=http://************:9100/thirdparty/getUserBaseInfo

gateway.crediting.url=http://antifraud-risk-gateway.test.rrdbg.com/risk/api/analyse


kafka.dp.hosts=hadoop-1:9092,hadoop-2:9092,hadoop-3:9092
kafka.mirror.dp.hosts=***********:9092,***********:9092,***********:9092
kafka.admin.cd.topic=risk.admin.cd.userKey.topic.test
kafka.admin.cd.topic.group.id=risk_admin_cd_group_test

kafka.engine.delay.event.message.topic=kafka.engine.delay.event.message.topic.test
kafka.engine.delay.event.message.topic.group.id=kafka.engine.delay.event.message.group

dc.batch.query.url=http://antifraud-risk-datacenter.test.rrdbg.com/dc/handler/queryUserBasicInfo
dc.inside.batch.query.url=http://antifraud-risk-datacenter-inside.test.rrdbg.com/dc/handler/queryUserBasicInfo

send.alert.url=http://antifraud-risk-alert.test.rrdbg.com/alert/api/event/riskAlert/v1

mongo.risk.host=***********:27017
mongo.risk.username=test
mongo.risk.password=test
mongo.risk.database=risk
mongo.risk.credentials=${mongo.risk.username}:${mongo.risk.password}@${mongo.risk.database}

#mongo.sharding.host=************:27017
#mongo.sharding.username=risk
#mongo.sharding.password=j8cWZbL9PHK3NUdAeNBB
#mongo.sharding.database=risk
#mongo.sharding.credentials=${mongo.sharding.username}:${mongo.sharding.password}@${mongo.sharding.database}

mongo.sharding.host=************:27017
mongo.sharding.username=test
mongo.sharding.password=test
mongo.sharding.database=risk
mongo.sharding.credentials=${mongo.sharding.username}:${mongo.sharding.password}@${mongo.sharding.database}


haofenqi.userCancel.url=http://haofenqi-user-server.test.rrdbg.com/api/v1/user/cancel

youxin.env=DEV


metrics.point.kafka.hosts=hadoop-1:9092,hadoop-2:9092,hadoop-3:9092
metrics.point.kafka.topic=metrics.point.kafka.topic_test
metrics.point.kafka.group.id=metrics.point.kafka.group_test
metrics.point.kafka.topic.list=metrics.point.kafka.topic_test,metrics.point.kafka.topic.gateway_test
metrics.point.mirror.kafka.hosts=***********:9092,***********:9092,***********:9092

# xxljob config
xxl.job.admin.addresses=http://risk-xxl-job-manager.test.rrdbg.com
xxl.job.accessToken=
xxl.job.executor.appname=risk-credit-driver
xxl.job.executor.address=
xxl.job.executor.ip=
xxl.job.executor.port=-1
xxl.job.executor.logpath=/tmp/
xxl.job.executor.logretentiondays=-1